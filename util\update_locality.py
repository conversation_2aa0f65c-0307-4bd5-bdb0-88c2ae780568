from main import app
import requests
from time import sleep
import html
from db_config import db
from models import DriverInfo, DriverBaseLoc
import csv
import logging
from sqlalchemy import or_, and_, not_
from datetime import datetime
import os

script_dir = os.path.dirname(os.path.abspath(__file__))

output_dir = os.path.join(script_dir, 'output')
os.makedirs(output_dir, exist_ok=True)

log_file_path = os.path.join(output_dir, 'update_latlong.log')
csv_file_path = os.path.join(output_dir, 'latlong_updates.csv')

logging.basicConfig(filename=log_file_path, level=logging.INFO, format='%(asctime)s - %(message)s')

def copy_latlong_from_baseloc_to_info():
    drivers_to_update = db.session.query(DriverBaseLoc, DriverInfo).join(
        DriverInfo, DriverBaseLoc.driver_id == DriverInfo.driver_id
    ).filter(
        and_(
            or_(DriverInfo.pres_addr_lat <= 1, DriverInfo.pres_addr_lng <= 1),
            and_(DriverBaseLoc.homelat > 1, DriverBaseLoc.homelong > 1)
        )
    ).all()

    with open(csv_file_path, mode='w', newline='') as csv_file:
        fieldnames = ['driver_id', 'old_lat', 'old_lng', 'new_lat', 'new_lng']
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
        writer.writeheader()

        for dbl_instance, di_instance in drivers_to_update:
            old_lat = di_instance.pres_addr_lat
            old_lng = di_instance.pres_addr_lng
            new_lat = dbl_instance.homelat
            new_lng = dbl_instance.homelong

            di_instance.pres_addr_lat = new_lat
            di_instance.pres_addr_lng = new_lng

            db.session.add(di_instance)

            logging.info(f'driver_id {di_instance.driver_id} lat/lng from ({old_lat}, {old_lng}) to ({new_lat}, {new_lng})')

            writer.writerow({
                'driver_id': di_instance.driver_id,
                'old_lat': old_lat,
                'old_lng': old_lng,
                'new_lat': new_lat,
                'new_lng': new_lng
            })

    db.session.commit()


if __name__ == '__main__':
    print("we started updating regions............", flush=True)
    with app.app_context():
        copy_latlong_from_baseloc_to_info()