tags:
  - Booking
summary: Confirm a booking
description: This API confirms a booking using the given search ID and payment type.
consumes:
  - application/x-www-form-urlencoded
produces:
  - application/json
parameters:
  - name: search_id
    in: formData
    type: integer
    required: true
    description: ID of the previously registered search
  - name: payment_type
    in: formData
    type: integer
    required: true
    description: Payment method selected by the user
  - name: is_immediate
    in: formData
    type: boolean
    required: false
    default: false
    description: "Set to true if the booking should be made immediately"
  - name: driver_type
    in: formData
    type: integer
    required: false
    default: 0
    description: "0 for classic, 1 for premium"
  - name: X-Timezone
    in: header
    type: string
    required: false
    default: Asia/Kolkata
    description: Client timezone
responses:
  200:
    description: Booking confirmed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Booking created successfully
        data:
          type: object
          properties:
            search_id:
              type: integer
              example: 1234
            reference_id:
              type: string
              example: "ABCD1234"
            id:
              type: string
              example: "**********"
            estimate:
              type: object
              properties:
                final_price:
                  type: number
                  example: 850.00
                fare_breakdown:
                  type: object
                  properties:
                    base_fare:
                      type: number
                      example: 600.0
                    dist_fare:
                      type: number
                      example: 100.0
                    insurance_fee:
                      type: number
                      example: 20.0
                    night_charge:
                      type: number
                      example: 50.0
                    surge_charge:
                      type: number
                      example: 0.0
                    driver_type_surcharge:
                      type: number
                      example: 30.0
                    driver_base_charge:
                      type: number
                      example: 80.0
                    booking_charge:
                      type: number
                      example: 20.0
                    immediate_surcharge:
                      type: number
                      example: 10.0
                    coupon_discount:
                      type: number
                      example: -30.0
  400:
    description: Bad Request or business validation failure
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        status:
          type: integer
          example: 400
        message:
          type: string
          example: Booking window expired
  401:
    description: Unauthorized or forbidden role
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: integer
          example: 401
        message:
          type: string
          example: Invalid role - Driver
  403:
    description: Banned user attempting to book
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: integer
          example: 403
        message:
          type: string
          example: User banned to create booking
  422:
    description: Validation error for payload
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        message:
          type: array
          items:
            type: string
  500:
    description: Internal Server Error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -14
        status:
          type: integer
          example: 500
        message:
          type: string
          example: Server error during confirmation
