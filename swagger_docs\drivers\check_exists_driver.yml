tags:
  - Authentication
summary: Check if mobile is registered as driver or user
description: |
  Checks if the provided mobile number exists in the system and whether it is registered as a driver, user, or not at all. 
  Also indicates if the user is banned.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number to check
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")

responses:
  200:
    description: Existence check completed
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: The account is registered as a driver.
        data:
          type: object
          properties:
            exists:
              type: integer
              description: >
                Indicates user status:
                - 0: Not registered
                - 1: Registered as user
                - 2: Registered as driver
                - -1: Banned
              example: 2

  400:
    description: Validation error in request
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Missing required field: mobile"

  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
