#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra# === Standard Library ===

# === Standard Library ===

import http.client
import time
from sqlalchemy import exc
from flask import jsonify
from datetime import timedelta,datetime
from flask import current_app as app
from flask_jwt_extended import create_access_token,create_refresh_token, set_access_cookies, set_refresh_cookies
import base64
import os
import json
import re, requests
import hashlib

# === Third-Party Libraries ===

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

# === Application (Internal) Imports ===

from db_config import db
from services.socket.socketio_app import live_update_to_channel,send_notification_to_channel
from models.models import Drivers, DriverLog, UserToken
from services.customer_app.otp_service import verify_otp_gupshup
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from utils.auth.login_utils import verify_token,validate_pass
from utils.security_utils import get_pwd,get_salt
from utils.response_utils import standard_response
from utils.profile.profile_utils import account_enabled,get_user_ref_code,get_pic_url
from utils.auth.login_params import ExpiryTime,TokenValidity
from models.models import Users, UserToken, AdminUserLog
from urllib.parse import urlencode
from urllib.parse import quote
from config import BaseConfig

def register_customer(validated_data: dict) -> dict:
    mobile = validated_data["mobile"]
    pwd = validated_data["pwd"]
    fname = validated_data["fname"]
    token_and_secret = validated_data["token_and_secret"]
    lname = validated_data.get("lname", "")
    email = validated_data.get("email", "")
    country_code = validated_data.get("countrycode", "91").replace("+", "")

    # Step 1: Token validation
    token_verification = verify_token(mobile, token_and_secret)
    if token_verification == TokenValidity.INVALID_TOKEN:
        return standard_response(-7, 401, "Invalid token or secret. Verification failed.")
    elif token_verification == TokenValidity.EXPIRED_TOKEN:
        return standard_response(-8, 403, "The token has expired. Please request a new password reset link.")

    # Step 2: Check email existence
    if email:
        if db.session.query(Users).filter(Users.email == email).first():
            return standard_response(-10, 409, "Email is already registered.",response_status="error")

    # Step 3: Check user existence
    exist_user = db.session.query(Users, AdminUserLog).filter(Users.mobile == mobile).first()

    if exist_user:
        user, admin_log = exist_user
        if user.is_blocked:
            return standard_response(-4, 409, "Mobile number is already registered.")

        if admin_log and admin_log.action == AdminUserLog.USER_CREATED:
            user.fname = fname
            user.lname = lname
            pwd_salt = get_salt()
            user.salt = pwd_salt
            user.pwd = get_pwd(pwd, pwd_salt)
            admin_log.action = AdminUserLog.USER_UPDATED
        else:
            return standard_response(-1, 400, "Missing required fields: mobile, pwd, fname.",response_status="error")
    else:
        user = Users(
            mobile=mobile,
            country_code=country_code,
            fname=fname,
            lname=lname,
            email=email,
            pwd=get_pwd(pwd, get_salt()),
            role=Users.ROLE_USER,
        )
        db.session.add(user)

    # Step 4: Save and Notify
    try:
        user = db.session.query(Users).filter(Users.mobile == mobile).first()
        notification = {
            'id': mobile,
            'type': 'App Registration',
            'username': f"{fname} {lname}".strip(),
            'content': f"New customer {fname} {lname} registered.",
            'imageUrl': '/assets/icons/register_icon.svg',
            'timestamp': int(time.time() * 1000)
        }
        send_notification_to_channel(notification, f'Customer Register:{user.region}')
        db.session.commit()
        return {'response': 0, 'msg': "Success"}
    except exc.IntegrityError:
        db.session.rollback()
        return standard_response(-9, 500, "An unexpected error occurred. Please try again later.",response_status="error")
    except Exception:
        db.session.rollback()
        return standard_response(-9, 500, "An unexpected error occurred. Please try again later.",response_status="error")
   
    
def handle_login(validated_data, user_agent):
    mobile = validated_data.mobile
    password = validated_data.password
    auth_type = validated_data.auth_type
    country_code = validated_data.countrycode.replace("+", "") or "91"

    cur_user = Users.query.filter_by(mobile=mobile).first()

    if not cur_user:
        return standard_response(
            success=-2,
            status=404,
            message="Mobile number not found. Please sign up first."
        ), 404

    if not cur_user.enabled:
        return standard_response(
            success=-12,
            status=423,
            message="Account is banned.",
            response_status="error"
        ), 423

    admin_user = AdminUserLog.query.filter_by(user=cur_user.id).first()
    if admin_user and admin_user.action == AdminUserLog.USER_CREATED:
        return standard_response(
            success=-1,
            status=400,
            message="Please register yourself",
            response_status="error"
        ), 400

    redis_key = f"failed_login_attempts_{mobile}"
    lock_ttl = execute_with_fallback('ttl', redis_key)
    if lock_ttl > 0:
        return standard_response(
            success=-8,
            status=423,
            message=f"Account locked due to multiple failed attempts. Try again in {lock_ttl} seconds.",
            response_status="error"
        ), 423

    is_valid = False
    if auth_type == 'password':
        is_valid = validate_pass(user=cur_user, pwd=password)
    elif auth_type == 'otp':
        otp_response = verify_otp_gupshup(country_code + mobile, password)
        is_valid = otp_response.get("status") == "success"

    if is_valid:
        write_redis_data(redis_key, {"count": 0})
        cur_user.agent = user_agent

        remember = validated_data.remember or False
        expires_access = timedelta(days=730 if remember else 365)
        expires_refresh = timedelta(days=3650)

        identity_with_claims = {
            'id': cur_user.id,
            'roles': cur_user.role,
            'region': cur_user.region,
            'name': f'{cur_user.fname} {cur_user.lname}',
        }

        access_token = create_access_token(
            identity=cur_user.id,
            additional_claims=identity_with_claims,
            expires_delta=expires_access
        )
        refresh_token = create_refresh_token(
            identity=identity_with_claims,
            additional_claims=identity_with_claims,
            expires_delta=expires_refresh
        )

        expiry = datetime.utcnow() + expires_refresh
        token_entry = UserToken(
            cur_user.id, refresh_token, user_agent, expiry, login_from=UserToken.CUST_LOGIN
        )

        try:
            db.session.add(token_entry)
            db.session.commit()
        except exc.IntegrityError:
            db.session.rollback()
            return standard_response(
                success=-10,
                status=500,
                message="An unexpected error occurred. Please try again later."
            ), 500

        access_expiry_time = datetime.utcnow() + expires_access
        refresh_expiry_time = datetime.utcnow() + expires_refresh

        response_data = {
            'success': 1,
            'user_id': cur_user.id,
            'user_fname': cur_user.fname,
            'user_mobile': cur_user.mobile,
            'user_countrycode': '+' + country_code,
            'user_email': cur_user.email,
            'user_lname': cur_user.lname,
            'user_restore_id': cur_user.restore_id,
            'user_ref_code': get_user_ref_code(cur_user),
            'user_credit': cur_user.credit,
            'access_token': access_token,
            'refresh_token': refresh_token,
            'access_expiry': access_expiry_time.isoformat() + 'Z',
            'refresh_expiry': refresh_expiry_time.isoformat() + 'Z'
        }

        # Create the response only once
        response_payload = standard_response(
            success=1,
            status=200,
            message="Login successful.",
            data=response_data,
            response_status="success"
        )

        resp = jsonify(response_payload)
        set_access_cookies(resp, access_token)
        set_refresh_cookies(resp, refresh_token)

        return resp, 200


    else:
        redis_data,_ = read_redis_data(redis_key)
        failed_attempts = redis_data.get('count', 0) + 1

        if failed_attempts >= ExpiryTime.FAILED_ATTEMPTS_ALLOWED:
            base_expiry = 900
            additional_attempts = failed_attempts - 3
            lock_expiry = base_expiry * (2 ** additional_attempts)
            update_redis_data(redis_key, {"count": failed_attempts})
            lock_key = f"{redis_key}_lock"
            execute_with_fallback('set', lock_key, "locked")
            execute_with_fallback('expire', lock_key, lock_expiry)

            return standard_response(
                success=-8,
                status=423,
                message=f"Account locked due to multiple failed attempts. Try again in {lock_expiry} seconds.",
                response_status="error"
            ), 423

        update_redis_data(redis_key, {"count": failed_attempts})
        return standard_response(
            success=-5,
            status=401,
            message=f"Incorrect password/otp. Failed attempt: {failed_attempts}.",
            response_status="error"
        ), 401


def handle_driver_login(validated_data, user_agent: str) -> dict:
    mobile = validated_data.mobile
    password = validated_data.password
    auth_type = validated_data.auth_type
    country_code = validated_data.countrycode
    device_type = validated_data.device_type
    remember = validated_data.remember if hasattr(validated_data, 'remember') else False

    # Rate-limit check
    redis_key = f"failed_login_attempts_{mobile}"
    lock_ttl = execute_with_fallback('ttl', redis_key)
    if lock_ttl > 0:
        return standard_response(
            success=-8,
            status=423,
            message=f"Account locked due to multiple failed attempts. Try again in {lock_ttl} seconds.",
            response_status="error"
        )

    # Fetch user
    cur_user = Users.query.filter_by(mobile=mobile).first()
    if not cur_user or not cur_user.enabled:
        return standard_response(
            success=-2,
            status=401,
            message="Invalid mobile number or account disabled.",
            response_status="error"
        )

    # Credentials check
    if auth_type == 'password':
        valid = validate_pass(user=cur_user, pwd=password)
    else:
        valid = verify_otp_gupshup(country_code + mobile, password)

    if not valid:
        # Increment failed attempts
        data,_ = read_redis_data(redis_key)
        attempts = data.get('count', 0) + 1
        if attempts >= ExpiryTime.FAILED_ATTEMPTS_ALLOWED:
            # exponential backoff lock
            lock_expiry = 900 * (2 ** (attempts - 3))
            update_redis_data(redis_key, {'count': attempts})
            execute_with_fallback('set', f"{redis_key}_lock", 'locked')
            execute_with_fallback('expire', f"{redis_key}_lock", lock_expiry)
            return standard_response(
                success=-3,
                status=423,
                message=f"Account locked due to multiple failed attempts. Try again in {lock_expiry} seconds.",
                response_status="error"
            )
        update_redis_data(redis_key, {'count': attempts})
        return standard_response(
            success=-2,
            status=401,
            message=f"Invalid credentials. Failed attempt: {attempts}.",
            response_status="error"
        )

    # Success path: reset counter
    write_redis_data(redis_key, {'count': 0})

    # Driver approval check
    cur_driver = Drivers.query.filter_by(user=cur_user.id).first()
    if not cur_driver or cur_driver.approved != Drivers.APPROVED:
        return standard_response(
            success=-3,
            status=403,
            message="Driver account is not approved.",
            response_status="error"
        )

    # Token generation
    expires_access = timedelta(days=730 if remember else 365)
    expires_refresh = timedelta(days=3650)
    claims = {
        'id': cur_user.id,
        'roles': cur_user.role,
        'region': cur_user.region,
        'name': f"{cur_user.fname} {cur_user.lname}",
    }
    access_token = create_access_token(identity=cur_user.id, additional_claims=claims, expires_delta=expires_access)
    refresh_token = create_refresh_token(identity=claims, additional_claims=claims, expires_delta=expires_refresh)

    # Persist token and log
    try:
        expiry = datetime.utcnow() + expires_refresh
        token_entry = UserToken(cur_user.id, refresh_token, user_agent, expiry, login_from=UserToken.DRIVER_LOGIN)
        db.session.add(token_entry)
        db.session.add(DriverLog(cur_driver.id, 'login', 'Login successful'))
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return standard_response(
            success=-7,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )

    # Prepare response
    result = {
        'success': 1,
        'driver_id': cur_driver.id,
        'user_id': cur_user.id,
        'user_ref_code': get_user_ref_code(cur_user),
        'available': cur_driver.available,
        'approved': cur_driver.approved,
        'driver_pic': get_pic_url(cur_driver.pic),
        'driver_rating': cur_driver.rating,
        'user_mobile': cur_user.mobile,
        'user_fname': cur_user.fname,
        'user_lname': cur_user.lname,
        'user_email': cur_user.email,
        'access_token': access_token,
        'refresh_token': refresh_token
    }
    resp = jsonify(result)
    set_access_cookies(resp, access_token)
    set_refresh_cookies(resp, refresh_token)
    return result


# === Service ===
def handle_forgot_password(data) -> dict:
    mobile = data.mobile
    password = data.password
    token_and_secret = data.token_and_secret
    country_code = data.countrycode

    # Verify token and secret
    token_status = verify_token(mobile, token_and_secret)
    if token_status == TokenValidity.INVALID_TOKEN:
        return standard_response(
            success=-5,
            status=401,
            message="Invalid token or secret. Verification failed.",
            response_status="error"
        )
    if token_status == TokenValidity.EXPIRED_TOKEN:
        return standard_response(
            success=-6,
            status=403,
            message="The token has expired. Please request a new password reset link.",
            response_status="error"
        )

    # Check user exists
    user = Users.query.filter_by(mobile=mobile).first()
    if not user:
        return standard_response(
            success=-7,
            status=404,
            message="Mobile number not registered.",
            response_status="error"
        )

    # Reset failed attempt counter
    write_redis_data(f"failed_login_attempts_{mobile}", {'count': 0})

    # Update password
    new_salt = get_salt()
    user.salt = new_salt
    user.pwd = get_pwd(password, new_salt)

    try:
        db.session.commit()
        return standard_response(
            success=1,
            status=200,
            message="Password reset successfully.",
            response_status="success"
        )
    except Exception as e:
        db.session.rollback()
        return standard_response(
            success=-8,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )


