from datetime import datetime,timedelta   
from models.affiliate_models import db,Affiliate,AffiliateRep,AffiliateRepLogs,Affiliate<PERSON>poc,AffiliateAddress
from conftest import create_master_affiliate
from models.models import Users
from unittest.mock import patch
from sqlalchemy import exc
import pytest


#  API -  /api/admin/affiliate/register_rep

def test_affiliate_register_rep_incomplete_form(client, admin_login):
    auth_headers, _ = admin_login
    form_data = {
        'username': 'rep_user',
        'mobile': '9876543210',
        'regions':'0',
        # Missing 'affiliate_id'
    }
    response = client.post('/api/admin/affiliate/register_rep', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['msg'] == "Incomplete form"
    
def test_affiliate_register_rep_username_exists(client, admin_login):
    auth_headers, _ = admin_login
    # Create a dummy rep
    rep = AffiliateRep(
        aff_id=1,
        fullname='Existing Rep',
        user_name='rep_user',
        mobile='9876543210',
        pwd='dummy',
        admin=1,
        notification_access=0,
        tab_access=0,
        region_access='0',
        create_booking_access='0',
        email='<EMAIL>',
        enabled=True
    )
    db.session.add(rep)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    form_data = {
        'affiliate_id': '1',
        'username': 'rep_user',
        'mobile': '9876543210',
        'regions':'0',
    }
    response = client.post('/api/admin/affiliate/register_rep', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    json_data = response.get_json()
    assert json_data['success'] == 0
    assert 'Username already exists' in json_data['message']
    
def test_affiliate_register_rep_invalid_affiliate(client, admin_login):
    auth_headers, _ = admin_login
    form_data = {
        'affiliate_id': '99999',  # Non-existent
        'username': 'rep_user',
        'mobile': '9876543210',
        'email': '<EMAIL>',
        'regions':'0',
    }
    response = client.post('/api/admin/affiliate/register_rep', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -2
    assert 'Affiliate ID is invalid' in json_data['message']
    
    
def test_affiliate_register_rep_invalid_regions(client, admin_login):
    auth_headers, _ = admin_login

    # Assuming affiliate with id 1 exists and has regions = 'Region1,Region2'
    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None
    form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'rep_user',
        'mobile': '9876543210',
        'email': '<EMAIL>',
        'regions': '1'  # Invalid
    }
    response = client.post('/api/admin/affiliate/register_rep', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -3
    assert 'Invalid regions' in json_data['message']
    
    

def test_affiliate_register_rep_success(client, admin_login):
    auth_headers, admin = admin_login

    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None

    form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'unique_user',
        'mobile': '9876543210',
        'email': '<EMAIL>',
        'fullname': 'Rep Fullname',
        'regions': '0',
        'tab_access': '1,2',
        'notifications': '0,1',
        'regions':'0',
    }
    response = client.post('/api/admin/affiliate/register_rep', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert 'Registered Successfully' in json_data['message']
    
    
# --------------------------------- 

#  API -  /api/admin/affiliate/all_affiliate_rep
    
def test_affiliate_all_reps_success(client, admin_login):
    auth_headers, admin = admin_login

    # Create master affiliate
    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None

    # Create rep for that affiliate
    rep_form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'allrep_testuser',
        'mobile': '9988776655',
        'email': '<EMAIL>',
        'fullname': 'All Rep Fullname',
        'regions': '0',
        'tab_access': '0,1',
        'notifications': '2,3',
        'regions':'0',
    }
    form_data = {
        'regions':'0',
    }
    register_response = client.post('/api/admin/affiliate/register_rep', data=rep_form_data, headers=auth_headers)
    assert register_response.status_code == 200
    json_data = register_response.get_json()
    assert json_data['success'] == 1
    print( json_data)
    # Fetch all affiliate reps
    fetch_response = client.post('/api/admin/affiliate/all_affiliate_rep',data=form_data, headers=auth_headers)
    assert fetch_response.status_code == 200
    res_json = fetch_response.get_json()

    assert res_json['success'] == 1
    print( res_json)
    reps = res_json.get('data', [])
    usernames = [rep['rep_username'] for rep in reps]
    assert 'allrep_testuser' in usernames
    
    
# --------------------------------- 

#  API -  /api/admin/affiliate/all_affiliate_rep

def test_add_new_spoc_success(client, admin_login):
    auth_headers, admin = admin_login

    # Create master affiliate
    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None

    # Create rep for that affiliate
    rep_form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'spoc_testuser',
        'mobile': '9988776655',
        'email': '<EMAIL>',
        'fullname': 'Spoc Test Fullname',
        'regions': '0',          # region provided during registration
        'tab_access': '0,1',
        'notifications': '2,3'
    }
    register_response = client.post('/api/admin/affiliate/register_rep', data=rep_form_data, headers=auth_headers)
    assert register_response.status_code == 200
    reg_json = register_response.get_json()
    # Ensure rep registration succeeded
    assert reg_json['success'] == 1

    # Retrieve the rep from the DB (or use returned data if available)
    rep = db.session.query(AffiliateRep).filter_by(user_name='spoc_testuser').first()
    assert rep is not None

    # Prepare payload for adding SPOC
    spoc_payload = {
        "rep_id": rep.id,
        "spocDetails": [
            {
                "id": -1,
                "name": "fff",
                "mobile": "9879898798",
                "region": 0,
                "global": True
            }
        ],
        "regions": "-1"
    }

    # Call the add_new_spoc endpoint with JSON payload
    response = client.post('/api/admin/affiliate/add_spoc', json=spoc_payload, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert "SPOC data inserted successfully" in json_data['message']
    
# --------------------------------- 

#  API -  /api/admin/affiliate/get_update_affiliate_rep

def test_get_affiliate_rep_details(client, admin_login):
    auth_headers, admin = admin_login

    # Create master affiliate
    master_affiliate, _ = create_master_affiliate()

    # Register a new rep
    rep_form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'rep_get_test',
        'mobile': '9123456789',
        'email': '<EMAIL>',
        'fullname': 'Rep Get Test',
        'regions': '0',
        'tab_access': '0,1',
        'notifications': '2,3'
    }
    register_response = client.post('/api/admin/affiliate/register_rep', data=rep_form_data, headers=auth_headers)
    assert register_response.status_code == 200
    reg_data = register_response.get_json()
    assert reg_data['success'] == 1

    rep = AffiliateRep.query.filter_by(user_name='rep_get_test').first()
    assert rep is not None

    get_form = {
        'type': 1,
        'rep_id': rep.id
    }

    response = client.post('/api/admin/affiliate/get_update_affiliate_rep', data=get_form, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()

    assert json_data['success'] == 1
    assert json_data['data']['username'] == 'rep_get_test'
    
 
# --------------------------------- 

#  API -  /api/admin/affiliate/get_update_affiliate_rep

   
def test_delete_affiliate_rep(client, admin_login):
    auth_headers, admin = admin_login
    # Create master affiliate
    master_affiliate, _ = create_master_affiliate()

    # Register a rep
    rep_form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'rep_delete_test',
        'mobile': '9000000000',
        'email': '<EMAIL>',
        'fullname': 'Rep Delete Test',
        'regions': '0',
        'tab_access': '0,1',
        'notifications': '2,3'
    }
    client.post('/api/admin/affiliate/register_rep', data=rep_form_data, headers=auth_headers)
    rep = AffiliateRep.query.filter_by(user_name='rep_delete_test').first()
    assert rep is not None

    delete_form = {
        'type': 3,
        'rep_id': rep.id
    }
    rep_id = rep.id

    response = client.post('/api/admin/affiliate/get_update_affiliate_rep', data=delete_form, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()

    assert json_data['success'] == 1
    assert "deleted successfully" in json_data['msg']
    rep = AffiliateRep.query.filter_by(user_name='rep_delete_test').first()
    # Confirm it's removed from DB
    deleted_rep = AffiliateRep.query.filter_by(id=rep_id).first()
    assert deleted_rep is None
 
# --------------------------------- 

#  API -  /api/admin/affiliate/get_affiliate_rep_logs
    
def test_get_affiliate_rep_logs(client, admin_login):
    auth_headers, admin = admin_login
    # Create master affiliate
    master_affiliate, _ = create_master_affiliate()

    # Register a rep
    rep_form_data = {
        'affiliate_id': master_affiliate.id,
        'username': 'log_test_rep',
        'mobile': '9000111122',
        'email': '<EMAIL>',
        'fullname': 'Log Rep',
        'regions': '0',
        'tab_access': '0,1',
        'notifications': '2,3'
    }
    register_response = client.post('/api/admin/affiliate/register_rep', data=rep_form_data, headers=auth_headers)
    assert register_response.status_code == 200
    json_data = register_response.get_json()
    assert json_data['success'] == 1

    # Fetch the rep
    rep = AffiliateRep.query.filter_by(user_name='log_test_rep').first()
    assert rep is not None
    rep_id = rep.id

    # Make request to logs API
    response = client.get(f'/api/admin/affiliate/get_affiliate_rep_logs?rep_id={rep.id}&regions=0', headers=auth_headers)
    assert response.status_code == 200
    res_json = response.get_json()

    assert res_json['success'] == 1
    assert 'logs' in res_json
    assert len(res_json['logs']) > 0

    log_item = res_json['logs'][0]
    rep = db.session.query(AffiliateRep).filter_by(id=rep_id).first()
    admin = db.session.query(Users).filter_by(id=admin).first()
    assert log_item['rep_id'] == rep.id
    assert log_item['action'] == 'Created'
    assert log_item['changed_by'] == admin.id
    assert log_item['changed_by_name'] == admin.fname + " " + admin.lname
    assert log_item['oldvalue'] == "N/A"
    assert log_item['newvalue'] == "0,1"
    

@pytest.fixture
def test_affiliate_and_rep():
    aff = Affiliate("SpocTestAff","SpocTestAff",  0 , -1, 10, 1)
    db.session.add(aff)
    db.session.flush()

    rep = AffiliateRep(  aff_id=aff.id,
        fullname='Existing Rep',
        user_name='rep_user',
        mobile='9876543210',
        pwd='dummy',
        admin=1,
        notification_access=0,
        tab_access=0,
        region_access='0',
        email='<EMAIL>',
        create_booking_access='0',
        enabled=True
        )
    db.session.add(rep)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()


    return aff, rep  
    


#  API -  /api/admin/affiliate/spoc_list_rep 
    
    
def test_spoc_list_rep_success(client, admin_login, test_affiliate_and_rep):
    auth_headers, _ = admin_login
    _, rep = test_affiliate_and_rep

    response = client.get(
        f"/api/admin/affiliate/spoc_list_rep?rep_id={rep.id}&regions=0",
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert isinstance(res["spocs"], list)


def test_spoc_list_rep_missing_id(client, admin_login):
    auth_headers, _ = admin_login
    response = client.get(
        "/api/admin/affiliate/spoc_list_rep?regions=0",  # No rep_id param
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 400
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative ID is required"


def test_spoc_list_rep_not_found(client, admin_login):
    auth_headers, _ = admin_login
    response = client.get(
        "/api/admin/affiliate/spoc_list_rep?rep_id=999999&regions=0",  # Invalid rep_id
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 404
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative not found"

# --------------------------------- 

#  API -  /api/admin/affiliate/update_spoc 

def test_update_spoc_success(client, admin_login, test_affiliate_and_rep):
    auth_headers, _ = admin_login
    aff, rep = test_affiliate_and_rep
    spoc = AffiliateSpoc("SPOC", 9999999999, 1, rep.id, aff.id,0)
    db.session.add(spoc)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    spoc_payload = {
        "rep_id": rep.id,
        "spoc": {
            "id": spoc.spoc_id,
            "spoc_name": "SPOC One",
            "spoc_mobile": 9999999999,
            "spoc_type": 1,
            "spoc_region": "0",
        },
        "regions":"0",
    }

    response = client.put(
        "/api/admin/affiliate/update_spoc",
        json=spoc_payload,
        headers=auth_headers
    )
    res = response.get_json()
    print(res)
    assert response.status_code == 200
    assert res["success"] == 1
    assert "spoc_id" in res


def test_update_spoc_missing_rep(client, admin_login):
    auth_headers, _ = admin_login
    spoc_payload = {
        "spoc": {
            "spoc_id": 1,
            "spoc_name": "SPOC One",
            "spoc_mobile": "9999999999",
            "spoc_type": "Primary",
            "spoc_region": "0"
        },
        "regions":"0",
    }

    response = client.put(
        "/api/admin/affiliate/update_spoc",
        json=spoc_payload,
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 400
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative ID is required"


def test_update_spoc_invalid_rep(client, admin_login):
    auth_headers, _ = admin_login
    spoc_payload = {
        "rep_id": 999999,
        "spoc": {
            "spoc_id": 1,
            "spoc_name": "Ghost Spoc",
            "spoc_mobile": "8888888888",
            "spoc_type": "Secondary",
            "spoc_region": "0"
        },
        "regions":"0",
    }

    response = client.put(
        "/api/admin/affiliate/update_spoc",
        json=spoc_payload,
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 404
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative not found"
    
# --------------------------------- 

#  API -  /api/admin/affiliate/add_address
 
def test_add_address_success(client, admin_login,test_affiliate_and_rep):
    auth_headers, _ = admin_login
    aff, rep = test_affiliate_and_rep
    address_payload = {
        "rep_id": rep.id,
        "address": [
            {
                "id": -1,
                "lat": 12.9741044,
                "long": 77.7286148,
                "address": "Tesco Bengaluru Main Campus 81 & 82, EPIP Zone, Whitefield, Bengaluru, Karnataka 560066, India",
                "nickname": "test",
                  "region":"0",
                "spoc_list": [
                    {
                        "spoc_id": 2,
                        "spoc_name": "Test",
                          "region":"0",
                    }
                ],
                "global": True
            }
        ],
        "regions":"0",
    }

    response = client.post(
        "/api/admin/affiliate/add_address",
        json=address_payload,
        headers=auth_headers
    )
    res = response.get_json()
    print(res)
    assert response.status_code == 200
    assert res["success"] == 1
    assert res["message"] == "Addresses added successfully"


def test_add_address_missing_rep(client, admin_login):
    auth_headers, _ = admin_login
    response = client.post(
        "/api/admin/affiliate/add_address",
        json={"address": [],"regions":"0"},
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 400
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative ID is required"


# --------------------------------- 

#  API -  /api/admin/affiliate/address_list

def test_get_address_success(client, admin_login,test_affiliate_and_rep):
    auth_headers, _ = admin_login
    aff, rep = test_affiliate_and_rep
    response = client.get(
        f"/api/admin/affiliate/address_list?rep_id={rep.id}&regions=0",
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert "addresses" in res

def test_get_address_rep_not_found(client, admin_login):
    auth_headers, _ = admin_login
    response = client.get(
        "/api/admin/affiliate/address_list?rep_id=999999&regions=0",
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 404
    assert res["success"] == 0
    assert res["message"] == "Affiliate representative not found"

# --------------------------------- 

#  API -  /api/admin/affiliate/update_address

def test_update_address_success(client, admin_login,test_affiliate_and_rep):
    auth_headers, _ = admin_login
    aff, rep = test_affiliate_and_rep
    add = AffiliateAddress("test", "test", 13.0, 77.0, 1, aff.id, rep.id)
    db.session.add(add)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    update_payload = {
        "rep_id": rep.id,
        "regions": "0",
        "address": 
            {
                "id": add.add_id,  # ✅ required for updating
                "lat": 13.0,
                "long": 77.0,
                "address": "Updated Address",
                "nickname": "updated-nick",
                "region":"0",
                "global": True,  # ✅ stays inside each address dict
                "spoc_list": [
                    {
                        "spoc_id": 2,
                        "spoc_name": "Test",
                        "region":"0",
                    }
                ]
            }
    }
    response = client.put(
        "/api/admin/affiliate/update_address",
        json=update_payload,
        headers=auth_headers
    )
    res = response.get_json()
    print(res)
    assert response.status_code == 200
    assert res["success"] == 1
    assert res["message"] == "Addresses updated successfully"
    
    
# --------------------------------- 

#  API -  /api/admin/affiliate/delete_address

def test_delete_address_success(client, admin_login,test_affiliate_and_rep):
    auth_headers, _ = admin_login
    aff, rep = test_affiliate_and_rep
    add = AffiliateAddress("test", "test", 13.0, 77.0, 1, aff.id, rep.id)
    db.session.add(add)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    response = client.delete(
        "/api/admin/affiliate/delete_address",
        data={"address_id": add.add_id,"regions":"0"},  # Provide a valid address ID
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert res["message"] == "Address deleted successfully"

# --------------------------------- 


        
        
