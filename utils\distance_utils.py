#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  distance_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

"""
Utility functions for distance calculations.
"""

import math
import requests
from shapely.geometry import Point, Polygon
from flask import current_app as app
from utils.bookings.booking_params import Regions, CountryBoundary, BookingParams


MIN_LATITUDE_WORLD = -90
MAX_LATITUDE_WORLD = 90
MIN_LONGITUDE_WORLD = -180
MAX_LONGITUDE_WORLD = 180

MIN_LATITUDE_INDIA = 6.55
MAX_LATITUDE_INDIA = 37.10
MIN_LONGITUDE_INDIA = 68.10
MAX_LONGITUDE_INDIA = 97.40
EARTH_RADIUS = 6373  # Earth radius in kilometers

def is_valid_lat_long_world(lat, lon):
    return MIN_LATITUDE_WORLD <= lat <= MAX_LATITUDE_WORLD and MIN_LONGITUDE_WORLD <= lon <= MAX_LONGITUDE_WORLD

def is_valid_lat_long_for_india(lat, lon):
    return MIN_LATITUDE_INDIA <= lat <= MAX_LATITUDE_INDIA and MIN_LONGITUDE_INDIA <= lon <= MAX_LONGITUDE_INDIA

def get_locality_by_lat_long(lat, long):
    try:
        if not lat or not long:
            return False

        url = f"https://apis.mapmyindia.com/advancedmaps/v1/{app.config['MAP_MY_INDIA']}/rev_geocode"
        params = {
            'lat': lat,
            'lng': long
        }
        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            if 'results' in data and data['results']:
                locality = data['results'][0].get('locality') or data['results'][0].get('village') or data['results'][0].get('subDistrict') or data['results'][0].get('district') or data['results'][0].get('formatted_address')
                locality = locality if locality else ''
                formatted_address = data['results'][0].get('formatted_address') if data['results'][0].get('formatted_address') else ''
                return [locality, formatted_address]
            else:
                return False
        else:
            return False


    except Exception as e:
        return False
    
    
def point_inside(dest_long, dest_lat, src_long, src_lat, book_type, location_map, city):
    try:
        cityy, cityx = location_map[city]
    except KeyError:
        # If city is not in location_map, return False
        return False
    pointdest = Point(float(dest_long), float(dest_lat))
    pointsrc = Point(float(src_long), float(src_lat))
    country_map=CountryBoundary.COUNTRY_BOUNDARIES
    indiay,indiax=country_map[CountryBoundary.INDIA]
    polygon_coords_india = list(zip(indiay, indiax))
    polygon_coords_city = list(zip(cityy, cityx))
    polygon_india = Polygon(polygon_coords_india)
    polygon_city = Polygon(polygon_coords_city)

    # Check if the book_type is 1 (check only the source)
    if book_type == 1:
        if not pointsrc.within(polygon_india):
            return False
    else:
        # Check if both the source and destination are within India's boundaries
        if not pointsrc.within(polygon_india) or not pointdest.within(polygon_india):
            return False
    # check booking type
    return check_booking_type(book_type, pointsrc, pointdest, polygon_city)

def point_inside_polygon(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city):
    try:
        if city in Regions.ENABLED_CITIES:
            return point_inside(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city)
        return False
    except (ValueError, KeyError):
        # Handle invalid coordinates or missing city data gracefully
        return False
    
def check_booking_type(book_type, pointsrc, pointdest, polygon_city):
    if book_type == BookingParams.TYPE_ONEWAY:
        # Both destination and source points must be inside or on the boundary
        return (polygon_city.contains(pointdest) or pointdest.touches(polygon_city)) and \
               (polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city))

    elif book_type in (BookingParams.TYPE_MINIOS_ONEWAY, BookingParams.TYPE_OUTSTATION_ONEWAY):
        # One point is within or touching the polygon, the other is not
        #(also we allow if both src and dest are inside)
        return ((polygon_city.contains(pointdest) or pointdest.touches(polygon_city)) and \
                (polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city))) or \
               (((pointdest.within(polygon_city) or pointdest.touches(polygon_city)) and \
                not pointsrc.within(polygon_city)) or \
               (not pointdest.within(polygon_city) and \
               (pointsrc.within(polygon_city) or pointsrc.touches(polygon_city))))

    else:
        # For other booking types, only the src point is checked
        return polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city)
    
    
def get_dist_on_map(source_lat, source_long, dest_lat, dest_long):
    url = "https://maps.googleapis.com/maps/api/distancematrix/json"
    params = {
        "units": "imperial",
        "origins": f"{source_lat},{source_long}",
        "destinations": f"{dest_lat},{dest_long}",
        "mode": "driving",
        "key": app.config['MAPS_DIST_API_KEY']
    }

    for _ in range(5):  # Retry up to 5 times
        try:
            response = requests.get(url, params=params, timeout=1)  # Add timeout for fast failures
            response.raise_for_status()  # Raise error for HTTP failures (4xx, 5xx)
            resp = response.json()
            
            if resp['rows'][0]['elements'][0]['status'] == 'OK':
                return resp['rows'][0]['elements'][0]['distance']['value']

        except (requests.RequestException, KeyError, IndexError) as e:
            print("Exception fetching data from GMap:", str(e))

    return 0  # Return 0 if all attempts fail