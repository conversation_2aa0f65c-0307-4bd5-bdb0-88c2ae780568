#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  security_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>


def feet_and_inches_to_cm(feet: float, inches: float) -> float:
    total_inches = (feet * 12) + inches
    cm = total_inches * 2.54
    return cm


def cm_to_feet_and_inches(cm):
    total_inches = cm / 2.54
    feet = int(total_inches // 12)
    inches = round(total_inches % 12, 2)
    return feet, inches

def add_inches_to_cm(old_cm,additional_inches):
    feet, inches = cm_to_feet_and_inches(old_cm)
    total_inches = (feet * 12) + inches + additional_inches
    new_cm = total_inches * 2.54
    return new_cm
