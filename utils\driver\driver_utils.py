
dl_fetch_url = "https://api.gridlines.io/dl-api/fetch"
face_match_url = "https://api.gridlines.io/face-api/verify"
driver_pic_base_url = "https://storage.drivers4me.com/static/uploads/driver_docs/"
bank_verify_url = "https://api.gridlines.io/bank-api/verify"

    
def compute_driver_wallet(driver_details, total_owed):
    driver_withdrawable, driver_wallet = driver_details.withdrawable, driver_details.wallet
    sum_left = driver_wallet + driver_withdrawable - total_owed
    if total_owed == 0:
        return driver_wallet, driver_withdrawable
    if total_owed < 0 and driver_wallet > 0:
        return driver_wallet, driver_withdrawable - total_owed
    if total_owed > 0 and total_owed < driver_wallet:
        return driver_wallet - total_owed, driver_withdrawable
    if sum_left > 0:
        return 0, sum_left
    return sum_left, 0