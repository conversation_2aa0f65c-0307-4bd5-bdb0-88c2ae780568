#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>


# Built-in imports
from datetime import datetime, timedelta, date
import time
import calendar

# Third-party imports
from flask import Blueprint, request, jsonify
from flasgger import swag_from
from pydantic import BaseModel,ValidationError,Field,field_validator
from typing import Optional, Literal ,List
from flask_jwt_extended import jwt_required
import traceback
from flask import current_app as app

# Internal application imports
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from utils.response_utils import standard_response
from services.admin_dashboard.analytics_general_service import admin_analytics_count_service,get_daily_sales_data, \
    get_analytics_graph_sales,get_analytics_daily_revenue,get_revenue_graph_data,get_daily_trips_graph_data, \
    get_trips_graph_data,get_total_ratings_data,get_customer_registration_counts,get_customer_source_counts, \
    get_cancellation_reason_counts
    
from schemas.admin_dashboard.analytics_schemas import AnalyticsCountPayload, AdminAnalyticsDailySalesPayload, \
    AdminAnalyticsSalesPayload, AdminAnalyticsDailyRevenuePayload, AnalyticsRevenuePayload, DailyTripsPayload, \
        TripsAnalyticsPayload, RatingAnalyticsPayload, CustomerRegistrationCountPayload, CustomerRegisterSourcePayload, \
            CancellationReasonCountPayload

analytics_general = Blueprint('analytics_general', __name__)

  
@analytics_general.route('/api/admin/analytics_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_count.yml')
def admin_analytics_count():
    """
    Get analytics dashboard counts for admin.

    This endpoint returns metrics like total bookings, completed trips, cancellations,
    and revenue based on the input filters.

    Args:
        request (json): JSON payload with optional filters:
            - from_date (str): Start date in YYYY-MM-DD format.
            - to_date (str): End date in YYYY-MM-DD format.
            - region_id (int, optional): Region filter.

    Returns:
        Response: JSON response containing:
            - success (bool)
            - status (int)
            - message (str)
            - data (dict): Dictionary with count metrics.
    """
    try:
        try:
            payload = AnalyticsCountPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                data={'error': error_details},
                response_status="error"
            )), 422

        success, message, data = admin_analytics_count_service(payload)
        if success < 0:
            return jsonify(standard_response(
                success=success,
                status=400,
                message=message,
                response_status="error"
            )), 400
        return jsonify(standard_response(
            success=success,
            status=200,
            message=message,
            data=data,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_count: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
@analytics_general.route('/api/admin/analytics_graph_daily_sales', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS,Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_sales.yml')
def admin_analytics_graph_daily_sales():
    """
    Get daily sales data for admin dashboard.

    Returns daily sales figures grouped by date.

    Args:
        request (json): JSON payload with filters:
            - from_date (str): Start date.
            - to_date (str): End date.
            - region_id (int, optional): Region ID.

    Returns:
        Response: JSON with sales data by day.
    """
    try:
        try:
            payload = AdminAnalyticsDailySalesPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': error_details}
            )), 422

        response_data = get_daily_sales_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily sales analytics",
            data=response_data,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_sales: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500

    
@analytics_general.route('/api/admin/analytics_graph_sales', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_sales.yml')
def admin_analytics_graph_sales():
    """
    Get total sales data over a specified time range.

    Args:
        request (json): JSON payload with filters:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON response with total sales breakdown.
    """
    try:
        try:
            payload = AdminAnalyticsSalesPayload(**request.form.to_dict())
        except ValidationError as ve:
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': ve.errors()}
            )), 422

        result = get_analytics_graph_sales(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved sales analytics",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_sales: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    

@analytics_general.route('/api/admin/analytics_graph_daily_revenue', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_revenue.yml')  
def admin_analytics_graph_daily_revenue():
    """
    Get daily revenue data for admin dashboard.

    Args:
        request (json): JSON payload with:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with daily revenue values.
    """
    try:
        try:
            payload = AdminAnalyticsDailyRevenuePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': error_details}
            )), 422

        result = get_analytics_daily_revenue(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily revenue analytics",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_revenue: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
    
@analytics_general.route('/api/admin/analytics_graph_revenue', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_revenue.yml')
def admin_analytics_graph_revenue():
    """
    Get overall revenue metrics over a date range.

    Args:
        request (json): JSON with:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with total revenue data.
    """
    try:
        try:
            payload = AnalyticsRevenuePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': error_details}
            )), 422

        result, status = get_revenue_graph_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved revenue analytics",
            data=result,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_revenue: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
    
@analytics_general.route('/api/admin/analytics_graph_daily_trips', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_trips.yml')
def admin_analytics_graph_daily_trips():
    """
    Get number of trips per day in the selected date range.

    Args:
        request (json): JSON payload including:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with trip counts per day.
    """
    try:
        try:
            payload = DailyTripsPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status_code = get_daily_trips_graph_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily trips analytics",
            data=[response],
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_trips: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        

@analytics_general.route('/api/admin/analytics_graph_trips', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_trips.yml')
def admin_analytics_graph_trips():
    """
    Get total trip count within a date range.

    Args:
        request (json): JSON payload:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with trip count summary.
    """
    try:
        try:
            payload = TripsAnalyticsPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status = get_trips_graph_data(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved trips analytics",
                data=[response],
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_trips: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_general.route('/api/admin/total_ratings', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_user_rating_count.yml')
def total_counts_ratings():
    """
    Get total and average user ratings.

    Args:
        request (json): JSON payload:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with:
            - rating_count (int)
            - average_rating (float)
    """
    try:
        try:
            payload = RatingAnalyticsPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Missing or invalid date values",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status = get_total_ratings_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved total ratings and average rating data",
            data=response,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_ratings: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_general.route('/api/admin/customer_register_reg_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_customer_register_reg_count.yml')
def total_counts_reg_customer():
    """
    Get customer registration counts grouped by city.

    Args:
        request (json): JSON payload:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with registration counts per city.
    """
    try:
        try:
            payload = CustomerRegistrationCountPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Missing or invalid date values",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status = get_customer_registration_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved total customer registration counts by city",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_reg_customer: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        

@analytics_general.route('/api/admin/customer_register_source_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_customer_register_source_count.yml')
def total_counts_source_customer():
    """
    Get customer registrations grouped by source (app, website, etc).

    Args:
        request (json): JSON:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with source-wise customer counts.
    """
    try:
        try:
            payload = CustomerRegisterSourcePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Missing or invalid date values",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status = get_customer_source_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved total customer registration counts by source",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_source_customer: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_general.route('/api/admin/cancellation_reason_counts', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_cancellation_reason_counts.yml')
def cancellation_reason_counts():
    """
    Get counts of cancellation reasons across bookings.

    Args:
        request (json): JSON payload:
            - from_date (str)
            - to_date (str)
            - region_id (int, optional)

    Returns:
        Response: JSON with counts grouped by cancellation reason.
    """
    try:
        try:
            payload = CancellationReasonCountPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Missing or invalid date values",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status = get_cancellation_reason_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved cancellation reason counts",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in cancellation_reason_counts: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
 
