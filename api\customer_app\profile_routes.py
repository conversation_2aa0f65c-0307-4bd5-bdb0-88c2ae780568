#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

#=== Standard Library ===
from typing import Optional
import re
import logging
from sqlalchemy.exc import SQLAlchemyError
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity,set_access_cookies, set_refresh_cookies, create_access_token, create_refresh_token
from datetime import timedelta,datetime

# === Third-Party Libraries ===
from pydantic import BaseModel, ValidationError,Field, validator
from flasgger import swag_from
from pydantic import ValidationError

# === Application (Internal) Imports ===
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from models.models import Users
from services.customer_app.profile_service import handle_add_emergency_contact
from services.customer_app.otp_service import process_otp_request, process_otp_validation
from services.customer_app.profile_service import get_driver_approval_status
from services.customer_app.token_service import handle_token_refresh, check_mobile_existence, check_driver_existence
from services.customer_app.register_login_service import register_customer,handle_login, handle_driver_login, handle_forgot_password
from utils.response_utils import standard_response



customer_profile = Blueprint('customer_app_profile', __name__)
logger = logging.getLogger(__name__)



class EmergencyContactSchema(BaseModel):
    country_code: str
    contact_number: str
    otp: str

    @validator('country_code')
    def validate_country_code(cls, v):
        if not re.match(r"\+\d{1,4}", v):
            raise ValueError("Invalid country code format.")
        return v

    @validator('contact_number')
    def validate_contact_number(cls, v):
        if not re.match(r"\d{10}", v):
            raise ValueError("Invalid emergency contact number format.")
        return v

    @validator('otp')
    def validate_otp(cls, v):
        if not v.strip():
            raise ValueError("OTP is required.")
        return v
    

# === Route ===
@customer_profile.route('/api/emergency_contact/add', methods=['POST'])
@jwt_required()
@swag_from('/app/swagger_docs/auth/user_add_emergency_contact.yml')
def add_emergency_contact():
    """
    Adds or updates the authenticated user's emergency contact.
    """
    try:
        form_data = request.form.to_dict()
        validated = EmergencyContactSchema(**form_data)
        user_id = get_jwt_identity()

        response = handle_add_emergency_contact(validated, user_id)
        return jsonify(response), response.get('status_code', 200)

    except ValidationError as ve:
        logger.warning(f"Validation error in add_emergency_contact: {ve}")
        return jsonify(standard_response(
            success=-3,
            status=400,
            message=ve.errors()[0]['msg']
        )), 400

    except Exception as e:
        logger.exception("Unexpected error in add_emergency_contact")
        return jsonify(standard_response(
            success=-9,
            status=500,
            message="An unexpected error occurred. Please try again later."
        )), 500
    
@customer_profile.route('/api/driver/approval_status', methods=['GET'])
@jwt_required()
def driver_approval_status():
    """
    Retrieves the approval status and region details for the authenticated driver.
    """
    try:
        user_id = get_jwt_identity()
        response = get_driver_approval_status(user_id)
        return jsonify(response), response.get('status_code', 200)

    except SQLAlchemyError as sae:
        logger.exception("Database error in driver_approval_status")
        return jsonify(standard_response(
            success=-4,
            status=500,
            message="An unexpected error occurred. Please try again later."
        )), 500

    except Exception as e:
        logger.exception("Unexpected error in driver_approval_status")
        return jsonify(standard_response(
            success=-4,
            status=500,
            message=f"An unexpected error occurred. Please try again later."
        )), 500
   