{"tests/test_customer_app/test_bookings.py::test_confirm_booking_invalid_search_id_negative_one": true, "tests/test_customer_app/test_bookings.py::test_register_search_success_coupon": true, "tests/test_customer_app/test_bookings.py::test_confirm_booking_user_negative_credit": true, "tests/test_login/test_driver_login.py": true, "tests/test_login/test_user_login.py": true, "tests/test_affiliate/create_affiliate.py::test_mark_favourite": true, "tests/test_login/test_admin_login.py::test_missing_credentials": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_missing_dates_b2b": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_b2b_success": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_b2b_success_aff_filter": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_sales_success_b2b": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_revenue_success_b2b": true, "tests/test_new_admin/test_analytics.py::test_admin_analytics_revenue_success_b2b": true, "tests/test_new_admin/test_analytics.py::test_analytics_graph_trip_metrics_success": true, "tests/test_new_admin/test_analytics.py::test_analytics_driver_earning": true, "tests/test_new_admin/test_analytics.py::test_analytics_affiliate_transactions": true, "tests/test_new_admin/test_admin_driver.py::test_successful_data_retrieval": true, "tests/test_new_admin/test_admin_driver.py::test_missing_last_timestamp": true, "tests/test_new_admin/test_admin_driver.py::test_no_new_data": true, "tests/test_new_admin/test_admin_driver.py::test_missing_id_no_parameter": true, "tests/test_new_admin/test_admin_driver.py::test_driver_details_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_driving_id_already_fetched": true, "tests/test_new_admin/test_admin_driver.py::test_voter_id_does_not_exist": true, "tests/test_new_admin/test_admin_driver.py::test_voter_details_fetched_but_failed_to_verify": true, "tests/test_new_admin/test_admin_driver.py::test_successful_voter_details_verification": true, "tests/test_new_admin/test_admin_driver.py::test_invalid_voter_id": true, "tests/test_new_admin/test_admin_driver.py::test_voter_details_verified_but_disputed": true, "tests/test_new_admin/test_admin_driver.py::test_unexpected_api_response_code": true, "tests/test_new_admin/test_admin_driver.py::test_external_api_request_exception": true, "tests/test_new_admin/test_admin_driver.py::test_missing_v_reverify": true, "tests/test_new_admin/test_admin_driver.py::test_force_verify_success": true, "tests/test_new_admin/test_admin_driver.py::test_match_voter_details_success": true, "tests/test_new_admin/test_admin_driver.py::test_disputed_voter_details": true, "tests/test_new_admin/test_admin_driver.py::test_verification_failure": true, "tests/test_new_admin/test_admin_driver.py::test_update_and_log_missing_driver_id": true, "tests/test_new_admin/test_admin_driver.py::test_update_and_log_non_existing_driver": true, "tests/test_new_admin/test_admin_driver.py::test_update_and_log_success": true, "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_failed_verification": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_missing_driver_id": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_driver_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_bank_info_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_force_verify": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_successful_verification": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_details_disputed": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_verification_failed": true, "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_db_commit_failure": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_missing_id_no": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_missing_driver_id": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_driver_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_driver_info_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_aadhaar_no_mobile": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_exceed_limit": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_aadhaar_does_not_exist": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_invalid_aadhaar": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_already_sent": true, "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_success": true, "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_id_no": true, "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_driver_id": true, "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_trans_id": true, "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driver_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driver_details_not_found": true, "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driving_id_already_fetched": true, "tests/test_new_admin/test_admin_driver.py::test_aadhaar_session_expired": true, "tests/test_new_admin/test_admin_driver.py::test_aadhaar_otp_attempts_exceeded": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_incomplete_form": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_invalid_reason_format": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_success": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_success_redis_off": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_incomplete_form": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_invalid_reason_format": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_success": true, "tests/test_affiliate/test_affiliate_book_admin.py::test_restart_affiliate_trip": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_success": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_invalid_report_type": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_invalid_email_list_format": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_missing_required_fields": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_as_draft": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_success": true, "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_already_set": true}