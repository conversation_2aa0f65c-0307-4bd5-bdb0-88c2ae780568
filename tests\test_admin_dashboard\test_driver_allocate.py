from datetime import datetime,timedelta   
from models.models import Users,Drivers,Bookings,Trip,db,BookPricing
from conftest import unique_user_data,driver_bookings,driver_trip,create_user_and_driver,driver_details
from unittest.mock import patch
from sqlalchemy import exc

#  API - /api/admin/booking_allocate_driver_list

def test_allocate_driver_list_success(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '-1'
    }
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_details_id=driver_details(driver.id)
    driver=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver.approved=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    response = client.post('/api/admin/booking_allocate_driver_list',data=form_data,headers=auth_headers)
    assert response.status_code == 200
    response_json = response.json
    assert response_json['success']==1
    
def test_allocate_driver_list_filter_success(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '-1',
        'filter_by': '2',
    }
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_details_id=driver_details(driver.id)
    data2 = unique_user_data()
    driver_user2,driver2=create_user_and_driver(data2)
    driver_details_id2=driver_details(driver2.id)
    driver2fetch=db.session.query(Drivers).filter(Drivers.id==driver2.id).first()
    driver2fetch.approved=1
    driver2fetch.rating=5
    driver2fetch.available=0
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    response = client.post('/api/admin/booking_allocate_driver_list',data=form_data,headers=auth_headers)
    assert response.status_code == 200
    response_json = response.json
    assert response_json['success']==1
    data = response_json['data']
    assert data[0]['driver_rating']==5.0
    assert data[0]['driver_trip_count']==1
    assert data[0]['availablity']==0
    
    
def test_allocate_driver_list_exception(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '-1',
    }    
    with patch('models.db.session.query') as mock_commit:
        mock_commit.side_effect = exc.IntegrityError(None, None, None)
        response = client.post('/api/admin/booking_allocate_driver_list',data=form_data,headers=auth_headers)
        assert response.status_code == 500
        json_data = response.get_json()
        assert json_data['success'] == -3
        
# ---------------------------------       

#  API - /api/admin/booking_allocate_driver
   
def test_allocate_driver_no_driver_found(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id)
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    response.status_code==404
    json_data = response.get_json()
    assert json_data['success'] == -2
    assert json_data['message'] == 'Driver Not Found'
   
def test_allocate_driver_booking_already_allocated(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=1
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    response.status_code==409
    json_data = response.get_json()
    assert json_data['success'] == -4
    assert json_data['message'] == 'Booking already allocated'
    
def test_allocate_driver_booking_cancelled(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=-1
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    response.status_code==409
  
     
def test_allocate_driver_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    driver_booking=driver_bookings('1','1')
    book_pricing = BookPricing(
            bid=driver_booking,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    json_data = response.get_json()
    response.status_code==200
    json_data['success']==1
    json_data['message']='Booking Allocated Successfully'

def test_reallocate_driver_same_driver(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    driver_booking=driver_bookings('1',driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id,
        'reallocating':1
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    json_data = response.get_json()
    print(json_data)
    response.status_code==409
    json_data['success']==-5
    json_data['message']='Cannot Reallocate to Same Driver'
    
def test_reallocate_driver_trip_going(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=1
    driver_trip(driver_booking,0)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id,
        'reallocating':1
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    json_data = response.get_json()
    response.status_code==400
    json_data['success']==-2
    json_data['message']='Trip already created'
    
def test_reallocate_driver_success(client, admin_login):   
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    driver_user2,driver2=create_user_and_driver(data2)
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    driver_booking=driver_bookings('1',driver2.id)
    details_id=driver_details(driver.id)
    details_id2=driver_details(driver2.id)
    book_pricing = BookPricing(
            bid=driver_booking,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    details_id=driver_details(driver.id)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'driver_id':driver.id,
        'reason':1,
        'reallocating':1
    }  
    response = client.post('/api/admin/booking_allocate_driver',data=form_data,headers=auth_headers)
    json_data = response.get_json()
    response.status_code==200
    json_data['success']==1
    json_data['message']='Booking Allocated Successfully'
    
# ---------------------------------       

#  API - /api/admin/unallocate/new   

def test_unallocate_driver_success(client, admin_login):   
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_book=db.session.query(Drivers).filter(Drivers.id==driver.id).first()
    driver_book.approved=1
    driver_booking=driver_bookings('1',driver.id)
    details_id=driver_details(driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'reason':'1'
    }  
    response = client.post('/api/admin/unallocate/new',data=form_data,headers=auth_headers)
    json_data = response.get_json()
    response.status_code==200
    json_data['success']==1
    json_data['message']='Booking Unallocated Successfully'
    
# ---------------------------------  