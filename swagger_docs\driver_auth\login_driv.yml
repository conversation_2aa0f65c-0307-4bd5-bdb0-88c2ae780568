tags:
  - Authentication
summary: Driver login with OTP or password
description: |
  Authenticates a driver using either OTP or password and returns access/refresh tokens upon success. 
  Includes checks for account status, rate-limiting on failed attempts, and driver approval status.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the driver
  - name: password
    in: formData
    type: string
    required: true
    description: Password or OTP, depending on `auth_type`
  - name: auth_type
    in: formData
    type: string
    required: true
    enum: [password, otp]
    description: Authentication type — either 'password' or 'otp'
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")
  - name: device_type
    in: formData
    type: string
    required: true
    description: Device type (e.g., "android", "ios", etc.)
  - name: remember
    in: formData
    type: boolean
    required: false
    description: If true, sets longer token expiry (2 years instead of 1)

responses:
  200:
    description: Driver authenticated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        driver_id:
          type: integer
          example: 42
        user_id:
          type: integer
          example: 1001
        user_ref_code:
          type: string
          example: "ABC123"
        available:
          type: boolean
          example: true
        approved:
          type: boolean
          example: true
        driver_pic:
          type: string
          example: "https://example.com/driver/image.jpg"
        driver_rating:
          type: number
          format: float
          example: 4.7
        user_mobile:
          type: string
          example: "**********"
        user_fname:
          type: string
          example: "John"
        user_lname:
          type: string
          example: "Doe"
        user_email:
          type: string
          example: "<EMAIL>"
        access_token:
          type: string
          example: "eyJ0eXAiOiJKV1QiLCJh..."
        refresh_token:
          type: string
          example: "eyJ0eXAiOiJKV1QiLCJh..."

  401:
    description: Invalid credentials or disabled account
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid mobile number or account disabled."

  403:
    description: Driver account is not approved
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Driver account is not approved."

  423:
    description: Account temporarily locked due to multiple failed attempts
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -8
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Account locked due to multiple failed attempts. Try again in 900 seconds."

  400:
    description: Validation error in request
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Missing required field: mobile"

  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
