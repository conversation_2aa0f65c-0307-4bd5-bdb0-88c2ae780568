from datetime import date,datetime
from pydantic import BaseModel, Field, field_validator,ValidationError,root_validator
from typing import Optional


class DashDriverSearchParams(BaseModel):
    starting_from: int = Field(..., ge=0)
    no_of_logs: int = Field(..., ge=1)
    search_query: Optional[str] = None
    region: Optional[str] = None
    status: Optional[str] = None
    approval: Optional[str] = None
    rating_gt: Optional[float] = Field(None, ge=0)
    rating_lt: Optional[float] = Field(None, ge=0)
    timestamp_gt: Optional[str] = None
    timestamp_lt: Optional[str] = None
    is_global: Optional[int] = Field(0, ge=0, le=2)
    label: Optional[str] = None
    ongoing: Optional[str] = None
    sort_by: Optional[str] = Field("1")
    sort_order: Optional[str] = Field("desc")
    lat: Optional[float] = None
    long: Optional[float] = None
    radius_km: Optional[float] = Field(None, gt=0)

    @field_validator("timestamp_gt", "timestamp_lt", mode="before")
    @classmethod
    def validate_datetime_format(cls, v):
        if v:
            try:
                datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                raise ValueError("Invalid datetime format. Expected 'YYYY-MM-DD HH:MM:SS'")
        return v
    
class SingleDriverRequestSchema(BaseModel):
    driver_id: int = Field(..., description="Driver ID")
    
    
class DriverTripsLogPayload(BaseModel):
    driver_id: int = Field(..., description="Driver ID")
    sort_by: Optional[int] = 1
    offset: Optional[int] = 0
    limit: Optional[int] = 10
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    search_query: Optional[str] = ''
    book_type: Optional[int] = None
    

class DriverApprovalLogParams(BaseModel):
    driver_id: str = Field(..., description="Driver ID")
    sortby: Optional[str] = 'timestamp'
    sorttype: Optional[str] = '0'
    fromdate: Optional[str]
    todate: Optional[str]
    search_query: Optional[str] = ''
    approval: Optional[str] = None 
    editedby: Optional[str] = None
    change_from: Optional[str]  = None
    change_to: Optional[str] = None
    offset: Optional[int] = 0
    limit: Optional[int] = 10
    
IS_GLOBAL_DRIVER_LICENCE = 1
IS_GLOBAL_DRIVER_MOBILE = 2

SORT_BY_TIMESTAMP_ASC = 1
SORT_BY_TIMESTAMP_DESC = 2
SORT_BY_RATING_ASC = 3
SORT_BY_RATING_DESC = 4

SORT_BY_DRIVER_TIMESTAMP_DESC = 1
SORT_BY_DRIVER_TIMESTAMP_ASC = 2
SORT_BY_DRIVER_RATING_DESC = 3
SORT_BY_DRIVER_RATING_ASC = 4



    

class DriverDetailsParams(BaseModel):
    driver_id: Optional[int] = Field(None, description="Driver ID")
    driver_user_id: Optional[int] = Field(None, description="Driver User ID")

    @root_validator(pre=True)
    def at_least_one_field(cls, values):
        if not values.get('driver_id') and not values.get('driver_user_id'):
            raise ValueError("Either driver_id or driver_user_id must be provided.")
        return values
        
        

class UpdateDriverRequest(BaseModel):
    driver_id: int
    approval: str
    editedby: str
    search_region: str
    remark: str
    
    
class GetLocalityPayload(BaseModel):
    lat: float = Field(..., description="Latitude of the location")
    long: float = Field(..., description="Longitude of the location")
    

class DriverDLVerifyPayload(BaseModel):
    dl_no: str = Field(..., description="Driving License Number")
    dob: str = Field(..., description="Date of Birth in YYYY-MM-DD format")
    driver_id: int = Field(..., description="Driver ID")

    @field_validator("dob")
    @classmethod
    def validate_dob(cls, value):
        try:
            return date.fromisoformat(value)
        except ValueError:
            raise ValueError("dob must be in YYYY-MM-DD format")
        
class DriverDLReverifySchema(BaseModel):
    driver_id: int
    remarks: Optional[str] = None
    force_verify: Optional[int] = 0
    
class BankDocVerifyPayload(BaseModel):
    acc_no: str = Field(..., min_length=5)
    ifsc: str = Field(..., min_length=5)
    driver_id: str