import sys
sys.path.append("/app/")

import traceback
from collections import defaultdict
from models import db, Bookings, ZoomcarBookings
from datetime import datetime, timedelta
from sqlalchemy.sql import func
import time
from booking_params import Regions
from _utils_booking import (
    _get_zoomcar_booking_state,
    _mark_zoomcar_booking_accepted, _mark_zoomcar_booking_rejected
)
from affiliate_api.hook.utils import (
    ZOOMCAR_STATE_CREATED,
    ZOOMCAR_STATE_REJECTED, ZOOMCAR_STATE_ACCEPTED,
    _zoomcar_change_state
)

HOUR_SLOTS = {
    Regions.REGN_KOLKATA: [20, 20, 20, 20, 20, 20,
                           20, 20, 20, 20, 20, 20,
                           20, 20, 20, 20, 20, 20,
                           20, 20, 20, 20, 20, 20]
}

IMMEDIATE_THRESH_MIN = 1*60
AUTO_CANCEL_THRESH_MIN = 1*60

def _accept_zoomcar_booking(book_id):
    if _get_zoomcar_booking_state(book_id) != ZoomcarBookings.STATUS_ACCEPTED:
        _zoomcar_change_state(ZOOMCAR_STATE_CREATED, book_id)
        _zoomcar_change_state(ZOOMCAR_STATE_ACCEPTED, book_id)
        _mark_zoomcar_booking_accepted(book_id)
        print(book_id, "accepted")
        return 1

    print(book_id, "was accepted already")
    return 0

def _reject_zoomcar_booking(book_id):
    if _get_zoomcar_booking_state(book_id) != ZoomcarBookings.STATUS_REJECTED:
        _zoomcar_change_state(ZOOMCAR_STATE_REJECTED, book_id)
        _mark_zoomcar_booking_rejected(book_id)
        print(book_id, "rejected")
        return 1
    print(book_id, "was accepted already")
    return 0

def _get_unalloc_trips_zc(city=0):
    now = datetime.utcnow()
    all_future = db.session.query(ZoomcarBookings, Bookings). \
                    filter(Bookings.id == ZoomcarBookings.ref). \
                    filter(func.concat(Bookings.startdate, ' ', Bookings.starttime) > now). \
                    filter(Bookings.region == city). \
                    filter(Bookings.valid >= 0). \
                    order_by(Bookings.startdate). \
                    order_by(Bookings.starttime). \
                    order_by(ZoomcarBookings.status.desc()). \
                    all()
    return all_future

def _split_trips(all_unalloc_trips):
    hmap = defaultdict(list)
    for trip in all_unalloc_trips:
        hmap[(trip[1].startdate, trip[1].starttime.hour)].append(trip)
    return hmap

def _accept_or_reject_trips(hour_day, trips, city=0):
    must_accept = can_accept = rejected = limbo = 0
    hour = hour_day[1]
    # max_slot = HOUR_SLOTS.get(city)[hour]
    min_thresh = timedelta(seconds=IMMEDIATE_THRESH_MIN*60)
    now = datetime.utcnow()
    """
    if len(trips) < max_slot:
        return
    """
    for trip in trips:
        """
        if trip[1].status == ZoomcarBookings.STATUS_ACCEPTED:
            # continue
            accepted += 1
            continue
        if must_accept >= max_slot and now - trip[1].time > one_hr:
            _mark_zoomcar_booking_rejected(trip[1].id)
            _zoomcar_change_state(ZOOMCAR_STATE_REJECTED, trip[1].code)
            rejected += 1
        elif must_accept + can_accept <= max_slot:
            _mark_zoomcar_booking_accepted
            _zoomcar_change_state(ZOOMCAR_STATE_ACCEPTED, trip[1].code)
            can_accept += 1
        else:
            start_dt = datetime(trip[1].startdate.year, trip[1].startdate.month,
                                trip[1].startdate.day, trip[1].starttime.hour,
                                trip[1].starttime.minute, trip[1].starttime.second)
            if (start_dt - now < timedelta(seconds=6*3600) and
                now - trip[1].time > min_thresh):
                _mark_zoomcar_booking_rejected(trip[1].id)
                _zoomcar_change_state(ZOOMCAR_STATE_REJECTED, trip[1].code)
                rejected += 1
            else:
                limbo += 1
        """
        # print(trip)
        start_dt = datetime(trip[1].startdate.year, trip[1].startdate.month,
                            trip[1].startdate.day, trip[1].starttime.hour,
                            trip[1].starttime.minute, trip[1].starttime.second)
        print(start_dt - now)
        if start_dt - now < timedelta(seconds=AUTO_CANCEL_THRESH_MIN*60) and trip[1].driver == 1:
            # if SLA has expired
            if now - trip[1].timestamp > min_thresh:
                # and if this is not a trip just booked
                # then reject
                rejected += _reject_zoomcar_booking(trip[1].id)
                try:
                    cur_booking = db.session.query(Bookings).filter(Bookings.id == trip[1].id)
                    cur_booking.update({Bookings.valid: -3})
                    db.session.commit()
                except Exception as e:
                    print("Could not cancel booking", trip[1].id, ", error:", str(e))
            else:
                # limbo, idk what to do?
                print("Trip id", trip[1].code, "in limbo state")
        else:
            if trip[1].driver > 1:
                print("Trip id", trip[1].code, "already accepted")
                must_accept += 1
            # otherwise, keep accepting
            can_accept += _accept_zoomcar_booking(trip[1].id)


    return must_accept + can_accept, rejected, limbo


def _check_unalloc_bookings_zc(city=0):
    all_unalloc_trips = _get_unalloc_trips_zc(city)
    print("Found", len(all_unalloc_trips), "unallocated trips")
    trip_hour_sets = _split_trips(all_unalloc_trips)
    for hour_day, trips in trip_hour_sets.items():
        # print(hour_day, trips)
        acc, rej, limbo = _accept_or_reject_trips(hour_day, trips, city)
        print("For", hour_day, "accepted=%d, rejected=%d, limbo=%d" %
              (acc, rej, limbo))


if __name__ == '__main__':
    while True:
        print("Checking bookings for unallocated ZC trips")
        try:
            _check_unalloc_bookings_zc()
        except Exception as e:
            print("Exception:", str(e), traceback.format_exc())
            pass
        time.sleep(60)

