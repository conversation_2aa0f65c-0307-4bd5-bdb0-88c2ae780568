from datetime import datetime, timedelta, time
from models import BookingCancelled
from _utils import read_redis_data
import time as t
import re
from affiliate_b2b.affiliate_models import AffiliateCollections
import math

def format_duration(duration_str):
    """Convert '1 hr', '2 hrs' to '1:00:00' or '2:00:00'."""
    match = re.search(r'(\d+)', duration_str)
    if match:
        hours = int(match.group(1))
        return f"{hours}:00:00"
    return "00:00:00"

def parse_time(time_str):
    if isinstance(time_str, time):
        return time_str
    return datetime.strptime(time_str, "%H:%M:%S").time()

class PriceOneWay:

    def __init__(self, client_name):
        print("Client Name", client_name, flush=True)
        client_key = f"affiliate_{client_name}_pricing_customer"
        driver_key = f"affiliate_{client_name}_pricing_driver"
        client_price_data, client_status = read_redis_data(client_key)
        driver_price_data, driver_status = read_redis_data(driver_key)
        if client_status and driver_status:
            client_price_oneway = client_price_data.get("oneway")
            driver_price_oneway = driver_price_data.get("oneway")
        else:
            affiliate_data = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
            client_price_oneway = affiliate_data.get("customer_pricing_oneway")
            driver_price_oneway = affiliate_data.get("driver_pricing_oneway")

        # Customer Price Value
        self.CUST_BASE_CH = client_price_oneway.get("base_breakup")
        client_add_charges = client_price_oneway.get("additional_charges")
        self.CUST_DEFAULT_DUR = format_duration(client_add_charges.get("default_duration"))
        self.IS_INS_AVL = int(client_add_charges.get("insurance_charge_enabled"))
        self.INS_CH = 0
        if self.IS_INS_AVL:
            self.INS_CH = float(client_add_charges.get("insurance_charge"))
        self.IS_NIGHT_CH_ENABLED = int(client_add_charges.get("is_night_enabled"))
        if self.IS_NIGHT_CH_ENABLED:
            self.CUST_NIGHT_CH = float(client_add_charges.get("night_charge"))
            self.CUST_NIGHT_CH_TYPE = client_add_charges.get("charge_type")
            self.CUST_NT_0 = parse_time(client_add_charges.get("start_night_time"))
            self.CUST_NT_1 = parse_time(client_add_charges.get("end_night_time"))
        self.CUST_OT_CH_TYPE = client_add_charges.get("overtime_type")
        self.CUST_OT_CH = float(client_add_charges.get("overtime_charge"))

        # Driver Price Value
        self.DRIVER_BASE_CH = driver_price_oneway.get("base_breakup")
        driver_add_charges = driver_price_oneway.get('additional_charges')
        self.DRIVER_DEFAULT_DUR = format_duration(driver_add_charges.get("default_duration"))
        self.IS_DRIVER_NIGHT_CH_ENABLED = int(driver_add_charges.get("is_night_enabled"))
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            self.DRIVER_NIGHT_CH = float(driver_add_charges.get("night_charge"))
            self.DRIVER_NIGHT_CH_TYPE = driver_add_charges.get("charge_type")
            self.DRIVER_NT_0 = parse_time(driver_add_charges.get("start_night_time"))
            self.DRIVER_NT_1 = parse_time(driver_add_charges.get("end_night_time"))
        self.DRIVER_OT_CH_TYPE = driver_add_charges.get("overtime_type")
        self.DRIVER_OT_CH = float(driver_add_charges.get("overtime_charge"))


    CANCEL_THRESH = []
    CANCEL_CHARGES_VAR = [[], [], [], []]
    CANCEL_CHARGES_FLAT = []

    def get_base_fare(self, distance, base_fare):
        sorted_rates = sorted(base_fare, key=lambda x: float(x["min_distance"]))
        fare = 0.0
        previous_distance = 0.0

        for i in range(len(sorted_rates)):
            rate = sorted_rates[i]
            current_min = float(rate["min_distance"])
            hike_type = rate["hike_type"]
            amount = float(rate["amount"])

            if i < len(sorted_rates) - 1:
                current_max = float(sorted_rates[i + 1]["min_distance"])
            else:
                current_max = float('inf')

            # Skip if this range is already covered
            if distance <= current_min:
                break

            upper_bound = min(distance, current_max)
            interval_distance = upper_bound - current_min
            if interval_distance <= 0:
                continue

            if hike_type == "Flat hike":
                fare += amount
            elif hike_type == "Per km hike":
                fare += (interval_distance) * amount
    
            previous_distance = current_max

        return fare


    def get_night_fare(self, ch_type, night_dur, night_ch):
        # night_duration in mins
        if ch_type == "Flat":
            return night_ch
        elif ch_type == "per hour":
            return night_dur/60 * night_ch
        elif ch_type == "per min":
            return night_dur * night_ch
        else:
            return 0

    def get_overtime_fare(self, ot_type, ot_dur, ot_ch):
        if ot_type == "Flat":
            return ot_ch
        elif ot_type == "per hour":
            return ot_dur/60 * ot_ch
        elif ot_type == "per min":
            return ot_dur * ot_ch
        else:
            return 0

    def get_customer_price(self, dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        chargeable_dist_km = math.ceil(dist/1000)
        base_fare = self.get_base_fare(chargeable_dist_km, self.CUST_BASE_CH)
        # Night charge calculation
        if self.IS_NIGHT_CH_ENABLED:
            night = is_trip_night(self.CUST_NT_0, self.CUST_NT_1, starttime.hour, endtime.hour)
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.CUST_NT_0, self.CUST_NT_1)
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)

        return base_fare, night_fare, night

    def get_driver_price(self, dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        chargeable_dist_km = math.floor(dist/1000)
        base_fare = self.get_base_fare(chargeable_dist_km, self.DRIVER_BASE_CH)
        # Night charge calculation
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night = is_trip_night(self.DRIVER_NT_0, self.DRIVER_NT_1, starttime.hour, endtime.hour)
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)

        return base_fare, night_fare, night

    def get_price(self, dur, starttime, endtime, dist, startdate, enddate):
        cust_base_fare, cust_night_fare, night = self.get_customer_price(dur, starttime, endtime, dist, startdate, enddate)
        driver_base_fare, driver_night_fare, night = self.get_driver_price(dur, starttime, endtime, dist, startdate, enddate)
        total_cust_fare = cust_base_fare + cust_night_fare
        total_with_ins = total_cust_fare
        ins_avl = self.IS_INS_AVL
        ins_ch = self.INS_CH
        if ins_avl:
            total_with_ins += ins_ch
        total_driver_fare = driver_base_fare + driver_night_fare
        booking_charge = total_cust_fare - total_driver_fare

        return int(round(total_cust_fare)), int(round(cust_base_fare)), int(round(cust_night_fare)), 0, int(round(booking_charge)), \
                0, int(round(total_cust_fare)), 0, 0, int(round(total_with_ins)), int(round(ins_ch)), int(night), \
                int(round(total_driver_fare)), int(round(driver_base_fare)), int(round(driver_night_fare)), ins_avl

    def get_customer_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.CUST_OT_CH_TYPE, extra_dur, self.CUST_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_NIGHT_CH_ENABLED:
            night = is_trip_night(self.CUST_NT_0, self.CUST_NT_1, trip_starttime.hour, trip_stoptime.hour)
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.CUST_NT_0, self.CUST_NT_1)
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare

    def get_driver_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.DRIVER_OT_CH_TYPE, extra_dur, self.DRIVER_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night = is_trip_night(self.DRIVER_NT_0, self.DRIVER_NT_1, trip_starttime.hour, trip_stoptime.hour)
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare


    def get_trip_price(self, book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch):
        customer_price, night_fare, ot_fare = self.get_customer_trip_price(book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)

        driver_price, driver_night_fare, driver_ot_fare = self.get_driver_trip_price(book_delta, real_delta, driver_est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)

        booking_ch = customer_price - driver_price
        total_cust_fare = customer_price
        total_cust_fare += insurance_ch

        return int(round(total_cust_fare)), int(round(customer_price)), 0, 0, int(round(booking_ch)), int(round(driver_price)), \
                int(round(night_fare)), int(round(ot_fare)), int(round(driver_night_fare)), int(round(driver_ot_fare))

    @staticmethod
    def get_cancel_level(curtime, starttime, has_trip=False):
        if has_trip:
            return len(PriceOneWay.CANCEL_THRESH) - 1
        if curtime > starttime:
            return len(PriceOneWay.CANCEL_THRESH) - 1
        delta = starttime - curtime
        if delta.days > 0:
            return 0
        sec_delta = delta.seconds
        for idx in range(len(PriceOneWay.CANCEL_THRESH)):
            if sec_delta > PriceOneWay.CANCEL_THRESH[idx]:
                return idx
        return -1

    @staticmethod
    def get_cancel_ch(curtime, starttime, cancel_cat, has_trip, client_name):
        cancellation_key = f"affiliate_{client_name}_pricing_cancellation"
        cancellation_data, status = read_redis_data(cancellation_key)
        if status:
            cancel_charges = cancellation_data.get("cancel_charges")
            cancel_charge_static = cancellation_data.get("cancel_charge_static")
        else:
            affiliate_data = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
            cancellation_data = affiliate_data.get("pricing_cancellation_data")
            cancel_charges = cancellation_data.get("cancel_charges")
            cancel_charge_static = cancellation_data.get("cancel_charge_static")

        PriceOneWay.CANCEL_THRESH = []
        PriceOneWay.CANCEL_CHARGES_VAR = [[], [], [], []]
        PriceOneWay.CANCEL_CHARGES_FLAT = []

        PriceOneWay.CANCEL_THRESH = list(reversed([
                                float(charge.get("hour_range")) * 60 * 60
                                for charge in cancel_charges
                                if charge.get("hour_range") != "Max"
                            ]))

        for charge in reversed(cancel_charges):
            customer_cancel = float(charge.get("customer_cancel"))
            driver_cancel = float(charge.get("driver_cancel"))
            both_cancel = charge.get("both_cancel")
            both_cust_cancel = float(both_cancel.get("customer"))
            both_driver_cancel = float(both_cancel.get("driver"))

            PriceOneWay.CANCEL_CHARGES_VAR[0].append((customer_cancel, 0))  # Customer cancel
            PriceOneWay.CANCEL_CHARGES_VAR[1].append((0, driver_cancel))  # Driver cancel
            PriceOneWay.CANCEL_CHARGES_VAR[2].append((both_cust_cancel, both_driver_cancel))  # Both
            PriceOneWay.CANCEL_CHARGES_VAR[3].append((0, 0))  # Neither

        customer_cancel = float(cancel_charge_static.get("customer_cancel"))
        driver_cancel = float(cancel_charge_static.get("driver_cancel"))
        both_cancel_customer = float(cancel_charge_static.get("both_cancel").get("customer"))
        both_cancel_driver = float(cancel_charge_static.get("both_cancel").get("driver"))

        PriceOneWay.CANCEL_CHARGES_FLAT = [(customer_cancel, 0), (0, driver_cancel), (both_cancel_customer, both_cancel_driver), (0, 0)]

        to_charge, fixed = BookingCancelled.reason_type(cancel_cat)
        charges = (0, 0)
        if to_charge < 0:
            return [0, 0], -1
        if not fixed:
            level = PriceOneWay.get_cancel_level(curtime, starttime, has_trip)
            charges = PriceOneWay.CANCEL_CHARGES_VAR[to_charge][level]
        else:
            level = len(PriceOneWay.CANCEL_THRESH) - 1
            charges = PriceOneWay.CANCEL_CHARGES_FLAT[to_charge]
        return tuple(int(c) for c in charges), level



class PriceRound:

    def __init__(self, client_name):
        print("Client Name", client_name, flush=True)
        client_key = f"affiliate_{client_name}_pricing_customer"
        driver_key = f"affiliate_{client_name}_pricing_driver"
        client_price_data, client_status = read_redis_data(client_key)
        driver_price_data, driver_status = read_redis_data(driver_key)
        if client_status and driver_status:
            client_price_oneway = client_price_data.get('roundtrip')
            driver_price_oneway = driver_price_data.get('roundtrip')
        else:
            affiliate_data = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
            client_price_oneway = affiliate_data.get("customer_pricing_roundtrip")
            driver_price_oneway = affiliate_data.get("driver_pricing_roundtrip")

        # Customer Price Value
        client_base_charges = client_price_oneway.get("base_breakup")
        client_add_charges = client_price_oneway.get('additional_charges')

        self.CUST_MIN_HOUR = float(client_base_charges.get("minimum_hour"))
        self.CUST_MIN_CHARGE = float(client_base_charges.get("minimum_charge"))
        self.CUST_HOUR_CHARGE = float(client_base_charges.get("hourly_charge"))
        self.IS_INS_AVL = int(client_add_charges.get("insurance_charge_enabled"))
        self.INS_CH = 0
        if self.IS_INS_AVL:
            self.INS_CH = float(client_add_charges.get("insurance_charge"))
        self.IS_NIGHT_CH_ENABLED = int(client_add_charges.get("is_night_enabled"))
        if self.IS_NIGHT_CH_ENABLED:
            self.CUST_NIGHT_CH = float(client_add_charges.get("night_charge"))
            self.CUST_NIGHT_CH_TYPE = client_add_charges.get("charge_type")
            self.CUST_NT_0 = parse_time(client_add_charges.get("start_night_time"))
            self.CUST_NT_1 = parse_time(client_add_charges.get("end_night_time"))
        self.CUST_OT_CH_TYPE = client_base_charges.get("overtime_type")
        self.CUST_OT_CH = float(client_base_charges.get("overtime_charge"))

        # Driver Price Value
        driver_base_charges = driver_price_oneway.get("base_breakup")
        driver_add_charges = driver_price_oneway.get('additional_charges')
        self.DRIVER_MIN_HOUR = float(driver_base_charges.get("minimum_hour"))
        self.DRIVER_MIN_CHARGE = float(driver_base_charges.get("minimum_charge"))
        self.DRIVER_HOUR_CHARGE = float(driver_base_charges.get("hourly_charge"))

        self.IS_DRIVER_NIGHT_CH_ENABLED = int(driver_add_charges.get("is_night_enabled"))
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            self.DRIVER_NIGHT_CH = float(driver_add_charges.get("night_charge"))
            self.DRIVER_NIGHT_CH_TYPE = driver_add_charges.get("charge_type")
            self.DRIVER_NT_0 = parse_time(driver_add_charges.get("start_night_time"))
            self.DRIVER_NT_1 = parse_time(driver_add_charges.get("end_night_time"))

        self.DRIVER_OT_CH_TYPE = driver_base_charges.get("overtime_type")
        self.DRIVER_OT_CH = float(driver_base_charges.get("overtime_charge"))


    def get_base_fare(self, dur, min_hour, min_ch, hour_ch):
        if isinstance(dur, datetime):
            dur = dur.strftime("%H:%M:%S")
        hours, minutes, seconds = map(int, dur.split(":"))
        total_hours = hours + minutes / 60 + seconds / 3600

        if total_hours <= min_hour:
            return min_ch
        return min_ch + (total_hours - min_hour) * hour_ch

    def get_night_fare(self, ch_type, night_dur, night_ch):
        if ch_type == "Flat":
            return night_ch
        elif ch_type == "per hour":
            return night_dur/60 * night_ch
        elif ch_type == "per min":
            return night_dur * night_ch
        else:
            return 0

    def get_overtime_fare(self, ot_type, ot_dur, ot_ch):
        if ot_type == "Flat":
            return ot_ch
        elif ot_type == "per hour":
            return ot_dur/60 * ot_ch
        elif ot_type == "per min":
            return ot_dur * ot_ch
        else:
            return 0


    def get_customer_price(self, dur, starttime, endtime, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        base_fare = self.get_base_fare(dur, self.CUST_MIN_HOUR, self.CUST_MIN_CHARGE, self.CUST_HOUR_CHARGE)
        # Night charge calculation
        if self.IS_NIGHT_CH_ENABLED:
            night = is_trip_night(self.CUST_NT_0, self.CUST_NT_1, starttime.hour, endtime.hour)
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.CUST_NT_0, self.CUST_NT_1)
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)
        return base_fare, night_fare, night

    def get_driver_price(self, dur, starttime, endtime, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        base_fare = self.get_base_fare(dur, self.DRIVER_MIN_HOUR, self.DRIVER_MIN_CHARGE, self.DRIVER_HOUR_CHARGE)
        # Night charge calculation
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night = is_trip_night(self.DRIVER_NT_0, self.DRIVER_NT_1, starttime.hour, endtime.hour)
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)

        return base_fare, night_fare, night


    def get_price(self, dur, starttime, endtime, startdate, enddate):
        cust_base_fare, cust_night_fare, night = self.get_customer_price(dur, starttime, endtime, startdate, enddate)
        driver_base_fare, driver_night_fare, night = self.get_driver_price(dur, starttime, endtime, startdate, enddate)
        total_cust_fare = cust_base_fare + cust_night_fare
        total_with_ins = total_cust_fare
        ins_avl = self.IS_INS_AVL
        ins_ch = self.INS_CH

        if ins_avl:
            total_with_ins += ins_ch
        total_driver_fare = driver_base_fare + driver_night_fare
        booking_charge = total_cust_fare - total_driver_fare

        return int(round(total_cust_fare)), int(round(cust_base_fare)), int(round(cust_night_fare)), 0, int(round(booking_charge)), \
                0, int(round(total_cust_fare)), 0, 0, int(round(total_with_ins)), int(round(ins_ch)), int(night), \
                int(round(total_driver_fare)), int(round(driver_base_fare)), int(round(driver_night_fare)), ins_avl


    def get_customer_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.CUST_OT_CH_TYPE, extra_dur, self.CUST_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_NIGHT_CH_ENABLED:
            night = is_trip_night(self.CUST_NT_0, self.CUST_NT_1, trip_starttime.hour, trip_stoptime.hour)
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.CUST_NT_0, self.CUST_NT_1)
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare

    def get_driver_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.DRIVER_OT_CH_TYPE, extra_dur, self.DRIVER_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night = is_trip_night(self.DRIVER_NT_0, self.DRIVER_NT_1, trip_starttime.hour, trip_stoptime.hour)
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare


    def get_trip_price(self, book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch):
        customer_price, night_fare, ot_fare = self.get_customer_trip_price(book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)

        driver_price, driver_night_fare, driver_ot_fare = self.get_driver_trip_price(book_delta, real_delta, driver_est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)
        print("Customer Price", customer_price, flush=True)
        print("Driver Price", driver_price, flush=True)
        booking_ch = customer_price - driver_price
        total_cust_fare = customer_price
        total_cust_fare += insurance_ch

        return int(round(total_cust_fare)), int(round(customer_price)), 0, 0, int(round(booking_ch)), int(round(driver_price)), \
                int(round(night_fare)), int(round(ot_fare)), int(round(driver_night_fare)), int(round(driver_ot_fare))


class PriceOutStation:

    def __init__(self, client_name):
        print("Client Name", client_name, flush=True)
        client_key = f"affiliate_{client_name}_pricing_customer"
        driver_key = f"affiliate_{client_name}_pricing_driver"
        client_price_data, client_status = read_redis_data(client_key)
        driver_price_data, driver_status = read_redis_data(driver_key)
        if client_status and driver_status:
            client_price_oneway = client_price_data.get('outstationtrip')
            driver_price_oneway = driver_price_data.get('outstationtrip')
        else:
            affiliate_data = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
            client_price_oneway = affiliate_data.get("customer_pricing_outstationtrip")
            driver_price_oneway = affiliate_data.get("driver_pricing_outstationtrip")
         # Customer Price Value
        client_base_charges = client_price_oneway.get("base_breakup")
        client_add_charges = client_price_oneway.get('additional_charges')

        self.CUST_MIN_HOUR = float(client_base_charges.get("minimum_hour"))
        self.CUST_MIN_CHARGE = float(client_base_charges.get("minimum_charge"))
        self.CUST_HOUR_CHARGE = float(client_base_charges.get("hourly_charge"))
        self.IS_INS_AVL = int(client_add_charges.get("insurance_charge_enabled"))
        self.INS_CH = 0
        if self.IS_INS_AVL:
            self.INS_CH = float(client_add_charges.get("insurance_charge"))
        self.IS_NIGHT_CH_ENABLED = int(client_add_charges.get("is_night_enabled"))
        if self.IS_NIGHT_CH_ENABLED:
            self.CUST_NIGHT_CH = float(client_add_charges.get("night_charge"))
            self.CUST_NIGHT_CH_TYPE = client_add_charges.get("charge_type")
            self.CUST_NT_0 = parse_time(client_add_charges.get("start_night_time"))
            self.CUST_NT_1 = parse_time(client_add_charges.get("end_night_time"))
        self.CUST_OT_CH_TYPE = client_base_charges.get("overtime_type")
        self.CUST_OT_CH = float(client_base_charges.get("overtime_charge"))

        # Driver Price Value
        driver_base_charges = driver_price_oneway.get("base_breakup")
        driver_add_charges = driver_price_oneway.get('additional_charges')
        self.DRIVER_MIN_HOUR = float(driver_base_charges.get("minimum_hour"))
        self.DRIVER_MIN_CHARGE = float(driver_base_charges.get("minimum_charge"))
        self.DRIVER_HOUR_CHARGE = float(driver_base_charges.get("hourly_charge"))

        self.IS_DRIVER_NIGHT_CH_ENABLED = int(driver_add_charges.get("is_night_enabled"))
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            self.DRIVER_NIGHT_CH = float(driver_add_charges.get("night_charge"))
            self.DRIVER_NIGHT_CH_TYPE = driver_add_charges.get("charge_type")
            self.DRIVER_NT_0 = parse_time(driver_add_charges.get("start_night_time"))
            self.DRIVER_NT_1 = parse_time(driver_add_charges.get("end_night_time"))

        self.DRIVER_OT_CH_TYPE = driver_base_charges.get("overtime_type")
        self.DRIVER_OT_CH = float(driver_base_charges.get("overtime_charge"))

    def get_base_fare(self, dur, min_hour, min_ch, hour_ch):
        if isinstance(dur, datetime):
            hours = dur.hour
            minutes = dur.minute
            dur = f"{hours:02}:{minutes:02}:00"

        days, hours, minutes = map(int, dur.split(":"))
        total_hours = days * 24 + hours + minutes / 60

        if total_hours <= min_hour:
            return min_ch
        return min_ch + (total_hours - min_hour) * hour_ch


    def get_night_fare(self, ch_type, night_dur, night_ch):
        night_fare = 0
        if ch_type == "Flat":
            night_fare = night_ch
        elif ch_type == "per hour":
            night_fare = night_dur/60 * night_ch
        elif ch_type == "per min":
            night_fare = night_dur * night_ch
        else:
            night_fare = 0
        return night_fare

    def get_overtime_fare(self, ot_type, ot_dur, ot_ch):
        if ot_type == "Flat":
            return ot_ch
        elif ot_type == "per hour":
            return ot_dur/60 * ot_ch
        elif ot_type == "per min":
            return ot_dur * ot_ch
        else:
            return 0

    def get_customer_price(self, dur, starttime, endtime, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        base_fare = self.get_base_fare(dur, self.CUST_MIN_HOUR, self.CUST_MIN_CHARGE, self.CUST_HOUR_CHARGE)
        # Night charge calculation
        if self.IS_NIGHT_CH_ENABLED:
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.CUST_NT_0, self.CUST_NT_1)
            night = 1 if night_dur > 0 else 0
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)
        return base_fare, night_fare, night

    def get_driver_price(self, dur, starttime, endtime, startdate, enddate):
        base_fare = 0
        night_fare = 0
        night = False
        # Base fare calculation
        base_fare = self.get_base_fare(dur, self.DRIVER_MIN_HOUR, self.DRIVER_MIN_CHARGE, self.DRIVER_HOUR_CHARGE)
        # Night charge calculation
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night_dur = get_night_duration(startdate, starttime, enddate, endtime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night = 1 if night_dur > 0 else 0
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)

        return base_fare, night_fare, night


    def get_price(self, dur, starttime, endtime, startdate, enddate):
        cust_base_fare, cust_night_fare, night = self.get_customer_price(dur, starttime, endtime, startdate, enddate)
        driver_base_fare, driver_night_fare, night = self.get_driver_price(dur, starttime, endtime, startdate, enddate)
        total_cust_fare = cust_base_fare + cust_night_fare
        total_with_ins = total_cust_fare
        ins_avl = self.IS_INS_AVL
        ins_ch = self.INS_CH

        if ins_avl:
            total_with_ins += ins_ch
        total_driver_fare = driver_base_fare + driver_night_fare
        booking_charge = total_cust_fare - total_driver_fare
        return int(round(total_cust_fare)), int(round(cust_base_fare)), int(round(cust_night_fare)), 0, int(round(booking_charge)), \
                0, int(round(total_cust_fare)), 0, 0, int(round(total_with_ins)), int(round(ins_ch)), int(night), \
                int(round(total_driver_fare)), int(round(driver_base_fare)), int(round(driver_night_fare)), ins_avl


    def get_customer_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.CUST_OT_CH_TYPE, extra_dur, self.CUST_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_NIGHT_CH_ENABLED:
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.CUST_NT_0, self.CUST_NT_1)
            night = 1 if night_dur > 0 else 0
            night_fare = night * self.get_night_fare(self.CUST_NIGHT_CH_TYPE, night_dur, self.CUST_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare

    def get_driver_trip_price(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate):
        ot_fare = 0
        price = 0
        night_fare = 0
        if real_delta > book_delta:
            extra_dur = real_delta - book_delta
            ot_fare = self.get_overtime_fare(self.DRIVER_OT_CH_TYPE, extra_dur, self.DRIVER_OT_CH)
            price = est + ot_fare
        else:
            price = est
        if self.IS_DRIVER_NIGHT_CH_ENABLED:
            night_dur = get_night_duration(startdate, trip_starttime, enddate, trip_stoptime, self.DRIVER_NT_0, self.DRIVER_NT_1)
            night = 1 if night_dur > 0 else 0
            night_fare = night * self.get_night_fare(self.DRIVER_NIGHT_CH_TYPE, night_dur, self.DRIVER_NIGHT_CH)
            price += night_fare

        return price, night_fare, ot_fare


    def get_trip_price(self, book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch):
        customer_price, night_fare, ot_fare = self.get_customer_trip_price(book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)

        driver_price, driver_night_fare, driver_ot_fare = self.get_driver_trip_price(book_delta, real_delta, driver_est, book_starttime, book_stoptime, trip_starttime,
                                                    trip_stoptime, startdate, enddate)
        print("Customer Price", customer_price, flush=True)
        print("Driver Price", driver_price, flush=True)
        booking_ch = customer_price - driver_price
        total_cust_fare = customer_price
        total_cust_fare += insurance_ch

        return int(round(total_cust_fare)), int(round(customer_price)), 0, 0, int(round(booking_ch)), int(round(driver_price)), \
                int(round(night_fare)), int(round(ot_fare)), int(round(driver_night_fare)), int(round(driver_ot_fare))


def is_trip_night(night_thresh_0, night_thresh_1, start_hour, end_hour):
    night = False

    if night_thresh_0.hour > night_thresh_1.hour:
        night_hours = list(
            set(range(night_thresh_0.hour, 24)).union(
            set(range(0, night_thresh_1.hour + 1))))
    else:
        night_hours = list(set(range(night_thresh_0.hour,
                                night_thresh_1.hour + 1)))

    print("NIGHT HOURS", night_hours, flush=True)
    while start_hour != end_hour:
        if start_hour in night_hours:
            night = True
            break
        start_hour = (start_hour + 1) % 24

    if end_hour in night_hours:
        night = True

    return night

def get_night_duration(startdate, starttime, enddate, stoptime, start_night, end_night):

    start_datetime = datetime.combine(startdate, starttime)
    end_datetime = datetime.combine(enddate, stoptime)

    total_night_minutes = 0
    current_datetime = start_datetime

    while current_datetime < end_datetime:
        night_start = datetime.combine(current_datetime.date(), start_night)
        night_end = datetime.combine(current_datetime.date(), end_night)

        # Adjust night_end to next day if the night period crosses midnight
        if start_night > end_night:
            night_end += timedelta(days=1)

        overlap_start = max(current_datetime, night_start)
        overlap_end = min(end_datetime, night_end)

        if overlap_start < overlap_end:
            night_minutes = (overlap_end - overlap_start).total_seconds() / 60
            total_night_minutes += night_minutes

        # Move to the next day
        current_datetime = datetime.combine(current_datetime.date(), time(0, 0)) + timedelta(days=1)

    return int(total_night_minutes)

