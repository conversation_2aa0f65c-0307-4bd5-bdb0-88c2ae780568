#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  booking_params.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>tra

# ──────────── Built‑ins ────────────
import math
import configparser
from collections import defaultdict
from datetime import timedelta

# ────────── Third‑party ────────────
from shapely.geometry import Point, Polygon

# ─────────── Internal ─────────────
from db_config import db
from utils.bookings.booking_utils import distance_on_earth

class Rating:
    LAST_RATE_THRESH = 5
    MEAN_RATING_THRESH = 4.3
    RATING_DEFAULT = 0
    RATING_HIGH = 5
    APP_RATE_THRESH = 3

    #deprecated
    @staticmethod
    def is_rating_good():
        return False

class Regions:
    REGN_UNKNOWN = -2
    INVALID_REGION = -1
    REGN_KOLKATA = 0
    REGN_HYDERABAD = 1
    REGN_GUWAHATI = 2
    REGN_NAGPUR = 3
    REGN_PUNE = 4
    REGN_MUMBAI = 5
    REGN_DELHI = 6
    REGN_CHENNAI = 7
    REGN_BANGALORE = 8
    REGN_AHMEDABAD = 9
    REGN_SILIGURI = 10
    REGN_BHUBANESWAR = 11
    REGN_PATNA = 12
    REGN_RANCHI = 13
    REGN_GURGAON = 14
    REGN_NOIDA = 15
    REGN_JAIPUR = 16
    REGN_LUCKNOW = 17
    REGN_CHANDIGARH = 18
    REGN_ALAMPUR = 19
    REGN_NAME = ["Kolkata", "Hyderabad", "Guwahati", "Nagpur", "Pune", "Mumbai",
                 "Delhi", "Chennai", "Bangalore", "Ahmedabad", "Siliguri",
                 "Bhubaneswar", "Patna", "Ranchi", "Gurgaon", "Noida", "Jaipur",
                 "Lucknow", "Chandigarh", "Alampur",
                 # ADD BEFORE THIS
                 "All Regions!"]
    
    ALL_REGIONS_ACCESS = '-1'

    REGN_STATE = ["West Bengal", "Telengana", "Assam", "Maharashtra",
                  "Maharashtra", "Maharashtra", "Delhi", "Tamil Nadu",
                  "Karnataka", "Gujarat", "West Bengal", "Odisha", "Bihar",
                  "Jharkhand", "Haryana", "Uttar Pradesh", "Rajasthan",
                  "Uttar Pradesh", "Chandigarh", "Alampur",
                  # ADD BEFORE THIS
                  "All"]
    # Regions serviceable by drivers of some other location
    DRIVER_ASSIGN_EXTRAS = {14: [6], 19: [0]}
    ADMIN_VIEW_EXTRAS = {6: [14, 16], 0: [19], -2: [1,8]}
        
    #allowed locations
    kolkatax = [22.4762336, 22.4660815, 22.4600533, 22.4521210, 22.4473615, 22.4432364, 22.4381592, 22.4314951, 22.4235612, 22.4146748, 22.4099139, 22.3956304, 22.3876945, 22.3762660, 22.3667415, 22.3581689, 22.3480081, 22.3438801, 22.3362588, 22.3330831, 22.3372115, 22.3445152, 22.3534061, 22.3572164, 22.3686464, 22.3784883, 22.3641668, 22.3571816, 22.3438454, 22.3273320, 22.3155809, 22.2996995, 22.2914405, 22.3016054, 22.3009701, 22.3006524, 22.3057347, 22.3149457, 22.3273320, 22.3495611, 22.3667068, 22.3733740, 22.3794059, 22.3901993, 22.4063878, 22.4228918, 22.4352685, 22.4380935, 22.4495169, 22.4609394, 22.4647467, 22.4660158, 22.4698229, 22.4787059, 22.4888571, 22.4990076, 22.5231120, 22.5345285, 22.5636995, 22.5877926, 22.6093460, 22.6334312, 22.6613140, 22.6904581, 22.7398622, 22.7423953, 22.7778533, 22.8107703, 22.8158338, 22.8069726, 22.7981109, 22.7778533, 22.7487278, 22.7398622, 22.7563263, 22.7626581, 22.7841841, 22.8006428, 22.8031748, 22.8031748, 22.8208970, 22.8234286, 22.8202345, 22.8316262, 22.7835214, 22.7721256, 22.7518642, 22.7075319, 22.6999306, 22.7100655, 22.7163995, 22.6872609, 22.6745900, 22.6593833, 22.6213594, 22.5922006, 22.5604993, 22.4983435, 22.4755037, 22.4805796, 22.4762336]
    kolkatay = [88.0391140, 88.0439227, 88.0562881, 88.0590359, 88.0731187, 88.0878884, 88.1012842, 88.1033451, 88.1105582, 88.1188018, 88.1284193, 88.1397542, 88.1521195, 88.1655153, 88.1830329, 88.2087940, 88.2218464, 88.2376465, 88.2575685, 88.2685599, 88.2836731, 88.2936341, 88.3128691, 88.3245475, 88.3290127, 88.3375998, 88.3594431, 88.3814259, 88.3979130, 88.4027218, 88.4137132, 88.4291699, 88.4429092, 88.4569919, 88.4676399, 88.4813791, 88.4817226, 88.4800052, 88.4851575, 88.4855009, 88.4714182, 88.4776008, 88.4858444, 88.4951184, 88.4944315, 88.4999272, 88.5088577, 88.5333573, 88.5677055, 88.5841926, 88.6130451, 88.6473933, 88.6789936, 88.6886111, 88.7023504, 88.7202114, 88.7353246, 88.7518118, 88.7531857, 88.7435682, 88.7531857, 88.7476900, 88.7202114, 88.7215854, 88.7105939, 88.6570108, 88.6309061, 88.6075494, 88.5704533, 88.5416008, 88.4893916, 88.4770262, 88.4632870, 88.4454259, 88.4289388, 88.4206952, 88.4028341, 88.3904688, 88.3643642, 88.3643642, 88.3368856, 88.3039114, 88.2880360, 88.2742968, 88.2646793, 88.2385747, 88.2110961, 88.2138440, 88.1808697, 88.1437736, 88.1121733, 88.0654598, 88.0407291, 88.0022591, 88.0077548, 87.9926416, 87.9898937, 87.9885198, 87.9953895, 88.0159984, 88.0391140]
    delhiy = [76.9647217, 76.9633484, 76.9729614, 76.9866943, 77.0127869, 77.0443726, 77.0690918, 77.0924377, 77.0938110, 77.1047974, 77.1295166, 77.1459961, 77.1844482, 77.2270203, 77.2462463, 77.2654724, 77.2695923, 77.2764587, 77.2888184, 77.2860718, 77.3135376, 77.3492432, 77.3725891, 77.3767090, 77.3794556, 77.3808289, 77.3794556, 77.3725891, 77.3492432, 77.3368835, 77.3410034, 77.3657227, 77.4357605, 77.4975586, 77.5112915, 77.5332642, 77.5538635, 77.5607300, 77.5827026, 77.6033020, 77.6156616, 77.5991821, 77.5923157, 77.5689697, 77.5469971, 77.5387573, 77.5112915, 77.4948120, 77.4769592, 77.4687195, 77.4591064, 77.4563599, 77.4508667, 77.4851990, 77.4755859, 77.4824524, 77.4948120, 77.5071716, 77.4769592, 77.4604797, 77.4481201, 77.4398804, 77.4261475, 77.4014282, 77.3712158, 77.3519897, 77.3519897, 77.3519897, 77.3519897, 77.3382568, 77.3245239, 77.3066711, 77.2833252, 77.2654724, 77.2586060, 77.2366333, 77.2105408, 77.1926880, 77.1762085, 77.1624756, 77.1459961, 77.1212769, 77.0965576, 77.0800781, 77.0608521, 77.0512390, 77.0416260, 77.0251465, 77.0169067, 77.0169067, 77.0320129, 77.0388794, 77.0169067, 77.0361328, 77.0416260, 77.0223999, 77.0004272, 76.9812012, 76.9592285, 76.9592285, 76.9537354, 76.9413757, 76.9400024, 76.9564819]
    delhix = [28.3986488, 28.3926085, 28.3756938, 28.3660270, 28.3599848, 28.3575679, 28.3539423, 28.3515252, 28.3793186, 28.4034808, 28.4143520, 28.3974408, 28.3901923, 28.3889841, 28.4191833, 28.4336758, 28.4046888, 28.3684438, 28.3382301, 28.3019625, 28.2922890, 28.3031716, 28.3080079, 28.3297688, 28.3442736, 28.3611933, 28.3926085, 28.4252221, 28.4457514, 28.4674841, 28.4843841, 28.4904192, 28.4529961, 28.4240144, 28.3877760, 28.3430649, 28.3563594, 28.3793186, 28.3998568, 28.4276375, 28.4457514, 28.4602404, 28.4457514, 28.4662768, 28.4988677, 28.5145561, 28.5411002, 28.5495447, 28.5760801, 28.5941685, 28.6074314, 28.6182816, 28.6243090, 28.6267199, 28.6387734, 28.6556460, 28.6701060, 28.6773353, 28.6845641, 28.6966109, 28.7098608, 28.7182917, 28.7158830, 28.7134742, 28.7146786, 28.7231091, 28.7496006, 28.7772890, 28.7929358, 28.7965462, 28.7905287, 28.7953428, 28.8073769, 28.8182063, 28.8049701, 28.8121901, 28.8121901, 28.8001566, 28.7881217, 28.7881217, 28.8145966, 28.8157999, 28.8049701, 28.7977497, 28.7857145, 28.7664553, 28.7423763, 28.7279262, 28.7002247, 28.6592612, 28.6267199, 28.6098426, 28.6001973, 28.5688438, 28.5495447, 28.5483384, 28.5495447, 28.5435130, 28.5266224, 28.5073156, 28.4880052, 28.4662768, 28.4409214, 28.4167677]
    hyderabady = [78.3760070, 78.3883666, 78.3993530, 78.4158325, 78.4295654, 78.4515380, 78.4680175, 78.4858703, 78.4927368, 78.5009765, 78.5119628, 78.5366821, 78.5531616, 78.5820007, 78.5929870, 78.6039733, 78.6341857, 78.6492919, 78.6630248, 78.6767578, 78.6822509, 78.6698913, 78.6534118, 78.6286926, 78.6396789, 78.6465454, 78.6355590, 78.6273193, 78.6012268, 78.5778808, 78.5655212, 78.5394287, 78.5133361, 78.4968566, 78.4831237, 78.4680175, 78.4515380, 78.4281921, 78.4048461, 78.3842468, 78.3718872, 78.3444213, 78.3142089, 78.2730102, 78.2482910, 78.2290649, 78.2194518, 78.2304382, 78.2345580, 78.2400512, 78.2427978, 78.2524108, 78.2688903, 78.3114623, 78.3361816, 78.3430480, 78.3471679, 78.3471679, 78.3540344, 78.3636474, 78.3718872, 78.3691406, 78.3677673]
    hyderabadx = [17.2273192, 17.1958534, 17.1984757, 17.1932310, 17.1945422, 17.1906086, 17.1945422, 17.1984757, 17.2115870, 17.2273192, 17.2391175, 17.2522258, 17.2561581, 17.2614011, 17.2758185, 17.2980977, 17.3046498, 17.3112018, 17.3334767, 17.3505086, 17.3898070, 17.4199300, 17.4317160, 17.4343350, 17.4500481, 17.4709969, 17.4867069, 17.5102694, 17.5155050, 17.5181228, 17.5403725, 17.5573851, 17.5626194, 17.5678536, 17.5717791, 17.5743961, 17.5809384, 17.5887888, 17.5940222, 17.5835552, 17.5704706, 17.5665451, 17.5586937, 17.5521506, 17.5521506, 17.5469160, 17.5181228, 17.5076515, 17.4945614, 17.4709969, 17.4500481, 17.4369539, 17.4107627, 17.4068337, 17.4042143, 17.3871874, 17.3649190, 17.3609890, 17.3347869, 17.3138225, 17.2758185, 17.2535366, 17.2404284]
    jaipury = [75.7193757, 75.7182313, 75.7250978, 75.7319643, 75.7415773, 75.7539369, 75.7745363, 75.7992555, 75.8280946, 75.8528139, 75.8706666, 75.8857728, 75.8912660, 75.8953859, 75.9027100, 75.9054566, 75.9130097, 75.9315491, 75.9336091, 75.9336091, 75.9342957, 75.9288026, 75.9191895, 75.9102631, 75.9027100, 75.8999634, 75.8889771, 75.8731843, 75.8670045, 75.8553315, 75.8560181, 75.8608247, 75.8711243, 75.8704377, 75.8848572, 75.8834840, 75.8814240, 75.8697510, 75.8608247, 75.8450318, 75.8381654, 75.8354188, 75.8340455, 75.8326722, 75.8230592, 75.8134461, 75.8052064, 75.8065797, 75.7928467, 75.7887269, 75.7901002, 75.7949067, 75.7921601, 75.7997132, 75.7846070, 75.7667542, 75.7530213, 75.7392884, 75.7344819, 75.7296754, 75.7077027, 75.7077027, 75.7104493, 75.7104493, 75.7118226, 75.7125092, 75.7193757]
    jaipurx = [26.8681129, 26.8606918, 26.8496711, 26.8386495, 26.8239522, 26.7994525, 26.7884260, 26.7749476, 26.7675951, 26.7602421, 26.7639186, 26.7724968, 26.7798490, 26.7994525, 26.8246393, 26.8460721, 26.8613787, 26.8607664, 26.8650520, 26.8650520, 26.8779075, 26.8883133, 26.8907616, 26.8968820, 26.9121818, 26.9158534, 26.9244200, 26.9268675, 26.9287031, 26.9360452, 26.9378806, 26.9446103, 26.9439986, 26.9513396, 26.9543982, 26.9599036, 26.9684669, 26.9751948, 26.9703018, 26.9696902, 26.9672436, 26.9599036, 26.9556217, 26.9501161, 26.9439986, 26.9397160, 26.9439986, 26.9537865, 26.9647970, 26.9678553, 26.9831454, 26.9892608, 26.9978219, 27.0082166, 27.0155535, 27.0155535, 27.0173876, 27.0088281, 26.9984334, 26.9825338, 26.9556217, 26.9342097, 26.9268675, 26.9121818, 26.8999421, 26.8883133, 26.8681129]
    bangalorey = [77.4582781, 77.4685826, 77.4782001, 77.4857567, 77.4885045, 77.4913162, 77.4937206, 77.4968119, 77.4978424, 77.4995598, 77.5019648, 77.5036822, 77.5084909, 77.5139867, 77.5215433, 77.5280694, 77.5366565, 77.5442131, 77.5514262, 77.5624176, 77.5682568, 77.5799352, 77.5892092, 77.5960859, 77.6043295, 77.6166948, 77.6233085, 77.6322390, 77.6384217, 77.6511305, 77.6648698, 77.6744873, 77.6841048, 77.6933788, 77.7061954, 77.7110041, 77.7261173, 77.7419175, 77.7632134, 77.7794134, 77.8017347, 77.8278393, 77.8663093, 77.8773007, 77.8745528, 77.8786746, 77.8951617, 77.8608135, 77.8580657, 77.8731789, 77.8965357, 77.9089010, 77.9281360, 77.9061532, 77.8882921, 77.8553178, 77.8278393, 77.8333350, 77.8360828, 77.8305871, 77.8150023, 77.7751584, 77.7463059, 77.7366884, 77.7298188, 77.7037141, 77.6843982, 77.6652321, 77.6377535, 77.6143967, 77.5745528, 77.5388307, 77.5044825, 77.5141000, 77.5250914, 77.5237175, 77.5223436, 77.4989868, 77.5003607, 77.4632647, 77.4179251, 77.3918205, 77.3725855, 77.3547244, 77.3547244, 77.3657158, 77.3849508, 77.4069337, 77.4151772, 77.4179251, 77.4261687, 77.4302904, 77.4192990, 77.4220469, 77.4330383, 77.4454036, 77.4582781]
    bangalorex = [12.8937870, 12.8844161, 12.8810693, 12.8797306, 12.8763837, 12.8732146, 12.8712065, 12.8705371, 12.8698677, 12.8678595, 12.8671901, 12.8651818, 12.8618348, 12.8594918, 12.8561447, 12.8534669, 12.8514586, 12.8481114, 12.8444293, 12.8437599, 12.8454335, 12.8430904, 12.8437599, 12.8424549, 12.8404465, 12.8411160, 12.8417188, 12.8363629, 12.8320113, 12.8266552, 12.8223034, 12.8219686, 12.8192905, 12.8233076, 12.8347559, 12.8468064, 12.8441286, 12.8434591, 12.8293999, 12.8344264, 12.8690989, 12.8798090, 12.8865026, 12.9052438, 12.9306759, 12.9507521, 12.9668119, 12.9882234, 13.0270269, 13.0765260, 13.0845519, 13.1139782, 13.1300274, 13.1487501, 13.1728201, 13.1674714, 13.1848542, 13.2196161, 13.2557097, 13.2797691, 13.3060188, 13.3073552, 13.3020095, 13.3207190, 13.3354183, 13.3354183, 13.3317879, 13.3367546, 13.3233917, 13.3220553, 13.3207190, 13.3247280, 13.3060188, 13.2792888, 13.2592394, 13.2177987, 13.1910591, 13.1897220, 13.1696652, 13.1816995, 13.1656537, 13.1562931, 13.1348963, 13.0974473, 13.0974473, 13.0800583, 13.0693568, 13.0693568, 13.0573170, 13.0318978, 13.0104901, 12.9823898, 12.9609778, 12.9315334, 12.9127942, 12.9047627, 12.8937870]
    
    LOCATION_MAP = {
        REGN_KOLKATA: [kolkatay, kolkatax],
        REGN_DELHI: [delhiy, delhix],
        REGN_HYDERABAD: [hyderabady, hyderabadx],
        REGN_JAIPUR: [jaipury, jaipurx],
        REGN_BANGALORE: [bangalorey, bangalorex]
    }
    
    LOCATION_MAP_POLYGON = {
        REGN_KOLKATA: Polygon(zip(kolkatay, kolkatax)),
        REGN_DELHI: Polygon(zip(delhiy, delhix)),
        REGN_HYDERABAD: Polygon(zip(hyderabady, hyderabadx)),
        REGN_JAIPUR: Polygon(zip(jaipury, jaipurx)),
        REGN_BANGALORE: Polygon(zip(bangalorey, bangalorex)),
    }

    #allowed locations
    ENABLED_CITIES = [
        REGN_KOLKATA,
        REGN_DELHI,
        REGN_HYDERABAD,
        REGN_JAIPUR,
        REGN_BANGALORE
    ]
    
    RADIAL_CENTERS = {
        REGN_GUWAHATI: (26.1445, 91.7362),
        REGN_NAGPUR: (21.1458, 79.0882),
        REGN_PUNE: (18.5204, 73.8567),
        REGN_MUMBAI: (19.0760, 72.8777),
        REGN_CHENNAI: (13.0827, 80.2707),
        REGN_AHMEDABAD: (23.0225, 72.5714),
        REGN_SILIGURI: (26.7271, 88.3953),
        REGN_BHUBANESWAR: (20.2961, 85.8245),
        REGN_PATNA: (25.5941, 85.1376),
        REGN_RANCHI: (23.3441, 85.3096),
        REGN_GURGAON: (28.4595, 77.0266),
        REGN_NOIDA: (28.5355, 77.3910),
        REGN_LUCKNOW: (26.8467, 80.9462),
        REGN_CHANDIGARH: (30.7333, 76.7794),
        REGN_ALAMPUR: (16.0678, 77.4724),
    }

    @staticmethod
    def to_string(region_code):
        if region_code >= -1 and region_code < len(Regions.REGN_NAME):
            return Regions.REGN_NAME[region_code]
        else:
            return "N/A"
        
    @staticmethod
    def to_string_reg(region_code):
        if region_code >= 0 and region_code < len(Regions.REGN_NAME):
            return Regions.REGN_NAME[region_code]
        else:
            return "Others"


    @staticmethod
    def city_to_region(region_str):
        if region_str not in Regions.REGN_NAME:
            return 0
        else:
            return Regions.REGN_NAME.index(region_str)

    @staticmethod
    def city_to_region_unsafe(region_str):
        return Regions.REGN_NAME.index(region_str)

    @staticmethod
    def driver_service_regions(region_code):
        if region_code in Regions.DRIVER_ASSIGN_EXTRAS:
            return [region_code] + Regions.DRIVER_ASSIGN_EXTRAS[region_code]
        return [region_code]

    @staticmethod
    def admin_view_regions(region_code):
        if region_code in Regions.ADMIN_VIEW_EXTRAS:
            return [region_code] + Regions.ADMIN_VIEW_EXTRAS[region_code]
        return [region_code]
    
    @staticmethod
    def find_region(lat, lng):
        point = Point(lng, lat)  # Shapely uses (longitude, latitude)

        # Check if the point lies within any polygon
        for region, polygon in Regions.LOCATION_MAP_POLYGON.items():
            if polygon.contains(point):
                return region

        # Check radial distance for other cities
        for region, center in Regions.RADIAL_CENTERS.items():
            lat = float(lat)
            lng= float(lng)
            distance = distance_on_earth(lat, lng, center[0], center[1])
            if distance <= 30:  # 30 km threshold
                return region

        # If no match is found
        return Regions.REGN_UNKNOWN

class CountryBoundary:
    
    INDIA = 0

    indiay = [77.8374510, 76.8717220, 75.7570610, 74.2402030, 73.7499480, 74.1042940, 74.0863037, 74.4515590, 75.2586420, 74.4059290, 74.4213800, 73.4506380, 72.8063965, 72.4438477, 72.0812988, 71.7776660, 70.6164960, 70.5432129, 70.4058838, 70.2081299, 69.8840332, 69.4335938, 69.4445801, 69.6807861, 70.1689270, 69.9884033, 70.2828730, 70.8446990, 71.0432400, 69.6752930, 69.1040039, 68.7744141, 68.1766450, 68.6151123, 69.2962646, 69.4940186, 69.0600586, 68.6700439, 70.3894043, 70.8178711, 71.1752730, 72.4932861, 72.7954102, 72.6690674, 72.6196289, 72.7294922, 72.7349854, 72.8393555, 72.9876709, 73.1909180, 73.4381104, 73.7951660, 73.9764404, 74.2565918, 74.3829346, 74.4708252, 74.8429871, 75.0421143, 75.2096558, 75.3961010, 75.7301331, 75.8660889, 76.1050415, 76.2176514, 76.5087891, 76.9537354, 77.2503662, 77.5552368, 77.9411650, 78.0661011, 78.1622314, 78.2779410, 79.2141724, 79.5011902, 79.3844604, 79.2471313, 79.1853333, 79.0480042, 78.9422607, 79.2333984, 79.3611145, 79.8925781, 79.8925781, 80.1892090, 80.2523804, 80.2798462, 80.2862940, 80.3430176, 80.3773499, 80.3649902, 80.2578735, 80.1535034, 80.2194214, 80.0779724, 80.4418945, 80.9692383, 81.3757324, 81.7012024, 81.9648743, 82.2065735, 82.3315430, 82.3974609, 82.3645020, 82.8149414, 83.2640076, 83.6279297, 84.0454102, 84.5892334, 84.8158264, 85.1021576, 86.5612793, 87.2204590, 86.9787598, 87.0666504, 87.6434326, 87.8713989, 88.2312012, 89.0881348, 89.0936279, 88.9370728, 88.9508057, 88.7612915, 88.8519287, 88.6349487, 88.7338257, 88.6999400, 88.1872559, 88.3063730, 88.4619141, 88.4838867, 88.7942505, 88.9480591, 89.0167236, 88.8711548, 88.8134766, 88.7956238, 88.7599182, 88.6871338, 88.5855103, 88.4825134, 88.2641602, 88.1309509, 88.1597900, 88.2009888, 88.2833862, 88.5630490, 89.3550940, 89.8324810, 89.9206930, 90.8722110, 91.7995960, 92.3762010, 91.9150930, 91.4677300, 91.1589630, 91.7064750, 91.8699280, 92.1460350, 92.6727210, 93.1661280, 93.0602940, 93.2863270, 93.3251880, 94.1067420, 94.5526580, 94.6032490, 95.1551530, 95.1247680, 96.4193660, 97.1339990, 97.0519890, 97.4025610, 97.3271140, 96.2488330, 96.5865910, 96.1176790, 95.4048020, 94.5659900, 93.4133480, 92.5031190, 91.6966570, 92.1037110, 92.0334840, 91.2175120, 90.3732750, 89.7445280, 88.8356430, 88.8142480, 88.7303260, 88.1204410, 88.0431330, 88.1748040, 88.0602380, 87.2274720, 86.0243930, 85.2517790, 84.6750180, 83.3042490, 81.9999870, 81.0572030, 80.0884250, 80.4767210, 81.1112560, 79.7213670, 79.2553711, 78.7388940, 78.4584460, 79.1761290, 79.2088920, 78.8110860, 78.9122690, 77.8374510 ]
    indiax = [35.4940100, 34.6535440, 34.5049230, 34.7488870, 34.3176990, 33.4414730, 33.0961383, 32.7649000, 32.2711050, 31.6926390, 30.9798150, 29.9764130, 29.0321993, 28.8687817, 28.5894313, 27.9131800, 27.9891960, 28.0768578, 28.0574999, 27.9120020, 27.5229009, 27.0102359, 26.7456104, 26.5491264, 26.4918720, 26.0962492, 25.7222290, 25.2151020, 24.3565240, 24.3371360, 24.3020816, 24.1516477, 23.6919650, 23.0846804, 22.7964131, 22.5684330, 22.5735171, 22.4213747, 20.8228430, 20.6583945, 20.7574410, 21.2587085, 20.4939603, 20.1487995, 19.8287313, 19.2178089, 18.9062925, 18.5785749, 17.9264760, 16.9674901, 15.9930149, 15.2523651, 14.9129323, 14.5942033, 14.3016466, 13.9820397, 12.7381400, 12.1789186, 11.9734975, 11.7812450, 11.3090551, 10.8117272, 10.2987049, 9.6061692, 8.8850623, 8.3255850, 8.0863857, 7.9762840, 8.2529590, 8.3514596, 8.4668671, 8.9330470, 9.1888545, 9.1182692, 9.2851266, 9.3853831, 9.3135391, 9.3487425, 9.5140605, 9.8984945, 10.2811237, 10.2499977, 12.0379666, 12.4257860, 12.7140272, 12.8586647, 13.0062610, 13.2185559, 13.3081052, 13.4216805, 13.8420840, 14.1885192, 14.4971870, 15.1291322, 15.6970377, 15.6228480, 16.2675902, 16.2990449, 16.3899665, 16.5111230, 16.5782467, 16.7414033, 17.0252787, 17.3192006, 17.5903872, 17.9787993, 18.1979836, 18.8166049, 19.1335060, 19.3817506, 19.9939093, 20.7560725, 21.1254976, 21.4223487, 21.6166572, 21.7136544, 21.5398625, 21.4836232, 22.0550961, 22.8976934, 23.1985148, 23.2287125, 23.4632608, 23.6520836, 23.9285149, 24.2337150, 24.5821412, 24.8660790, 25.0059669, 25.1651977, 25.1576740, 25.1626803, 25.3055517, 25.3663441, 25.5053249, 25.5362552, 25.5350246, 25.5027560, 25.5424144, 25.6464899, 25.8098062, 25.8245739, 25.9876919, 26.1234169, 26.1480659, 26.4465260, 26.0144070, 25.9650820, 25.2697500, 25.1326010, 25.1474310, 24.9766930, 24.1304140, 24.0726390, 23.5035270, 22.9852640, 23.6243460, 23.6274990, 22.0412390, 22.2784600, 22.7031110, 23.0436580, 24.0785560, 23.8507410, 24.6752380, 25.1624950, 26.0013070, 26.5735720, 27.2645890, 27.0837740, 27.6990590, 27.8825360, 28.2615830, 28.4110310, 28.8309800, 29.4528020, 29.0317170, 29.2774380, 28.6406290, 27.8968760, 27.7717420, 27.4526140, 26.8383100, 26.8086480, 26.8757240, 26.7194030, 27.0989660, 27.2993160, 28.0868650, 27.8765420, 27.4458190, 26.8104050, 26.4146150, 26.3978980, 26.6309850, 26.7261980, 27.2349010, 27.3645060, 27.9254790, 28.4160950, 28.7944700, 29.7298650, 30.1834810, 30.8827150, 31.4053426, 31.5159060, 32.6181640, 32.4837800, 32.9943940, 33.5061980, 34.3219360, 35.4940100]
    COUNTRY_BOUNDARIES = {
        INDIA: [indiay,indiax]
    }
    
    
    
class BookingParams:
    
    BOOKING_DATA_BATCHSIZE_BOTTOM = 12 #Hours
    BOOKING_DATA_BATCHSIZE_TOP = 48 #Hours
    SEARCH_BOOKING_DATA_BATCHSIZE = 150 #Hours
    # if you change no of any trip type, also change ALL_CUST_TYPE
    ALL_CUST_TYPES = ["roundtrip", "os", "oneway", "minios", "os-oneway", "minios-oneway"]
    ALL_CUST_TYPES_ACKO = ["In-City Roundtrip", "OS Round Trip", "In-City Oneway", "In-City Roundtrip", "OS Oneway", "In-City Oneway"]
    TYPE_DIST_ON = {"roundtrip": 0, "os": 0, "minios": 0, "os-oneway": 1, "oneway": 1, "minios-oneway": 1}
    TYPE_ROUNDTRIP = 1
    TYPE_OUTSTATION = 2
    TYPE_ONEWAY = 3
    TYPE_MINIOS = 4
    TYPE_OUTSTATION_ONEWAY = 5
    TYPE_MINIOS_ONEWAY = 6
    # TYPE_CAR_HB_MAN = 0
    # TYPE_CAR_SEDAN_MAN = 1
    # TYPE_CAR_SUV_MAN = 2
    # TYPE_CAR_LUX_MAN = 3
    # TYPE_CAR_HB_AUTO = 4
    # TYPE_CAR_SEDAN_AUTO = 5
    # TYPE_CAR_SUV_AUTO = 6
    # TYPE_CAR_LUX_AUTO = 7
    # TYPE_CAR_EV_MAN = 8
    # TYPE_CAR_EV_AUTO = 9
    # TYPE_CAR_HB_EV = 10
    # TYPE_CAR_SEDAN_EV = 11
    # TYPE_CAR_SUV_EV = 12
    # TYPE_CAR_LUX_EV = 13
    # TYPE_CAR_MUV_MAN = 14
    # TYPE_CAR_MUV_AUTO = 15
    # TYPE_CAR_MUV_EV = 16
    
    # Warning - C24 reserves 50, 500, 501 (default + pickup + delivery)
    ALL_B2B_TYPES = ["Cars24", "Revv", "Zoomcar", "Gujral", "OLX", "CarDekho", "Bhandari",
                     "Mahindra", "Revv", "Spinny", "Pride Honda"]
    TYPE_B2B = 100
    TYPE_C24 = 50
    TYPE_REVV = 51
    TYPE_ZOOMCAR = 52
    TYPE_GUJRAL = 53
    TYPE_OLX = 54
    TYPE_CARDEKHO = 55
    TYPE_BHANDARI = 56
    TYPE_MAHINDRA = 57
    TYPE_REVV_V2 = 58
    TYPE_SPINNY = 59
    TYPE_PRIDEHONDA = 60
    # Parameters
    BOOKING_MIN_THRESH = 5 * 60  # 115 minutes
    BOOKING_MAX_THRESH = 11 * 86400  # 10 days
    BOOKING_MAX_DUR = 20  # 20 hours
    BOOKING_MIN_DUR = 1  # 1 hours
    BOOKING_DUMMY_ID = 1  # fake driver id
    BOOKING_DUMMY_EST = 0
    BOOKING_MAX_RADIUS = 20
    BOOKING_MIN_RADIUS = 2
    BOOKING_EXPAND_RADIUS = 2
    BOOKING_RADIUS = defaultdict(lambda: 999, {Regions.REGN_KOLKATA: 15})
    BUFFER_MINUTES = 80
    SEL_DRIVER_COUNT = 300
    RATING_THRESH = 1
    HOUR_RATIO = 60
    NO_ASSIGN = False
    ZOOMCAR_NO_ASSIGN = False

    RETURN_TRIP = 1

    DID_NO = '08062358619'
    DID_LIST = ['+917969256040', '+917969256041', '+917969256042', '+917969256043', '+917969256044', 
            '+917969256045', '+917969256046', '+917969256047', '+917969256048', '+917969256049', 
            '+917969256050', '+917969256051', '+917969256052', '+917969256053', '+917969256054', 
            '+917969256055', '+917969256056', '+917969256057', '+917969256058', '+917969256059', 
            '+917969256060', '+917969256061', '+917969256062', '+917969256063', '+917969256064', 
            '+917969256065', '+917969256067', '+917969256068', '+917969256069', 
            '+917969256070', '+917969256071', '+917969256072', '+917969256073', '+917969256074', 
            '+917969256075', '+917969256076', '+917969256077', '+917969256078', '+917969256079', 
            '+917969256080', '+917969256081', '+917969256082', '+917969256083', '+917969256084', 
            '+917969256085', '+917969256086', '+917969256087', '+917969256088', '+917969256089', 
            '+917969256090', '+917969256091', '+917969256092', '+917969256093', '+917969256094', 
            '+917969256095', '+917969256096', '+917969256097', '+917969256098', '+917969256099']
    COOLING_TIME_PERIOD = 1800 # in seconds
    USER_OFFICE_CONTACT = 9163712120
    DRIVER_OFFICE_CONTACT = 9088464646
    REPEAT_BOOKING_INS = 3
    WRONG_INPUT_THRESH = 4

    MAX_LOC_MINUTES_AGO = 30
    MAX_DIST_DRIV_IMM = 8
    MAX_DELTA_IMM_TRIP_HOURS = 2

    MAX_THRESH_BEFORE_NEXT_TRIP = 1 * 60 # in minutes

    USER_MUL_BOOK = [10,100939,153871,53557,51393,165059,176990,176991,176992, 575, 251799, 251964, 253769, 231483, 227716]
    USER_BANNED_LIST = [5489, 49392]
    
    CAR_TYPE_NAME = ['Hatchback', 'Sedan', 'SUV', 'MUV', 'Luxury']
    GEAR_TYPE_NAME = ['Manual', 'Automatic','EV']
    
    
    
    TYPE_CAR_HB = 0
    TYPE_CAR_SEDAN = 1
    TYPE_CAR_MUV = 2
    TYPE_CAR_SUV = 3
    TYPE_CAR_LUX = 4
    TYPE_TRANS_MAN = 0
    TYPE_TRANS_AUTO = 1
    TYPE_TRANS_EV = 2
    
    car_type_mapping = {
    0: [0, 4, 10],  # HB
    1: [1, 5, 11],  # Sedan
    2: [14, 15, 16],  # MUV
    3: [2, 6, 12],  # SUV
    4: [3, 7, 13],  # LUX
    }

    
    DURATION_PATTERN = r'^\d+d \d+h \d+m$'
    
    valid_sources = {'ios', 'android', 'web'}
    
    CHARGE_TYPE = "Flat"
    
    IMMEDIATE_PERCENT_INC = 0.15
    IMMEDIATE_FLAT_CHARGE = 100
    
    
    CLASSIC_DRIVER = 0
    PREMIUM_DRIVER = 1
    
    PREMIUM_DRIVER_PERCENT_INC = 0.15
    PREMIUM_DRIVER_FLAT_CHARGE = 100
    
    
    THRESHOLD_FORWARD_NIGHT = 30 #min
    THRESHOLD_BACKWARD_NIGHT = 30
    THRESHOLD_END_NIGHT_FORWARD = 30
    
    THRESHOLD_FORWARD_OP = 30
    THRESHOLD_BACKWARD_OP = 30
    
    CONFIRM_THRESHOLD_SEARCH = timedelta(minutes=10) #min
    
    
        

    @staticmethod
    def is_cust_booktype(type):
        if type <= len(BookingParams.ALL_CUST_TYPES):
            return True
        else:
            return False

    @staticmethod
    def is_dist_on(type_str):
        if type_str in BookingParams.TYPE_DIST_ON.keys():
            return BookingParams.TYPE_DIST_ON[type_str]
        else:
            return 0

    @staticmethod
    def maybe_is_oneway(type):
        if type == BookingParams.TYPE_ONEWAY or \
           type == BookingParams.TYPE_OUTSTATION_ONEWAY or \
           type == BookingParams.TYPE_MINIOS_ONEWAY or \
           not BookingParams.is_cust_booktype(type):
            return True
        return False

    @staticmethod
    def get_no_broadcast_bd():
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        BookingParams.ZOOMCAR_NO_ASSIGN = bool(int(config_parser.get('bhandari', 'no_broadcast')))
        return BookingParams.ZOOMCAR_NO_ASSIGN

    @staticmethod
    def set_no_broadcast_bd(val_str):
        if val_str == "False" or val_str == "false":
            no_broadcast = False
        else:
            no_broadcast = True
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        if not config_parser.has_section('bhandari'):
            config_parser.add_section('bhandari')
        config_parser.set('bhandari', 'no_broadcast', str(int(no_broadcast)))
        with open('/app/config/config.ini', 'w+') as conffile:
            config_parser.write(conffile)
        BookingParams.ZOOMCAR_NO_ASSIGN = no_broadcast
        return

    @staticmethod
    def get_no_broadcast_cd():
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        BookingParams.ZOOMCAR_NO_ASSIGN = bool(int(config_parser.get('cardekho', 'no_broadcast')))
        return BookingParams.ZOOMCAR_NO_ASSIGN

    @staticmethod
    def set_no_broadcast_cd(val_str):
        if val_str == "False" or val_str == "false":
            no_broadcast = False
        else:
            no_broadcast = True
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        if not config_parser.has_section('cardekho'):
            config_parser.add_section('cardekho')
        config_parser.set('cardekho', 'no_broadcast', str(int(no_broadcast)))
        with open('/app/config/config.ini', 'w+') as conffile:
            config_parser.write(conffile)
        BookingParams.ZOOMCAR_NO_ASSIGN = no_broadcast
        return

    @staticmethod
    def get_no_broadcast_zoom():
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        BookingParams.ZOOMCAR_NO_ASSIGN = bool(int(config_parser.get('zoomcar', 'no_broadcast')))
        return BookingParams.ZOOMCAR_NO_ASSIGN

    @staticmethod
    def set_no_broadcast_zoom(val_str):
        if val_str == "False" or val_str == "false":
            no_broadcast = False
        else:
            no_broadcast = True
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        if not config_parser.has_section('zoomcar'):
            config_parser.add_section('zoomcar')
        config_parser.set('zoomcar', 'no_broadcast', str(int(no_broadcast)))
        with open('/app/config/config.ini', 'w+') as conffile:
            config_parser.write(conffile)
        BookingParams.ZOOMCAR_NO_ASSIGN = no_broadcast
        return

    @staticmethod
    def get_no_broadcast():
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        BookingParams.NO_ASSIGN = bool(int(config_parser.get('booking', 'no_broadcast')))
        return BookingParams.NO_ASSIGN

    @staticmethod
    def set_no_broadcast(val_str):
        if val_str == "False" or val_str == "false":
            no_broadcast = False
        else:
            no_broadcast = True
        config_parser = configparser.ConfigParser()
        config_parser.read('/app/config/config.ini', encoding='utf8')
        config_parser.set('booking', 'no_broadcast', str(int(no_broadcast)))
        with open('/app/config/config.ini', 'w+') as conffile:
            config_parser.write(conffile)
        BookingParams.NO_ASSIGN = no_broadcast
        return

    @staticmethod
    def get_type_to_str(type_no):
        type_offset = type_no - BookingParams.TYPE_C24
        if type_offset < 0:
            return "Customer"
        elif type_offset > len(BookingParams.ALL_B2B_TYPES):
            return "Unknown B2B"
        return BookingParams.ALL_B2B_TYPES[type_offset]
    
    @staticmethod
    def get_b2b_trip_type(trip_name):
        if trip_name == "One Way":
            return BookingParams.TYPE_ONEWAY
        elif trip_name == "Round":
            return BookingParams.TYPE_ROUNDTRIP
        elif trip_name == "Outstation Round":
            return BookingParams.TYPE_OUTSTATION
        return -1
    
    @staticmethod
    def get_b2b_trip_name(trip_type):
        if trip_type == BookingParams.TYPE_ONEWAY:
            return "One Way"
        elif trip_type == BookingParams.TYPE_ROUNDTRIP:
            return "Round"
        elif trip_type == BookingParams.TYPE_OUTSTATION:
            return "Outstation Round"
        return -1

    @staticmethod
    def determine_booking_type(booking_type):
        print("book_type",booking_type,flush=True)
        if booking_type is not None:
            if isinstance (booking_type,tuple):
                booking_type = booking_type[0]  # Extract the single value from the tuple
            return 1 if booking_type < BookingParams.TYPE_C24 else 0 if booking_type == BookingParams.TYPE_B2B else -1
        else:
            raise ValueError('Booking type not found')
        
    @staticmethod
    def is_outstation(book_type):
        return book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY

'''
Utility functions
'''


def normalize_distance(dist):
    return math.ceil(dist / BookingParams.BOOKING_EXPAND_RADIUS) * BookingParams.BOOKING_EXPAND_RADIUS

