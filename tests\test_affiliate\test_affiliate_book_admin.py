from datetime import datetime,timedelta
from models.affiliate_models import db,Affiliate,AffiliateDriverSearch,AffBookingLogs,AffiliateWalletLogs, \
    AffiliateRep,AffiliateRepLogs,AffiliateSpoc,AffiliateAddress
from conftest import create_master_affiliate,create_user_and_driver,unique_user_data,create_user
from models.models import Bookings,DriverTrans,Trip,Drivers,Users
from test_driver.test_driver_booking import create_driver_with_wallet_and_due
from unittest.mock import patch
from sqlalchemy import exc
import pytest
from utils.bookings.booking_utils import get_b2b_book_code
from test_affiliate_rep import test_affiliate_and_rep
from db_config import mdb
import redis_config
eps = 1e-7

#  API - /api/admin/affiliate/booking/cancel_charge

def test_booking_cancel_charge_incomplete_form(client, admin_login):
    auth_headers, _ = admin_login
    print("mongo name",mdb.name)
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    today = datetime.utcnow()
    booking = Bookings(
            user=user.id,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    db.session.add(booking)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    get_b2b_book_code(booking.id)
    form_data = {
        'book_code': booking.code,
        "regions":'0',
        # Missing 'reason'
    }
    response = client.post('/api/admin/affiliate/booking/cancel_charge', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    json_data = response.get_json()
    print("Response", json_data)
    assert json_data['success'] == -1
    assert json_data['message'] == "Incomplete Form Details"


def test_booking_cancel_charge_invalid_reason_format(client, admin_login):
    auth_headers, _ = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    today = datetime.utcnow()
    booking = Bookings(
            user=user.id,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    get_b2b_book_code(booking.id)
    form_data = {
        'book_code': booking.code,
        "regions":'0',
        'reason': 'Invalid'  # should be an integer
    }
    response = client.post('/api/admin/affiliate/booking/cancel_charge', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['message'] == "Invalid Data Format"


def test_booking_cancel_charge_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_book = AffBookingLogs(admin_id=admin.id, aff_id=master_affiliate.id, book_id=booking.id,
                        mapped_by=master_affiliate.id, mapped_wallet=master_affiliate.id)
    db.session.add(aff_book)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    get_b2b_book_code(booking.id)
    form_data = {
        'book_code': booking.code,
        "regions":'0',
        'reason': '4'
    }
    redis_config.redis_available = True
    response = client.post('/api/admin/affiliate/booking/cancel_charge', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    print(response)
    json_data = response.get_json()
    print(json_data)
    assert json_data['success'] == 1
    assert json_data['charge'] ==  [0, 15]
    
    
    
def test_booking_cancel_charge_success_redis_off(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    master_affiliate, redis_key = create_master_affiliate()
    assert master_affiliate is not None
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_book = AffBookingLogs(admin_id=admin.id, aff_id=master_affiliate.id, book_id=booking.id,
                        mapped_by=master_affiliate.id, mapped_wallet=master_affiliate.id)
    db.session.add(aff_book)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    get_b2b_book_code(booking.id)
    form_data = {
        'book_code': booking.code,
        "regions":'0',
        'reason': '4'
    }
    redis_config.redis_available = False
    response = client.post('/api/admin/affiliate/booking/cancel_charge', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1


# ---------------------------------


#  API - /api/admin/affiliate/booking/cancel

def test_booking_cancel_incomplete_form(client, admin_login):
    auth_headers, _ = admin_login

    # Missing 'reason'
    form_data = {
        'book_code': 'TEST123',
        "regions":'0'
    }
    response = client.post(
        '/api/admin/affiliate/booking/cancel',
        data=form_data,
        headers=auth_headers
    )
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['message'] == 'Incomplete Form Details'


def test_booking_cancel_invalid_reason_format(client, admin_login):
    auth_headers, _ = admin_login

    # Create a minimal booking so code exists
    booking = Bookings(
        user=None, skey='abc', driver=1,
        lat=0.0, long=0.0,
        starttime='00:00:00', startdate='2025-01-01',
        dur=3600, endtime='01:00:00', enddate='2025-01-01',
        estimate=100, pre_tax=90, loc='X', car_type=0
    )
    booking.valid = 1
    db.session.add(booking)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    # generate booking.code via your helper
    get_b2b_book_code(booking.id)
    form_data = {
        'book_code': booking.code,
        'reason': 'not_an_int',
        'regions': '0'
    }
    response = client.post(
        '/api/admin/affiliate/booking/cancel',
        data=form_data,
        headers=auth_headers
    )
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == 0
    assert json_data['message'] == 'Invalid reason code.'



def test_booking_cancel_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    master_affiliate, redis_key = create_master_affiliate()
    # Create a dummy booking and map it to an affiliate rep
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp

        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    trip= Trip(booking.id,datetime.utcnow() + timedelta(hours=2),1,1,6)
    db.session.add(trip)
    db.session.flush()
    get_b2b_book_code(booking.id)
    code = booking.code
    try:
        db.session.commit()
        # db.session.remove()
    except Exception as e:
        print("error",e)
        db.session.rollback()

    # generate booking.code
    form_data = {
        'book_code': code,
        'reason': '2',
        'regions': '0',
    }
    redis_config.redis_available = True
    response = client.post(
        '/api/admin/affiliate/booking/cancel',
        data=form_data,
        headers=auth_headers
    )
    print(response.get_json(), flush=True)
    assert response.status_code == 200
    json_data = response.get_json()
    print(json_data)
    assert json_data['success'] == 1
    assert json_data['message'] == 'Booking cancelled successfully'
    assert json_data['penalty'] == [70,0]

# ---------------------------------


#  API - /api/admin/restart_trip

def test_restart_affiliate_trip(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    master_affiliate, redis_key = create_master_affiliate()
    # Create a dummy booking and map it to an affiliate rep
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    search_entry = AffiliateDriverSearch(
        "tezt1",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp

        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt1',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    wallet_logs = AffiliateWalletLogs(-10*100, method=f"Booking #{booking.code}",
                                              from_account=master_affiliate.id, wallet_before=master_affiliate.wallet,
                                              wallet_after=120, source=AffiliateWalletLogs.SOURCE_ADMIN)
    db.session.add(wallet_logs)
    dt = DriverTrans(
            booking.driver, 50 * 100, wall_a=500, wall_b=550,
            with_a=30, with_b=50,
            method=f"Booking {booking.code}", status=DriverTrans.COMPLETED, stop=True
        )
    db.session.add(dt)
    db.session.flush()
    trip= Trip(booking.id,datetime.utcnow() - timedelta(hours=2),1,1,0)
    db.session.add(trip)
    db.session.flush()
    trip.endtime = datetime.utcnow() - timedelta(hours=1)
    trip.aff_trans = wallet_logs.id
    trip.driver_trans = dt.id
    driver = create_driver_with_wallet_and_due(wallet=500, withdrawable=200, due_threshold=1000, driver_id=driver.id)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    form_data = {
        'book_id':booking.id,
        'regions': '-1',
    }
    # Step 2: Call the restart API
    response = client.post(
        "/api/admin/restart_trip",
        headers=auth_headers,
       data=form_data
    )
    json_data = response.get_json()
    print(json_data)
    assert response.status_code == 200


    assert json_data["success"] == 1
    assert "Trip restarted successfully" in json_data["message"]