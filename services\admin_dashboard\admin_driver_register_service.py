from datetime import datetime
import requests
from sqlalchemy.sql import func, case, and_
from models.models import db, Drivers, Users, DriverInfo, Bookings, DriverApprovalLog, DriverGLDrivLicDetails
from utils.time_utils import convert_to_local_time
from utils.bookings.booking_params import Regions
from schemas.admin_dashboard.driver_register_schema import RegisterDriverListPayload
from flask import current_app as app

def register_driver_list_service(payload:RegisterDriverListPayload,tz:str):
    """
    Fetches a list of drivers for registration screen, filtered by timestamp and region.

    Args:
        payload (RegisterDriverListPayload): Filter data with optional timestamp_gt, timestamp_lt, and regions.
        tz (str): Timezone string for formatting registration timestamps.

    Returns:
        dict: Contains driver data with registration info, trip stats, and remarks.
    """
    timestamp_gt = datetime.strptime(payload['timestamp_gt'], '%Y-%m-%d %H:%M:%S') if payload.get('timestamp_gt') else None
    timestamp_lt = datetime.strptime(payload['timestamp_lt'], '%Y-%m-%d %H:%M:%S') if payload.get('timestamp_lt') else None
    regions = payload.get('regions')

    base_query = db.session.query(Drivers.id.label('driver_id')).join(Users, Drivers.user == Users.id)

    if timestamp_gt:
        base_query = base_query.filter(Users.reg >= timestamp_gt)
    if timestamp_lt:
        base_query = base_query.filter(Users.reg <= timestamp_lt)
    if regions and regions != Regions.ALL_REGIONS_ACCESS:
        region_list = [int(r.strip()) for r in regions.split(',')]
        base_query = base_query.filter(Users.region.in_(region_list))

    driver_ids = [row.driver_id for row in base_query.all()]
    if not driver_ids:
        return {
            "success": 1,
            "message": "Drivers fetched successfully",
            "status": "success",
            "status_code": 200,
            "data": []
        }

    driver_trips_cte = (
        db.session.query(
            Bookings.driver.label('driver_id'),
            func.coalesce(func.sum(case((and_(Bookings.valid == 1, Bookings.enddate < func.now()), 1), else_=0)), 0).label('completed_bookings'),
            func.coalesce(func.sum(case((and_(Bookings.valid == 1, Bookings.enddate >= func.now()), 1), else_=0)), 0).label('upcoming_bookings'),
        )
        .filter(Bookings.driver.in_(driver_ids))
        .group_by(Bookings.driver)
        .cte('DriverTrips')
    )

    driver_remarks_cte = (
        db.session.query(
            DriverApprovalLog.driver.label('driver_id'),
            func.group_concat(DriverApprovalLog.remark.op('SEPARATOR')(' | ')).label('remarks')
        )
        .filter(DriverApprovalLog.timestamp >= (timestamp_gt if timestamp_gt else func.now()))
        .filter(DriverApprovalLog.driver.in_(driver_ids))
        .group_by(DriverApprovalLog.driver)
        .cte('DriverRemarks')
    )

    query = (
        db.session.query(
            Drivers.id.label('driver_id'),
            Users.mobile,
            func.concat(Users.fname, ' ', Users.lname).label('name'),
            Users.region,
            Users.reg.label('reg_timestamp'),
            Drivers.approved.label('approval'),
            Drivers.available.label('available'),
            DriverInfo.driver_remark.label('remarks'),
            func.coalesce(driver_trips_cte.c.completed_bookings, 0).label('completed_bookings'),
            func.coalesce(driver_trips_cte.c.upcoming_bookings, 0).label('upcoming_bookings'),
        )
        .join(Users, Drivers.user == Users.id)
        .join(DriverInfo, Drivers.id == DriverInfo.driver_id)
        .outerjoin(driver_trips_cte, Drivers.id == driver_trips_cte.c.driver_id)
        .outerjoin(driver_remarks_cte, Drivers.id == driver_remarks_cte.c.driver_id)
        .filter(Drivers.id.in_(driver_ids))
        .order_by(Users.reg.desc())
    )

    results = query.all()
    return {
        "success": 1,
        "message": "Drivers fetched successfully",
        "status": "success",
        "status_code": 200,
        'data': [
        {
            'driver_id': r.driver_id,
            'driver_id': r.driver_id,
            'mobile': r.mobile,
            'name': r.name,
            'region': r.region,
            'registration_date': convert_to_local_time(r.reg_timestamp, tz) if r.reg_timestamp else None,
            'approval': r.approval,
            'status': r.available,
            'completed': r.completed_bookings,
            'upcoming': r.upcoming_bookings,
            'remark': r.remarks or "-",
        }
        for r in results
    ]}
    