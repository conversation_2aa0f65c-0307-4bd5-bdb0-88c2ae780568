import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str_1 = (datetime.now() - timedelta(days=8)).date().strftime("%d%m%Y")
date_str_2 = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathZK = D4M_UTIL_PATH + 'output/kolkata-zoomcar-driver.csv'
subjectZK = "Zoomcar Driver Payment - Kolkata - " + date_str_2
filepathCK = D4M_UTIL_PATH + 'output/kolkata-cardekho-driver.csv'
subjectCK = "Cardekho Driver Payment - Kolkata - " + date_str_2
filepathOK = D4M_UTIL_PATH + 'output/kolkata-olx-driver.csv'
subjectOK = "OLX Driver Payment - Kolkata - " + date_str_2
filepathBK = D4M_UTIL_PATH + 'output/kolkata-bhandari-driver.csv'
subjectBK = "Bhandari Driver Payment - Kolkata - " + date_str_2
filepathSP = D4M_UTIL_PATH + 'output/kolkata-spinny-driver.csv'
subjectSP = "Spinny Driver Payment - Kolkata - " + date_str_2
filepathMK = D4M_UTIL_PATH + 'output/kolkata-mohan-driver.csv'
subjectMK = "Mohan Motors Driver Payment - Kolkata - " + date_str_2
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
#send_mail(from_addr, to_addr_list, subjectZK, content, filepathZK)
#send_mail(from_addr, to_addr_list, subjectCK, content, filepathCK)
send_mail(from_addr, to_addr_list, subjectBK, content, filepathBK)
send_mail(from_addr, to_addr_list, subjectSP, content, filepathSP)
#send_mail(from_addr, to_addr_list, subjectMK, content, filepathMK)