
from flask import Flask, request, render_template
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>
from flask_caching import Cache
from flask_util_js import FlaskUtilJs
from flask_s3 import FlaskS3
from flask_cors import CORS
from pymongo import MongoClient
from firebase_admin import credentials, initialize_app, firestore
import os, redis, json, gevent
from datetime import timed<PERSON>ta
from config import ProdConfig, TestConfig
from db_config import init_dbs
from redis_config import init_redis
from flask import jsonify
from io import StringIO
from socketio_config import socketio
from _utils import upload_log_to_s3
from datetime import datetime
import pytz
import traceback
from error_mail_util import send_error_email
from werkzeug.exceptions import RequestEntityTooLarge
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

class Server:
    SERVER_URL = "0.0.0.0"
    SERVER_PORT = 5000

cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})
jwt = JWTManager()
s3 = FlaskS3()
mongo_client = None
fb_db = None
fujs = None
app = None

def create_app(config_class):
    global socketio, mongo_client, fb_db, fujs

    app = Flask(__name__)
    app.config.from_object(config_class)

    CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)
    cache.init_app(app)
    jwt.init_app(app)

    @jwt.expired_token_loader
    def expired_token_cb(jwt_header, jwt_data):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, expired token"}}), 401

    @jwt.invalid_token_loader
    def invalid_token_cb(_):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, invalid token"}}), 401

    @jwt.unauthorized_loader
    def unauth_token_cb(_):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access or missing header"}}), 401

    @jwt.user_identity_loader
    def user_identity_lookup(identity):
        return identity['id'] if isinstance(identity, dict) else identity
    s3.init_app(app)
    fujs = FlaskUtilJs(app)

    app.secret_key = app.config['SECRET_KEY']
    redis_client, redis_url = init_redis(app)
    app.redis_client = redis_client
    print("Here", flush=True)
    async_mode = 'threading' if app.config['TESTING'] else 'gevent'
    socketio.init_app(app, async_mode=async_mode, message_queue=redis_url)

    #socketio = init_socketio(app, redis_url, async_mode)
    #app.socketio = socketio
    from socketio_app import live_update_to_channel
    from live_update_booking import send_live_update_of_booking
    from live_update_aff_book import send_live_aff_booking_table
    from register_cust import regcust
    from login import loginp
    from register_driver import regdrive
    from book_ride import bookride
    from acc_profile import acc_profile
    from drivers import drivers
    #from drivers_new_app import driversnew
    from users import users
    from trip import trips
    from website import website
    from admin import adminLogin, admin
    from referral import referral
    from affiliate import affiliate
    from payments import payments
    from track import track
    from slackbot import slackbot
    from api import apip
    from campaign import campaign
    from delete_account import delete_account
    from affiliate_api.zoomcar import zoomapi
    from seo import seo
    from adminnew.drivers.driver_admin import admin_driver
    from adminnew.booking.booking_admin import admin_booking
    from adminnew.customer.customer_admin import admin_customer
    from call_masking import callMasking
    from adminnew.analytics.analytics_admin import admin_analytics
    from adminnew.coupon.coupon_admin import admin_coupon
    from adminnew.affiliate.affiliate_admin import admin_affiliate
    from adminnew.create_booking.create_booking_admin import create_booking
    from adminnew.dues_credit.dues_credit_admin import dues_credit
    from adminnew.pricing.pricing_admin import admin_pricing
    from adminnew.utilities.utility_admin import admin_utility
    from adminnew.calling_admin import admin_calling
    from adminnew.login_admin import loginn
    from affiliate_b2b.affiliate_b2b import affiliaten
    from affiliate_b2b.affiliate_login import aff_login
    from affiliate_b2b.affiliate_booking import aff_book
    from affiliate_b2b.affiliate_profile import aff_profile
    from affiliate_b2b.affiliate_wallet import aff_wallet
    from affiliate_b2b.affiliate_report import aff_report
    from adminnew.affiliate.affiliate_admin import admin_affiliate
    from affiliate_b2b.affiliate_analytics import aff_analytics

    app.register_blueprint(regcust)
    app.register_blueprint(admin_utility)
    app.register_blueprint(loginp)
    app.register_blueprint(loginn)
    app.register_blueprint(admin_calling)
    app.register_blueprint(admin_driver)
    app.register_blueprint(admin_booking)
    app.register_blueprint(admin_customer)
    app.register_blueprint(admin_analytics)
    app.register_blueprint(admin_coupon)
    app.register_blueprint(create_booking)
    app.register_blueprint(dues_credit)
    app.register_blueprint(admin_pricing)
    app.register_blueprint(regdrive)
    app.register_blueprint(bookride)
    app.register_blueprint(admin)
    app.register_blueprint(drivers)
    app.register_blueprint(users)
    app.register_blueprint(acc_profile)
    app.register_blueprint(trips)
    app.register_blueprint(website)
    app.register_blueprint(adminLogin)
    app.register_blueprint(affiliate)
    app.register_blueprint(payments)
    app.register_blueprint(track)
    app.register_blueprint(slackbot)
    app.register_blueprint(apip)
    app.register_blueprint(campaign)
    app.register_blueprint(zoomapi)
    app.register_blueprint(seo)
    app.register_blueprint(referral)
    app.register_blueprint(delete_account)
    app.register_blueprint(callMasking)
    app.register_blueprint(affiliaten)
    app.register_blueprint(aff_login)
    app.register_blueprint(aff_book)
    app.register_blueprint(admin_affiliate)
    app.register_blueprint(aff_profile)
    app.register_blueprint(aff_wallet)
    app.register_blueprint(aff_report)
    app.register_blueprint(aff_analytics)
    #app.register_blueprint(notify)

    init_dbs(app)
    
   
   
    @app.before_request
    def before_request():
        request_url = request.url.replace("https://www.drivers4me.com", "")
        
        if "static/" in request_url:
            print("static call")
            return  # Let static requests pass

        try:
            # # Trigger form parsing to catch early body issues
            # _ = request.form
            # _ = request.files

            print("call", request_url, ":", request.form, ":", dict(request.headers))

        # except OSError as e:
        #     if "unexpected end of file" in str(e).lower():
        #         app.logger.warning(f"Incomplete request from user (slow network?): {request_url}")
        #         return jsonify({
        #             "status": -1,
        #             "message": "Your connection was too slow or got interrupted. Please try again."
        #         }), 400
        #     raise  

        # except RequestEntityTooLarge:
        #     return jsonify({
        #         "status": -1,
        #         "message": "Uploaded file too large"
        #     }), 413

        except Exception as e:
            print(f"Unexpected error during request.form parsing at {request_url}: {e}",flush=True)
            pass

        return  

    @app.after_request
    def after_request(response):
        request_url = request.url.replace("https://www.drivers4me.com", "")
        if "static/" in request_url:
            return response
        try:
            print("send", request_url, ":", str(response.data.decode("utf-8").replace("\n", "")[:200]).strip(), ":", dict(request.headers))
        except Exception:
            pass
        return response
    
    @app.errorhandler(500)
    def handle_500(e):
        page_name = request.url
        trace_lines = traceback.format_exc().splitlines()
        send_error_email(page_name, e, trace_lines, request)
        return jsonify({
            "status": -99,
            "message": "Internal Server Error. Please try again later.",
            "error": str(e)  # Remove this in production if sensitive
        }), 500
      
    @app.teardown_request
    def shutdown_session(exception=None):
        try:
            if exception:  # rollback only on error
                app.db.session.rollback()
        except Exception as e:
            app.logger.warning(f"Teardown commit failed: {e}")
            app.db.session.rollback()
        finally:    
            app.db.session.remove()  # Always clean up the session
    
    @app.context_processor
    def inject_fujs():
        return dict(fujs=fujs)
    
    app.loc_engine = create_engine(
        app.config['SQLALCHEMY_DATABASE_URI'],
        poolclass=NullPool
    )
    app.LocSession = sessionmaker(bind=app.loc_engine)

    return app, socketio