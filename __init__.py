#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  app_factory.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

"""
Application factory for initializing Flask app and extensions.
"""

# ──────────── Built‑ins ────────────
import os
import json
from io import String<PERSON>
from datetime import datetime, timedelta

# ────────── Third‑party ────────────
from flask import Flask, request, jsonify
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_caching import Cache
from flask_s3 import FlaskS3
from flask_cors import CORS
from pymongo import MongoClient
from firebase_admin import credentials, initialize_app, firestore
import redis
import gevent
import pytz

# ─────────── Internal ─────────────
from config import ProdConfig, TestConfig
from db_config import init_dbs
from redis_config import init_redis
from socketio_config import socketio
from utils.s3_utils import upload_log_to_s3

cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})
jwt = JWTManager()
s3 = FlaskS3()
mongo_client = None
fb_db = None
fujs = None
app = None


def create_app(config_class):
    global socketio, mongo_client, fb_db, fujs, app

    app = Flask(__name__)
    app.config.from_object(config_class)

    # CORS, caching, JWT, S3, and JS utilities
    CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)
    cache.init_app(app)
    jwt.init_app(app)
    s3.init_app(app)
    # fujs = FlaskUtilJs(app)

    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_cb(jwt_header, jwt_data):
        return jsonify(status=401, success=-1, message="Unauthorized access, expired token"), 401

    @jwt.invalid_token_loader
    def invalid_token_cb(_):
        return jsonify(status=401, success=-1, message="Unauthorized access, invalid token"), 401

    @jwt.unauthorized_loader
    def unauth_token_cb(_):
        return jsonify(status=401, success=-1, message="Unauthorized access or missing header"), 401

    @jwt.user_identity_loader
    def user_identity_lookup(identity):
        return identity.get('id') if isinstance(identity, dict) else identity

    app.secret_key = app.config['SECRET_KEY']

    # Initialize Redis, MongoDB, and Firestore
    redis_client, redis_url = init_redis(app)
    app.redis_client = redis_client

    mongo_client = MongoClient(app.config['MONGO_URI'])
    fb_db = initialize_app(credentials.Certificate(app.config['FIREBASE_CRED_PATH']))

    # Configure Socket.IO
    async_mode = 'threading' if app.config.get('TESTING') else 'gevent'
    socketio.init_app(app, async_mode=async_mode, message_queue=redis_url)

    # Register blueprints (full list)
    from api.customer_app.booking_routes import customer_booking
    from api.customer_app.login_routes import customer_login
    from api.admin_dashboard.login_routes import admin_login
    from api.admin_dashboard.analytics_general_routes import analytics_general
    from api.admin_dashboard.analytics_admin_routes import analytics_admin
    from api.admin_dashboard.admin_driver_routes import admin_driver
    from api.admin_dashboard.admin_dues_routes import admin_dues
    from api.admin_dashboard.admin_driver_register_routes import admin_driver_register
    from api.admin_dashboard.booking_routes import admin_booking

    app.register_blueprint(customer_login)
    app.register_blueprint(customer_booking)
    
    app.register_blueprint(admin_login)
    app.register_blueprint(analytics_general)
    app.register_blueprint(analytics_admin)
    app.register_blueprint(admin_driver)
    app.register_blueprint(admin_dues)
    app.register_blueprint(admin_driver_register)
    app.register_blueprint(admin_booking)
    

    # Initialize SQL/NoSQL databases
    init_dbs(app)

    # Teardown: log uncommitted changes and rollback
    @app.teardown_request
    def shutdown_session(exception=None):
        log_buffer = StringIO()
        try:
            if app.db.session.new or app.db.session.dirty or app.db.session.deleted:
                log_buffer.write(f"API Called: {request.method} {request.path}\n")
                for obj in app.db.session.new:
                    log_buffer.write(f"New: {repr(obj)}\n")
                for obj in app.db.session.dirty:
                    log_buffer.write(f"Updated: {repr(obj)}\n")
                for obj in app.db.session.deleted:
                    log_buffer.write(f"Deleted: {repr(obj)}\n")
                ist_time = datetime.now(pytz.timezone('Asia/Kolkata'))
                log_buffer.write(f"timestamp: {ist_time.strftime('%Y-%m-%d %H:%M:%S')}\n")

            app.db.session.rollback()
            log_content = log_buffer.getvalue()
            if log_content:
                upload_log_to_s3(log_content, prefix='teardown-logs/')
        except Exception as e:
            app.logger.warning(f"Teardown commit failed: {e}")
            app.db.session.rollback()
        finally:
            app.db.session.remove()

    # Inject FlaskUtilJs into templates
    # @app.context_processor
    # def inject_fujs():
    #     return dict(fujs=fujs)

    return app, socketio
