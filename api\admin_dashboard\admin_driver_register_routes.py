
from flask import Blueprint, request, jsonify
from flasgger import swag_from
from pydantic import ValidationError
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from utils.response_utils import standard_response
from schemas.admin_dashboard.driver_register_schema import RegisterDriverListPayload
from services.admin_dashboard.admin_driver_register_service import register_driver_list_service
from flask_jwt_extended import jwt_required
from flask import current_app as app

admin_driver_register = Blueprint('admin_driver_register', __name__)

@admin_driver_register.route('/api/admin/register_driver_list', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/register_driver_list.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def search_drivers_for_register_list():
    """
    Fetch the list of newly registered drivers based on filter criteria.

    This endpoint retrieves drivers who registered within a specified timestamp range
    and/or regions. It accepts data in form-data format and requires authentication and access control.

    Args:
        form-data:
            timestamp_gt (str, optional): Start of the registration timestamp range (exclusive).
            timestamp_lt (str, optional): End of the registration timestamp range (exclusive).
            regions (str, optional): Comma-separated list of region names to filter by.

    Headers:
        X-Timezone (str, optional): Client's timezone (default is 'Asia/Kolkata').

    Returns:
        JSON: A standardized JSON response containing:
            - success (int): 1 if successful, -1 or -99 on failure.
            - message (str): Status message.
            - response_status (str): 'success' or 'error'.
            - data (list or dict): List of matched driver records or error info.
            - status (int): HTTP status code (e.g. 200, 422, 500).
    """
    try:
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        payload = RegisterDriverListPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(success=-1, message="Invalid input", response_status="error",
                                         data=error_details,status=422)), 422
    try:
        results = register_driver_list_service(payload.dict(),tz)
        return jsonify(standard_response(success=results.get("success"), message=results.get("message"), response_status=results.get("status"),
                                         data=results.get("data"),status=results.get("status_code"))), results.get("status_code")
    except Exception as e:
        app.logger.error(f"[search_drivers_for_register_list] Unexpected error: {str(e)}")
        return jsonify(standard_response(success=-99 , 
                                         message="Failed to fetch drivers",
                                         response_status="error",
                                         data={"error": str(e)},
                                         status=500)), 500