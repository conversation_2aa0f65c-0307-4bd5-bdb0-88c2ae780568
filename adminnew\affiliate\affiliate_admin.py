from redis_config import redis_client,execute_with_fallback, is_redis_available
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt_identity,get_jwt
)
from pymongo.errors import PyMongoError
from _utils import validate_fields,convert_to_local_time
from login_common import check_token_revoked, check_access
from ..params.params import Tabs,AffiliateMembersTabs
from sqlalchemy.exc import SQLAlchemyError
import json
from sqlalchemy import text, or_, and_, desc,update, select,literal_column
from sqlalchemy.orm import aliased
from models import Users,Bookings, Trip,BookDest,BookingAlloc,TripPricing,BookPricing,ScheduledReport,BookingCancelled,DriverInfo,Drivers
from affiliate_b2b.affiliate_models import AffiliateToken, AffBookingLogs,AffiliateDriverSearch,AffiliateCollections
from  affiliate_b2b.affiliate_wallet import GST<PERSON>tate<PERSON>he<PERSON>
from db_config import db,mdb
from affiliate_b2b.affiliate_models import Affiliate,AffiliateRepLogs,AffiliateTaxTrans, AffiliateRep,ClientGstDetails, DraftAffiliate, AffiliatePricingLogs, AffiliateLogs, AffiliateCustomLogs,AffiliateWalletLogs,AffiliatePriceMapping,AffiliateAddress,AffiliateSpoc,AddressSpoc,AddressLogs,SpocLogs
from affiliate_b2b.affiliate_b2b import fetch_affiliate_details
from affiliate_b2b.affiliate_booking import process_booking_cancel_charge
from _utils import complete, convert_ist_to_utc, convert_utc_to_ist,get_safe,upload_pic,upload_pic_base64,ist_to_gmt
from adminnew.login_admin import notify_all_clients
from socketio_app import live_update_to_channel
from login_common import add_token_to_blacklist
from booking_params import BookingParams,Regions
from flasgger import swag_from
import requests
from _utils_booking import fetch_affiliate_wallet_logs
from  flask import current_app as app
from datetime import datetime
from sqlalchemy.sql import func,case
import os
import pandas as pd
from price import Price
from _email import send_mail
from b2b import get_cancel_reason
import math

admin_affiliate = Blueprint('admin_affiliate', __name__)


def read_redis_data(key):
    data = execute_with_fallback('get', key)
    if data != None:
        return json.loads(data) if data else {}
    else:
        return {}
    
def delete_redis_key(key):
    result = execute_with_fallback('delete', key)
    if result is not None:
        # result = redis_client.delete(key)
        if result == 1:
            print(f"Key '{key}' successfully deleted from Redis.")
        else:
            print(f"Key '{key}' not found in Redis.")
    else:
        print("Redis client is not connected.")


def write_redis_data(key, data):
    execute_with_fallback('set', key, json.dumps(data))

def update_redis_data(key, data):
    print("Redis data", data)
    write_redis_data(key, data)

def remove_all_tokens_to_avoid_missuse(user_id):
    filter_date = datetime.utcnow() - timedelta(days=10)
    user_tokens = AffiliateToken.query.filter(
        AffiliateToken.rep_id == user_id,
        AffiliateToken.timestamp > filter_date
    ).all()
    for token in user_tokens:
        add_token_to_blacklist(token.refresh, token.expiry)

# Function to add a new client and update the slaves of all masters in the hierarchy
def add_new_client(new_client):
    # Initialize the master client ID
    current_master_id = new_client["master"]
    new_client_name = new_client["form_details"]["client_name"]

    # Traverse the hierarchy to update each master's slaves
    while current_master_id != -1:
        # Retrieve the master client name from the database
        current_master_name = db.session.query(Affiliate.client_name).filter(Affiliate.id == current_master_id).scalar()

        # Retrieve the master client data from Redis
        master_data = read_redis_data(f'affiliate_{current_master_name}')

        if master_data:
            # Append the new client name to the master's slaves if not already present
            if new_client_name not in master_data["slaves"]:
                master_data["slaves"].append(new_client_name)
                # Update the master's data back to Redis
                write_redis_data(f'affiliate_{current_master_name}', master_data)

            # Move up the hierarchy to the next master
            current_master_id = master_data.get("master")
        else:
            break

def update_existing_client(updated_client):
    # Initialize the current master client ID for the updated client
    current_master_id = updated_client["master"]
    updated_client_name = updated_client["form_details"]["client_name"]

    # Traverse the hierarchy to update each master's slaves as needed
    while current_master_id != -1:
        # Retrieve the current master's client name from the database
        current_master_name = db.session.query(Affiliate.client_name).filter(Affiliate.id == current_master_id).scalar()

        # Retrieve the current master client data from Redis
        master_data = read_redis_data(f'affiliate_{current_master_name}')

        if master_data:
            # Check if the updated client name is in the master's slaves
            if updated_client_name in master_data["slaves"]:
                # If the client name is already present, we might update other details if needed
                # (for example, removing the client name if they are no longer a slave)
                # Alternatively, if no removal is needed, we skip further checks here.
                pass
            else:
                # If the client name is not present, add it to the master's slaves
                master_data["slaves"].append(updated_client_name)
                print(f"Adding {updated_client_name} to {current_master_name}'s slaves in Redis.")

            # Update the master's data in Redis
            write_redis_data(f'affiliate_{current_master_name}', master_data)

            # Move up the hierarchy to the next master
            current_master_id = master_data.get("master")
        else:
            # If master data is not found, exit the loop
            break


def generate_trip_type_data(data,redis_data, n):
    # Base structure with fixed trip type data

    # Generate dynamic trip type entries according to n
    for i in range(1, n + 1):
        trip_key = f"trip_type_{i}"

        # Adding trip type details to the dictionary
        redis_data["trip_type"].update({
            trip_key: {
                "type": "text",
                "placeholder": "Enter trip type name",
                "label": f"Trip type {i}",
                "value": data.get(trip_key, "")
            },
            f"select_type_{i}": {
                "type": "dropdown",
                "placeholder": "",
                "label": f"Select type {i}",
                "value": ["one way", "round"]
            },
            f"{trip_key}_start_image": {
                "type": "image",
                "placeholder": "Upload starting image",
                "label": f"Starting Image for Trip Type {i}",
                "value": data.get(f"{trip_key}_start_image", "")
            },
            f"{trip_key}_stop_image": {
                "type": "image",
                "placeholder": "Upload stopping image",
                "label": f"Stopping Image for Trip Type {i}",
                "value": data.get(f"{trip_key}_stop_image", "")
            }
        })

    return redis_data



def remove_affiliate_from_slaves_mysql(affiliate_id):
    """
    Remove the affiliate ID from all masters' slaves array in MySQL.
    """
    if not isinstance(affiliate_id, int):
        raise ValueError("Invalid input: affiliate_id must be an integer.")
    try:
        remove_affiliate_from_slaves_query = text("""
            UPDATE affiliate
            SET aff_slave = JSON_SET(
                aff_slave,
                '$.slaves',
                IFNULL(
                    (
                        SELECT JSON_ARRAYAGG(value)
                        FROM JSON_TABLE(
                            aff_slave->'$.slaves',
                            "$[*]" COLUMNS (
                                value INT PATH "$"
                            )
                        ) AS jt
                        WHERE value <> :value_to_exclude
                    ),
                    JSON_ARRAY()  -- default to an empty JSON array if result is NULL
                )
            )
            WHERE JSON_CONTAINS(aff_slave->'$.slaves', CAST(:value_to_exclude AS JSON));
        """)
        db.session.execute(remove_affiliate_from_slaves_query, {'value_to_exclude': affiliate_id})
        db.session.commit()
        print(f"Affiliate {affiliate_id} removed from all masters' slaves in MySQL.")
    except Exception as e:
        db.session.rollback()
        raise Exception(f"Error removing affiliate from slaves in MySQL: {e}")


def remove_affiliate_from_slaves_redis(client_name):
    """
    Remove the given client_name from the 'slaves' array of its masters in Redis.
    The function traverses up the hierarchy using each master’s 'master' field.
    """
    try:
        # Look up the affiliate record in the database to get its master id.
        new_client = db.session.query(Affiliate).filter(Affiliate.client_name == client_name).first()
        if not new_client:
            print(f"Client {client_name} not found in the database.")
            return

        current_master_id = new_client.master  # starting master for the client

        # Traverse the hierarchy until no master remains (assuming -1 indicates end of chain)
        while current_master_id != -1:
            # Retrieve the master's client name from the database
            current_master_name = (
                db.session.query(Affiliate.client_name)
                .filter(Affiliate.id == current_master_id)
                .scalar()
            )
            if not current_master_name:
                print(f"Master with ID {current_master_id} not found in the database.")
                break

            # Retrieve the master's data from Redis
            redis_key = f'affiliate_{current_master_name}'
            master_data = read_redis_data(redis_key)

            if master_data:
                # Check if the client is in the master's slaves list and remove it if present
                if "slaves" in master_data and client_name in master_data["slaves"]:
                    master_data["slaves"].remove(client_name)
                    write_redis_data(redis_key, master_data)
                    print(f"Removed {client_name} from {redis_key}")
                # Move up to the next master in the hierarchy using the master's own 'master' field
                current_master_id = master_data.get("master", -1)
            else:
                print(f"No Redis data found for key {redis_key}.Continuing traversal.")

        print(f"Affiliate {client_name} has been removed from the slaves lists of all relevant masters.")
    except Exception as e:
        raise Exception(f"Error removing affiliate from Redis: {e}")


def create_and_commit_log_data(section,changes_made, log_id, changed_by, custom, client_name):
    db.session.add(AffiliateLogs(datetime.now(),changed_by,section,changes_made,f"affiliate_{custom}_logs{log_id}",f"affiliate_{custom}_logs{log_id}",log_id,client_name))
    db.session.commit()

def log_affiliate_changes(changed_by, affMainChanges, affMainRoundChanges,
                           oneway_id =-1, round_id =-1 , trip_id=-1,affiliate_pricing=-1,
                             pricingCustChargeChanges="",pricingCustBaseChanges="",pricingDriverBaseChanges="",
                             pricingDriverChargeChanges="",pricingRoundCustBaseChanges="",
                             pricingRoundDriverBaseChanges="",pricingRoundCustChargeChanges="",
                             pricingRoundDriverChargeChanges="", pricingOutstationCustChargeChanges="",
                             pricingOutstationDriverChargeChanges="",pricingOutstationCustBaseChanges="",
                             pricingOutstationDriverBaseChanges="",cancelChargeChanges="",cancelChargeStaticChanges="", client_name=""):

    if oneway_id !=-1:
        log_data = create_and_commit_log_data("Form Fields (Oneway)", "Custom Field",oneway_id, changed_by,"custom",client_name)

    if round_id !=-1:
        log_data = create_and_commit_log_data("Form Fields (Round)", "Custom Field", round_id, changed_by,"custom",client_name)

    if trip_id !=-1:
        log_data = create_and_commit_log_data("Trip Details", "Trip Types", trip_id, changed_by,"custom",client_name)

    if pricingCustBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Oneway)","Customer Base Breakup", affiliate_pricing , changed_by,"pricing",client_name)

    if pricingRoundCustBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Roundtrip)", "Customer Base Breakup", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingCustChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Oneway)","Customer Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingRoundCustChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Roundtrip)","Customer Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingDriverBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Oneway)","Driver Base Breakup", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingRoundDriverBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Roundtrip)","Driver Base Breakup", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingDriverChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Oneway)","Driver Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)

    if pricingRoundDriverChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Roundtrip)","Driver Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)

    if  pricingOutstationCustChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Outstation)","Customer Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)
    if pricingOutstationDriverChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Outstation)","Driver Additional Charges", affiliate_pricing , changed_by,"pricing", client_name)
    if pricingOutstationCustBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Outstation)","Customer Base Breakup", affiliate_pricing , changed_by,"pricing", client_name)

    if  pricingOutstationDriverBaseChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Outstation)","Driver Base Breakup", affiliate_pricing , changed_by,"pricing", client_name)

    if cancelChargeChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Cancellation)","Cancel Charges", affiliate_pricing , changed_by,"pricing", client_name)

    if cancelChargeStaticChanges == "yes":
        log_data = create_and_commit_log_data("Pricing (Cancellation)","Cancel charges Static", affiliate_pricing , changed_by,"pricing", client_name)

    sections = [
        {"name": "Form Fields (Oneway)", "changes": affMainChanges},
        {"name": "Form Fields (Round)", "changes": affMainRoundChanges},
    ]

    for section in sections:
        section_name = section["name"]
        changes = section["changes"]

        for change in changes:
            field = change["field"]
            old_value = change.get("oldValue")
            new_value = change.get("newValue")
            changes_made = field
            if field == "selected_group":
                continue
            if field in ["clientDisplayName","selected_group_name"]:
                section_name = "Client Details"

            if field in ["trip_type_placeholder", "trip_type_label", "trip_types"]:
                section_name = "Trip Details"

            # if field == "trip_types":
            #     old_value = "affiliate_custom_logs"+str(trip_id)
            #     new_value = "affiliate_custom_logs"+str(trip_id)

            # Create and add the log
            log = AffiliateLogs(datetime.now(),changed_by,section_name,changes_made,str(old_value),str(new_value), affiliate_pricing, client_name)
            db.session.add(log)

    db.session.commit()


def get_slave_unique_regions(affiliate):
    # Get the list of slave ids from the existing affiliate
    slave_ids = affiliate.slave.get("slaves", [])
    print("slave_ids", slave_ids,flush=True)
    # Use a set to automatically remove duplicate regions
    unique_regions = set()

    for slave_id in slave_ids:
        # Query each slave affiliate by id
        slave_affiliate = Affiliate.query.filter_by(id=slave_id).first()
        if slave_affiliate and slave_affiliate.client_region:
            # Split the comma-separated string into individual region strings
            regions = [region.strip() for region in slave_affiliate.client_region.split(',') if region.strip()]
            # Add each region to the set
            unique_regions.update(regions)

    # Convert the set back to a list if needed
    unique_regions_list = list(unique_regions)
    print("List: ",unique_regions_list,flush=True)
    return unique_regions_list


def get_master_unique_regions(affiliate):

    unique_regions = set()
    master_affiliate = Affiliate.query.filter_by(id= affiliate.master).first()

    if master_affiliate and master_affiliate.client_region:
        # Split the comma-separated string into individual region strings
        regions = [region.strip() for region in master_affiliate.client_region.split(',') if region.strip()]
        # Add each region to the set
        unique_regions.update(regions)

    # Convert the set back to a list if needed
    unique_regions_list = list(unique_regions)
    print("List: ",unique_regions_list,flush=True)
    return unique_regions_list

@admin_affiliate.route('/api/admin/affiliate/create_affiliate', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/create_affiliate.yml')
def create_affiliate():
    admin = get_jwt_identity()
    data = request.get_json()
    #print("Json data", data)
    master = data.get("master", -1)
    client_name = (data["clientDetails"]["clientName"]).strip()
    wallet_threshold = data["clientDetails"]["wallet_threshold"]
    mapped_wallet_affiliate = data["clientDetails"]["mapped_wallet_affiliate"]
    cgst_percent = data["clientDetails"]["cgst_percent"]
    sgst_percent = data["clientDetails"]["sgst_percent"]
    gst_numbers = data["clientDetails"].get("gst_numbers", [])


    already_affiliate= db.session.query(Affiliate).filter(Affiliate.client_name== client_name).first()
    if already_affiliate:
        return jsonify({'success': -1, 'message': 'Affiliate with this client name already exists!'}),400

    # Create a new AffiliateSchema object with the received data
    logo=None
    client_display_logo=data.get("clientDetails",{}).get("clientDisplayLogo",{})
    if client_display_logo:
            if  isinstance(client_display_logo, str) and client_display_logo.startswith("data:"):
                logo = upload_pic_base64(client_display_logo,path="")
            else:
                logo = client_display_logo
    new_affiliate = Affiliate(client_name, data["clientDetails"]["clientDisplayName"], data["clientDetails"]["selectedCities"], master, admin,None, wallet_threshold = wallet_threshold,logo=logo)

    # Add the new affiliate record to the session and commit
    try:
        db.session.add(new_affiliate)
        db.session.flush()
        new_client_id = new_affiliate.id
        if mapped_wallet_affiliate == "0":
            new_affiliate.mapped_wallet_affiliate = new_client_id
        else:
            new_affiliate.mapped_wallet_affiliate = mapped_wallet_affiliate
        db.session.commit()
        print("affiliate added to rdbms")
    except Exception as e:
        db.session.rollback()  # Rollback in case of error
        print("Error", e)
        return jsonify({'success': -1, 'message': 'Failed to add affiliate to RDBMS','error': str(e)}), 500

    redis_data = {
        "master": master,
        "slaves": [],
        "wallet_threshold": wallet_threshold,
        "mapped_wallet_affiliate":  new_affiliate.mapped_wallet_affiliate,
        "form_field_oneway": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("affiliateIdEnabled", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("vehicleModelEnabled", "")
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("city_label", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("one_way", {}).get("custom_fields_one_way", [])
            ]
        },
        "form_field_round": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("affiliateIdEnabled", "")
            },
            "no_of_bookings":{
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("noOfBookingsEnabled", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_label", ""),
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_placeholder", ""),
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("city_label", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("vehicleModelEnabled", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("round", {}).get("custom_fields_round", [])
            ]
        },
        "form_details": {
            "client_name": client_name,
            "client_id": new_client_id,
            "client_display_name": data["clientDetails"]["clientDisplayName"],
            "select_group": ["Global", "Local"], # No check for other than global
            "city": data["clientDetails"]["selectedCities"],
            "client_logo":logo
        },
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "tripTypes": [
            {
                "trip_type_name": trip.get("trip_type_name", ""),
                "trip_type_category": trip.get("trip_type_category", ""),
                "tripIndex": trip.get("tripIndex", ""),
                "tripType": trip.get("tripType", ""),
                "startImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("startImages", [])
                ],
                "stopImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("stopImages", [])
                ]
            }
            for trip in data.get("clientDetails", {}).get("tripTypes", [])
        ]
    }
    cities_str = data["clientDetails"]["selectedCities"]
    # Split into a list and strip whitespace
    cities = [city.strip() for city in cities_str.split(",") if city.strip()]

    for city in cities:
        region = int(city)  # Convert city code to integer if needed
        if data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("base_breakup", {}):
            update_affiliate_price_mapping(new_client_id, region, trip_type=BookingParams.TYPE_ONEWAY, map_id=new_client_id, map_id_name=client_name)
        if data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"):
            update_affiliate_price_mapping(new_client_id, region, trip_type=BookingParams.TYPE_ROUNDTRIP, map_id=new_client_id, map_id_name=client_name)
        if data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"):
            update_affiliate_price_mapping(new_client_id, region, trip_type=BookingParams.TYPE_OUTSTATION, map_id=new_client_id, map_id_name=client_name)


    # Calculate total number of start images
    total_start_images = sum(len(trip.get("startImages", [])) for trip in redis_data["tripTypes"])

    # Calculate total number of stop images
    total_stop_images = sum(len(trip.get("stopImages", [])) for trip in redis_data["tripTypes"])

    redis_data["totalStartImages"] = total_start_images
    redis_data["totalStopImages"] = total_stop_images

    redis_key = f'affiliate_{client_name}'

    customerbaseBreakup = data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("base_breakup", {})

    customer_pricing_data = {
        "cgst" : cgst_percent,
        "sgst": sgst_percent,
        "gst_numbers": gst_numbers,
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in customerbaseBreakup
            ],
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "default_duration": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            },
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
                "outstation_charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationChargeType"),
                "outstation_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationRate")
            },
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        }
    }


    driverbaseBreakup = data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("base_breakup", {})

    driver_pricing_data = {
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in driverbaseBreakup
            ],
            "additional_charges": {
                "is_night_enabled":  data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "default_duration": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled":  data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        }
    }

    cancelCharges = data.get("pricing_cancellation", {}).get("cancelCharges", {})

    pricing_cancellation_data = {
    "cancel_charges": [
        {
            "hour_range": item.get("hourRange"),
            "customer_cancel": item.get("customerCancelCharge"),
            "driver_cancel": item.get("driverCancelCharge"),
            "both_cancel": {
                "customer": item.get("bothCancelCharge", {}).get("customer"),
                "driver": item.get("bothCancelCharge", {}).get("driver")
            }
        }
        for item in cancelCharges
    ],
    "cancel_charge_static": {
        "customer_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticCustomerCharge"),
        "driver_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticDriverCharge"),
        "both_cancel": {
            "customer": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("customer"),
            "driver": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("driver")
        }
    }
    }


    print("Redis done!")

    new_affiliate_mongo = {
        "affiliate_id": new_client_id,
        "client_name": client_name,
        "trip_type": redis_data["tripTypes"],
        "total_start_images": redis_data["totalStartImages"],
        "total_stop_images": redis_data["totalStopImages"],
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "form_field_oneway": redis_data['form_field_oneway'],
        "form_field_round": redis_data['form_field_round'],
        "sgst": customer_pricing_data['sgst'],
        "cgst": customer_pricing_data['cgst'],
        "gst_numbers": customer_pricing_data['gst_numbers'],
        "customer_pricing_oneway": customer_pricing_data['oneway'],
        "customer_pricing_roundtrip": customer_pricing_data['roundtrip'],
        "customer_pricing_outstationtrip": customer_pricing_data['outstationtrip'],
        "driver_pricing_oneway": driver_pricing_data['oneway'],
        "driver_pricing_roundtrip": driver_pricing_data['roundtrip'],
        "driver_pricing_outstationtrip": driver_pricing_data["outstationtrip"],
        "pricing_cancellation_data": pricing_cancellation_data,
        "created_at": datetime.now().strftime("%d %b, %y, %H:%M:%S")
    }

    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_details.insert_one(new_affiliate_mongo)
        print("New affiliate added successfully.")
    except PyMongoError as mongo_error:
        print(f"MongoDB error: {mongo_error}")
        try:
            # Rollback SQLAlchemy changes to maintain consistency
            db.session.delete(new_affiliate)
            db.session.rollback()
        except SQLAlchemyError as sql_error:
            print(f"SQLAlchemy rollback error: {sql_error}")
            return jsonify({
                'success': -1,
                'message': 'Failed to add draft affiliate to MongoDB, and rollback failed',
                'mongo_error': str(mongo_error),
                'sql_error': str(sql_error),
            }), 500

    if master != -1:
        try:
            add_new_client(redis_data)  # Add new client in Redis
            print("New client added successfully to Redis.")
        except Exception as e:
            print(f"Error adding new client to Redis: {e}")
            return jsonify({'success': -1, 'message': 'Failed to update hierarchy in Redis','error': str(e)}), 500

        if not isinstance(master, int) or not isinstance(new_client_id, int):
            raise ValueError("Invalid input: master_id and new_client_id must be integers.")

        # Recursive CTE query to find all masters up the hierarchy
        recursive_cte_query = text("""
            WITH RECURSIVE master_hierarchy AS (
                SELECT aff_id, aff_master
                FROM affiliate
                WHERE aff_id = :master_id
                UNION ALL
                SELECT c.aff_id, c.aff_master
                FROM affiliate c
                INNER JOIN master_hierarchy mh ON c.aff_id = mh.aff_master
            )
            UPDATE affiliate
            SET aff_slave = JSON_SET(
                aff_slave,
                '$.slaves',
                JSON_ARRAY_APPEND(
                    COALESCE(JSON_EXTRACT(aff_slave, '$.slaves'), JSON_ARRAY()),
                    '$',
                    :new_client_id
                )
            )
            WHERE aff_id IN (SELECT aff_id FROM master_hierarchy);
        """)
        try:
            # Execute the recursive CTE query
            db.session.execute(recursive_cte_query, {'master_id': master, 'new_client_id': new_client_id})
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print("Error executing recursive CTE query:", e)
            return jsonify({'success': -1, 'message': 'Failed to update affiliate hierarchy in RDBMS','error': str(e)}), 500
    data= {
            'id': new_client_id,
            'client_name': client_name,
            'client_regions':data["clientDetails"]["selectedCities"]
    }
    live_update_to_channel(data, room_name='affiliate', type='affiliate', region=0, channel='new_affiliate')

    #delete draft if it exists

    draft = DraftAffiliate.query.filter_by(client_name=client_name).first()
    if draft and not draft.is_favourite:
        delete_draft = {
            "draft_id": draft.id,
        }
        db.session.delete(draft)
        db.session.commit()

        #also delete from mongodb
        try:
            delete_result = AffiliateCollections.draft_affiliates.delete_one({"mysql_id": draft.id})
            if delete_result.deleted_count == 0:
                print("Draft not found in MongoDB")

            print("Draft deleted from MongoDB")
        except Exception as e:
            print("Error while deleting from MongoDB:", e)

        live_update_to_channel(delete_draft, room_name='affiliate', type='affiliate', region=0, channel='delete_draft')
     #add to redis
    write_redis_data(redis_key, redis_data)
    write_redis_data(f'{redis_key}_pricing_customer', customer_pricing_data)
    write_redis_data(f'{redis_key}_pricing_driver', driver_pricing_data)
    write_redis_data(f'{redis_key}_pricing_cancellation', pricing_cancellation_data)
    return jsonify({'success': 1, 'message': 'Affiliate added and stored in Redis successfully'}), 201

def update_affiliate_pricing(existing_affiliate):
    # Parse the `slave` field to extract the list of slave affiliates
    try:
        slave_data = json.loads(existing_affiliate.slave) if isinstance(existing_affiliate.slave, str) else existing_affiliate.slave
        slave_affiliate_ids = slave_data.get("slaves", []) if slave_data else []
    except Exception as e:
        print(f"Error parsing slave data: {e}")
        slave_affiliate_ids = []

    # Include the existing affiliate itself in the list
    all_affiliates_to_update = slave_affiliate_ids + [existing_affiliate.id]

    # print("all_affiliates_to_update",all_affiliates_to_update)
    AffiliateAlias = aliased(Affiliate)
    stmt = (
        update(AffiliatePriceMapping)
        .where(AffiliatePriceMapping.mapped_price_affiliate.in_(all_affiliates_to_update))
        .values(
            mapped_price_affiliate=AffiliatePriceMapping.aff_id,
            mapped_price_affiliate_name=(
                db.session.query(AffiliateAlias.client_name)
                .filter(AffiliateAlias.id == AffiliatePriceMapping.aff_id)
                .scalar_subquery()
            )
        )
    )

    # Execute the update in a single query
    db.session.execute(stmt)
    # Bulk update: Set `mapped_price_affiliate` to `aff_id` (self-mapping)
        # db.session.execute(
        #     update(AffiliatePriceMapping)
        #     .where(AffiliatePriceMapping.mapped_price_affiliate.in_(all_affiliates_to_update))
        #     .values(mapped_price_affiliate=AffiliatePriceMapping.aff_id)
        # )

        # # Fetch updated mappings to update `mapped_price_affiliate_name`
        # mappings_to_update = db.session.query(AffiliatePriceMapping).filter(
        #     AffiliatePriceMapping.mapped_price_affiliate.in_(all_affiliates_to_update)
        # ).all()

        # print("mappings_to_update",mappings_to_update)

        # for mapping in mappings_to_update:
        #     prev_affiliate = db.session.query(Affiliate).filter_by(id=mapping.aff_id).first()
        #     print("prev_affiliate",prev_affiliate.client_name)
        #     if prev_affiliate:
        #         mapping.mapped_price_affiliate_name = prev_affiliate.client_name  # Update name

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print(f"Error committing changes: {e}")


@admin_affiliate.route('/api/admin/affiliate/update_affiliate', methods=['PUT'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/update_affiliate.yml')
def update_affiliate():
    admin_id = get_jwt_identity()
    data = request.get_json()
    client_name = (data["clientDetails"]["clientName"]).strip()
    client_updated_name = (data["clientDetails"]["clientUpdatedName"]).strip()
    redis_key = f'affiliate_{data["clientDetails"]["clientName"]}'
    existing_affiliate = Affiliate.query.filter_by(client_name=client_name).first()
    wallet_threshold = data["clientDetails"]["wallet_threshold"]
    cgst_percent = data["clientDetails"]["cgst_percent"]
    sgst_percent = data["clientDetails"]["sgst_percent"]
    client_display_logo=data.get("clientDetails",{}).get("clientDisplayLogo",{})
    mapped_wallet_affiliate = data["clientDetails"].get("mapped_wallet_affiliate")

    if not existing_affiliate:
        return jsonify({'success': -1, 'message': 'Affiliate not found', 'error': 'No affiliate found with the provided client name'}), 404
    affiliate_id = db.session.query(Affiliate.id).filter(Affiliate.client_name== client_name).scalar()

    slave_regions = get_slave_unique_regions(existing_affiliate)

    selected_cities = [city.strip() for city in data["clientDetails"]["selectedCities"].split(',') if city.strip()]
    existing_regions = [region.strip() for region in existing_affiliate.client_region.split(',') if region.strip()]

    # Determine regions present in existing_affiliate but not in selectedCities.
    missing_regions = set(existing_regions) - set(selected_cities)

    print("missing regions:", missing_regions)
    print("selected cities:", selected_cities)
    print("existing regions:", existing_regions)
    print("slave regions:", slave_regions)

    # Check if each missing region is present in unique_regions_list.
    for region in missing_regions:
        if slave_regions and region in slave_regions:
                return jsonify({'success': -1, 'message': 'Cannot remove region as it is used by a slave affiliate'}), 500

    if data.get("master") != -1:
        #check if this master is not one of its slaves
        slave_ids = existing_affiliate.slave.get("slaves", [])
        if data.get("master") in slave_ids:
            return jsonify({'success': -1, 'message': 'Cannot set this master as its already its slave'}), 500
        #any new extra region in a slave must be used by its master
        extra_regions = set(selected_cities) - set(existing_regions)
        master_regions = get_master_unique_regions(existing_affiliate)
        for region in extra_regions:
            if master_regions and region not in master_regions:
                return jsonify({'success': -1, 'message': 'Cannot add region as it is used by its master affiliate'}), 500

    should_recursive_cte= False

    if not existing_affiliate.master == data["master"]:
        should_recursive_cte = True
        try:
            # Step 1: Remove the affiliate ID from all masters' slaves array in MySQL
            remove_affiliate_from_slaves_mysql(affiliate_id) #level 4
            update_affiliate_pricing(existing_affiliate)

        except Exception as e:
            db.session.rollback()
            print(f"An error occurred: {e}")
            return jsonify({'success': -1, 'message': 'error occurred in resetting affiliate in db'}), 500

        print("Affiliate removed from slaves in RDBMS",flush=True)
        # Step 2: Update Redis - Remove affiliate ID from masters' slaves arrays
        remove_affiliate_from_slaves_redis(client_name)
        print("Affiliate removed from slaves in Redis",flush=True)

    # Update fields of the existing affiliate with new data
    try:
        existing_affiliate.client_name = data["clientDetails"]["clientName"]
        existing_affiliate.client_region = data["clientDetails"]["selectedCities"]
        if int(data.get("master")) != int(existing_affiliate.master):
              existing_affiliate.mapped_wallet_affiliate = existing_affiliate.id
        else:
            existing_affiliate.mapped_wallet_affiliate = mapped_wallet_affiliate if mapped_wallet_affiliate else existing_affiliate.id #empty string
        existing_affiliate.master = data.get("master", existing_affiliate.master)
        existing_affiliate.display_name = data["clientDetails"]["clientDisplayName"]

        logo=None
        if client_display_logo:
            if  isinstance(client_display_logo, str) and client_display_logo.startswith("data:"):
                logo = upload_pic_base64(client_display_logo,path="")
            else:
                logo = client_display_logo
            existing_affiliate.logo = logo
        existing_affiliate.wallet_threshold = wallet_threshold

        if data["clientDetails"]["selectedCities"]:
            socket_data= {
            'id': affiliate_id,
            'client_name': client_name,
            'client_regions':data["clientDetails"]["selectedCities"]
            }
            live_update_to_channel(socket_data, room_name='affiliate', type='affiliate', region=0, channel='affiliate_region_update')

        # Commit changes to the database
        db.session.commit()
        print("Affiliate updated in RDBMS")

    except Exception as e:
        db.session.rollback()
        print("Error updating affiliate:", e)
        return jsonify({'success': -1, 'message': 'Failed to update affiliate in RDBMS', 'error': str(e)}), 500

    print("logs done",flush=True)

    initial_combined_pricing={
        "customer": read_redis_data(f'{redis_key}_pricing_customer'),
        "driver": read_redis_data(f'{redis_key}_pricing_driver'),
        'cancellation': read_redis_data(f'{redis_key}_pricing_cancellation')
    }

    initial_redis_data = read_redis_data(redis_key)

    initial_custom_oneway_data = initial_redis_data["form_field_oneway"]["add_custom_fields"] if initial_redis_data else []
    initial_custom_round_data = initial_redis_data["form_field_round"]["add_custom_fields"]  if initial_redis_data else []

    initial_trip_data = initial_redis_data["tripTypes"] if initial_redis_data else []

    print("entering redis",flush=True)
    # Prepare updated data for Redis
    redis_data = {
        "master": data.get("master"),
        "slaves": [],
        "wallet_threshold": wallet_threshold,
        "mapped_wallet_affiliate": existing_affiliate.mapped_wallet_affiliate,
        "form_field_oneway": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("affiliateIdEnabled", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("vehicleModelEnabled", "")
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("city_label", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("one_way", {}).get("custom_fields_one_way", [])
            ]
        },
        "form_field_round": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("affiliateIdEnabled", "")
            },
            "no_of_bookings":{
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("noOfBookingsEnabled", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_label", ""),
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_placeholder", ""),
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("city_label", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("vehicleModelEnabled", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("round", {}).get("custom_fields_round", [])
            ]
        },
        "form_details": {
            "client_name": client_name,
            "client_id": affiliate_id,
            "client_display_name": data["clientDetails"]["clientDisplayName"],
            "select_group": ["Global", "Local"], # No check for other than global
            "city": data["clientDetails"]["selectedCities"],
            "client_logo":logo
        },
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "tripTypes": [
            {
                "trip_type_name": trip.get("trip_type_name", ""),
                "trip_type_category": trip.get("trip_type_category", ""),
                "tripIndex": trip.get("tripIndex", ""),
                "tripType": trip.get("tripType", ""),
                "startImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("startImages", [])
                ],
                "stopImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("stopImages", [])
                ]
            }
            for trip in data.get("clientDetails", {}).get("tripTypes", [])
        ]
    }

    # Update the affiliate data in Redis

    total_start_images = sum(len(trip.get("startImages", [])) for trip in redis_data["tripTypes"])

    # Calculate total number of stop images
    total_stop_images = sum(len(trip.get("stopImages", [])) for trip in redis_data["tripTypes"])

    redis_data["totalStartImages"] = total_start_images
    redis_data["totalStopImages"] = total_stop_images

    redis_key = f'affiliate_{data["clientDetails"]["clientName"]}'
    write_redis_data(redis_key, redis_data)

    customerbaseBreakup = data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("base_breakup", {})

    customer_pricing_data = {
        "cgst": cgst_percent,
        "sgst": sgst_percent,
        "wallet_threshold":wallet_threshold,
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in customerbaseBreakup
            ],
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "default_duration": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceChEnabled"),
                # "price_mapping": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("price_mapping")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceChEnabled"),
                # "price_mapping": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("price_mapping")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
                "outstation_charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationChargeType"),
                "outstation_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationRate")
            },
            "additional_charges": {
                "is_night_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceChEnabled"),
                # "price_mapping": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("price_mapping")
            }
        }
    }
    cities_str = data["clientDetails"]["selectedCities"]
    cities = [city.strip() for city in cities_str.split(",") if city.strip()]
    if customerbaseBreakup:
        # Get the outstation price mapping list (if it exists; default to an empty list)
        one_way_mapping_list = data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("price_mapping")

        # Build a set of city keys already present in the mapping list.
        existing_city_keys = {item.get("city_key") for item in one_way_mapping_list} if one_way_mapping_list else set()

        # For each city in the provided list, if its key is not in the mapping, update the price mapping.
        for city in cities:
            if city not in existing_city_keys:
                region = int(city)  # Convert city code to integer if needed
                update_affiliate_price_mapping(
                    existing_affiliate.id,
                    region,
                    trip_type=BookingParams.TYPE_ONEWAY,
                    map_id=existing_affiliate.id,
                    map_id_name=client_name
                )
        update_all_affiliate_price_mappings(affiliate_id, one_way_mapping_list, trip_type=BookingParams.TYPE_ONEWAY)
    if data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"):
        round_mapping_list = data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("price_mapping")
        existing_city_keys = {item.get("city_key") for item in round_mapping_list} if round_mapping_list else set()
        # For each city in the provided list, if its key is not in the mapping, update the price mapping.
        for city in cities:
            if city not in existing_city_keys:
                region = int(city)  # Convert city code to integer if needed
                update_affiliate_price_mapping(
                    existing_affiliate.id,
                    region,
                    trip_type=BookingParams.TYPE_ROUNDTRIP,
                    map_id=existing_affiliate.id,
                    map_id_name=client_name
                )
        update_all_affiliate_price_mappings(affiliate_id, round_mapping_list, trip_type=BookingParams.TYPE_ROUNDTRIP)
    if data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"):
        outstation_mapping_list = data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("price_mapping")
        existing_city_keys = {item.get("city_key") for item in outstation_mapping_list} if outstation_mapping_list else set()
        # For each city in the provided list, if its key is not in the mapping, update the price mapping.
        for city in cities:
            if city not in existing_city_keys:
                region = int(city)  # Convert city code to integer if needed
                update_affiliate_price_mapping(
                    existing_affiliate.id,
                    region,
                    trip_type=BookingParams.TYPE_OUTSTATION,
                    map_id=existing_affiliate.id,
                    map_id_name=client_name
                )
        update_all_affiliate_price_mappings(affiliate_id, outstation_mapping_list, trip_type=BookingParams.TYPE_OUTSTATION)

    write_redis_data(f'{redis_key}_pricing_customer', customer_pricing_data)

    driverbaseBreakup = data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("base_breakup", {})


    driver_pricing_data = {
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in driverbaseBreakup
            ],
            "additional_charges": {
                "is_night_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "default_duration": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_night_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime")),
                "end_night_time": convert_ist_to_utc(data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime")),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        }
    }

    cancelCharges = data.get("pricing_cancellation", {}).get("cancelCharges", {})

    write_redis_data(f'{redis_key}_pricing_driver', driver_pricing_data)

    pricing_cancellation_data = {
        "cancel_charges": [
            {
                "hour_range": item.get("hourRange"),
                "customer_cancel": item.get("customerCancelCharge"),
                "driver_cancel": item.get("driverCancelCharge"),
                "both_cancel": {
                    "customer": item.get("bothCancelCharge", {}).get("customer"),
                    "driver": item.get("bothCancelCharge", {}).get("driver")
                }
            }
            for item in cancelCharges
        ],
        "cancel_charge_static": {
            "customer_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticCustomerCharge"),
            "driver_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticDriverCharge"),
            "both_cancel": {
                "customer": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("customer"),
                "driver": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("driver")
            }
        }
    }

    write_redis_data(f'{redis_key}_pricing_cancellation', pricing_cancellation_data)

    #push data into pricing logs table
    updated_combined_pricing ={
        "customer": customer_pricing_data,
        "driver": driver_pricing_data,
        "cancellation": pricing_cancellation_data
    }

    updated_custom_oneway_data = redis_data["form_field_oneway"]["add_custom_fields"]
    updated_custom_round_data = redis_data["form_field_round"]["add_custom_fields"]

    updated_trip_data = redis_data["tripTypes"]

    affiliate_pricing = -1
    trip_id =-1
    round_id =-1
    oneway_id = -1

    if not initial_custom_oneway_data == updated_custom_oneway_data :
        custom_log = AffiliateCustomLogs(client_name= client_name,category = AffiliateCustomLogs.CATEGORY_CUSTOM_FORM_FIELD_ONEWAY,changed_from=initial_custom_oneway_data,changed_to=updated_custom_oneway_data)
        try:
            db.session.add(custom_log)
            db.session.commit()
            oneway_id = custom_log.log_id
        except Exception as e:
            db.session.rollback()  # Rollback in case of error
            print("Error", e)
            return jsonify({'success': -1, 'message': 'Failed to add oneway data into logs','error': str(e)}), 500


    if not initial_custom_round_data == updated_custom_round_data :
        custom_log = AffiliateCustomLogs(client_name= client_name,category = AffiliateCustomLogs.CATEGORY_CUSTOM_FORM_FIELD_ROUND,changed_from=initial_custom_round_data,changed_to=updated_custom_round_data)
        try:
            db.session.add(custom_log)
            db.session.commit()
            round_id = custom_log.log_id
        except Exception as e:
            db.session.rollback()  # Rollback in case of error
            print("Error", e)
            return jsonify({'success': -1, 'message': 'Failed to add oneway data into logs','error': str(e)}), 500
    if initial_trip_data != updated_trip_data:
        if data["changedFields"] ["affCustImagesChanges"] == "yes":
            category = AffiliateCustomLogs.CATEGORY_TRIP_IMAGES
        else:
            category = AffiliateCustomLogs.CATEGORY_TRIP_DATA

        custom_log = AffiliateCustomLogs(client_name= client_name,category = category,changed_from=initial_trip_data,changed_to=updated_trip_data)
        try:
            db.session.add(custom_log)
            db.session.commit()
            trip_id = custom_log.log_id
        except Exception as e:
            db.session.rollback()  # Rollback in case of error
            print("Error", e)
            return jsonify({'success': -1, 'message': 'Failed to dump pricing data into logs','error': str(e)}), 500

    if not initial_combined_pricing == updated_combined_pricing:
        pricing_log = AffiliatePricingLogs(client_name= client_name,changed_from=initial_combined_pricing,changed_to=updated_combined_pricing)
        try:
            db.session.add(pricing_log)
            db.session.commit()
            affiliate_pricing = pricing_log.log_id
        except Exception as e:
            db.session.rollback()  # Rollback in case of error
            print("Error", e)
            return jsonify({'success': -1, 'message': 'Failed to dump pricing data into logs','error': str(e)}), 500


    affiliate_oneway_changes = data["changedFields"] ["affMainChanges"]
    affiliate_round_changes = data["changedFields"] ["affMainRoundChanges"]
    pricing_cust_charge_changes = data["changedFields"]["pricingCustChargeChanges"]
    pricing_cust_base_changes = data["changedFields"]["pricingCustBaseChanges"]
    pricing_driver_base_changes = data["changedFields"]["pricingDriverBaseChanges"]
    pricing_driver_charge_changes = data["changedFields"]["pricingDriverChargeChanges"]
    pricing_round_cust_base_changes = data["changedFields"]["pricingRoundCustBaseChanges"]
    pricing_round_driver_base_changes = data["changedFields"]["pricingRoundDriverBaseChanges"]
    pricing_round_cust_charge_changes = data["changedFields"]["pricingRoundCustChargeChanges"]
    pricing_round_driver_charge_changes = data["changedFields"]["pricingRoundDriverChargeChanges"]
    pricing_outstation_cust_charge_changes = data["changedFields"]["pricingOutstationCustChargeChanges"]
    pricing_outstation_driver_charge_changes = data["changedFields"]["pricingOutstationDriverChargeChanges"]
    pricing_outstation_cust_base_changes = data["changedFields"]["pricingOutstationCustBaseChanges"]
    pricing_outstation_driver_base_changes = data["changedFields"]["pricingOutstationDriverBaseChanges"]
    cancel_charge_changes = data["changedFields"]["cancelChargeChanges"]
    cancel_charge_static_changes = data["changedFields"]["cancelChargeStaticChanges"]

    creator = db.session.query(Users).filter(Users.id == admin_id).first()

    log_affiliate_changes(creator.get_name(),affMainChanges=affiliate_oneway_changes,
        affMainRoundChanges=affiliate_round_changes, oneway_id=oneway_id, round_id= round_id, trip_id= trip_id, affiliate_pricing=affiliate_pricing,
        pricingCustChargeChanges=pricing_cust_charge_changes, pricingCustBaseChanges=pricing_cust_base_changes,
        pricingDriverChargeChanges=pricing_driver_charge_changes,pricingDriverBaseChanges=pricing_driver_base_changes,
        pricingRoundCustChargeChanges=pricing_round_cust_charge_changes, pricingRoundCustBaseChanges=pricing_round_cust_base_changes,
        pricingRoundDriverChargeChanges= pricing_round_driver_charge_changes, pricingRoundDriverBaseChanges=pricing_round_driver_base_changes,
        pricingOutstationCustChargeChanges = pricing_outstation_cust_charge_changes,pricingOutstationDriverChargeChanges= pricing_outstation_driver_charge_changes,
        pricingOutstationCustBaseChanges = pricing_outstation_cust_base_changes,pricingOutstationDriverBaseChanges = pricing_outstation_driver_base_changes,
        cancelChargeChanges=cancel_charge_changes, cancelChargeStaticChanges= cancel_charge_static_changes, client_name = client_name
    )

    existing_mongo_affiliate = AffiliateCollections.affiliates_details.find_one({"client_name": data["clientDetails"]["clientName"]})

    if not existing_mongo_affiliate:
        return jsonify({'success': -1, 'message': 'Affiliate not found in mongodb', 'error': 'No affiliate found with the provided client name'}), 404

    existing_mongo_affiliate_id = existing_mongo_affiliate["_id"]

    # Define the fields you want to update
    update_affiliate_mongo = {
        "affiliate_id": affiliate_id,
        "client_name": data["clientDetails"]["clientName"],
        "trip_type": redis_data["tripTypes"],
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "total_start_images": redis_data["totalStartImages"],
        "total_stop_images": redis_data["totalStopImages"],
        "form_field_oneway": redis_data['form_field_oneway'],
        "form_field_round": redis_data['form_field_round'],
        "sgst": customer_pricing_data['sgst'],
        "cgst": customer_pricing_data['cgst'],
        # "gst_numbers": customer_pricing_data['gst_numbers'],
        "customer_pricing_oneway": customer_pricing_data['oneway'],
        "customer_pricing_roundtrip": customer_pricing_data['roundtrip'],
        "customer_pricing_outstationtrip": customer_pricing_data['outstationtrip'],
        "driver_pricing_oneway": driver_pricing_data['oneway'],
        "driver_pricing_roundtrip": driver_pricing_data['roundtrip'],
        "driver_pricing_outstationtrip": driver_pricing_data["outstationtrip"],
        "pricing_cancellation_data": pricing_cancellation_data
    }

    try:
        # Update the existing affiliate document
        AffiliateCollections.affiliates_details.update_one(
            {"_id": existing_mongo_affiliate_id},  # Use the retrieved document's ID as identifier
            {"$set": update_affiliate_mongo}
        )
        print("Affiliate updated successfully in MongoDB.")
    except Exception as e:
        print(f"An error occurred: {e}")
        return jsonify({'success': -1, 'message': 'Failed to update affiliate in MongoDB', 'error': str(e)}), 500

    if data["master"] != -1:
        try:
            add_new_client(redis_data)  # Add new client in Redis
            print("New client added successfully to Redis.")
        except Exception as e:
            print(f"Error adding new client to Redis: {e}")
            return jsonify({'success': -1, 'message': 'Failed to update hierarchy in Redis','error': str(e)}), 500

        if not isinstance(data["master"], int):
            raise ValueError("Invalid input: master_id must be an integer.")

        if should_recursive_cte:
        # Recursive CTE query to find all masters up the hierarchy
            recursive_cte_query = text("""
                WITH RECURSIVE master_hierarchy AS (
                    SELECT aff_id, aff_master
                    FROM affiliate
                    WHERE aff_id = :master_id
                    UNION ALL
                    SELECT c.aff_id, c.aff_master
                    FROM affiliate c
                    INNER JOIN master_hierarchy mh ON c.aff_id = mh.aff_master
                )
                UPDATE affiliate
                SET aff_slave = JSON_SET(
                    aff_slave,
                    '$.slaves',
                    JSON_ARRAY_APPEND(
                        COALESCE(JSON_EXTRACT(aff_slave, '$.slaves'), JSON_ARRAY()),
                        '$',
                        :new_client_id
                    )
                )
                WHERE aff_id IN (SELECT aff_id FROM master_hierarchy);
            """)
            try:
                # Execute the recursive CTE query
                db.session.execute(recursive_cte_query, {'master_id': data["master"], 'new_client_id': existing_affiliate.id})
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print("Error executing recursive CTE query:", e)
                return jsonify({'success': -1, 'message': 'Failed to update affiliate hierarchy in RDBMS','error': str(e)}), 500
    if client_updated_name != client_name:
        try:
            redis_key = f'affiliate_{client_name}'
            redis_key_updated = f'affiliate_{client_updated_name}'
            redis_data = read_redis_data(redis_key)
            if redis_data:
                redis_data["form_details"]["client_name"] = client_updated_name
                write_redis_data(redis_key_updated, redis_data)
                delete_redis_key(redis_key)
            redis_key_pricing_customer = f'{redis_key}_pricing_customer'
            redis_key_pricing_customer_updated = f'{redis_key_updated}_pricing_customer'
            redis_data = read_redis_data(redis_key_pricing_customer)
            if redis_data:
                write_redis_data(redis_key_pricing_customer_updated, redis_data)
                delete_redis_key(redis_key_pricing_customer)
            redis_key_pricing_driver = f'{redis_key}_pricing_driver'
            redis_key_pricing_driver_updated = f'{redis_key_updated}_pricing_driver'
            redis_data = read_redis_data(redis_key_pricing_driver)
            if redis_data:
                write_redis_data(redis_key_pricing_driver_updated, redis_data)
                delete_redis_key(redis_key_pricing_driver)
            redis_key_pricing_cancellation = f'{redis_key}_pricing_cancellation'
            redis_key_pricing_cancellation_updated = f'{redis_key_updated}_pricing_cancellation'
            redis_data  = read_redis_data(redis_key_pricing_cancellation)
            if redis_data:
                write_redis_data(redis_key_pricing_cancellation_updated, redis_data)
                delete_redis_key(redis_key_pricing_cancellation)
            existing_affiliate.client_name = client_updated_name
            # Update the existing affiliate document
            AffiliateCollections.affiliates_details.update_one(
                {"_id": existing_mongo_affiliate_id},  # Use the retrieved document's ID as identifier
                {"$set": {"client_name": client_updated_name}}
            )
            affiliate_price_mappings = AffiliatePriceMapping.query.filter_by(mapped_price_affiliate_name=client_name).all()
            for mapping in affiliate_price_mappings:
                mapping.mapped_price_affiliate_name = client_updated_name
            all_custom_logs = AffiliateCustomLogs.query.filter_by(client_name=client_name).all()
            for log in all_custom_logs:
                log.client_name = client_updated_name
            all_affiliate_logs = AffiliateLogs.query.filter_by(client_name=client_name).all()
            for log in all_affiliate_logs:
                log.client_name = client_updated_name
            all_affiliate_pricing_logs = AffiliatePricingLogs.query.filter_by(client_name=client_name).all()
            for log in all_affiliate_pricing_logs:
                log.client_name = client_updated_name
            db.session.commit()       
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': -1, 'message': 'Failed to update affiliate name ', 'error': str(e)}), 500
        
    return jsonify({'success': 1, 'message': 'Affiliate updated successfully'}), 200

def update_all_affiliate_price_mappings(aff_id, mapping_list, trip_type):
    for item in mapping_list:
        # Convert city_key to an integer if needed
        region = int(item.get("city_key", 0))
        map_id = item.get("affiliate_id", None)
        map_id_name = item.get("affiliate_name", "")
        if map_id is not None:
            update_affiliate_price_mapping(aff_id, region, trip_type, map_id, map_id_name)
        else:
            print(f"Skipping mapping for region {region} because affiliate_id is missing")


def update_affiliate_price_mapping(aff_id, region, trip_type, map_id, map_id_name):
    # Query for an existing mapping with the given aff_id, region, and trip type
    mapping = AffiliatePriceMapping.query.filter_by(
        aff_id=aff_id,
        mapped_region=region,
        mapped_trip_type=trip_type
    ).first()
    if mapping:
        # Update existing record
        mapping.mapped_price_affiliate = map_id
        mapping.mapped_price_affiliate_name = map_id_name
        print(f"Updated mapping for aff_id {aff_id}, region {region}, trip_type {trip_type}")
        db.session.commit()
    else:
        # Create a new mapping record
        new_mapping = AffiliatePriceMapping(aff_id=aff_id,mapped_region = region,mapped_trip_type =trip_type, mapped_price_affiliate=map_id, mapped_price_affiliate_name=map_id_name)
        db.session.add(new_mapping)
        db.session.commit()
        print(f"Created new mapping for aff_id {aff_id}, region {region}, trip_type {trip_type}")

@admin_affiliate.route('/api/admin/affiliate/save_draft', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/save_draft_affiliate.yml')
def save_draft_affiliate():
    try:
        draft_creator_id = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    data = request.get_json()
    master = data.get("master", -1)

    draft_title = data.get("draft_title","")
    client_name = data["clientDetails"]["clientName"].strip()
    wallet_threshold = data["clientDetails"]["wallet_threshold"]
    cgst_percent = data["clientDetails"]["cgst_percent"]
    sgst_percent = data["clientDetails"]["sgst_percent"]
    gst_numbers = data["clientDetails"].get("gst_numbers", [])
    mapped_wallet_affiliate = data["clientDetails"]["mapped_wallet_affiliate"]
    logo=None
    client_display_logo=data.get("clientDetails",{}).get("clientDisplayLogo",{})
    if client_display_logo:
            if  isinstance(client_display_logo, str) and client_display_logo.startswith("data:"):
                logo = upload_pic_base64(client_display_logo,path="")
            else:
                logo = client_display_logo
    # Create a new AffiliateSchema object with the received data
    new_draft_affiliate = DraftAffiliate(data["clientDetails"]["clientName"],data["clientDetails"]["clientDisplayName"], data["clientDetails"]["selectedCities"], master, draft_creator_id, draft_creator_id,draft_title,mapped_wallet_affiliate=None,is_favourite= False, wallet_threshold= wallet_threshold,logo=logo)

    # Add the new affiliate record to the session and commit
    try:
        db.session.add(new_draft_affiliate)
        db.session.flush()
        new_draft_id = new_draft_affiliate.id
        if mapped_wallet_affiliate == "0":
            new_draft_affiliate.mapped_wallet_affiliate = new_draft_id
        else:
            new_draft_affiliate.mapped_wallet_affiliate = mapped_wallet_affiliate
        db.session.commit()
        print("affiliate added to rdbms")
    except Exception as e:
        db.session.rollback()  # Rollback in case of error
        print("Error", e)
        return jsonify({'success': -1, 'message': 'Failed to add draft_affiliate to RDBMS','error': str(e)}), 500

    json_data = {
        "master": master,
        "slaves": [],
        "form_field_oneway": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("affiliateIdEnabled", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("vehicleModelEnabled", "")
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("city_label", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("one_way", {}).get("custom_fields_one_way", [])
            ]
        },
        "form_field_round": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("affiliateIdEnabled", "")
            },
            "no_of_bookings":{
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("noOfBookingsEnabled", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_label", ""),
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_placeholder", ""),
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("city_label", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("vehicleModelEnabled", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("round", {}).get("custom_fields_round", [])
            ]
        },
        "form_details": {
            "client_name": client_name,
            "client_display_name": data["clientDetails"]["clientDisplayName"],
            "select_group": ["Global", "Local"], # No check for other than global
            "city": data["clientDetails"]["selectedCities"],
            "client_logo":logo
        },
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "tripTypes": [
            {
                "trip_type_name": trip.get("trip_type_name", ""),
                "trip_type_category": trip.get("trip_type_category", ""),
                "tripIndex": trip.get("tripIndex", ""),
                "tripType": trip.get("tripType", ""),
                "startImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("startImages", [])
                ],
                "stopImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("stopImages", [])
                ]
            }
            for trip in data.get("clientDetails", {}).get("tripTypes", [])
        ]
    }


    customerbaseBreakup = data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("base_breakup", {})


    customer_pricing_data = {
        "cgst": cgst_percent,
        "sgst": sgst_percent,
        "gst_numbers" : gst_numbers,
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in customerbaseBreakup
            ],
            "additional_charges": {
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "default_duration": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
                "outstation_charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationChargeType"),
                "outstation_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationRate")
            },
            "additional_charges": {
                "is_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        }
    }

    driverbaseBreakup = data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("base_breakup", {})

    driver_pricing_data = {
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in driverbaseBreakup
            ],
            "additional_charges": {
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "default_duration": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        }
    }


    cancelCharges = data.get("pricing_cancellation", {}).get("cancelCharges", {})

    pricing_cancellation_data = {
    "cancel_charges": [
        {
            "hour_range": item.get("hourRange"),
            "customer_cancel": item.get("customerCancelCharge"),
            "driver_cancel": item.get("driverCancelCharge"),
            "both_cancel": {
                "customer": item.get("bothCancelCharge", {}).get("customer"),
                "driver": item.get("bothCancelCharge", {}).get("driver")
            }
        }
        for item in cancelCharges
    ],
    "cancel_charge_static": {
        "customer_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticCustomerCharge"),
        "driver_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticDriverCharge"),
        "both_cancel": {
            "customer": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("customer"),
            "driver": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("driver")
        }
    }
    }


    new_draft_affiliate_mongo = {
        "mysql_id": new_draft_id,
        "client_name": data["clientDetails"]["clientName"],
        "trip_type": json_data["tripTypes"],
        "tripTypeLabel": json_data["tripTypeLabel"],
        "tripTypePlaceholder": json_data["tripTypePlaceholder"],
        "form_field_oneway": json_data['form_field_oneway'],
        "form_field_round": json_data['form_field_round'],
        "driverAvailVisEnabled": json_data["driverAvailVisEnabled"],
        "maxBookDistAllowed": json_data["maxBookDistAllowed"],
        "cgst": customer_pricing_data['cgst'],
        "sgst": customer_pricing_data['sgst'],
        "gst_numbers": customer_pricing_data['gst_numbers'],
        "customer_pricing_oneway": customer_pricing_data['oneway'],
        "customer_pricing_roundtrip": customer_pricing_data['roundtrip'],
        "customer_pricing_outstationtrip": customer_pricing_data['outstationtrip'],
        "driver_pricing_oneway": driver_pricing_data['oneway'],
        "driver_pricing_roundtrip": driver_pricing_data['roundtrip'],
        "driver_pricing_outstationtrip": driver_pricing_data['outstationtrip'],
        "pricing_cancellation_data": pricing_cancellation_data
    }

    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.draft_affiliates.insert_one(new_draft_affiliate_mongo)
        print("New draft added successfully.")
    except PyMongoError as mongo_error:
        print(f"MongoDB error: {mongo_error}")
        try:
            # Rollback SQLAlchemy changes to maintain consistency
            db.session.delete(new_draft_affiliate)
            db.session.rollback()
        except SQLAlchemyError as sql_error:
            print(f"SQLAlchemy rollback error: {sql_error}")
            return jsonify({
                'success': -1,
                'message': 'Failed to add draft affiliate to MongoDB, and rollback failed',
                'mongo_error': str(mongo_error),
                'sql_error': str(sql_error),
            }), 500

    creator = db.session.query(Users).filter(Users.id == draft_creator_id).first()
    # Ensure creator exists
    if creator:
        creator_name = creator.get_name()  # Assuming get_name() is a method in Users model
    else:
        creator_name = 'Unknown'  # Fallback in case creator is not found
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    data= {
            "draft_id": new_draft_id,
            "draft_title": new_draft_affiliate.draft_title,
            "draft_creator_name": creator_name,
            'is_favourite':False,
            "reg_time":  convert_to_local_time(new_draft_affiliate.reg,tz).strftime("%d %b, %y, %H:%M:%S")
    }
    live_update_to_channel(data, room_name='affiliate', type='affiliate', region=0, channel='new_draft')
    return jsonify({'success': 1, 'message': 'Draft affiliate added successfully'}), 201

@admin_affiliate.route('/api/admin/affiliate/edit_draft/<draft_id>', methods=['PUT'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/edit_draft_affiliate.yml')
def edit_draft_affiliate(draft_id):
    try:
        editor_id = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    data = request.get_json()
    master = data.get("master", -1)
    draft_title = data.get("draft_title", "")
    draft_id = int(draft_id)
    wallet_threshold = data["clientDetails"]["wallet_threshold"]
    cgst_percent = data["clientDetails"]["cgst_percent"]
    sgst_percent = data["clientDetails"]["sgst_percent"]
    mapped_wallet_affiliate = data["clientDetails"]["mapped_wallet_affiliate"]
    gst_numbers = data["clientDetails"].get("gst_numbers", [])

    # Fetch the existing draft from RDBMS using the draft_id
    existing_draft = DraftAffiliate.query.get(draft_id)
    if not existing_draft:
        return jsonify({'success': -1, 'message': 'Draft affiliate not found'}), 404
    client_name = data["clientDetails"]["clientName"]
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    # Update RDBMS fields with new data
    if existing_draft.draft_title != draft_title:
        live_update_data= {
            "draft_id": draft_id,
            "draft_title": draft_title,
            "draft_creator_id": existing_draft.draft_creator_id,
            "is_favourite": existing_draft.is_favourite,
            "reg_time": convert_to_local_time(existing_draft.reg,tz).strftime("%d %b, %y, %H:%M:%S")
        }
        live_update_to_channel(live_update_data, room_name='affiliate', type='affiliate', region=0, channel='draft_title_changed')
    existing_draft.draft_title = draft_title
    existing_draft.client_name = data["clientDetails"]["clientName"].strip()
    existing_draft.client_region = data["clientDetails"]["selectedCities"]
    existing_draft.display_name = data["clientDetails"]["clientDisplayName"]
    if int(data.get("master")) != int(existing_draft.master):
        existing_draft.mapped_wallet_affiliate = existing_draft.id
    else:
        existing_draft.mapped_wallet_affiliate = mapped_wallet_affiliate if mapped_wallet_affiliate else existing_draft.id #empty string
    existing_draft.wallet_threshold = wallet_threshold
    existing_draft.master = master
    logo=None
    existing_draft.master = data.get("master", existing_draft.master)
    client_display_logo=data.get("clientDetails",{}).get("clientDisplayLogo",{})
    if client_display_logo:
            if  isinstance(client_display_logo, str) and client_display_logo.startswith("data:"):
                logo = upload_pic_base64(client_display_logo,path="")
            else:
                logo = client_display_logo
            existing_draft.logo = logo
    try:
        db.session.commit()
        print("Affiliate draft updated in RDBMS.")
    except Exception as e:
        db.session.rollback()
        print("Error updating RDBMS draft:", e)
        return jsonify({'success': -1, 'message': 'Failed to update draft in RDBMS', 'error': str(e)}), 500

    json_data = {
        "master": master,
        "slaves": [],
        "form_field_oneway": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("affiliateIdEnabled", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("one_way", {}).get("vehicleModelEnabled", "")
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("city_label", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("one_way", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("one_way", {}).get("custom_fields_one_way", [])
            ]
        },
        "form_field_round": {
            "affiliate_id": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("affiliate_id_label", "Affiliate id"),
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("affiliateIdEnabled", "")
            },
            "no_of_bookings":{
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("noOfBookingsEnabled", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_label", ""),
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("no_of_bookings_placeholder", ""),
            },
            "city": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("city_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("city_label", "")
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": data.get("form_fields_details", {}).get("round", {}).get("vehicleModelEnabled", "")
            },
            "pickup_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("pickup_addr_label", ""),
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_placeholder", ""),
                "label": data.get("form_fields_details", {}).get("round", {}).get("dropoff_addr_label", ""),
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_name_label", "Source spoc name"),
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("source_spoc_number_label", "Source spoc number"),
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_name_label", "Destination spoc name"),
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_placeholder", "Give a placeholder"),
                "label": data.get("form_fields_details", {}).get("round", {}).get("destination_spoc_number_label", "Destination spoc number"),
            },
            "add_custom_fields": [
                {
                    "type": custom_field.get("fieldType", "dropdown"),
                    "placeholder": custom_field.get("placeholder", "Select attribute"),
                    "label": custom_field.get("title", "Add custom field"),
                    "value": custom_field.get("dropdownFields", [])
                }
                for custom_field in data.get("form_fields_details", {}).get("round", {}).get("custom_fields_round", [])
            ]
        },
        "form_details": {
            "client_name": client_name,
            "client_display_name": data["clientDetails"]["clientDisplayName"],
            "select_group": ["Global", "Local"], # No check for other than global
            "city": data["clientDetails"]["selectedCities"],
            "client_logo":logo
        },
        "driverAvailVisEnabled": data["clientDetails"].get("driverAvailVisEnabled", "0"),
        "maxBookDistAllowed": data["clientDetails"].get("maxBookDistAllowed", "0"),
        "tripTypeLabel": data["clientDetails"]["tripTypeLabel"],
        "tripTypePlaceholder": data["clientDetails"]["tripTypePlaceholder"],
        "tripTypes": [
            {
                "trip_type_name": trip.get("trip_type_name", ""),
                "trip_type_category": trip.get("trip_type_category", ""),
                "tripIndex": trip.get("tripIndex", ""),
                "tripType": trip.get("tripType", ""),
                "startImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("startImages", [])
                ],
                "stopImages": [
                    {
                        "imageTitle": image.get("imageTitle", ""),
                        "imageType": image.get("imageType", ""),
                        "imageDataType": image.get("imageDataType", ""),
                        "imageDesc": image.get("imageDesc", ""),
                        "imageTripType": image.get("imageTripType", ""),
                        "imageTripStage": image.get("imageTripStage", "")
                    } for image in trip.get("stopImages", [])
                ]
            }
            for trip in data.get("clientDetails", {}).get("tripTypes", [])
        ]
    }


    customerbaseBreakup = data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("base_breakup", {})

    customer_pricing_data = {
        "cgst": cgst_percent,
        "sgst": sgst_percent,
        "gst_numbers": gst_numbers,
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in customerbaseBreakup
            ],
            "additional_charges": {
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "default_duration": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("oneway", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("roundtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
                "outstation_charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationChargeType"),
                "outstation_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("base_breakup", {}).get("outstationRate")
            },
            "additional_charges": {
                "is_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "insurance_charge":  data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceCharge"),
                "insurance_charge_enabled": data.get("customer_pricing_data", {}).get("customer", {}).get("outstationtrip", {}).get("additional_charges", {}).get("insuranceChEnabled")
            }
        }
    }

    driverbaseBreakup = data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("base_breakup", {})

    driver_pricing_data = {
        "oneway": {
            "base_breakup": [
                {
                    "min_distance": item.get("minDist"),
                    "hike_type": item.get("HikeType"),
                    "amount": item.get("Amount")
                }
                for item in driverbaseBreakup
            ],
            "additional_charges": {
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeNight"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("overTimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("chargeTypeOvertime"),
                "default_duration": data.get("driver_pricing_data", {}).get("driver", {}).get("oneway", {}).get("additional_charges", {}).get("defaultDuration")
            }
        },
        "roundtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("roundtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        },
        "outstationtrip": {
            "base_breakup": {
                "minimum_hour": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minHour"),
                "minimum_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("minCharge"),
                "hourly_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("hourlyCharge"),
                "overtime_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeCharge"),
                "overtime_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("base_breakup", {}).get("overtimeType"),
            },
            "additional_charges": {
                "is_enabled": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChEnabled"),
                "night_charge": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightCharge"),
                "start_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeStartTime"),
                "end_night_time": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("nightChargeEndTime"),
                "charge_type": data.get("driver_pricing_data", {}).get("driver", {}).get("outstationtrip", {}).get("additional_charges", {}).get("chargeTypeNight")
            }
        }
    }


    cancelCharges = data.get("pricing_cancellation", {}).get("cancelCharges", {})

    pricing_cancellation_data = {
        "cancel_charges": [
            {
                "hour_range": item.get("hourRange"),
                "customer_cancel": item.get("customerCancelCharge"),
                "driver_cancel": item.get("driverCancelCharge"),
                "both_cancel": {
                    "customer": item.get("bothCancelCharge", {}).get("customer"),
                    "driver": item.get("bothCancelCharge", {}).get("driver")
                }
            }
            for item in cancelCharges
        ],
        "cancel_charge_static": {
            "customer_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticCustomerCharge"),
            "driver_cancel": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("staticDriverCharge"),
            "both_cancel": {
                "customer": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("customer"),
                "driver": data.get("pricing_cancellation", {}).get("cancelChargeStatic", {}).get("bothStaticCancelCharge", {}).get("driver")
            }
        }
    }

    existing_mongo_draft_affiliate = AffiliateCollections.draft_affiliates.find_one({"mysql_id": draft_id})

    if not existing_mongo_draft_affiliate:
        return jsonify({'success': -1, 'message': 'Draft not found in mongodb', 'error': 'No affiliate found with the provided client name'}), 404

    existing_mongo_draft_affiliate_id = existing_mongo_draft_affiliate["_id"]

    # Create the MongoDB update document
    updated_draft_mongo = {
        "mysql_id": draft_id,
        "client_name": data["clientDetails"]["clientName"],
        "trip_type": json_data["tripTypes"],
        "tripTypeLabel": json_data["tripTypeLabel"],
        "tripTypePlaceholder": json_data["tripTypePlaceholder"],
        "driverAvailVisEnabled": json_data["driverAvailVisEnabled"],
        "maxBookDistAllowed": json_data["maxBookDistAllowed"],
        "form_field_oneway": json_data['form_field_oneway'],
        "form_field_round": json_data['form_field_round'],
        "cgst": customer_pricing_data['cgst'],
        "sgst": customer_pricing_data['sgst'],
        "gst_numbers": customer_pricing_data['gst_numbers'],
        "customer_pricing_oneway": customer_pricing_data['oneway'],
        "customer_pricing_roundtrip": customer_pricing_data['roundtrip'],
        "customer_pricing_outstationtrip": customer_pricing_data['outstationtrip'],
        "driver_pricing_oneway": driver_pricing_data['oneway'],
        "driver_pricing_roundtrip": driver_pricing_data['roundtrip'],
        "driver_pricing_outstationtrip": driver_pricing_data['outstationtrip'],
        "pricing_cancellation_data": pricing_cancellation_data
    }

    try:
        # Update the draft document in MongoDB
        result = AffiliateCollections.draft_affiliates.update_one(
            {"_id": existing_mongo_draft_affiliate_id},
            {"$set": updated_draft_mongo}
        )
        if result.matched_count == 0:
            return jsonify({'success': -1, 'message': 'Draft affiliate not found in MongoDB'}), 404

        print("Affiliate draft updated in MongoDB.")
    except Exception as e:
        print(f"An error occurred while updating MongoDB: {e}")
        return jsonify({'success': -1, 'message': 'Failed to update draft in MongoDB', 'error': str(e)}), 500

    return jsonify({'success': 1, 'message': 'Draft affiliate updated successfully'}), 200


@admin_affiliate.route('/api/admin/affiliate/client_names', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE,Tabs.BOOKINGS_AFFILIATE, Tabs.CREATE_BOOKINGS])
@swag_from('/app/swagger_docs/affiliate/get_affiliate_client_names.yml')
def get_all_affiliate_client_names():
    try:
        regions = request.args.get('regions').split(",")
        affiliate = aliased(Affiliate)

        # Subquery to clean up aff_region string
        subquery = (
            db.session.query(
                affiliate.id,
                affiliate.client_name,
                affiliate.client_region,
                func.replace(affiliate.client_region, ' ', '').label('region_cleaned')
            ).subquery()
        )
        sub = aliased(subquery)

        # Build dynamic condition
        if '-1' in regions:
            query = select(sub.c.id, sub.c.client_name, sub.c.client_region)
        else:
            # Filter by region match
            conditions = [func.find_in_set(region, sub.c.region_cleaned) > 0 for region in regions]
            query = select(sub.c.id, sub.c.client_name, sub.c.client_region).where(or_(*conditions))

        result = db.session.execute(query).fetchall()
        client_names_list = [{'id': client.id, 'client_name': client.client_name,'client_regions':client.client_region} for client in result]

        return jsonify({'success': 1,'client_names': client_names_list}), 200
    except Exception as e:
        print(str(e),flush=True)
        return jsonify({'success': -1,'error': 'Failed to fetch client names', 'message': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/client_slaves_masters', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
# @swag_from('/app/swagger_docs/affiliate/get_affiliate_client_names.yml')
def get_all_affiliate_client_slaves_names():
    try:
        master_aff_id = request.args.get('master_aff_id')
        if not master_aff_id:
            return jsonify({'success': 0, 'error': 'Incomplete Form Details'}), 400
        master_aff_id = int(master_aff_id)
        master_aff = db.session.query(Affiliate).filter(Affiliate.id == master_aff_id).first()
        if not master_aff:
            return jsonify({'success': 0, 'error': 'Affiliate not found'}), 404

        #get all the masters of this up the hierarchy

        recursive_master_query = text("""
            WITH RECURSIVE master_chain AS (
                SELECT aff_id, aff_master, aff_name, aff_region
                FROM affiliate
                WHERE aff_id = :affiliate_id

                UNION ALL

                SELECT a.aff_id, a.aff_master, a.aff_name, a.aff_region
                FROM affiliate a
                INNER JOIN master_chain mc ON a.aff_id = mc.aff_master
            )
            SELECT aff_id,
                aff_name AS client_name,
                aff_region AS client_region
            FROM master_chain
            WHERE aff_id != :affiliate_id;
        """)

        result = db.session.execute(recursive_master_query, {'affiliate_id': master_aff_id})
        masters = [
            {'id': row.aff_id, 'client_name': row.client_name, 'client_region': row.client_region}
            for row in result
        ]
        # Extract slave IDs from the JSON field (assumes structure: {"slaves": [15, 16]})
        slave_ids = []
        if master_aff.slave:
            slave_ids = master_aff.slave.get("slaves", [])

        # Query for slave affiliates using the list of IDs
        slaves = (
            db.session.query(Affiliate.id, Affiliate.client_name, Affiliate.client_region)
            .filter(Affiliate.id.in_(slave_ids))
            .all()
        )
        slave_list = []
        for s in slaves:
            # For each slave, fetch its own affiliate data from MongoDB based on its client_name.
            affiliate_mongo_slave = AffiliateCollections.affiliates_details.find_one({'client_name': s.client_name})
            if not affiliate_mongo_slave:
                # If no MongoDB record is found for this slave, you can decide to skip it or set defaults.
                client_trip_types = {'One Way': False, 'Round': False, 'Outstation Round': False}
            else:
                one_way = True if affiliate_mongo_slave.get("customer_pricing_oneway", {}).get("base_breakup") else False
                round_trip = True if affiliate_mongo_slave.get("customer_pricing_roundtrip", {}).get("base_breakup", {}).get("minimum_hour") else False
                outstation = True if affiliate_mongo_slave.get("customer_pricing_outstationtrip", {}).get("base_breakup", {}).get("minimum_hour") else False
                client_trip_types = {'One Way': one_way, 'Round': round_trip, 'Outstation Round': outstation}

            slave_list.append({
                'id': s.id,
                'client_name': s.client_name,
                'client_region': s.client_region,
                'client_trip_types': client_trip_types
            })

        return jsonify({'success': 1, 'client_slaves': slave_list,"master_names":masters}), 200
    except Exception as e:
        print(str(e))
        return jsonify({'success': -1, 'error': 'Failed to fetch client slaves names', 'message': str(e)}), 500

@admin_affiliate.route('/api/admin/affiliate/affiliate_list', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
@swag_from('/app/swagger_docs/affiliate/get_affiliate_list.yml')
def get_affiliate_list():
    try:
        # Get query parameters for pagination and search
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        search_query = request.args.get('search_query', type=str)
        search_by = request.args.get('search_by', type=int)
        restricted = request.args.get('restricted', 0, type=int)
        offset = (page - 1) * per_page

        enabled = True if restricted== 0 else False
        # Start the query for fetching Affiliate data
        query = db.session.query(
            Affiliate.id,
            Affiliate.client_name,
            Affiliate.display_name,
            Affiliate.client_region,
            Affiliate.reg,
            Affiliate.master,
            Affiliate.enabled
        ).filter(Affiliate.enabled==enabled)

        # Apply search filters if search_query is provided
        if search_query:
            if search_by == 1:  # Search by admin ID
                try:
                    search_id = int(search_query)
                    query = query.filter(Affiliate.id == search_id)
                except ValueError:
                    pass  # Invalid ID, do nothing

            elif search_by == 2:  # Search by client name or region
                name_parts = search_query.split()
                if len(name_parts) == 2:
                    query = query.filter(
                        or_(
                            and_(
                                Affiliate.client_name.ilike(f"%{name_parts[0]}%"),
                                Affiliate.client_region.ilike(f"%{name_parts[1]}%")
                            ),
                            and_(
                                Affiliate.client_name.ilike(f"%{name_parts[1]}%"),
                                Affiliate.client_region.ilike(f"%{name_parts[0]}%")
                            )
                        )
                    )
                else:
                    query = query.filter(
                        or_(
                            Affiliate.client_name.ilike(f"%{search_query}%"),
                            Affiliate.client_region.ilike(f"%{search_query}%")
                        )
                    )

        # Apply ordering and pagination
        query = query.order_by(desc(Affiliate.reg)).offset(offset).limit(per_page)

        # Getting the total count of records
        total_clients = query.count()
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        # Constructing a list of client details dictionaries
        client_details_list = [
            {
                'id': client.id,
                'client_name': client.client_name,
                'client_display_name': client.display_name,
                'region': client.client_region,
                "enabled":client.enabled,
                'registration_date': convert_to_local_time(client.reg).strftime('%d %b %Y, %H:%M'),
                'type': 'Global' if client.master == -1 else 'Inherited'
            }
            for client in query.all()
        ]

        # Returning the data as JSON response with pagination metadata
        return jsonify({
            'success': 1,
            'client_details': client_details_list,
            'total': total_clients,
            'page': page,
            'per_page': per_page,
            'pages': (total_clients + per_page - 1) // per_page  # Calculates the total number of pages
        }), 200
    except Exception as e:
        print(str(e))
        return jsonify({'success': -1, 'error': 'Failed to fetch client details', 'message': str(e)}), 500

def affiliate_update_pricing_times_to_ist(pricing_data):
    """
    For each trip type in the pricing data (oneway, roundtrip, outstationtrip),
    update the 'start_night_time' and 'end_night_time' fields in the
    additional_charges section from UTC to IST.
    """
    if not pricing_data:
        return pricing_data

    for trip_type in ['oneway', 'roundtrip', 'outstationtrip']:
        trip_data = pricing_data.get(trip_type)
        if trip_data:
            additional_charges = trip_data.get('additional_charges', {})
            if additional_charges:
                if additional_charges.get('start_night_time'):
                    additional_charges['start_night_time'] = convert_utc_to_ist(additional_charges['start_night_time'])
                if additional_charges.get('end_night_time'):
                    additional_charges['end_night_time'] = convert_utc_to_ist(additional_charges['end_night_time'])
    return pricing_data

def get_price_mappings_for_affiliate(aff_id: int):
    # Query for each trip type
    oneway_entries = AffiliatePriceMapping.query.filter_by(aff_id=aff_id, mapped_trip_type=3).all()
    round_entries = AffiliatePriceMapping.query.filter_by(aff_id=aff_id, mapped_trip_type=1).all()
    outstation_entries = AffiliatePriceMapping.query.filter_by(aff_id=aff_id, mapped_trip_type=2).all()

    # Build mapping arrays
    oneway_price_mapping = [
        {
            "affiliate_id": str(entry.mapped_price_affiliate),
            "affiliate_name": entry.mapped_price_affiliate_name,
            "city_key": str(entry.mapped_region)
        }
        for entry in oneway_entries
    ]

    round_price_mapping = [
        {
            "affiliate_id": str(entry.mapped_price_affiliate),
            "affiliate_name": entry.mapped_price_affiliate_name,
            "city_key": str(entry.mapped_region)
        }
        for entry in round_entries
    ]

    outstation_price_mapping = [
        {
            "affiliate_id": str(entry.mapped_price_affiliate),
            "affiliate_name": entry.mapped_price_affiliate_name,
            "city_key": str(entry.mapped_region)
        }
        for entry in outstation_entries
    ]

    return {
        "oneway": oneway_price_mapping,
        "roundtrip": round_price_mapping,
        "outstationtrip": outstation_price_mapping
    }


# Helper function to process and add SPOC details
def process_spocs(spoc_data, rep_id, aff_id):
    """
    Processes a list of SPOC details and adds them to the session.
    Raises a ValueError on validation errors.
    # """
    # if not isinstance(spoc_data, list):
    #     raise ValueError('Invalid payload. Expected a list of SPOC details.')

    spocs_to_add = []
    for spoc in spoc_data:
        spoc_name = spoc.get('name')
        mobile = spoc.get('mobile')
        region = spoc.get('region')
        spoc_type_flag = spoc.get('global')
        # Convert flag to internal constant
        if spoc_type_flag is False:
            spoc_type = AffiliateSpoc.LOCAL_SPOC
        else:
            spoc_type = AffiliateSpoc.GLOBAL_SPOC

        # Validate required fields
        if not spoc_name or not mobile:
            raise ValueError(f"Missing 'spoc_name' or 'spoc_mobile' for entry: {spoc}")
        if len(mobile) != 10 or not mobile.isdigit():
            raise ValueError(f"Invalid 'spoc_mobile' for entry: {spoc}")

        # Check for duplicate global or local entries
        global_spoc = db.session.query(AffiliateSpoc).filter_by(
            spoc_mobile=mobile, aff_id=aff_id, spoc_type=AffiliateSpoc.GLOBAL_SPOC
        ).first()
        if global_spoc:
            raise ValueError(f"{mobile} is globally registered")

        local_spoc = db.session.query(AffiliateSpoc).filter_by(
            spoc_mobile=mobile, spoc_type=AffiliateSpoc.LOCAL_SPOC, aff_id=aff_id, rep_id=rep_id
        ).first()
        if local_spoc and spoc_type == AffiliateSpoc.LOCAL_SPOC:
            raise ValueError(f"{mobile} is locally registered")

        spocs_to_add.append(AffiliateSpoc(spoc_name, mobile, spoc_type, rep_id, aff_id,region))

    db.session.add_all(spocs_to_add)
    return spocs_to_add

# Helper function to process and add Address details
def process_addresses(address_data, rep_id, aff_id):
    """
    Processes a list of address details, adds addresses and their associated SPOCs to the session.
    Raises a ValueError on validation errors.
    """
    # if not isinstance(address_data, list):
    #     raise ValueError('Invalid payload. Expected a list of addresses.')

    addresses_added = []
    address_spocs_to_add = []
    for addr in address_data:
        address = addr.get('address')
        region = addr.get('region')
        try:
            lat = round(float(addr.get('lat')), 4)
            long = round(float(addr.get('long')), 4)
        except (TypeError, ValueError):
            raise ValueError(f"Invalid latitude or longitude for entry: {addr}")
        nickname = addr.get('nickname')
        if nickname:
            nickname = nickname.strip()
        add_type_flag = addr.get('global')
        spoc_list = addr.get('spoc_list')

        if add_type_flag is False:
            address_type = AffiliateAddress.LOCAL_ADDRESS
        else:
            address_type = AffiliateAddress.GLOBAL_ADDRESS

        if not address or not nickname:
            raise ValueError(f"Missing 'address' or 'nickname' for entry: {addr}")

        # Check for duplicate nickname in global or local scope
        global_address = db.session.query(AffiliateAddress).filter(
            db.func.lower(AffiliateAddress.nickname) == nickname.lower(),
            AffiliateAddress.aff_id == aff_id,
            AffiliateAddress.address_type == AffiliateAddress.GLOBAL_ADDRESS
        ).first()
        if global_address:
            raise ValueError(f"{nickname} is globally registered")

        local_address = db.session.query(AffiliateAddress).filter(
            db.func.lower(AffiliateAddress.nickname) == nickname.lower(),
            AffiliateAddress.aff_id == aff_id,
            AffiliateAddress.rep_id == rep_id,
            AffiliateAddress.address_type == AffiliateAddress.LOCAL_ADDRESS
        ).first()
        if local_address and address_type == AffiliateAddress.LOCAL_ADDRESS:
            raise ValueError(f"{nickname} is locally registered")

        new_address = AffiliateAddress(address, nickname, lat, long, address_type, rep_id, aff_id,region)
        db.session.add(new_address)
        db.session.flush()  # to populate new_address.add_id
        addresses_added.append(new_address)

        # Process associated SPOCs for the address if provided
        if spoc_list:
            if not isinstance(spoc_list, list):
                raise ValueError(f"Invalid spoc_list for entry: {addr}")
            for spoc in spoc_list:
                spoc_id = spoc.get('spoc_id')
                spoc_name = spoc.get('spoc_name')
                if not spoc_id or not spoc_name:
                    raise ValueError(f"{spoc}: Missing 'spoc_id' or 'spoc_name'")
                address_spocs_to_add.append(AddressSpoc(spoc_id, spoc_name, new_address.add_id))

    db.session.add_all(address_spocs_to_add)
    return addresses_added, address_spocs_to_add


# Helper function to get local SPOCs for a representative
def get_local_spocs(rep_id, aff_id):
    """
    Returns a list of local SPOCs for the given affiliate representative.
    """
    return db.session.query(AffiliateSpoc).filter(
        AffiliateSpoc.aff_id == aff_id,
        AffiliateSpoc.rep_id == rep_id,
        AffiliateSpoc.spoc_type == AffiliateSpoc.LOCAL_SPOC
    ).all()

# Helper function to get global SPOCs excluding those already in local SPOCs
def get_global_spocs(aff_id, local_mobile_numbers):
    """
    Returns a list of global SPOCs for the given affiliate that are not already
    registered locally (based on their mobile numbers).
    """
    return db.session.query(AffiliateSpoc).filter(
        AffiliateSpoc.aff_id == aff_id,
        AffiliateSpoc.spoc_type == AffiliateSpoc.GLOBAL_SPOC,
        ~AffiliateSpoc.spoc_mobile.in_(local_mobile_numbers)
    ).all()

# Helper function to update a SPOC record based on provided data.
def process_update_spoc(data, rep_id, aff_id):
    """
    Update the SPOC record based on the provided data.
    Expected keys in data: 'id', 'name', 'mobile', 'global'
    Returns the spoc_id if updated successfully.
    Raises a ValueError with a message if any validation fails.
    """
    spoc_id = data.get('id')
    spoc_name = data.get('name')
    mobile = data.get('mobile')
    region = data.get('region')
    spoc_type_flag = data.get('global')  # True for Global, False for Local

    if not spoc_id:
        raise ValueError("Missing SPOC id")

    spoc = db.session.query(AffiliateSpoc).filter(AffiliateSpoc.spoc_id == spoc_id).first()
    if not spoc:
        raise ValueError("SPOC not found")

    # Determine original type of the spoc
    original_type_is_global = (spoc.spoc_type == AffiliateSpoc.GLOBAL_SPOC)

    # Conversion from Local to Global
    if spoc_type_flag and not original_type_is_global:
        # Check if a global SPOC with the same mobile exists
        global_spoc = db.session.query(AffiliateSpoc).filter(
            AffiliateSpoc.spoc_mobile == mobile,
            AffiliateSpoc.aff_id == aff_id,
            AffiliateSpoc.spoc_type == AffiliateSpoc.GLOBAL_SPOC
        ).first()
        if global_spoc:
            raise ValueError(f"Mobile {mobile} exists globally")
        spoc.spoc_type = AffiliateSpoc.GLOBAL_SPOC

    # Conversion from Global to Local
    elif not spoc_type_flag and original_type_is_global:
        local_spoc = db.session.query(AffiliateSpoc).filter(
            AffiliateSpoc.spoc_mobile == mobile,
            AffiliateSpoc.aff_id == aff_id,
            AffiliateSpoc.rep_id == rep_id,
            AffiliateSpoc.spoc_type == AffiliateSpoc.LOCAL_SPOC
        ).first()
        if local_spoc:
            raise ValueError(f"Mobile {mobile} exists locally")
        spoc.spoc_type = AffiliateSpoc.LOCAL_SPOC
        spoc.rep_id = rep_id

    # Update name and mobile if provided.
    if spoc_name:
        spoc.spoc_name = spoc_name
    if mobile:
        spoc.spoc_mobile = mobile
    if region:
        spoc.region = region

    return spoc_id

# Helper function to delete a SPOC record.
def process_delete_spoc(spoc_id):
    """
    Delete the SPOC record with the provided spoc_id.
    Raises a ValueError if the SPOC is not found.
    """
    spoc = db.session.query(AffiliateSpoc).filter(AffiliateSpoc.spoc_id == spoc_id).first()
    if not spoc:
        raise ValueError("SPOC not found")
    db.session.delete(spoc)
    return spoc_id

# Helper function for updating an address
def process_update_address(data, rep_id, aff_id):
    """
    Updates an AffiliateAddress record based on provided data.
    Expected data keys:
      - 'id': the address ID
      - 'address': the new address text
      - 'lat' and 'long': latitude and longitude values
      - 'nickname': the address nickname (used for uniqueness)
      - 'global': a flag (True for Global, False for Local)
      - 'spoc_list': a list of associated SPOCs with each having 'spoc_id' and 'spoc_name'
    Returns the address_id on success.
    Raises a ValueError on any validation error.
    """
    # Extract and validate fields
    try:
        address_id = int(data.get('id'))
        address_text = data.get('address')
        lat = round(float(data.get('lat')), 4)
        long_val = round(float(data.get('long')), 4)
        region = data.get('region')
    except (TypeError, ValueError):
        raise ValueError("Invalid or missing id, lat, or long")

    nickname = data.get('nickname')
    if nickname:
        nickname = nickname.strip()
    else:
        raise ValueError("Missing 'nickname'")

    add_type_flag = data.get('global')
    spoc_list = data.get('spoc_list', [])

    # Retrieve the existing address record
    aff_address = db.session.query(AffiliateAddress).filter(
        AffiliateAddress.add_id == address_id
    ).first()
    if not aff_address:
        raise ValueError("Address not found")

    original_type_is_global = (aff_address.address_type == AffiliateAddress.GLOBAL_ADDRESS)

    # Conversion from Local to Global
    if add_type_flag and not original_type_is_global:
        global_address = db.session.query(AffiliateAddress).filter(
            db.func.lower(AffiliateAddress.nickname) == nickname.lower(),
            AffiliateAddress.aff_id == aff_id,
            AffiliateAddress.address_type == AffiliateAddress.GLOBAL_ADDRESS
        ).first()
        if global_address:
            raise ValueError(f"{nickname} exists globally")
        aff_address.address_type = AffiliateAddress.GLOBAL_ADDRESS
    # Conversion from Global to Local
    elif not add_type_flag and original_type_is_global:
        local_address = db.session.query(AffiliateAddress).filter(
            db.func.lower(AffiliateAddress.nickname) == nickname.lower(),
            AffiliateAddress.aff_id == aff_id,
            AffiliateAddress.rep_id == rep_id,
            AffiliateAddress.address_type == AffiliateAddress.LOCAL_ADDRESS
        ).first()
        if local_address:
            raise ValueError(f"{nickname} exists locally")
        aff_address.address_type = AffiliateAddress.LOCAL_ADDRESS

    # Update basic fields
    if address_text:
        aff_address.address = address_text
    aff_address.nickname = nickname
    aff_address.latitude = lat
    aff_address.longitude = long_val
    aff_address.rep_id = rep_id
    aff_address.region = region

    # Process associated SPOCs for the address
    existing_spocs = db.session.query(AddressSpoc).filter(
        AddressSpoc.address_id == address_id
    ).all()
    existing_spoc_ids = {spoc.spoc_id for spoc in existing_spocs}
    provided_spoc_ids = {spoc.get('spoc_id') for spoc in spoc_list if spoc.get('spoc_id')}

    # Delete SPOCs that are not in the provided list
    spocs_to_delete = existing_spoc_ids - provided_spoc_ids
    if spocs_to_delete:
        db.session.query(AddressSpoc).filter(
            AddressSpoc.address_id == address_id,
            AddressSpoc.spoc_id.in_(spocs_to_delete)
        ).delete(synchronize_session=False)

    # Add or update provided SPOCs
    for spoc in spoc_list:
        spoc_id = spoc.get('spoc_id')
        spoc_name = spoc.get('spoc_name')
        if not spoc_id or not spoc_name:
            raise ValueError(f"{spoc}: Missing 'spoc_id' or 'spoc_name'")

        existing_entry = db.session.query(AddressSpoc).filter(
            AddressSpoc.address_id == address_id,
            AddressSpoc.spoc_id == spoc_id
        ).first()
        if existing_entry:
            existing_entry.spoc_name = spoc_name
        else:
            new_spoc = AddressSpoc(spoc_id, spoc_name, address_id)
            db.session.add(new_spoc)

    return address_id


# Helper function for deleting an address
def process_delete_address(address_id):
    """
    Deletes an AffiliateAddress record.
    Raises a ValueError if the address is not found.
    """
    address = db.session.query(AffiliateAddress).filter(
        AffiliateAddress.add_id == address_id
    ).first()
    if not address:
        raise ValueError("Address not found")
    db.session.delete(address)
    return address_id


# Helper function to get local addresses for a rep
def get_local_addresses(rep_id):
    """
    Returns a list of local addresses for the given representative.
    """
    return db.session.query(AffiliateAddress).filter(
        AffiliateAddress.rep_id == rep_id,
        AffiliateAddress.address_type == AffiliateAddress.LOCAL_ADDRESS
    ).all()


# Helper function to get global addresses excluding those with local nicknames
def get_global_addresses(aff_id, local_nicknames):
    """
    Returns a list of global addresses for the affiliate that are not already registered locally
    (based on their nickname).
    """
    return db.session.query(AffiliateAddress).filter(
        AffiliateAddress.aff_id == aff_id,
        AffiliateAddress.address_type == AffiliateAddress.GLOBAL_ADDRESS,
        ~AffiliateAddress.nickname.in_(local_nicknames)
    ).all()

@admin_affiliate.route('/api/admin/affiliate/get_affiliate_data', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/get_affiliate_data.yml')
def get_affiliate_data():
    data = request.get_json()
    client_name = data.get('client_name', '').strip()
    if not client_name:
        return jsonify({'error': 'client_name is required'}), 400

    affiliate_sql = db.session.query(Affiliate).filter_by(client_name=client_name).first()
    if not affiliate_sql:
        return jsonify({'error': 'Affiliate not found in SQL'}), 404

    affiliate_master = db.session.query(Affiliate).filter_by(id=affiliate_sql.master).first()
    if affiliate_master:
        master_name = affiliate_master.client_name
        master_id = affiliate_master.id
    else:
        master_name = "Global"
        master_id = -1

    # Check Redis for data
    redis_key_affiliate = f'affiliate_{client_name}'
    redis_key_customer_pricing = f'{redis_key_affiliate}_pricing_customer'
    redis_key_driver_pricing = f'{redis_key_affiliate}_pricing_driver'
    redis_key_pricing_cancellation = f'{redis_key_affiliate}_pricing_cancellation'

    if  is_redis_available() and redis_client.exists(redis_key_affiliate):
        affiliate_data = redis_client.get(redis_key_affiliate)
        affiliate_data_pricing_customer = redis_client.get(redis_key_customer_pricing)
        affiliate_data_pricing_driver = redis_client.get(redis_key_driver_pricing)
        affiliate_data_pricing_cancellation = redis_client.get(redis_key_pricing_cancellation)

        try:
            affiliate_data = json.loads(affiliate_data) if affiliate_data else None
            affiliate_data_pricing_customer = json.loads(affiliate_data_pricing_customer) if affiliate_data_pricing_customer else None
            affiliate_data_pricing_driver = json.loads(affiliate_data_pricing_driver) if affiliate_data_pricing_driver else None
            affiliate_data_pricing_cancellation = json.loads(affiliate_data_pricing_cancellation) if affiliate_data_pricing_cancellation else None
        except json.JSONDecodeError as e:
            return jsonify({'error': 'Failed to parse JSON data from Redis', 'details': str(e)}), 500

        # Convert UTC times back to IST for pricing data
        affiliate_data_pricing_customer = affiliate_update_pricing_times_to_ist(affiliate_data_pricing_customer)
        affiliate_data_pricing_driver = affiliate_update_pricing_times_to_ist(affiliate_data_pricing_driver)

        price_mappings = get_price_mappings_for_affiliate(affiliate_sql.id)
        affiliate_data_pricing_customer["oneway"]["additional_charges"]["price_mapping"] = price_mappings["oneway"]
        affiliate_data_pricing_customer["roundtrip"]["additional_charges"]["price_mapping"] = price_mappings["roundtrip"]
        affiliate_data_pricing_customer["outstationtrip"]["additional_charges"]["price_mapping"] = price_mappings["outstationtrip"]

        return jsonify({
            'success': 1,
            'source': 'redis',
            'master': master_name,
            'master_id': master_id,
            'data': affiliate_data,
            'customer_pricing_data': affiliate_data_pricing_customer,
            'driver_pricing_data': affiliate_data_pricing_driver,
            'pricing_cancellation_data': affiliate_data_pricing_cancellation
        }), 200

    # Redis data not found; fetch from MySQL and MongoDB
    try:
        # Prepare form details
        form_details = {
            "client_id":affiliate_sql.id,
            "client_name":affiliate_sql.client_name,
            "client_display_name": affiliate_sql.display_name,
            "select_group": ["Global", "Local"],
            "client_logo":affiliate_sql.logo,
            "city": affiliate_sql.client_region
        }

        affiliate_mongo = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
        if not affiliate_mongo:
            return jsonify({"success": 0, "message": "Affiliate data not found in MongoDB"}), 404

        affiliate_mongo.pop('_id', None)

        affiliate_data = {
            "master": affiliate_sql.master,
            "slaves": affiliate_sql.slave['slaves'],
            "wallet_threshold": affiliate_sql.wallet_threshold,
            "mapped_wallet_affiliate": affiliate_sql.mapped_wallet_affiliate,
            "form_field_oneway": affiliate_mongo.get("form_field_oneway"),
            "form_field_round": affiliate_mongo.get("form_field_round"),
            "form_details": form_details,
            "tripTypeLabel": affiliate_mongo.get("tripTypeLabel", ""),
            "driverAvailVisEnabled": affiliate_mongo.get("driverAvailVisEnabled", "0"),
            "maxBookDistAllowed": affiliate_mongo.get("maxBookDistAllowed", "0"),
            "tripTypePlaceholder": affiliate_mongo.get("tripTypePlaceholder", ""),
            "tripTypes": affiliate_mongo.get("trip_type")
        }

        # Extract pricing-related data from MongoDB document
        customer_pricing_data = {
            "sgst": affiliate_mongo.get("sgst"),
            "cgst": affiliate_mongo.get("cgst"),
            "gst_numbers": affiliate_mongo.get("gst_numbers"),
            "oneway": affiliate_mongo.get("customer_pricing_oneway"),
            "roundtrip": affiliate_mongo.get("customer_pricing_roundtrip"),
            "outstationtrip": affiliate_mongo.get("customer_pricing_outstationtrip")
        }

        driver_pricing_data = {
            "oneway": affiliate_mongo.get("driver_pricing_oneway"),
            "roundtrip": affiliate_mongo.get("driver_pricing_roundtrip"),
            "outstationtrip": affiliate_mongo.get("driver_pricing_outstationtrip")
        }

        pricing_cancellation_data = affiliate_mongo.get("pricing_cancellation_data", {})
        # Before returning, update Redis with fresh data
        if is_redis_available():
            write_redis_data(redis_key_affiliate, affiliate_data)
            write_redis_data(redis_key_customer_pricing, customer_pricing_data)
            write_redis_data(redis_key_driver_pricing, driver_pricing_data)
            write_redis_data(redis_key_pricing_cancellation, pricing_cancellation_data)

        # Convert UTC times back to IST for pricing data
        customer_pricing_data = affiliate_update_pricing_times_to_ist(customer_pricing_data)
        driver_pricing_data = affiliate_update_pricing_times_to_ist(driver_pricing_data)
        price_mappings = get_price_mappings_for_affiliate(affiliate_sql.id)
        customer_pricing_data["oneway"]["additional_charges"]["price_mapping"] = price_mappings["oneway"]
        customer_pricing_data["roundtrip"]["additional_charges"]["price_mapping"] = price_mappings["roundtrip"]
        customer_pricing_data["outstationtrip"]["additional_charges"]["price_mapping"] = price_mappings["outstationtrip"]


        return jsonify({
            'success': 1,
            'source': 'mysql_mongo',
            'master': master_name,
            'master_id': master_id,
            'data': affiliate_data,
            'customer_pricing_data': customer_pricing_data,
            'driver_pricing_data': driver_pricing_data,
            'pricing_cancellation_data': pricing_cancellation_data
        }), 200

    except Exception as e:
        return jsonify({
            'success': -1,
            'error': 'Failed to retrieve affiliate data',
            'details': str(e)
        }), 500


@admin_affiliate.route('/api/admin/affiliate/affiliate_logs', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
@swag_from('/app/swagger_docs/affiliate/get_affiliate_logs.yml')
def get_affiliate_logs():
    try:
        # Get query parameters for pagination and search
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        client_name = request.args.get('client_name', type= str)
        search_query = request.args.get('search_query', type=str)
        offset = (page - 1) * per_page

        # Start the query for fetching AffiliateLogs data
        query = db.session.query(
            AffiliateLogs.log_id,
            AffiliateLogs.log_date_time,
            AffiliateLogs.changed_by,
            AffiliateLogs.section,
            AffiliateLogs.changes_made,
            AffiliateLogs.changed_from,
            AffiliateLogs.changed_to,
            AffiliateLogs.affiliate_foreign_log_id,
            AffiliateLogs.client_name
        ).filter(AffiliateLogs.client_name == client_name)

        # Apply search filters if search_query is provided
        if search_query:
            query = query.filter(
                or_(
                    AffiliateLogs.changed_by.ilike(f"%{search_query}%"),
                    AffiliateLogs.section.ilike(f"%{search_query}%"),
                    AffiliateLogs.client_name.ilike(f"%{search_query}%"),
                    AffiliateLogs.changes_made.ilike(f"%{search_query}%")
                )
            )
        total_logs = query.count()
        # Apply ordering and pagination
        query = query.order_by(desc(AffiliateLogs.log_date_time)).offset(offset).limit(per_page)

        # Getting the total count of records

        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        # Constructing a list of log details dictionaries
        log_details_list = [
            {
                'log_id': log.log_id,
                'log_date_time': convert_to_local_time(log.log_date_time,tz).strftime('%d %b %Y, %H:%M'),
                'changed_by': log.changed_by,
                'section': log.section,
                'changes_made': log.changes_made,
                'changed_from': log.changed_from,
                'changed_to': log.changed_to,
                'affiliate_foreign_log_id': log.affiliate_foreign_log_id,
                'client_name': log.client_name
            }
            for log in query.all()
        ]

        # Returning the data as JSON response with pagination metadata
        return jsonify({
            'success': 1,
            'log_details': log_details_list,
            'total': total_logs,
            'page': page,
            'per_page': per_page,
            'pages': (total_logs + per_page - 1) // per_page  # Calculates the total number of pages
        }), 200
    except Exception as e:
        return jsonify({
            'success': 0,
            'error': str(e)
        }), 500

@admin_affiliate.route('/api/admin/affiliate/restrict_affiliate', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/restrict_affiliate.yml')
def restrict_affiliate():
    try:
        # Get the user ID from the JWT identity
        user_id = get_jwt_identity()

        # Get the request data (affiliate ID)
        data = request.get_json()
        client_name = data.get("client_name")
        to_restrict = data.get("to_restrict",1)

        # Check if affiliate ID is provided
        if not client_name:
            return jsonify({'success': -1, 'message': 'Client name  is required'}), 400

        # Query the affiliate to check if it exists
        affiliate = Affiliate.query.filter_by(client_name= client_name).first()

        if not affiliate:
            return jsonify({'success': -1, 'message': 'Affiliate not found'}), 404

        print("user_id:", user_id)

        # # Optional: Check if the current user has the authority to restrict the affiliate
        # if not int(affiliate.admin) == user_id:
        #     return jsonify({'success': -1, 'message': 'You are not authorized to restrict this affiliate'}), 403
        # Change the 'enabled' status to False
        affiliate.enabled = False if to_restrict == 1 else True

        reps = AffiliateRep.query.filter(AffiliateRep.affiliate_id==affiliate.id).all()
        for rep in reps:
            if(to_restrict==1):
                remove_all_tokens_to_avoid_missuse(rep.id)
        db.session.commit()
        # Return success response
        return jsonify({'success': 1, 'message': 'Affiliate restricted successfully'}), 200

    except Exception as e:
        print("Error restricting affiliate:", e)
        return jsonify({'success': -1, 'message': 'Failed to restrict affiliate', 'error': str(e)}), 500



@admin_affiliate.route('/api/admin/affiliate/restrict_affiliate_member', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
def restrict_affiliate_member():
    try:

        # Get the request data (affiliate ID)
        rep_id = request.form['rep_id']
        enabled = request.form["enabled"]
        rep_id=int(rep_id)
        enabled=int(enabled)
        rep = AffiliateRep.query.filter_by(id=rep_id).first()
        # Check if affiliate ID is provided
        if not rep:
            return jsonify({'success': -1, 'message': 'Rep Not Found'}), 400


        rep.enabled = False if enabled == 0 else True
        db.session.commit()
        if enabled==0:
            remove_all_tokens_to_avoid_missuse(rep_id)
        # Return success response
        return jsonify({'success': 1, 'message': 'Affiliate member restricted successfully'}), 200

    except Exception as e:
        print("Error restricting affiliate member:", e)
        return jsonify({'success': -1, 'message': 'Failed to restrict affiliate member', 'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/delete_draft', methods=['DELETE'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE], json=True)
@swag_from('/app/swagger_docs/affiliate/delete_affiliate_draft.yml')
def delete_draft_affiliate():
    try:
        draft_creator_id = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401

    data = request.get_json()
    draft_id = data.get("draft_id")

    if not draft_id:
        return jsonify({'success': -1, 'message': 'Draft ID is required'}), 400

    try:
        # Start a transaction block
        draft = DraftAffiliate.query.filter_by(id=draft_id).first()

        if not draft:
            return jsonify({'success': -1, 'message': 'Draft not found in RDBMS'}), 404

        if int(draft.draft_creator_id) != draft_creator_id:
            return jsonify({'success': -1, 'message': 'Sorry! you are not authorized to delete this Draft!'}), 403

        # Delete from RDBMS (but do NOT commit yet)
        db.session.delete(draft)

        # Try MongoDB deletion
        delete_result = AffiliateCollections.draft_affiliates.delete_one({"mysql_id": draft_id})
        if delete_result.deleted_count == 0:
            db.session.rollback()  # rollback SQL deletion
            return jsonify({'success': -1, 'message': 'Draft not found in MongoDB, SQL rollback done'}), 404

        # If MongoDB deletion succeeds, commit SQL
        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print("Error during deletion:", e)
        return jsonify({'success': -1, 'message': 'Deletion failed', 'error': str(e)}), 500

    data = {
        "draft_id": draft_id,
    }
    live_update_to_channel(data, room_name='affiliate', type='affiliate', region=0, channel='delete_draft')
    return jsonify({'success': 1, 'message': 'Draft deleted successfully'}), 200

@admin_affiliate.route('/api/admin/affiliate/get_draft_data', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/get_affiliate_draft_data.yml')
def get_draft_affiliate_data():
    data = request.get_json()
    draft_id = data.get('draft_id')

    if not draft_id:
        return jsonify({'error': 'client_name is required'}), 400

    # Redis failed or data not found, so fetch from MySQL and MongoDB
    try:
        # Fetch from MySQL
        draft_affiliate_sql = db.session.query(DraftAffiliate).filter_by(id = draft_id).first()
        if not draft_affiliate_sql:
            return jsonify({'error': 'Affiliate not found in SQL'}), 404

        affiliate_master = db.session.query(Affiliate).filter_by(id = draft_affiliate_sql.master).first()
        if affiliate_master:
            master_name = affiliate_master.client_name
            master_id = affiliate_master.id
        else:
            master_name = "Global"
            master_id = -1

        # Convert SQL data to dictionary format
        form_details = {
            "client_name": draft_affiliate_sql.client_name,
            "client_display_name": draft_affiliate_sql.display_name,
            "select_group": ["Global", "Local"],
            "client_logo":draft_affiliate_sql.logo,
            "city": draft_affiliate_sql.client_region
        }

        # Fetch additional data from MongoDB
        draft_affiliate_mongo = AffiliateCollections.draft_affiliates.find_one({'mysql_id': draft_id})
        if not draft_affiliate_mongo:
            return jsonify({"success": 0, "message": "Affiliate data not found in MongoDB"}), 404

        # Remove MongoDB `_id` field
        draft_affiliate_mongo.pop('_id', None)

        # Structure the combined affiliate data
        draft_affiliate_data = {
            "master": draft_affiliate_sql.master,
            "draft_creator": draft_affiliate_sql.draft_creator_id,
            "draft_title": draft_affiliate_sql.draft_title,
            "is_favourite": draft_affiliate_sql.is_favourite,
            "slaves": draft_affiliate_sql.slave['slaves'],
            "wallet_threshold": draft_affiliate_sql.wallet_threshold,
            "mapped_wallet_affiliate":  draft_affiliate_sql.mapped_wallet_affiliate,
            "form_field_oneway": draft_affiliate_mongo.get("form_field_oneway"),
            "form_field_round": draft_affiliate_mongo.get("form_field_round"),
            "form_details": form_details,
            "tripTypeLabel": draft_affiliate_mongo.get("tripTypeLabel", ""),
            "driverAvailVisEnabled": draft_affiliate_mongo.get("driverAvailVisEnabled", "0"),
            "maxBookDistAllowed": draft_affiliate_mongo.get("maxBookDistAllowed", "0"),
            "tripTypePlaceholder": draft_affiliate_mongo.get("tripTypePlaceholder", ""),
            "tripTypes": draft_affiliate_mongo.get("trip_type"),
        }


        # Fetch additional data from MongoDB
        if draft_affiliate_mongo:
            draft_affiliate_mongo.pop('_id', None)
        else:
            draft_affiliate_mongo = {}


        # Extract pricing-related data from MongoDB document
        # Extract pricing-related data from MongoDB document
        customer_pricing_data = {
            "sgst": draft_affiliate_mongo.get("sgst"),
            "cgst": draft_affiliate_mongo.get("cgst"),
            "oneway": draft_affiliate_mongo.get("customer_pricing_oneway"),
            "roundtrip": draft_affiliate_mongo.get("customer_pricing_roundtrip"),
            "outstationtrip": draft_affiliate_mongo.get("customer_pricing_outstationtrip")
        }

        driver_pricing_data = {
            "oneway": draft_affiliate_mongo.get("driver_pricing_oneway"),
            "roundtrip": draft_affiliate_mongo.get("driver_pricing_roundtrip"),
            "outstationtrip": draft_affiliate_mongo.get("driver_pricing_outstationtrip")
        }

        pricing_cancellation_data = draft_affiliate_mongo.get("pricing_cancellation_data", {})

        return jsonify({
            'success': 1,
            'source': 'mysql_mongo',
            'master': master_name,
            "master_id": master_id,
            'data': draft_affiliate_data,
            'draft_creator' : draft_affiliate_mongo.get("draft_creator","None"),
            'draft_id' : draft_affiliate_mongo.get("mysql_id"),
            'customer_pricing_data': customer_pricing_data,
            'driver_pricing_data': driver_pricing_data,
            'pricing_cancellation_data': pricing_cancellation_data
        }), 200

    except Exception as e:
        return jsonify({'success': -1,'error': 'Failed to retrieve affiliate data', 'details': str(e)}), 500



@admin_affiliate.route('/api/admin/affiliate/draft_list', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
@swag_from('/app/swagger_docs/affiliate/get_list_affiliate_draft.yml')
def get_draft_list():
    try:
        region = request.args.get('region')
        # Get the list of drafts with only the required fields
        drafts = DraftAffiliate.query.all()  # Fetch all drafts

        # Format the drafts for JSON response
        draft_list = []

        for draft in drafts:
            # Query the Users table to find the creator by draft_creator_id
            creator = db.session.query(Users).filter(Users.id == draft.draft_creator_id).first()

            # Ensure creator exists
            if creator:
                creator_name = creator.get_name()  # Assuming get_name() is a method in Users model
            else:
                creator_name = 'Unknown'  # Fallback in case creator is not found
            tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
            draft_list.append({
                "draft_title": draft.draft_title,
                "draft_id": draft.id,
                "draft_creator_name": creator_name,  # Add creator's name to the response
                "draft_reg": convert_to_local_time(draft.reg,tz).strftime('%d %b %Y, %H:%M'),
                "is_favourite": draft.is_favourite
            })
        draft_list.sort(key=lambda x: datetime.strptime(x['draft_reg'], '%d %b %Y, %H:%M'), reverse=True)
        return jsonify({'success': 1, 'drafts': draft_list}), 200

    except Exception as e:
        print("Error retrieving draft list:", e)
        return jsonify({'success': -1, 'message': 'Failed to retrieve draft list', 'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/change_favourite', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
@swag_from('/app/swagger_docs/affiliate/change_favourite.yml')
def change_favourite():
    try:
        # Get the user ID from the JWT identity
        user_id = get_jwt_identity()

        # Get the request data (draft ID)
        data = request.get_json()
        draft_id = data.get("draft_id")

        # Check if draft ID is provided
        if not draft_id:
            return jsonify({'success': -1, 'message': 'Draft ID is required'}), 400

        # Query the draft to check if it exists and belongs to the current user
        draft = DraftAffiliate.query.filter_by(id=draft_id).first()

        if not draft:
            return jsonify({'success': -1, 'message': 'Draft not found or does not belong to you'}), 404

        print("user_id: ",user_id)
        print("admin_isd: ", draft.draft_creator_id)

        if not int(draft.draft_creator_id) == user_id:
            return jsonify({'success': -1, 'message': 'Sorry! you are not authorized to use this Draft!'}), 403

        #change favouriteness
        draft.is_favourite = not draft.is_favourite
        db.session.commit()
        message=""
        if draft.is_favourite:
            message="Draft marked as favourite successfully"
        else:
            message="Draft unmarked as not favourite successfully"
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        data= {
                "draft_id": draft_id,
                "draft_title": draft.draft_title,
                "draft_creator_id": draft.draft_creator_id,
                "is_favourite": draft.is_favourite,
                "reg_time": convert_to_local_time(draft.reg,tz).strftime("%d %b, %y, %H:%M:%S")
        }
        live_update_to_channel(data, room_name='affiliate', type='affiliate', region=0, channel='fav_changed')

        # Return success response
        return jsonify({'success': 1, 'message': message}), 200

    except Exception as e:
        print("Error marking draft as favourite:", e)
        return jsonify({'success': -1, 'message': 'Failed to mark draft as favourite', 'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/register_rep', methods=['POST'])
@swag_from('/app/swagger_docs/affiliate_b2b/register_affiliate_representative.yml')
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
def affiliate_reg_rep():
    claims = get_jwt()
    if not complete(request.form, ['affiliate_id', 'username', 'mobile']):
        return jsonify({'success': -1, 'msg': "Incomplete form"})
    else:
        email = request.form.get('email', '').strip()
        mobile = request.form['mobile'].replace("+91", "")
        aff_id = request.form['affiliate_id']
        regions = request.form.get('regions')
        fullname = request.form.get("fullname")
        tab_access = request.form.get('tab_access')
        notifications = request.form.get('notifications')
        book_acc = request.form.get('selected_clients_book')
        username = request.form['username'].strip()
        tab_bitflags = sum(1 << int(tab) for tab in tab_access.split(',')) if tab_access else 0
        notification_bitflags=0
        existing_username = AffiliateRep.query.filter_by(user_name=username).first()
        if existing_username:
            return jsonify({'success': 0, 'message': 'This Username already exists please try different'}), 404
        if notifications:
            notification_bitflags = sum(1 << int(notif) for notif in notifications.split(',')) if notifications else 0

        pwd = "temp" + mobile
        if not validate_fields(mobile, email):
            return jsonify({'success': -1, 'message': "Invalid fields"})
        affiliate = Affiliate.query.filter_by(id=aff_id).first()
        if not affiliate:
            return jsonify({'success': -2, 'message': "Affiliate ID is invalid"}), 400

        # Validate regions against the specific affiliate's `client_region`
        valid_regions = {region.strip() for region in affiliate.client_region.split(',')} if affiliate.client_region else set()
        input_regions = {region.strip() for region in regions.split(',')} if regions else set()

        if not input_regions.issubset(valid_regions):
            return jsonify({
                'success': -3,
                'message': f"Invalid regions for affiliate member"
            }), 400
        try:
            aff_rep = AffiliateRep(aff_id, fullname, username, mobile, pwd, claims['id'], notification_bitflags, tab_bitflags, regions,create_booking_access=book_acc, email=email, enabled=True)
            db.session.add(aff_rep)
            db.session.flush()
            create_rep_access(aff_rep.id, aff_id, tab_bitflags, notification_bitflags, input_regions)
            tab_admin_log = AffiliateRepLogs(
                    rep_id=aff_rep.id,
                    action=AffiliateRepLogs.AFFILIATE_REP_CREATED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Tab Access",
                    oldvalue=None,
                    newvalue=tab_access,
            )
            region_admin_log = AffiliateRepLogs(
                rep_id=aff_rep.id,
                action=AffiliateRepLogs.AFFILIATE_REP_CREATED,
                changed_by=claims['id'],
                changed_by_name=claims['name'],
                changes_made="Regions Access",
                oldvalue=None,
                newvalue=regions,
            )
            notif_admin_log = AffiliateRepLogs(
                rep_id=aff_rep.id,
                action=AffiliateRepLogs.AFFILIATE_REP_CREATED,
                changed_by=claims['id'],
                changed_by_name=claims['name'],
                changes_made="Notification Access",
                oldvalue=None,
                newvalue=notifications,
            )
            book_acc_log = AffiliateRepLogs(
                rep_id=aff_rep.id,
                action=AffiliateRepLogs.AFFILIATE_REP_CREATED,
                changed_by=claims['id'],
                changed_by_name=claims['name'],
                changes_made="Create Booking Aff Access",
                oldvalue=None,
                newvalue=book_acc,
            )
            db.session.bulk_save_objects([tab_admin_log, region_admin_log, notif_admin_log,book_acc_log])
            db.session.commit()
            return jsonify({'success': 1, 'message': "Affiliate Member Registered Successfully"}),200

        except Exception as e:
            db.session.rollback()
            print(f"An unexpected error occurred: {e}")
            return jsonify({'success': -5, 'message': "An unexpected error occurred"}),500


def create_rep_access(rep_id, aff_id, tab, notification, regions):
    preferences = {}
    visited_affiliates = set()
    current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_id).first()

    preferences[str(current_affiliate.id)] = notification
    slaves = current_affiliate.slave.get("slaves", [])

    for slave_id in slaves:
        preferences[str(slave_id)] = notification

    while current_affiliate and current_affiliate.master != -1:
        current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == current_affiliate.master).first()
        if current_affiliate.id not in visited_affiliates:
            preferences[str(current_affiliate.id)] = notification
            visited_affiliates.add(current_affiliate.id)

    rep_doc = {
        "rep_id": rep_id,
        "aff_id": aff_id,
        "tab": tab,
        "notification": notification,
        "regions": list(regions),
        "preference": preferences
    }

    result = AffiliateCollections.rep_access.update_one(
        {"rep_id": rep_id},
        {"$set": rep_doc},
        upsert=True
    )

    return {"success": 1, "message": "Document updated" if result.modified_count else "Document inserted"}



@admin_affiliate.route('/api/admin/affiliate/all_affiliate_rep', methods=['POST'])
@swag_from('/app/swagger_docs/affiliate/affiliate_rep_list.yml')
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
def all_affiliate_rep():
    rep_id = request.form.get('rep_id', '')
    search_query = request.form.get('search_query', '')
    affiliate_id = request.form.get('affiliate_id', '')
    restricted =  request.form.get('restricted', 0, type=int)
    enabled = True if restricted == 0 else False
    try:
        query = db.session.query(
        AffiliateRep.id,
        AffiliateRep.user_name,
        AffiliateRep.fullname,
        AffiliateRep.mobile,
        AffiliateRep.email,
        Affiliate.client_name,
        AffiliateRep.notification_access,
        AffiliateRep.tab_access,
        AffiliateRep.region_access,
        AffiliateRep.create_booking_access,
        AffiliateRep.enabled.label("rep_enabled"),
        Affiliate.enabled.label("affiliate_enabled"),
        ).join(Affiliate, AffiliateRep.affiliate_id == Affiliate.id)
        if restricted == 1:
            query = query.filter(
                db.or_(
                    Affiliate.enabled == False,  # Either Affiliate is disabled
                    AffiliateRep.enabled == False  # OR AffiliateRep is disabled
                )
            )
        else:
            query = query.filter(
                Affiliate.enabled == enabled,  # Both enabled values must match
                AffiliateRep.enabled == enabled
            )


        if rep_id:
            query = query.filter(AffiliateRep.id == rep_id)
        elif search_query:
            query = query.filter(
                (AffiliateRep.user_name.ilike(f"%{search_query}%")) |
                (AffiliateRep.mobile.ilike(f"%{search_query}%"))
            )

        if affiliate_id:
            affiliate_ids = [int(id.strip()) for id in affiliate_id.split(',') if id.strip().isdigit()]
            query = query.filter(AffiliateRep.affiliate_id.in_(affiliate_ids))

        reps = query.all()
        reps_list = [
            {
                "rep_id": rep.id,
                "rep_fullname":rep.fullname if rep.fullname else "",
                "rep_username": rep.user_name,
                "rep_mobile": rep.mobile,
                "rep_email": rep.email,
                "aff_name": rep.client_name,
                'aff_notification': (
                    ','.join(str(i) for i in range(128) if rep.notification_access & (1 << i))
                    if rep.notification_access is not None else None
                ),
                'aff_tab': (
                    ','.join(str(i) for i in range(128) if rep.tab_access & (1 << i))
                    if rep.tab_access is not None else None
                ),
                'aff_regions':rep.region_access,
                'selected_clients_book':rep.create_booking_access,
                "aff_approved": (
                True if rep.affiliate_enabled and rep.rep_enabled else False
            ),
            }
            for rep in reps
        ]

        return jsonify({'success': 1, 'data': reps_list}),200
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message': 'Internal Server Error'}),500

@admin_affiliate.route('/api/admin/affiliate/update_affiliate_rep', methods=['POST'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_rep_update.yml')
@check_access(tab_required=[Tabs.AFFILIATE])
def update_affiliate_rep():
    try:
        claims = get_jwt()
        if not complete(request.form, ['rep_id']):
            return jsonify({'success': -1, 'msg': "Incomplete form"}),400
        data = request.form
        rep_id = data.get('rep_id')
        mobile = data.get('mobile')
        regions = data.get('regions')
        book_acc = request.form.get('selected_clients_book')
        aff_id = data.get('affiliateId')
        tab_access = data.get('tab_access')
        notifications = data.get('notifications')
        username = data.get('username')
        fullname = data.get('fullname')
        email = data.get('email', '')
        restricted = data.get('restricted')
        if restricted is not None:
            restricted=int(restricted)
        rep_details = AffiliateRep.query.filter_by(id=rep_id).first()
        if not rep_details:
            return jsonify({'success': 0, 'message': 'Affiliate Rep not found'}), 400
        if username:
            existing_username = AffiliateRep.query.filter_by(user_name=username).first()
            if existing_username:
                return jsonify({'success': 0, 'message': 'This Username already exists please try different'}), 404
            rep_details.user_name = username.strip()
        if fullname:
            rep_details.fullname = fullname
        if email:
            rep_details.email = email
        if mobile:
            rep_exists = AffiliateRep.query.filter_by(mobile=mobile).first()
            if rep_exists:
                return jsonify({'success': 0, 'message': 'Mobile number already exists for Rep'}), 400
            rep_details.mobile = mobile
        if aff_id:
            affiliate = Affiliate.query.filter_by(id=aff_id).first()
            old_affiliate = Affiliate.query.filter_by(id=rep_details.affiliate_id).first()
            if not affiliate:
                return jsonify({'success': -2, 'message': "Affiliate ID is invalid"}), 400
            valid_regions = {region.strip() for region in affiliate.client_region.split(',')} if affiliate.client_region else set()
            if regions:
                input_regions = {region.strip() for region in regions.split(',')} if regions else set()
            else:
                input_regions = {region.strip() for region in rep_details.region_access.split(',')} if rep_details.region_access else set()
            if not input_regions.issubset(valid_regions):
                return jsonify({
                    'success': -3,
                    'message': f"Invalid regions for affiliate member",
                    "region":list(input_regions),
                    "p_regions": list(valid_regions)
                }), 400
            rep_details.affiliate_id=int(aff_id)
            aff_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Affiliate",
                    oldvalue=old_affiliate.client_name,
                    newvalue=affiliate.client_name,
            )
            db.session.add(aff_log)

        if restricted!=None:
            rep_details.enabled = False if restricted == 1 else True
            if restricted==1:
                remove_all_tokens_to_avoid_missuse(rep_id)
                rep_details.tab_access = 0
                rep_details.notification_access = 0
                restrict_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Restriction",
                    oldvalue="UnRestricted",
                    newvalue="Restricted",
                )
                db.session.add(restrict_log)
                db.session.commit()
                return jsonify({'success': 1, 'message': 'Affiliate Rep updated and logged successfully'}), 200
            else:
                restrict_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Restriction",
                    oldvalue="Restricted",
                    newvalue="UnRestricted",
                )
        if regions is not None:
            if isinstance(regions, int):  # Convert to string if passed as an integer
                regions = str(regions)
            if aff_id:
                affiliate = Affiliate.query.filter_by(id=aff_id).first()
                valid_regions = {region.strip() for region in affiliate.client_region.split(',')} if affiliate.client_region else set()
                input_regions = {region.strip() for region in regions.split(',')} if regions else set()
                if not input_regions.issubset(valid_regions):
                    return jsonify({
                        'success': -3,
                        'message': f"Invalid regions for affiliate member"
                    }), 400
            region_admin_log = AffiliateRepLogs(
                rep_id=rep_id,
                action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                changed_by=claims['id'],
                changed_by_name=claims['name'],
                changes_made="Regions Access",
                oldvalue=f"{rep_details.region_access}",
                newvalue=f"{regions}"
            )
            db.session.add(region_admin_log)
            rep_details.region_access=regions
        old_tab_access = ','.join(str(i) for i in range(128) if rep_details.tab_access & (1 << i))
        old_notification_access = ','.join(str(i) for i in range(128) if rep_details.notification_access & (1 << i))
        if tab_access:
            new_tab_bitflags = 0 if tab_access == '-1' else sum(1 << int(tab) for tab in tab_access.split(','))
            rep_details.tab_access = new_tab_bitflags
            tab_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Tab Access",
                    oldvalue=f"{old_tab_access}",
                    newvalue=f"{tab_access}",
            )
            db.session.add(tab_log)
            if  str(AffiliateMembersTabs.CREATE_BOOKING) not in tab_access.split(','):
                old_book_acc = rep_details.create_booking_access
                rep_details.create_booking_access = None
                book_acc_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Create Booking Aff Access",
                    oldvalue=f"{old_book_acc}",
                    newvalue="None",
                )
                db.session.add(book_acc_log)
        if notifications:
            new_notification_bitflags = 0 if notifications == '-1' else sum(1 << int(notif) for notif in notifications.split(','))
            rep_details.notification_access = new_notification_bitflags
            notif_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Notification Access",
                    oldvalue=f"{old_notification_access}",
                    newvalue=f"{notifications}",
            )
            db.session.add(notif_log)
        if (book_acc!=rep_details.create_booking_access) and book_acc and (not tab_access or str(AffiliateMembersTabs.CREATE_BOOKING) in tab_access.split(',')):
            old_book_acc = rep_details.create_booking_access
            rep_details.create_booking_access = book_acc
            book_acc_log = AffiliateRepLogs(
                    rep_id=rep_id,
                    action=AffiliateRepLogs.AFFILIATE_REP_EDITED,
                    changed_by=claims['id'],
                    changed_by_name=claims['name'],
                    changes_made="Create Booking Aff Access",
                    oldvalue=f"{old_book_acc}",
                    newvalue=f"{book_acc}",
            )
            db.session.add(book_acc_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Affiliate Rep updated and logged successfully'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': 0, 'message': str(e)}), 500

@admin_affiliate.route('/api/admin/affiliate/add_spoc', methods=['POST'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_rep_add_spoc.yml')
@check_access(tab_required=[Tabs.AFFILIATE], json=True)
def add_new_spoc():
    try:
        admin_id = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': "Unauthorized"}), 401
    try:
        data = request.get_json()

        rep_id = data.get('rep_id')
        if not rep_id:
            return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400
        rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
        if not rep:
            return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

        aff_id = rep.affiliate_id

        # Retrieve spocDetails from the input data.
        spoc_details = data.get("spocDetails")
        if spoc_details is None:
            raise ValueError("SPOC details not provided")

        process_spocs(spoc_details, rep_id, aff_id)

        db.session.commit()
        new_spoc_log = SpocLogs(
            admin_id= admin_id,
            add_source=SpocLogs.ADDED_BY_ADMIN,  # Using the constant for representative source
            spoc_state=SpocLogs.SPOC_ADD,                # Using the constant for "update" state
        )
        db.session.add(new_spoc_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'SPOC data inserted successfully'})
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -6, 'message': str(e)}), 500




@admin_affiliate.route('/api/admin/affiliate/get_update_affiliate_rep', methods=['POST'])
@swag_from('/app/swagger_docs/affiliate/affiliate_get_update_affiliate_rep.yml')
def get_update_affiliate_rep():
    if not complete(request.form, ['type', 'rep_id']):
        return jsonify({'success': -1, 'msg': "Incomplete form"})

    # 1 for get, 2 for update, 3 for delete
    type = int(request.form['type'])
    rep_id = request.form['rep_id']

    if type == 1:
        # Fetch affiliate representative details
        rep = db.session.query(AffiliateRep, Affiliate).filter(
            AffiliateRep.id == rep_id,
            Affiliate.id == AffiliateRep.affiliate_id
        ).first()

        if not rep:
            return jsonify({'success': -1, 'msg': "Affiliate representative not found"})

        rep_data = {
            'affiliate_name': rep[1].client_name,
            'affiliate_id': rep[1].id,
            'username': rep[0].user_name,
            'mobile': rep[0].mobile,
            'email': rep[0].email
        }
        return jsonify({'success': 1, 'data': rep_data})

    elif type == 2:
        # Update affiliate representative details
        new_mobile = request.form.get('mobile')
        new_email = request.form.get('email')
        new_name = request.form.get('username')
        new_affiliate_id = request.form.get('affiliate_id')
        enabled = request.form.get('enabled')

        rep = AffiliateRep.query.filter_by(id=rep_id).first()

        if not rep:
            return jsonify({'success': -1, 'msg': "Affiliate representative not found"})

        if new_mobile:
            rep.mobile = new_mobile
        if new_email:
            rep.email = new_email
        if new_name:
            rep.user_name = new_name
        if new_affiliate_id:
            rep.affiliate_id = new_affiliate_id
        if enabled:
            rep.enabled = enabled

        try:
            db.session.commit()
            return jsonify({'success': 1, 'msg': "Affiliate representative updated successfully"})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': -1, 'msg': f"Failed to update affiliate representative: {str(e)}"})

    elif type == 3:
        # Delete affiliate representative
        rep = AffiliateRep.query.filter_by(id=rep_id).first()

        if not rep:
            return jsonify({'success': -1, 'msg': "Affiliate representative not found"})

        try:
            db.session.delete(rep)
            db.session.commit()
            return jsonify({'success': 1, 'msg': "Affiliate representative deleted successfully"})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': -1, 'msg': f"Failed to delete affiliate representative: {str(e)}"})

    return jsonify({'success': -1, 'msg': "Invalid type provided"})


@admin_affiliate.route('/api/admin/affiliate/get_affiliate_rep_logs', methods=['GET'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_rep_logs_list.yml')
@check_access(tab_required=[Tabs.AFFILIATE])
def list_affiliate_rep_logs():
    try:
        rep_id = request.args.get('rep_id')
        if not rep_id:
            return jsonify({'success': 0, 'message': 'rep_id is required'}), 400

        logs_query = db.session.query(
            AffiliateRepLogs.rep_id,
            AffiliateRepLogs.action,
            AffiliateRepLogs.changed_by,
            AffiliateRepLogs.changed_by_name,
            AffiliateRepLogs.changes_made,
            AffiliateRepLogs.created_at,
            AffiliateRepLogs.oldvalue,
            AffiliateRepLogs.newvalue).filter(AffiliateRepLogs.rep_id == rep_id) \
         .order_by(desc(AffiliateRepLogs.created_at))

        log_list = []
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        for log in logs_query:
            log_list.append({
                'rep_id': log.rep_id,
                'action': 'Created' if log.action == 0 else 'Edited',
                'changed_by': log.changed_by,
                'changed_by_name': log.changed_by_name,
                'changes_made': log.changes_made,
                'created_at': convert_to_local_time(log.created_at,tz).strftime('%d %b %Y, %H:%M'),
                'oldvalue': log.oldvalue if log.oldvalue else "N/A",
                'newvalue': log.newvalue if log.newvalue else "N/A",
            })

        return jsonify({'success': 1, 'logs': log_list}), 200
    except Exception as e:
        print(e,flush=True)
        return jsonify({'success': 0, 'message': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/affiliate_credit', methods=['POST'])
@swag_from('/app/swagger_docs/affiliate/add_affiliate_credit.yml')
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def add_affiliate_credit():
    admin_user = get_jwt_identity()
    admin = db.session.query(Users).filter(Users.id == admin_user).first()
    admin_name = admin.get_name()
    if not complete(request.form, ['affiliate_id', 'amount', 'remark', 'credit_type']):
        return jsonify({'success': -2,'message':"Enter all the required fields"}), 500
    affiliate_id = request.form['affiliate_id']
    try:
        amount = int(request.form['amount']) * 100
        raw_amt = int(request.form['amount'])
    except Exception as e:
        amount = float(request.form['amount']) * 100
        raw_amt = float(request.form['amount'])
    remark = request.form['remark']
    credit_type = request.form['credit_type']
    gst_status = request.form['gst_status']
    trans_id = request.form.get('trans_id')
    gst_number = request.form.get('gst_number')
    tds_enabled = request.form.get('tds_enabled')
    transfer_affiliate = request.form.get('transfer_affiliate')
    credit_type_reason = str(get_safe(request.form, 'credit_type_reason', "")).strip()

    if tds_enabled:
        tds_enabled = True if tds_enabled == "1" else False

    if not credit_type_reason and credit_type in ["Fine","Credit Note"]:
        return jsonify({'success': -2, 'message': 'Fine reason is mandatory for Fines'})

    affiliate = db.session.query(Affiliate).filter(Affiliate.id == int(affiliate_id)).first()
    if not affiliate:
        return jsonify({'success': 0, 'message': 'Affiliate not found'}), 404
    affiliate_mongo = AffiliateCollections.affiliates_details.find_one({'client_name': affiliate.client_name})
    if not affiliate_mongo:
            return jsonify({"success": 0, "message": "Affiliate data not found in MongoDB"}), 404
    cgst_percent = affiliate_mongo.get("cgst",AffiliateTaxTrans.DEFAULT_CGST_PERCENT)
    sgst_percent = affiliate_mongo.get("sgst",AffiliateTaxTrans.DEFAULT_SGST_PERCENT)
    desc = ""
    from_account=None
    to_account=None
    match_found = False
    if gst_number:
        state_code_str = gst_number[:2]
        try:
            state_code_int = int(state_code_str)
        except ValueError:
            state_code_int = None

        # Optionally, fetch the state name from your mapping if needed:
        # state_name = GSTStateChecker.GST_STATE_CODES.get(state_code_str)

        existing_gst = None
        if state_code_int is not None:
            existing_gst = ClientGstDetails.query.filter(
                ClientGstDetails.client_id == affiliate.id,
                ClientGstDetails.client_gst_no == gst_number
            ).first()
        if not existing_gst:
            gst_url = "https://api.gridlines.io/gstin-api/fetch-detailed"
            gst_payload = {"gstin": gst_number, "consent": "Y"}
            gst_headers = {
                "X-Auth-Type": "API-Key",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-API-Key": app.config['GRID_LINES_KEY']
            }
            gst_response = requests.post(gst_url, json=gst_payload, headers=gst_headers)
            gst_response_data = gst_response.json()
            if gst_response.status_code == 200:
                # Assuming a code of "1000" means the API call was successful
                if gst_response_data.get('data', {}).get('code') == "1000":
                    gst_details = gst_response_data.get('data', {}).get('gstin_data', {})
                    client_gst_details = ClientGstDetails(
                        client_id=affiliate.id,
                        client_gst_no=gst_details.get("document_id", gst_number),
                        state_code=int(state_code_str),
                        address=gst_details.get("principal_address").get("address"),
                        legal_name=gst_details.get("legal_name"),
                        trade_name=gst_details.get("trade_name"),
                        email=gst_details.get("principal_address").get("email"),
                        phone_no=gst_details.get("principal_address").get("mobile"),
                    )
                    db.session.add(client_gst_details)
                elif gst_response_data.get('data', {}).get('code') == "1005":
                    # Optionally log or handle unsuccessful GST fetch (e.g., code not "1000")
                    return jsonify({'success': 0, 'message': 'GST Number not found'}), 404
                else:
                    return jsonify({'success': 0, 'message': 'Unknown error while fetching GST details'}), 404
            else:
                print(gst_response_data,flush=True)
                return jsonify({'success': 0, 'message': 'Error fetching GST details from Gridlines, Please try after some time'}), 404
        match_found = any(state_code_str == d4m[:2] for d4m in GSTStateChecker.D4M_GST_NUMBER)

    if match_found or not gst_number:
        # Intra-state: Calculate 9% CGST and 9% SGST, total 18% of raw_amt.
        if gst_status == "Included":
            if tds_enabled:
                cgst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)) - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)) * (int(cgst_percent)/100)
                sgst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)) - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)) * (int(sgst_percent)/100)
                total_gst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)) - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)) * ((int(cgst_percent) + int(sgst_percent))/100)
            else:
                cgst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)))) * (int(cgst_percent)/100)
                sgst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)))) * (int(sgst_percent)/100)
                total_gst_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)))) * ((int(cgst_percent) + int(sgst_percent))/100)
        else:
            cgst_amt = raw_amt * (int(cgst_percent)/100)
            sgst_amt = raw_amt * (int(sgst_percent)/100)
            total_gst_amt = raw_amt * ((int(cgst_percent) + int(sgst_percent))/100)
        igst_amt = 0.0
        sender_gst_no = gst_number if gst_number else None  # Use payer GST as receiver if exists
        if match_found:
            # Retrieve the matching GST code from our predefined list
            matched_code = next((d4m for d4m in GSTStateChecker.D4M_GST_NUMBER if state_code_str == d4m[:2]), None)
            receiver_gst_no = matched_code
        else:
            receiver_gst_no = GSTStateChecker.D4M_KOLKATA_GST_NUMBER # or leave it as gst_number if you prefer
    else:
        cgst_amt = 0.0
        sgst_amt = 0.0
        if gst_status == "Included":
            if tds_enabled:
                igst_amt = ((raw_amt * 100) / (100 + AffiliateTaxTrans.DEFAULT_IGST_PERCENT - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)) * (AffiliateTaxTrans.DEFAULT_IGST_PERCENT/100)
                total_gst_amt = ((raw_amt * 100) / (100 + AffiliateTaxTrans.DEFAULT_IGST_PERCENT - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)) * (AffiliateTaxTrans.DEFAULT_GST_PERCENT/100)
            else:
                igst_amt = ((raw_amt * 100) / (100 + AffiliateTaxTrans.DEFAULT_IGST_PERCENT)) * (AffiliateTaxTrans.DEFAULT_IGST_PERCENT/100)
                total_gst_amt = ((raw_amt * 100) / (100 + AffiliateTaxTrans.DEFAULT_IGST_PERCENT)) * (AffiliateTaxTrans.DEFAULT_GST_PERCENT/100)
        else:
            igst_amt = raw_amt * (AffiliateTaxTrans.DEFAULT_IGST_PERCENT/100)
            total_gst_amt = raw_amt * (AffiliateTaxTrans.DEFAULT_GST_PERCENT/100)
        receiver_gst_no =  GSTStateChecker.D4M_KOLKATA_GST_NUMBER  # Use payer GST as receiver if exists
        sender_gst_no = gst_number if gst_number else None  # Kolkata GST as sender
    try:
        if credit_type == "Transfer":
            receiver_affiliate = db.session.query(Affiliate).filter_by(id=transfer_affiliate).first()
            if not receiver_affiliate:
                return jsonify({'success': -6, "message": "Reciever affiliates not found"}), 404

            if affiliate.wallet < raw_amt:
                return jsonify({'success': -7, "message": "Insufficient balance in the sender's wallet"}), 400
            sender_wallet_before = affiliate.wallet
            receiver_wallet_before = receiver_affiliate.wallet

            sender_wallet_after = sender_wallet_before - raw_amt
            receiver_wallet_after = receiver_wallet_before + raw_amt

            affiliate.wallet = sender_wallet_after
            receiver_affiliate.wallet = receiver_wallet_after

            # Create wallet logs for sender and receiver
            sender_wallet_log = AffiliateWalletLogs(-raw_amt*100, method="Transfer",
                from_account=affiliate.id, to_account=receiver_affiliate.id, wallet_before=sender_wallet_before,
                wallet_after=sender_wallet_after,source=AffiliateWalletLogs.SOURCE_ADMIN,admin=admin_user,remark=remark)

            receiver_wallet_log = AffiliateWalletLogs(raw_amt*100, method="Transfer",
                from_account=affiliate.id, to_account=receiver_affiliate.id, wallet_before=receiver_wallet_before,
                wallet_after=receiver_wallet_after,source=AffiliateWalletLogs.SOURCE_ADMIN,admin=admin_user,remark=remark)

            db.session.bulk_save_objects([sender_wallet_log, receiver_wallet_log])
            db.session.commit()
            return jsonify({'success': 1,  'message': f'Affiliate credits transferred successfully to {receiver_affiliate.client_name} account.'}), 200
        else:
            if credit_type in ["Add", "Credit Note"]:
                action = "added to"
                if credit_type == "Add" and gst_status == "Included":
                    if tds_enabled:
                        base_raw_amt = (raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)) - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)
                        amount = (amount * 100) / (100 + (int(cgst_percent) + int(sgst_percent)) - AffiliateTaxTrans.DEFAULT_TDS_PERCENT)
                    else:
                        base_raw_amt = (raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)))
                        amount = (amount * 100) / (100 + (int(cgst_percent) + int(sgst_percent)))
                else:
                    base_raw_amt = raw_amt
                    amount = amount
                to_account = affiliate.id
                affiliate_credit = affiliate.wallet + base_raw_amt
            else:
                action = "deducted from"
                amount=-amount
                from_account = affiliate.id
                affiliate_credit = affiliate.wallet - raw_amt
            aff_trans = AffiliateWalletLogs(amt=amount,method=credit_type,from_account=from_account,to_account=to_account,
                                wallet_before=affiliate.wallet,wallet_after=affiliate_credit,source=AffiliateWalletLogs.SOURCE_ADMIN,admin=admin_user,reason=credit_type_reason,remark=remark,pay_id=trans_id)
            db.session.add(aff_trans)
            db.session.flush()
            affiliate.wallet=affiliate_credit
            if credit_type == "Add":
                if tds_enabled:
                    if gst_status == "Included":
                        tds_amt = ((raw_amt * 100) / (100 + (int(cgst_percent) + int(sgst_percent)- AffiliateTaxTrans.DEFAULT_TDS_PERCENT))) * (AffiliateTaxTrans.DEFAULT_TDS_PERCENT/100)
                        total_tds_amt = tds_amt
                    else:
                        tds_amt = raw_amt * (AffiliateTaxTrans.DEFAULT_TDS_PERCENT/100)
                        total_tds_amt = tds_amt
                else:
                    tds_amt = 0.0
                    total_tds_amt = 0.0
                tax_trans = AffiliateTaxTrans(
                    trans_id=aff_trans.id,
                    payer_gst_no=sender_gst_no,
                    receiver_gst_no=receiver_gst_no,
                    cgst_amt=cgst_amt,
                    sgst_amt=sgst_amt,
                    igst_amt=igst_amt,
                    total_gst_amt=total_gst_amt,
                    total_tds_amt=total_tds_amt
                )
                db.session.add(tax_trans)
            db.session.commit()
            return jsonify({'success': 1,  'message': f'Affiliate credits successfully {action} the account.'}), 200
    except Exception as exc:
        print(exc)
        return jsonify({'success': -1,'message': str(exc)})


@admin_affiliate.route('/api/admin/affiliate/spoc_list_rep', methods=['GET'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_spoc_list_rep.yml')
@check_access(tab_required=[Tabs.AFFILIATE])
def get_all_spocs():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    rep_id = request.args.get('rep_id')

    if not rep_id:
        return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400

    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

    aff_id = rep.affiliate_id
    try:
        # Fetch local SPOCs using the helper function
        local_spocs = get_local_spocs(rep_id, aff_id)
        # Extract mobile numbers from the local SPOCs
        local_mobile_numbers = {spoc.spoc_mobile for spoc in local_spocs}

        # Fetch global SPOCs using the helper function
        global_spocs = get_global_spocs(aff_id, local_mobile_numbers)

        # Combine the results
        all_spocs = local_spocs + global_spocs

        # Serialize the results for response
        spoc_list = [
            {
                'spoc_id': spoc.spoc_id,
                'spoc_name': spoc.spoc_name,
                'spoc_number': spoc.spoc_mobile,
                'spoc_type': spoc.spoc_type,
                'spoc_region': spoc.region
            }
            for spoc in all_spocs
        ]

        return jsonify({'success': 1, 'spocs': spoc_list}), 200

    except Exception as e:
        return jsonify({'success': -1, 'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/update_spoc', methods=['PUT'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_update_spoc.yml')
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
def update_spoc_data():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'message': "Unauthorized"}), 401

    data = request.get_json()
    rep_id = data.get('rep_id')
    if not rep_id:
        return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400

    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404
    aff_id = rep.affiliate_id

    try:
        # Retrieve spocDetails from the input.
        spoc_details = data.get("spoc")

        spoc_id = process_update_spoc(spoc_details, rep_id, aff_id)
        db.session.commit()
        new_spoc_log = SpocLogs(
            admin_id= admin_id,
            add_source=SpocLogs.ADDED_BY_ADMIN,  # Using the constant for representative source
            spoc_state=SpocLogs.SPOC_UPDATE,                # Using the constant for "update" state
        )
        db.session.add(new_spoc_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'SPOC updated successfully', 'spoc_id': spoc_id}), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': 0, 'error': str(e)}), 500



@admin_affiliate.route('/api/admin/affiliate/delete_spoc', methods=['DELETE'])
@swag_from('/app/swagger_docs/affiliate_profile/delete_spoc.yml')
@jwt_required()
def delete_spoc_data():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['spoc_id']):
        return jsonify({'success': 0, 'message': "Missing spoc id"}), 400

    try:
        spoc_id = int(request.form['spoc_id'])
        process_delete_spoc(spoc_id)
        db.session.commit()
        new_spoc_log = SpocLogs(
            admin_id= admin_id,
            add_source=SpocLogs.ADDED_BY_ADMIN,  # Using the constant for representative source
            spoc_state=SpocLogs.SPOC_DELETE,                # Using the constant for "update" state
        )
        db.session.add(new_spoc_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'SPOC deleted successfully', 'spoc_id': spoc_id}), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 404
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/add_address', methods=['POST'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_add_address.yml')
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
def add_new_address():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    data = request.get_json()
    rep_id=data.get('rep_id')
    if not rep_id:
        return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

    aff_id = rep.affiliate_id
    address_data= data.get("address")
    try:
        # Here data is expected to be the list of address entries.
        process_addresses(address_data, rep_id, aff_id)
        db.session.commit()
        new_address_log = AddressLogs(
            admin_id= admin_id,
            add_source=AddressLogs.ADDED_BY_ADMIN,   # Using the constant for admin source
            address_state=AddressLogs.ADDRESS_ADD          # Using the constant for "add" state
        )
        db.session.add(new_address_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Addresses added successfully'}), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -3, 'message': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/address_list', methods=['GET'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_address_list.yml')
@check_access(tab_required=[Tabs.AFFILIATE])
def get_all_addresses():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    rep_id = request.args.get('rep_id')
    if not rep_id:
        return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

    aff_id = rep.affiliate_id
    try:
        local_addresses = get_local_addresses(rep_id)
        local_nicknames = {address.nickname for address in local_addresses}
        global_addresses = get_global_addresses(aff_id, local_nicknames)
        all_addresses = local_addresses + global_addresses

        address_list = []
        for address in all_addresses:
            # Fetch associated SPOCs
            spocs = db.session.query(AddressSpoc).filter(
                AddressSpoc.address_id == address.add_id
            ).all()
            spoc_data = [{'spoc_id': spoc.spoc_id, 'spoc_name': spoc.spoc_name} for spoc in spocs]
            address_list.append({
                'add_id': address.add_id,
                'address': address.address,
                'nickname': address.nickname,
                'region': address.region,
                'lat': address.latitude,  # Adjust field names if necessary
                'long': address.longitude,
                'add_type': address.address_type,
                'spocs': spoc_data
            })

        return jsonify({'success': 1, 'addresses': address_list}), 200
    except Exception as e:
        return jsonify({'success': -1, 'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/update_address', methods=['PUT'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_update_address.yml')
@check_access(tab_required=[Tabs.AFFILIATE],json=True)
def update_address_data():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    data = request.get_json()
    rep_id=data.get('rep_id')
    if not rep_id:
        return jsonify({'success': 0, 'message': "Affiliate representative ID is required"}), 400

    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

    aff_id = rep.affiliate_id
    address_data= data.get("address")
    try:
        process_update_address(address_data, rep_id, aff_id)
        db.session.commit()
        new_address_log = AddressLogs(
            admin_id= admin_id,
            add_source=AddressLogs.ADDED_BY_ADMIN,   # Using the constant for admin source
            address_state=AddressLogs.ADDRESS_UPDATE         # Using the constant for "add" state
        )
        db.session.add(new_address_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Addresses updated successfully'}), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -3, 'message': str(e)}), 500



@admin_affiliate.route('/api/admin/affiliate/delete_address', methods=['DELETE'])
@jwt_required()
@swag_from('/app/swagger_docs/affiliate/affiliate_delete_address.yml')
@check_access(tab_required=[Tabs.AFFILIATE])
def delete_address_data():
    try:
        admin_id = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['address_id']):
        return jsonify({'success': 0, 'message': "Missing address id"}), 400

    try:
        address_id = int(request.form['address_id'])
        process_delete_address(address_id)
        db.session.commit()
        new_address_log = AddressLogs(
            admin_id= admin_id,
            add_source=AddressLogs.ADDED_BY_ADMIN,   # Using the constant for admin source
            address_state=AddressLogs.ADDRESS_DELETE         # Using the constant for "add" state
        )
        db.session.add(new_address_log)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Address deleted successfully'}), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({'success': -1, 'message': str(ve)}), 404
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@admin_affiliate.route('/api/admin/affiliate/wallet_logs', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE])
@swag_from('/app/swagger_docs/affiliate/affiliate_wallet_logs.yml')
def admin_affiliate_logs():
    # Get parameters from query string
    affiliate_id = request.args.get('affiliate_id')
    if not affiliate_id:
        return jsonify({'success': -1, 'message': 'Missing required parameter: affiliate_id'}), 400

    try:
        affiliate_id = int(affiliate_id)
    except ValueError:
        return jsonify({'success': -1, 'message': 'Invalid affiliate_id, must be an integer'}), 400

    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    trans_type = request.args.get('trans_type')
    page = request.args.get('page', '1')
    limit = request.args.get('limit', '25')
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')

    try:
        page = int(page)
        limit = int(limit)
    except ValueError:
        return jsonify({'success': -1, 'message': 'Invalid page or limit value'}), 400
    result = fetch_affiliate_wallet_logs(affiliate_id, start_date, end_date, trans_type, page, limit, tz)
    status_code = 200 if result.get("success") == 1 else 400
    return jsonify(result), status_code


@admin_affiliate.route('/api/admin/affiliate/details', methods=['POST'])
# @swag_from('/app/swagger_docs/affiliate_b2b/get_affiliate_details.yml')
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE, Tabs.CREATE_BOOKINGS])
@swag_from('/app/swagger_docs/affiliate/affiliate_details.yml')
def get_affiliate_details_admin():
    if not complete(request.form, ['affiliate_id', 'type']):
        return jsonify({'success': -1, 'msg': "Incomplete form"})
    admin_call = int((request.form.get('admin_call', '0')))
    details_type = int(request.form['type'])
    affiliate_id = int(request.form['affiliate_id'])
    result = fetch_affiliate_details(affiliate_id, details_type)
    if result.get('success') == 1:
        return jsonify(result), 200
    else:
        return jsonify(result), 400



def build_affiliate_report_query(report_type, book_valid, start_date=None, end_date=None, selected_affiliates=None, city_filter=None):
    is_booking_report = report_type == "Booking"
    start_date = ist_to_gmt(str(start_date)) if start_date else None
    end_date = ist_to_gmt(str(end_date)) if end_date else None

    # Common base columns
    columns = [
        Bookings.id,
        Bookings.code,
        Bookings.startdate,
        Bookings.starttime,
        Bookings.created_at,
        Bookings.region,
        Bookings.loc,
        Bookings.price,
        Bookings.comment,
        Affiliate.client_name,
        BookDest.name.label('destination'),
        AffiliateDriverSearch.dist,
        BookPricing.estimate,
        BookPricing.insurance_ch.label('estimated_ins_ch'),
        BookPricing.driver_base_ch.label('est_driver_base_charge'),
        BookPricing.driver_night_ch.label('est_driver_night_charge'),
        func.concat(Users.fname, ' ', Users.lname).label('driver_name'),
        Users.mobile.label('driver_contact'),
        Bookings.type
    ]

    # Base query and joins
    query = db.session.query(*columns)\
        .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)\
        .join(AffiliateDriverSearch, Bookings.search_key == AffiliateDriverSearch.id)\
        .outerjoin(BookDest, BookDest.book_id == Bookings.id)\
        .outerjoin(Affiliate, Affiliate.id == AffBookingLogs.aff_id)\
        .join(BookPricing, BookPricing.book_id == Bookings.id)\
        .join(Drivers, Bookings.driver == Drivers.id)\
        .join(DriverInfo, DriverInfo.driver_id == Drivers.id)\
        .join(Users, Users.id == Drivers.user)

    date_field = Trip.starttime if is_booking_report else Bookings.startdate

    if is_booking_report:
        # Booking-specific columns and joins
        columns.extend([
            Trip.starttime.label('trip_start'),
            Trip.endtime.label('trip_end'),
            TripPricing.driver_base_ch,
            TripPricing.driver_night_ch,
            TripPricing.driver_ot_ch,
            TripPricing.insurance_ch.label('insurance_charge'),
           (
                func.coalesce(TripPricing.base_ch, 0) +
                func.coalesce(TripPricing.cartype_ch, 0) +
                func.coalesce(TripPricing.night_ch, 0) +
                func.coalesce(TripPricing.ot_ch, 0) +
                func.coalesce(TripPricing.dist_ch, 0) +
                func.coalesce(TripPricing.cgst, 0) +
                func.coalesce(TripPricing.sgst, 0)
            ).label('final_cost_without_ins')
        ])
        query = query.join(Trip, Bookings.id == Trip.book_id)\
                     .join(TripPricing, Bookings.id == TripPricing.book_id)\
                     .filter(Trip.status == Trip.TRIP_STOPPED)
    else:
        # Subquery to get the latest cancellation reason for each booking
        latest_reason_subq = (
            db.session.query(
                BookingCancelled.booking.label('booking_id'),
                BookingCancelled.reason.label('latest_reason'),
                func.row_number().over(
                    partition_by=BookingCancelled.booking,
                    order_by=BookingCancelled.timestamp.desc()
                ).label('rn')
            ).subquery()
        )

        # Subquery to sum user and driver cancellation charges
        cancel_charges_subq = (
            db.session.query(
                BookingCancelled.booking.label('booking_id'),
                func.sum(BookingCancelled.penalty_user).label('total_user_charges'),
                func.sum(BookingCancelled.penalty_driver).label('total_driver_charges')
            ).group_by(BookingCancelled.booking).subquery()
        )

        # Extend columns with cancellation info
        columns.extend([
            latest_reason_subq.c.latest_reason.label('cancel_reason'),
            cancel_charges_subq.c.total_user_charges.label('user_cancel_charges'),
            cancel_charges_subq.c.total_driver_charges.label('driver_cancel_charges')
        ])

        # Join subqueries
        query = query.outerjoin(cancel_charges_subq, Bookings.id == cancel_charges_subq.c.booking_id)\
                     .outerjoin(latest_reason_subq,
                                (Bookings.id == latest_reason_subq.c.booking_id) &
                                (latest_reason_subq.c.rn == 1))

    # Apply updated column list (in case it was extended)
    query = query.with_entities(*columns)

    # Apply filters
    query = query.filter(Bookings.valid.in_(book_valid))

    if start_date and end_date:
        query = query.filter(date_field.between(start_date, end_date))
    elif start_date:
        query = query.filter(date_field >= start_date)
    elif end_date:
        query = query.filter(date_field <= end_date)

    if selected_affiliates:
        query = query.filter(AffBookingLogs.aff_id.in_(selected_affiliates))
    if city_filter :
        query = query.filter(Bookings.region.in_(city_filter))
    # Final ordering
    query = query.order_by(date_field.desc())

    return query



def build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map,sel_cols=None,is_sending=False):
    data = []

    for booking in results:
        destination, distance = booking.destination, booking.dist
        trip_start_diff, trip_start, trip_end = None, None, None

        # Always extract trip_start and trip_end regardless of report_type to handle cancellations with trips

        if report_type == "Booking":
            trip_start, trip_end = booking.trip_start, booking.trip_end
            book_start_datetime = datetime.combine(booking.startdate, booking.starttime)
            trip_start_datetime = booking.trip_start

            if trip_start_datetime and book_start_datetime:
                trip_start_diff = int((trip_start_datetime - book_start_datetime).total_seconds() )// 60

        latest_alloc_time = latest_alloc_times.get(booking.id)
        alloc_time_diff = None
        if latest_alloc_time and booking.created_at:
            alloc_time_diff = int((latest_alloc_time - booking.created_at).total_seconds() // 60)

        duration = None
        if trip_start and trip_end:
            duration = int((trip_end - trip_start).total_seconds() // 60)
        
        mongo_data = mongo_data_map.get(booking.id)
        booking_data = {
            'book_code': booking.code,
            'remarks': booking.comment,
            'book_start_date': (datetime.combine(booking.startdate, booking.starttime) + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y'),
            'book_start_time': (datetime.combine(booking.startdate, booking.starttime) + timedelta(hours=5, minutes=30)).strftime('%I:%M %p'),
            'book_raised_time': (booking.created_at + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y %I:%M %p'),
            'city_name': Regions.REGN_NAME[booking.region],
            'pickup_location': booking.loc,
            'drop_location': destination,
            'distance': math.ceil(distance / 1000),
            'client_name': booking.client_name,
            'driver_name': booking.driver_name,
            'estimated_cost_without_insurance': booking.estimate,
            'estimated_insurance_charge': booking.estimated_ins_ch,
            'estimated_driver_cost': booking.est_driver_base_charge + booking.est_driver_night_charge,
            'estiamted_cost_with_insurance_charge': booking.estimate + booking.estimated_ins_ch
        }
        #Only if we are not sending the report , include driver contact
        if not is_sending:
            booking_data['driver_contact'] = booking.driver_contact

        if report_type == "Booking":
            booking_data.update({
                'pick_up_date': (trip_start + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y') if trip_start else None,
                'trip_start_time': (trip_start + timedelta(hours=5, minutes=30)).strftime('%I:%M %p') if trip_start else None,
                'trip_end_time': (trip_end + timedelta(hours=5, minutes=30)).strftime('%I:%M %p') if trip_end else None,
                'duration': duration,
                'trip_delay_in_minutes': trip_start_diff if trip_start_diff and trip_start_diff > 0 else 0,
                'allocation_time': alloc_time_diff,
                'final_driver_cost': booking.driver_base_ch + booking.driver_night_ch + booking.driver_ot_ch,
                'final_insurance_charge': booking.insurance_charge,
                'final_cost_without_insurance':booking.final_cost_without_ins,
                'final_cost_with_insurance': booking.final_cost_without_ins + booking.insurance_charge
            })
        else:        
            booking_data.update({"user_cancel_charges":booking.user_cancel_charges,"cancel_reason":get_cancel_reason(booking.cancel_reason)})
        if mongo_data:
            booking_data.update({
                "affiliate_trip_type": mongo_data.get("trip_name", ""),
                "trip_type": mongo_data.get("trip_type", ""),
                "appointment_id": mongo_data.get("appointment_id", ""),
                "car_reg_no": mongo_data.get("vehicle_no", ""),
                "booked By": mongo_data.get("rep_fullname", ""),
                "vehicle_model": mongo_data.get("vehicle_model", ""),
            })
            # Handle nested dictionaries safely
            spoc_data = mongo_data.get("spoc_data", {}) or {}
            if isinstance(spoc_data, dict):
                booking_data.update({k: v for k, v in spoc_data.items() if k not in {'dest_spoc_contact', 'source_spoc_contact'}})
    

            custom_data = mongo_data.get("custom_data", {}) or {}
            booking_data.update(custom_data)
        if sel_cols:
            filtered_booking_data = {k: booking_data.get(k) for k in sel_cols}
            data.append(filtered_booking_data)
        else:
            data.append(booking_data)

    return data

@admin_affiliate.route('/api/admin/affiliate/admin_report', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def affiliate_report():
    claims = get_jwt()
    regions = request.args.get('regions')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    report_type = request.args.get('trans_type')
    selected_affiliates = request.args.get('selected_affiliates')

    if regions:
        regions = [int(a.strip()) for a in regions.split(',') if a.strip().isdigit()]
        if -1 in regions:
            regions = None
    else:
        regions = None


    if selected_affiliates:
        selected_affiliates = [int(a.strip()) for a in selected_affiliates.split(',') if a.strip().isdigit()]
    else:
        selected_affiliates = None


    # Convert start_date & end_date to datetime
    start_date = end_date = None
    try:
        if start_date_str:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d %H:%M:%S")
        if end_date_str:
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return jsonify({'success': 0, 'message': 'Invalid date format, use YYYY-MM-DD HH:mm:ss'}), 400


    book_valid = [1] if report_type == "Booking" else [-1, -2, -3]

    query = build_affiliate_report_query(
        report_type = report_type,
        book_valid = book_valid,
        start_date = start_date,
        end_date = end_date,
        selected_affiliates = selected_affiliates,
        city_filter = regions,
    )

    results = query.all()


    # Fetch latest allocation times efficiently
    book_ids_in_results = [booking.id for booking in results]
    latest_alloc_times = {}
    if book_ids_in_results:
        latest_alloc_times = dict(
            db.session.query(
                BookingAlloc.booking_id,
                func.max(BookingAlloc.timestamp)
            )
            .filter(BookingAlloc.booking_id.in_(book_ids_in_results))
            .group_by(BookingAlloc.booking_id)
            .all()
        )

    # Fetch MongoDB data in one query
    book_ids = [booking.id for booking in results]
    mongo_data_map = {
        doc['book_ref']: doc
        for doc in AffiliateCollections.affiliates_book.find(
            {"book_ref": {"$in": book_ids}},
            {
                "book_ref": 1,
                "trip_type": 1,
                "trip_name":1,
                "client_name": 1,
                "custom_data": 1,  # Fetch the ENTIRE custom_data object
                "vehicle_no": 1,
                "spoc_data": 1,
                "rep_fullname": 1,
                "appointment_id": 1,
                "vehicle_model": 1

            }
        )
    }

    # Process results
    data = build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map)
    return jsonify({'success': 1, 'data': data})

def create_excel_file(data, filename, sheet_name='Sheet1'):
    # Build full path
    D4M_UTIL_PATH = "/app/util/"

    filename = os.path.join(D4M_UTIL_PATH, 'output', filename)

    df = pd.DataFrame(data)
    writer = pd.ExcelWriter(filename, engine='xlsxwriter')
    df.to_excel(writer, sheet_name=sheet_name, index=False)
    writer._save()
    return filename



@admin_affiliate.route('/api/admin/affiliate/send_report', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def affiliate_report_excel():
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')
    report_type = request.form.get('trans_type')
    selected_affiliates = request.form.get('selected_affiliates')
    email_list = request.form.get('email_list')
    message = request.form.get('message')
    subject = request.form.get('subject')
    regions = request.form.get('regions')
    sel_col = request.form.get('selColums')
    try:
        sel_col = json.loads(sel_col)
        if not sel_col:  # empty list
            sel_col = None
    except (TypeError, ValueError): 
        sel_col = None
    # Parse filters
    if selected_affiliates:
        selected_affiliates = [int(a.strip()) for a in selected_affiliates.split(',') if a.strip().isdigit()]
    else:
        selected_affiliates = None
    if regions:
        regions = [int(a.strip()) for a in regions.split(',') if a.strip().isdigit()]
        if -1 in regions:
            regions = None

    try:
        start_date = datetime.strptime(start_date_str, "%Y-%m-%d %H:%M:%S") if start_date_str else None
        end_date = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S") if end_date_str else None
    except ValueError:
        return jsonify({'success': 0, 'message': 'Invalid date format, use YYYY-MM-DD HH:mm:ss'}), 400

    book_valid = [1] if report_type == "Booking" else [-1, -2, -3]
    # Get SQL results
    query = build_affiliate_report_query(
        report_type = report_type,
        book_valid = book_valid,
        start_date=start_date,
        end_date = end_date,
        selected_affiliates = selected_affiliates,
        city_filter = regions
    )
    results = query.all()

    # Get allocation times
    book_ids = [booking.id for booking in results]
    latest_alloc_times = {}
    if book_ids:
        latest_alloc_times = dict(
            db.session.query(
                BookingAlloc.booking_id,
                func.max(BookingAlloc.timestamp)
            ).filter(BookingAlloc.booking_id.in_(book_ids))
            .group_by(BookingAlloc.booking_id)
            .all()
        )

    # Mongo data
    mongo_data_map = {
        doc['book_ref']: doc
        for doc in AffiliateCollections.affiliates_book.find(
            {"book_ref": {"$in": book_ids}},
            {
                "book_ref": 1,
                "trip_type": 1,
                "client_name": 1,
                "custom_data": 1,
                "trip_name":1,
                "vehicle_no": 1,
                "spoc_data": 1,
                "rep_fullname": 1,
                "appointment_id": 1,
                "vehicle_model": 1
            }
        )
    }

    # Build final report data
    data = build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map,sel_col,True)
    # Create Excel file
    filename = f"affiliate_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    file_path = create_excel_file(data,filename)

    # Email recipients
    if not email_list:
        return jsonify({'success': 0, 'message': 'No email list provided'}), 400
    to_list = [email.strip() for email in email_list.split(',') if email.strip()]

    # Email content
    subject = subject if subject else f"{report_type} Report"
    content = message if message is not None else f"Hi,\n\nPlease find attached the affiliate report.\n\nRegards,\nTeam"
    content = content.replace('\n', '<br>')
    from_address="<EMAIL>"

    # Send mail
    try:
        send_mail(from_address, to_list, subject, content,file_path)
        os.remove(file_path)
        return jsonify({'success': 1, 'message': 'Report emailed successfully'})

    except Exception as e:
        if os.path.exists(file_path):
            os.remove(file_path)
        return jsonify({'success': 0, 'message': f"Email sending failed: {str(e)}"}), 500
    
@admin_affiliate.route('/api/admin/affiliate/create_report_schedule', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def create_scheduled_report():
    try:    
        report_type = request.form.get('report_type')
        frequency = request.form.get('frequency')
        duration = request.form.get('duration')
        subject = request.form.get('subject', '')
        message = request.form.get('message', '')
        is_draft=request.form.get('draft', 0)

        email_list = json.loads(request.form.get('email_list') or '[]')
        regions = [int(r) for r in json.loads(request.form.get('city') or '[]') if str(r).isdigit()]
        affiliates = [int(a) for a in json.loads(request.form.get('affiliate_filter') or '[]') if str(a).isdigit()]
    

        try:
            report_type = int(report_type) if report_type else None
            frequency = int(frequency) if frequency else None
            duration = int(duration) if duration else None
            regions = [int(r) for r in regions if r]
            affiliates = [int(aff) for aff in affiliates if aff]
            is_draft=int(is_draft)
        except ValueError as e:
            return jsonify({'success': 0, 'message': 'Invalid numeric value',}), 400
        if report_type not in [ScheduledReport.REPORT_BOOKING, ScheduledReport.REPORT_CANCELLATION]:
            return jsonify({'success': 0, 'message': 'Invalid report type'}), 400
        if frequency not in [ScheduledReport.FREQUENCY_DAILY, ScheduledReport.FREQUENCY_WEEKLY, ScheduledReport.FREQUENCY_MONTHLY]:
            return jsonify({'success': 0, 'message': 'Invalid frequency'}), 400
        if duration not in [ScheduledReport.DURATION_PREVIOUS_DAY, ScheduledReport.DURATION_THIS_WEEK, ScheduledReport.DURATION_THIS_MONTH]:
            return jsonify({'success': 0, 'message': 'Invalid duration'}), 400



            

        new_report = ScheduledReport(
            report_type=report_type,
            frequency=frequency,
            duration=duration,
            email_list=email_list,
            regions=regions,
            affiliates=affiliates,
            subject=subject,
            message=message,
            status=ScheduledReport.STATUS_DRAFT if is_draft else ScheduledReport.STATUS_ACTIVE,
            is_active=False if is_draft else True
            
            
        )

        db.session.add(new_report)
        db.session.commit()

        return jsonify({'success': 1, 'message': 'Scheduled report created successfully', 'id': new_report.id}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': 0, 'message': f'Error creating report: {str(e)}'}), 500
@admin_affiliate.route('/api/admin/affiliate/scheduled_reports', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def list_scheduled_reports():
    try:
        query = ScheduledReport.query
        report_types = request.args.getlist('report_type[]')
        if report_types:
            try:
                report_types = [int(rt) for rt in report_types if str(rt).isdigit()]
                if report_types:
                    query = query.filter(ScheduledReport.report_type.in_(report_types))
            except ValueError:
                return jsonify({'success': 0, 'message': 'Invalid report type'}), 400

        statuses = request.args.getlist('status[]')
        if statuses:
            try:
                statuses = [int(s) for s in statuses if str(s).isdigit()]
                if statuses:
                    query = query.filter(ScheduledReport.status.in_(statuses))
            except ValueError:
                return jsonify({'success': 0, 'message': 'Invalid status value'}), 400

        reports = query.order_by(ScheduledReport.created_at.desc()).all()
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        return jsonify({
            'success': 1,
            'reports': [
                {
                    'id': r.id,
                    'report_type': r.report_type,
                    'is_active': r.is_active,
                    'status': r.status,
                    'frequency': r.frequency,
                    'duration': r.duration,
                    'email_list': r.email_list,
                    'regions': r.regions,
                    'affiliates': r.affiliates,
                    'subject': r.subject,
                    'message': r.message,
                    'last_sent_at':convert_to_local_time(r.last_sent_at,tz) if r.last_sent_at else None,
                    'last_sent_status':r.last_sent_status,
                    'last_sent_error':r.last_sent_error,
                    'created_at':convert_to_local_time(r.created_at,tz)
                }
                for r in reports
            ]
        })

    except Exception as e:
        return jsonify({'success': 0, 'message': f'Error fetching scheduled reports: {str(e)}'}), 500
    
@admin_affiliate.route('/api/admin/affiliate/scheduled_reports/<int:report_id>/status', methods=['PATCH'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def update_scheduled_report_status(report_id):
    try:
        action = request.form.get('action')
        if not action:
            return jsonify({'success': 0, 'message': 'Action is required'}), 400

        try:
            action = int(action)
        except ValueError:
            return jsonify({'success': 0, 'message': 'Invalid action format'}), 400

        if action not in [ScheduledReport.STATUS_CANCELLED, ScheduledReport.STATUS_ACTIVE]:
            return jsonify({'success': 0, 'message': 'Invalid action. Must be activate or cancel'}), 400

        report = ScheduledReport.query.get(report_id)
        if not report:
            return jsonify({'success': 0, 'message': 'Scheduled report not found'}), 404

        if report.status == action:
            action_text = "Active" if action == ScheduledReport.STATUS_ACTIVE else "Cancelled"
            return jsonify({'success': 0, 'message': f'Schedule is already {action_text}'}), 400
        elif report.status == ScheduledReport.STATUS_DRAFT:
           return jsonify({'success': False, 'message': 'This report is still in draft. Please go to edit and complete all required details and submit it.'}), 400
        report.status = action
        report.is_active=not(report.is_active)
        db.session.commit()

        action_text = "Activated" if action == ScheduledReport.STATUS_ACTIVE else "Cancelled"
        return jsonify({'success': 1, 'message': f'Schedule {action_text} successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': 0, 'message': f'Error updating report status: {str(e)}'}), 500
    
@admin_affiliate.route('/api/admin/affiliate/scheduled_reports/<int:report_id>', methods=['PUT'])
@jwt_required()
@check_access(tab_required=[Tabs.AFFILIATE_WALLET])
def edit_scheduled_report(report_id):
    try:
        report = ScheduledReport.query.get(report_id)
        if not report:
            return jsonify({'success': 0, 'message': 'Scheduled report not found'}), 404

        report_type = request.form.get('report_type')
        frequency = request.form.get('frequency')
        duration = request.form.get('duration')
        subject = request.form.get('subject', '')
        message = request.form.get('message', '')
        is_draft = request.form.get('draft', 0)

        email_list = json.loads(request.form.get('email_list') or '[]')
        regions = [int(r) for r in json.loads(request.form.get('city') or '[]') if str(r).isdigit()]
        affiliates = [int(a) for a in json.loads(request.form.get('affiliate_filter') or '[]') if str(a).isdigit()]
        
        if int(report_type) not in [ScheduledReport.REPORT_BOOKING, ScheduledReport.REPORT_CANCELLATION]:
            return jsonify({'success': 0, 'message': 'Invalid report type'}), 400
        if int(frequency) not in [ScheduledReport.FREQUENCY_DAILY, ScheduledReport.FREQUENCY_WEEKLY, ScheduledReport.FREQUENCY_MONTHLY]:
            return jsonify({'success': 0, 'message': 'Invalid frequency'}), 400
        if int(duration) not in [ScheduledReport.DURATION_PREVIOUS_DAY, ScheduledReport.DURATION_THIS_WEEK, ScheduledReport.DURATION_THIS_MONTH]:
            return jsonify({'success': 0, 'message': 'Invalid duration'}), 400
        
        try:
            report.report_type = int(report_type) if report_type else None
            report.frequency = int(frequency) if frequency else None
            report.duration = int(duration) if duration else None
            report.email_list = email_list
            report.regions = regions
            report.affiliates = affiliates
            report.subject = subject
            report.message = message
            report.status=ScheduledReport.STATUS_DRAFT if int(is_draft) else ScheduledReport.STATUS_ACTIVE,
            report.is_active = False if int(is_draft) else True
        except ValueError:
            return jsonify({'success': 0, 'message': 'Invalid numeric value'}), 400

        db.session.commit()
        return jsonify({'success': 1, 'message': 'Scheduled report updated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': 0, 'message': f'Error updating report: {str(e)}'}), 500
