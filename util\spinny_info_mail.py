from main import app
import sys
sys.path.append("/app/")
import os
import pandas as pd

from datetime import datetime, timedelta
from sqlalchemy.sql import func
from _email import send_mail_v2
from _utils import get_dt_ist
from collections import defaultdict
from booking_params import BookingParams, Regions
from export_csv import D4M_UTIL_PATH
from models import db, Bookings, Drivers, Users, Trip

FROM_ADDR = "<EMAIL>"
REGION_MAIL = {
    Regions.REGN_KOLKATA: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_DELHI: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_HYDERABAD: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_BANGALORE: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
}

def group_by_day(bookings):
    grouped = defaultdict(list)
    for booking in bookings:
        start_datetime_str = f"{booking.startdate} {booking.starttime}"  # Format the combined string
        start_datetime = datetime.strptime(start_datetime_str, '%Y-%m-%d %H:%M:%S')  # Convert to datetime object
        adjusted_starttime = start_datetime + timedelta(hours=5, minutes=30)
        booking_date = adjusted_starttime.date()
        grouped[booking_date].append(booking)
    return grouped

def group_by_day_trip(bookings):
    grouped = defaultdict(list)
    for booking in bookings:
        adjusted_starttime = booking[1].starttime + timedelta(hours=5, minutes=30)
        booking_date = adjusted_starttime.date()
        grouped[booking_date].append(booking)
    return grouped

def _compute_info(trip_list):
    trip_info = []
    cols = ["cx_mobile", "cx_name", "cx_completed_trips", "driver_completed_trips",
            "booking_code", "booking_start", "booking_stop",
            "driver_alloc", "comment"]
    for trip in trip_list:
        user_id = trip.user
        cx_info = db.session.query(Users).filter(Users.id == user_id).first()
        cx_mobile = cx_info.mobile
        cx_name = cx_info.get_name()
        result = db.session.query(func.count(Trip.id).label('trip_count')). \
            join(Bookings, Trip.book_id == Bookings.id). \
            filter(Bookings.user == user_id). \
            group_by(Bookings.user). \
            first()
        cx_num_trips = result.trip_count if result else 0
        result_driver = db.session.query(func.count(Trip.id).label('trip_count')). \
            join(Bookings, Trip.book_id == Bookings.id). \
            filter(Bookings.driver == user_id). \
            group_by(Bookings.driver). \
            first()
        driver_num_trips = result_driver.trip_count if result_driver else 0
        trip_starttime_ist = get_dt_ist(trip.startdate, trip.starttime)
        trip_endtime_ist = get_dt_ist(trip.enddate, trip.endtime)
        if trip.driver == 1:
            driver_name = "Not Assigned"
        else:
            driver_name = db.session.query(Drivers, Users). \
                            filter(Drivers.id == trip.driver). \
                            filter(Drivers.user == Users.id).first()[1].get_name()
        trip_info.append([cx_mobile, cx_name, cx_num_trips, driver_num_trips, trip.code,
                          str(trip_starttime_ist),
                          str(trip_endtime_ist),
                          driver_name, trip.comment])
    trip_df = pd.DataFrame(trip_info, columns=cols)
    return trip_df

def compute_b2c_stats_and_send(region):
    time_yest = datetime.utcnow() + timedelta(seconds=330*60) - timedelta(days=1)
    time_yest_min = datetime(time_yest.year, time_yest.month, time_yest.day, 0, 0, 0)
    time_yest_max =  datetime(time_yest.year, time_yest.month, time_yest.day, 23, 59, 59)
    region_name = Regions.to_string(region)
    csv_paths = []
    start_of_month = time_yest.replace(day=1)+ timedelta(seconds=330*60)
    start_of_month = start_of_month.replace(hour=0, minute=0, second=0, microsecond=0)
    num_days_elapsed = (time_yest - start_of_month).days + 1
    completed_in_month = db.session.query(Bookings, Trip).filter(Bookings.region == region). \
                        filter(Bookings.id == Trip.book_id). \
                        filter(func.addtime(
                            Trip.starttime,
                            '05:30:00') >= start_of_month). \
                        filter(func.addtime(
                            Trip.starttime,
                            '05:30:00') <= time_yest_max). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        order_by(func.addtime(
                            Trip.starttime,
                            '05:30:00')).all()
    cancelled_before_alloc_in_month = db.session.query(Bookings).filter(Bookings.region == region). \
                        filter(Bookings.valid < 0). \
                        filter(Bookings.valid > Bookings.CANCELLED_D4M). \
                        filter(Bookings.driver == 1). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') >= start_of_month). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') <= time_yest_max). \
                        distinct(Bookings.user).all()

    d4m_cancelled_in_month = db.session.query(Bookings).filter(Bookings.region == region). \
                        filter(Bookings.valid == Bookings.CANCELLED_D4M). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') >= start_of_month). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') <= time_yest_max). \
                        distinct(Bookings.user).all()
    avg_completed_trip=round(len(completed_in_month)/num_days_elapsed,2)

    print("Computing info for region:", region_name)
    d4m_cancelled = db.session.query(Bookings).filter(Bookings.region == region). \
                        filter(Bookings.valid == Bookings.CANCELLED_D4M). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') >= time_yest_min). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') <= time_yest_max). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        order_by(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00')). \
                        distinct(Bookings.user).all()
    num_d4m_cancelled = len(d4m_cancelled)
    d4m_cancelled_df = _compute_info(d4m_cancelled)
    d4m_cancelled_csv_path = D4M_UTIL_PATH + 'output/' + region_name.lower() + \
                         '-d4m_cancelled-' + str(time_yest.date()) + ".csv"
    d4m_cancelled_df.to_csv(d4m_cancelled_csv_path, index=False)
    csv_paths.append(d4m_cancelled_csv_path)

    cancelled_after_alloc = \
            db.session.query(Bookings).filter(Bookings.region == region). \
                        filter(Bookings.valid < 0). \
                        filter(Bookings.valid > Bookings.CANCELLED_D4M). \
                        filter(Bookings.driver != 1). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') >= time_yest_min). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') <= time_yest_max). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        order_by(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00')). \
                        distinct(Bookings.user).all()
    num_cancelled_after_alloc = len(cancelled_after_alloc)
    cancelled_after_alloc_df = _compute_info(cancelled_after_alloc)
    cancelled_after_alloc_csv_path = D4M_UTIL_PATH + 'output/' + region_name.lower() + \
                         '-cancelled_after_alloc-' + str(time_yest.date()) + ".csv"
    cancelled_after_alloc_df.to_csv(cancelled_after_alloc_csv_path, index=False)
    csv_paths.append(cancelled_after_alloc_csv_path)


    cancelled_before_alloc = \
            db.session.query(Bookings).filter(Bookings.region == region). \
                        filter(Bookings.valid < 0). \
                        filter(Bookings.valid > Bookings.CANCELLED_D4M). \
                        filter(Bookings.driver == 1). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') >= time_yest_min). \
                        filter(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00') <= time_yest_max). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        order_by(func.addtime(
                            func.concat(Bookings.startdate, ' ', Bookings.starttime),
                            '05:30:00')). \
                        distinct(Bookings.user).all()
    num_cancelled_before_alloc = len(cancelled_before_alloc)
    cancelled_before_alloc_df = _compute_info(cancelled_before_alloc)
    cancelled_before_alloc_csv_path = D4M_UTIL_PATH + 'output/' + region_name.lower() + \
                         '-cancelled_before_alloc-' + str(time_yest.date()) + ".csv"
    cancelled_before_alloc_df.to_csv(cancelled_before_alloc_csv_path, index=False)
    csv_paths.append(cancelled_before_alloc_csv_path)

    completed = db.session.query(Bookings, Trip).filter(Bookings.region == region). \
                        filter(Bookings.id == Trip.book_id). \
                        filter(func.addtime(
                            func.concat(Trip.starttime),
                            '05:30:00') >= time_yest_min). \
                        filter(func.addtime(
                            func.concat(Trip.starttime),
                            '05:30:00') <= time_yest_max). \
                        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                        order_by(func.addtime(
                            func.concat(Trip.starttime),
                            '05:30:00')).all()
    num_completed = len(completed)
    daily_completed = group_by_day_trip(completed_in_month)
    daily_cancelled_before_alloc = group_by_day(cancelled_before_alloc_in_month)
    daily_d4m_cancelled = group_by_day(d4m_cancelled_in_month)
    total_completed = 0
    total_unique_cancelled = 0
    for day in daily_completed.keys():
        completed_set = set(daily_completed[day])
        cancelled_set = set(daily_cancelled_before_alloc.get(day, [])) | set(daily_d4m_cancelled.get(day, []))
        unique_cancelled = cancelled_set - completed_set
        total_completed += len(completed_set)
        total_unique_cancelled += len(unique_cancelled)
    if total_completed + total_unique_cancelled > 0:
        monthly_success_rate = round(total_completed / (total_completed + total_unique_cancelled) * 100, 2)
    else:
        monthly_success_rate = 100
    all_cancelled = cancelled_before_alloc + d4m_cancelled
    num_unique_cancelled = len(list(set(all_cancelled) - set(completed)))
    if num_completed + num_unique_cancelled != 0:
        success_pct = round(num_completed/(num_completed + num_unique_cancelled) * 100, 2)
    else:
        success_pct = 100
    completed_df = _compute_info([i[0] for i in completed])
    completed_csv_path = D4M_UTIL_PATH + 'output/' + region_name.lower() + \
                         '-completed-' + str(time_yest.date()) + ".csv"
    completed_df.to_csv(completed_csv_path, index=False)
    csv_paths.append(completed_csv_path)

    subjectD = "Spinny Booking Report - " + region_name + " - " + \
                str(time_yest.date())
    content = "Report for Spinny" + str(time_yest.date()) + "<br/>" + \
              "All cancellation numbers are unique<br/>" + \
              "Completed/Ongoing bookings: " + str(num_completed) + "<br/>" + \
              "D4M Cancelled bookings: " + str(num_d4m_cancelled) + "<br/>" + \
              "Unique cancellations: " + str(num_unique_cancelled) + "<br/>" + \
              "Success rate: " + str(success_pct) + "<br/>" + \
              "Monthly Success Rate: " + str(monthly_success_rate) + "%<br/>" + \
              "Average Monthly Completed Trip: " + str(avg_completed_trip)+ "<br/></br>" + \
              "CX/Driver cancelled after allocation: " + str(num_cancelled_after_alloc) + "<br/>" + \
              "CX cancelled before allocation: " + str(num_cancelled_before_alloc) + \
              "<br/><br/>--Drivers4Me team"
    to_addr_list = REGION_MAIL.get(region)
    if not to_addr_list:
        print("Not sending mail")
    else:
        print("Sending mail to", str(to_addr_list))
        send_mail_v2(FROM_ADDR, to_addr_list, subjectD, content, attachment_list="")
    #for csv_path in csv_paths:
        #os.remove(csv_path)


if __name__ == '__main__':
    with app.app_context():
        if len(sys.argv) < 2:
            raise Exception("No region specified")
        for region in sys.argv[1:]:
            try:
                compute_b2c_stats_and_send(int(region))
            except ValueError:
                print(region, "is not an integer")
            except Exception as e:
                raise e
