from models import DriverSearch, db , Users, Drivers, Bookings,  Trip
from datetime import date, time, datetime, timedelta

""" Test cases for api: api/search """

# Test for successful search
def test_successful_search(client, customer_login):

    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.57',
        'reflong': '88.36',
        'car_type': 1,
        'dur': '02:00:00',
        'time': '25/09/2025 14:35:00',
        # 'dest_lat': '22.51',
        # 'dest_long': '88.40',
        'region': 0,
        'type': 1,
        'city': 0,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

# Test fpr missing fields
def test_missing_fields(client, customer_login):
    auth_headers, data = customer_login()
    # Simulate a request with missing fields
    search_data = {
        'reflat': '22.5726',
        'reflong': '88.3639',
        'car_type': 'sedan'
        # 'dur', 'time' and other fields are missing
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    json_data = response.get_json()
    
    assert response.status_code == 201
    assert json_data['id'] == -1 

def test_invalid_jwt(client, customer_login):
    auth_headers, data = customer_login()
    # Simulate a request without authentication token
    search_data = {
        'reflat': '22.571256',
        'reflong': '88.428877',
        'car_type': '1',
        'dur': '02:00:00',
        'time': '25/09/2024 14:35:00',
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'region': '0',
        'type': '3',
        'city': '0',
        'insurance': '1',
        'ninsurance': '1',
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data)
    res_data = response.get_json()
    
    assert response.status_code == 401
    assert res_data['result'] == 'FAILURE'

def test_missing_destination_fields(client, customer_login):
    auth_headers, data = customer_login()
    # Simulate a request where destination fields are missing
    search_data = {
        'reflat': '22.5743545',
        'reflong': '88.3628734',
        'car_type': '1',
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'region': '0',
        'type': '1',
        'city':'0',
        'insurance': '1',
        'ninsurance': '1',
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 200

def test_disabled_account(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Simulate a user with a disabled account
    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()
    search_data = {
        'reflat': '22.571256',
        'reflong': '88.428877',
        'car_type': '1',
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'region': '1',
        'type': '1',
        'insurance': '1',
        'city': '0',
        'ninsurance': '1',
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    json_data = response.get_json()
    
    assert response.status_code == 401  
    assert json_data['id'] == -1


""" Test cases for api: api/book """

# Test for successful booking
def test_successful_booking(client, driver_login, book_search):
    search_id, auth_headers = book_search
    state = driver_login
    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data['result'] == 1

# Test for missing search id
def test_missing_search_id(client, book_search):
    # Simulate a booking request without search_id
    search_id, auth_headers = book_search

    booking_data = {
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5136',
        'dest_long': '88.403',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 201
    assert json_data['result'] == -1  

# Test for driver is trying to book
def test_driver_booking(client, driver_login, book_search):
    search_id, auth_headers = book_search
    state = driver_login
    access_token = state['access_token']
  
    headers = {'Authorization': f'Bearer {access_token}'}
    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5136',
        'dest_long': '88.403',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=headers)
    json_data = response.get_json()

    assert response.status_code == 401
 

# Test for invalid jwt token
def test_invalid_jwt_token(client, book_search):
    # Simulate a request without an auth token
    search_id, auth_headers = book_search

    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5136',
        'dest_long': '88.403',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data)
    json_data = response.get_json()

    assert response.status_code == 401
    assert json_data['result'] == "FAILURE"

# Test case for banned user
def test_banned_user(client, book_search):
    search_id, auth_headers = book_search

    search = DriverSearch.query.filter_by(id=search_id).first()
    user = Users.query.filter_by(id=search.user).first()
    user.enabled = False
    db.session.commit()

    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5136',
        'dest_long': '88.403',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 401
    assert json_data['result'] == -1


# Test for outside location 
def test_location_outside_service_area(client, book_search):
    search_id, auth_headers = book_search
    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '40.179',
        'dest_long': '74.123', # lat and long is outside
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 400
    assert json_data['result'] == -1  # Location outside service area

# Test for existing booking
def test_existing_booking(client, book_search):
    search_id, auth_headers = book_search
    current_date = date.today()

    # Add 2 days to current date
    startdate = current_date + timedelta(days=2)
    enddate = current_date + timedelta(days=2)

    starttime = time(10, 30)   
    endtime = time(12, 30) 

    # booking instance
    booking = Bookings(1, search_id, 10, 22.1, 88.1, starttime, startdate, '01:30:00', endtime, enddate, 200.0, 0, 'Kolkata', 3)
    db.session.add(booking)
    db.session.commit()

    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5136',
        'dest_long': '88.403',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
        'city': '0'
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data['result'] == 1
    assert json_data['exists'] == 1  # Booking already exists

# Test for invalid search id
def test_invalid_search_id(client, driver_login, book_search):
    search_id, auth_headers = book_search
    search_id = "some_random_key"
    state = driver_login
    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 500
    assert json_data['result'] == -1

# Test for user credit in negative
def test_negative_user_credit(client, driver_login, book_search):
    search_id, auth_headers= book_search
    search_user = db.session.query(DriverSearch, Users).filter(DriverSearch.id == search_id). \
                            filter(Users.id == DriverSearch.user).first()
    search_user[1].credit = -10
    db.session.commit()
    state = driver_login
    booking_data = {
        'search_id': search_id,
        'payment_type': '1',
        'loc': 'Test Location',
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': 'Destination Location',
        'type': '3', 
        'insurance': '1',
    }
    response = client.post('/api/book', data=booking_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data['result'] == -3

""" Test cases for api: api/user/past_ride """

def test_account_not_enabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()

    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    response = client.post('api/user/past_ride', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1

def test_past_ride_user_no_trip(client, customer_login):
    auth_header, user = customer_login()


    response = client.post('api/user/past_ride', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 200
    assert json_data['success'] == -1

""" Test cases for api: api/user/pending_ride """

def test_account_not_enabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    response = client.post('api/user/pending_ride', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1


def test_pending_ride_user_no_ride(client ,customer_login):
    auth_header, user = customer_login()

    response = client.post('api/user/pending_ride', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 200
    assert json_data['success'] == -1


""" Test cases for api: api/user/ongoing """

def test_ongoing_account_not_enabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()

    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    response = client.post('api/user/ongoing', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1


def test_ongoing_trip_user_no_trip(client ,customer_login):
    auth_header, user = customer_login()

    response = client.post('api/user/ongoing', headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 200
    assert json_data['success'] == -1


# """ Test cases for api: api/decline/user/charge """


def test_invalid_booking_id(client, customer_login):
    auth_headers, user = customer_login()
    
    # Simulate request with an invalid booking ID
    charge_data = {
        'reason': 0
    }
    
    response = client.post('/api/decline/user/charge', data=charge_data, headers=auth_headers)
    assert response.status_code == 401
    res_data = response.get_json()
    
    assert res_data['success'] == -1

def test_user_account_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()

    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    response = client.post('api/user/ongoing', headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1
