import json
from datetime import datetime,timedelta
from models.models import db, Drivers, Users, StrikeReason,DriverStrike,DriverDetails,StrikeReasonLog

import random


def create_strike(driver, reason, created_by, resolved=False, resolve_by=None):
    now = datetime.utcnow()
    strike = DriverStrike(
        driver_id=driver.id,
        reason=reason.id,
        starts_at=now,
        expires_at=now + timedelta(days=reason.cool_down_period),
        remarks="Test strike",
        strike_by=created_by.id,
        resolved=resolved,
        resolved_by=resolve_by.id if resolve_by else None,
        resolved_at=datetime.utcnow() if resolved else None,
        fine=reason.fine,
        cool_down_period=reason.cooldown,
        strike_weight=reason.strike_weight
    )
    db.session.add(strike)
    db.session.flush()
    return strike
def test_create_driver_strike_success(client, admin_login):
    auth_headers, _ = admin_login

    user = Users(mobile="**********", fname='Akash', lname='bose', email='<EMAIL>',pwd='abcd#123',role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DL12345", licDoc="doc.jpg", pic="pic.jpg", perma=False, approved=Drivers.APPROVED)
    driver.total_strike_count = 0
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    driver_details = DriverDetails(did=driver.id, ride=0, hour=0, rating=0, earning=0, owed=0, wallet=500, withdrawable=0, rating_ride=0)
    db.session.add(driver_details)
    db.session.commit()

    reason = StrikeReason(brief="Late arrival", strike_weight=1, cool_down_period=3,fine=100,created_by=user.id)
    db.session.add(reason)
    db.session.commit()

    form_data = {
        'driver_id': str(driver.id),
        'reason_id': str(reason.id),
        'remarks': 'Test remark',
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)
    print(response.data,flush=True)
    assert response.status_code == 201
    res = response.get_json()
    assert res['message'] == 'Strike issued successfully'
    assert res['driver_banned'] is False

    updated_driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert updated_driver_details.wallet == 400  # Initial wallet - fine

def test_create_driver_strike_missing_driver_or_reason(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'driver_id': '1',  # reason_id missing
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert 'driver_id and reason_id are required' in response.get_json()['error']

def test_create_driver_strike_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login
    reason = StrikeReason(brief="No show", strike_weight=1, cool_down_period=1,fine='100',created_by=admin)
    db.session.add(reason)
    db.session.commit()
    form_data = {
        'driver_id': '9999',  # nonexistent driver
        'reason_id': reason.id,
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()['error'] == 'Driver Not Found'


def test_create_driver_strike_invalid_reason(client, admin_login):
    auth_headers, _ = admin_login

    user = Users(mobile=random.randint(0000000000,9999999999), fname='Akash', lname='bose', email='<EMAIL>',pwd='abcd#123',role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DL54321", licDoc="doc2.jpg", pic="pic2.jpg", perma=False, approved=Drivers.APPROVED)
    driver.total_strike_count = 0
    db.session.add(driver)
    db.session.commit()

    form_data = {
        'driver_id': str(driver.id),
        'reason_id': '9999',  # invalid reason
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.get_json()['error'] == 'Invalid Reason Id'


def test_create_driver_strike_ban_driver(client, admin_login):
    auth_headers, admin_id = admin_login

    user = Users(mobile=random.randint(0000000000,9999999999), fname='Akash', lname='bose', email='<EMAIL>',pwd='abcd#123',role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()
    user_id = user.id
    driver = Drivers(curUser=user, licNo="DLBAN123", licDoc="doc_ban.jpg", pic="ban_pic.jpg", perma=False, approved=Drivers.APPROVED)
    driver.total_strike_count = 4  # near threshold
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    driver_details = DriverDetails(did=driver.id, ride=0, hour=0, rating=0, earning=0, owed=0, wallet=500, withdrawable=0, rating_ride=0)
    db.session.add(driver_details)    
    reason = StrikeReason(brief="No show", strike_weight=1, cool_down_period=1,fine=100,created_by=admin_id)
    db.session.add(reason)
    db.session.commit()

    form_data = {
        'driver_id': str(driver.id),
        'reason_id': str(reason.id),
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)

    assert response.status_code == 201
    res = response.get_json()
    assert res['driver_banned'] is True

    updated_driver = Drivers.query.get(driver_id)
    updated_driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert updated_driver_details.wallet == 400  # Initial wallet - fine
    assert updated_driver.approved == Drivers.BANNED


def test_create_driver_strike_driver_already_banned(client, admin_login):
    auth_headers, admin_id = admin_login

    user = Users(mobile=random.randint(0000000000,9999999999), fname='Akash', lname='bose', email='<EMAIL>',pwd='abcd#123',role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DLBANNED", licDoc="banned_doc.jpg", pic="ban.jpg", perma=False, approved=Drivers.BANNED)
    driver.total_strike_count = 5
    db.session.add(driver)
    db.session.flush()

    reason = StrikeReason(brief="Damage", strike_weight=1, cool_down_period=2,created_by=admin_id)
    db.session.add(reason)
    db.session.commit()

    form_data = {
        'driver_id': str(driver.id),
        'reason_id': str(reason.id),
        'region': '-1'
    }

    response = client.post('/api/admin/driver/driver_strikes', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.get_json()['error'] == 'Driver is already banned'

def test_resolve_strike_not_found(client, admin_login):
    """Test resolving non-existent strike"""
    auth_headers, _ = admin_login

    form_data = {
        'strike_id': '9999',
        'driver_id': '8888'
    }

    response = client.post('/api/admin/driver/resolve_strike', 
                          data=form_data, headers=auth_headers)

    assert response.status_code == 404
    assert "No unresolved strikes found" in response.json['error']

def test_resolve_driver_strike_success(client, admin_login):
    auth_headers, admin_id = admin_login

    # Create a driver user
    user = Users(
        mobile="**********",
        fname='Test',
        lname='Driver',
        email='<EMAIL>',
        pwd='secure#123',
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()

    # Create a driver
    driver = Drivers(
        curUser=user,
        licNo="DL67890",
        licDoc="license.jpg",
        pic="driver.jpg",
        perma=False,
        approved=Drivers.APPROVED,
        total_strike_count=0
    )
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    # Create driver details
    driver_details = DriverDetails(
        did=driver_id,
        wallet=500,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        withdrawable=0,
        rating_ride=0
    )
    db.session.add(driver_details)

    # Create strike reasons
    reason1 = StrikeReason(
        brief="Misbehavior", 
        strike_weight=2, 
        cool_down_period=2,
        fine=100,
        created_by=admin_id
        
    )
    reason2 = StrikeReason(
        brief="Late return", 
        strike_weight=3, 
        cool_down_period=3,
        fine=150,
        created_by=admin_id

    )
    db.session.add_all([reason1, reason2])
    db.session.commit()
    driver_id = driver.id    
    r_id1 = reason1.id
    r_id2 = reason2.id
    # Raise first strike through API
    strike1_data = {
        'driver_id': str(driver_id),
        'reason_id': str(r_id1),
        'remarks': 'First strike',
        'region': '1'
    }
    response1 = client.post(
        '/api/admin/driver/driver_strikes',
        data=strike1_data,
        headers=auth_headers
    )
    assert response1.status_code == 201
    strike1_id = response1.get_json()['strike_id']

    # Raise second strike through API
    strike2_data = {
        'driver_id': str(driver_id),
        'reason_id': str(r_id2),
        'remarks': 'Second strike',
        'region': '1'
    }
    response2 = client.post(
        '/api/admin/driver/driver_strikes',
        data=strike2_data,
        headers=auth_headers
    )
    assert response2.status_code == 201
    strike2_id = response2.get_json()['strike_id']

    # Verify wallet deductions
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert driver_details.wallet == 250  # 500 - 100 (first strike) - 150 (second strike)

    # Get current strikes to verify their IDs
    strike1 = DriverStrike.query.get(strike1_id)
    strike2 = DriverStrike.query.get(strike2_id)
    strike2_cooldown=strike2.cool_down_period
    s1_id =  strike1.id
    s2_id = strike2.id
    # Send request to resolve first strike
    resolve_data = {
        "strike_id": str(strike1_id),
        "driver_id": str(driver_id),
        "remarks": "Resolved first strike",
        "fine_waived":"true"
    }

    response = client.post(
        "/api/admin/driver/resolve_strike",
        data=resolve_data,
        headers=auth_headers
    )

    assert response.status_code == 200
    res = response.get_json()
    assert res["message"] == "Strike resolved and subsequent strikes reflowed"
    assert res["driver_strike_count"] == 3  # Only strike2 remains (weight=3)
    assert isinstance(res["driver_banned"], bool)

    # Verify database updates
    updated_strike1 = DriverStrike.query.get(s1_id)
    updated_strike2 = DriverStrike.query.get(s2_id)
    updated_driver = Drivers.query.get(driver_id)

    assert updated_strike1.resolved is True
    assert updated_strike1.resolved_by == admin_id
    assert updated_strike1.resolved_at is not None

    # Verify strike2 was reflowed (dates adjusted)
    assert updated_strike2.starts_at >= updated_strike1.resolved_at
    assert updated_strike2.expires_at == updated_strike2.starts_at + timedelta(days=strike2_cooldown)

    # Verify driver's strike count
    assert updated_driver.total_strike_count == 3  # Only strike2's weight counts now

    # Verify wallet refunded 
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert driver_details.wallet == 350


def test_resolve_strike_no_unresolved_strikes(client, admin_login):
    auth_headers, admin = admin_login

    user = Users(
        mobile="9999999999",
        fname='NoStrike',
        lname='User',
        email='<EMAIL>',
        pwd='test123',
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()

    driver = Drivers(
        curUser=user,
        licNo="DL00001",
        licDoc="doc1.jpg",
        pic="pic1.jpg",
        perma=False,
        approved=Drivers.APPROVED
    )
    db.session.add(driver)
    db.session.flush()

    reason = StrikeReason(
        brief="Test Reason",
        strike_weight=1,
        cool_down_period=1,
        fine=50,
        created_by=admin
    )
    db.session.add(reason)
    db.session.flush()

    strike = DriverStrike(
        driver_id=driver.id,
        reason=reason.id,
        remarks="Old resolved strike",
        strike_weight=reason.strike_weight,
        cool_down_period=reason.cool_down_period,
        starts_at=datetime.utcnow(),
        expires_at=datetime.utcnow() + timedelta(days=3),
        resolved=True,
        resolved_by=admin,
        resolved_at=datetime.utcnow(),
        strike_by=admin,
        fine=reason.fine
    )
    db.session.add(strike)
    db.session.commit()

    response = client.post(
        '/api/admin/driver/resolve_strike',
        data={'strike_id': str(strike.id), 'driver_id': str(driver.id)},
        headers=auth_headers
    )

    assert response.status_code == 404
    assert response.get_json()['error'] == "No unresolved strikes found for driver"


def test_resolve_strike_target_not_found(client, admin_login):
    auth_headers, admin = admin_login

    user = Users(mobile="8888888888", fname='Mismatch', lname='User', email='<EMAIL>',
                 pwd='test123', role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DL00002", licDoc="doc2.jpg", pic="pic2.jpg", perma=False)
    db.session.add(driver)
    db.session.flush()

    reason = StrikeReason(brief="Test Reason", strike_weight=1, cool_down_period=1, fine=50, created_by=admin)
    db.session.add(reason)
    db.session.flush()

    # unresolved strike
    strike = DriverStrike(
        driver_id=driver.id,
        reason=reason.id,
        remarks="Unresolved",
        starts_at=datetime.utcnow(),
        expires_at=datetime.utcnow() + timedelta(days=1),
        strike_by=admin,
        resolved=False,
        fine=reason.fine,
        cool_down_period=reason.cool_down_period,
        strike_weight=reason.strike_weight
    )
    db.session.add(strike)
    db.session.commit()

    fake_strike_id = strike.id + 1

    response = client.post(
        '/api/admin/driver/resolve_strike',
        data={'strike_id': str(fake_strike_id), 'driver_id': str(driver.id)},
        headers=auth_headers
    )

    assert response.status_code == 404
    assert response.get_json()['error'] == "Target strike not found for driver"


def test_resolve_reflow_cooldown_preserved(client, admin_login):
    auth_headers, admin = admin_login

    user = Users(mobile="7777777777", fname='Cooldown', lname='Check', email='<EMAIL>',
                 pwd='test123', role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DL00003", licDoc="doc3.jpg", pic="pic3.jpg", perma=False)
    db.session.add(driver)
    db.session.flush()

    r1 = StrikeReason(brief="A", strike_weight=1, cool_down_period=2,created_by=admin)
    r2 = StrikeReason(brief="B", strike_weight=2, cool_down_period=4,created_by=admin)
    
    db.session.add_all([r1, r2])
    db.session.flush()
    r2_cool_down_period=r2.cool_down_period
    now = datetime.utcnow()
    s1 = DriverStrike(
        driver_id=driver.id,  reason=r1.id,
        remarks="s1", starts_at=now,
        expires_at=now + timedelta(days=2), strike_by=admin, fine=r1.fine,
        cool_down_period=r1.cool_down_period,
        strike_weight=r1.strike_weight
    )
    s2 = DriverStrike(
        driver_id=driver.id,  reason=r2.id,
        remarks="s2", starts_at=now + timedelta(days=2),
        expires_at=now + timedelta(days=6), strike_by=admin ,fine=r2.fine,
        cool_down_period=r2.cool_down_period,
        strike_weight=r2.strike_weight
    )
    db.session.add_all([s1, s2])
    db.session.commit()
    s2_id=s2.id

    response = client.post('/api/admin/driver/resolve_strike', data={
        'strike_id': str(s1.id),
        'driver_id': str(driver.id)
    }, headers=auth_headers)

    assert response.status_code == 200
    s2 = DriverStrike.query.get(s2_id)
    assert s2.expires_at == s2.starts_at + timedelta(days=r2_cool_down_period)


def test_resolve_strike_driver_unbanned(client, admin_login):
    auth_headers, admin_user = admin_login

    user = Users(mobile="6666666666", fname='Ban', lname='Lift', email='<EMAIL>',
                 pwd='test123', role=Users.ROLE_DRIVER, region=1, enabled=False)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="DL00004", licDoc="doc4.jpg", pic="pic4.jpg",
                     perma=False, approved=Drivers.BANNED)
    db.session.add(driver)
    db.session.flush()

    reason = StrikeReason(brief="Heavy", strike_weight=5, cool_down_period=3,created_by=admin_user)
    db.session.add(reason)
    db.session.flush()
    driver_id=driver.id
    strike = DriverStrike(
        driver_id=driver.id, reason=reason.id, remarks="Severe",
        starts_at=datetime.utcnow(), expires_at=datetime.utcnow() + timedelta(days=3),
        strike_by=admin_user, fine=reason.fine,
        cool_down_period=reason.cool_down_period,
        strike_weight=reason.strike_weight
    )
    db.session.add(strike)
    db.session.commit()

    response = client.post('/api/admin/driver/resolve_strike', data={
        'strike_id': str(strike.id),
        'driver_id': str(driver.id)
    }, headers=auth_headers)

    assert response.status_code == 200
    updated_driver = Drivers.query.get(driver_id)
    assert updated_driver.approved == Drivers.UNAPPROVED
    assert updated_driver.total_strike_count == 0



def create_strike_for_test(user_id):
    # Create user
    user = Users(
        mobile="9998887777",
        fname="Test",
        lname="Driver",
        email="<EMAIL>",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()

    # Create driver
    driver = Drivers(
        curUser=user,
        licNo="LIC9988",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED
    )
    db.session.add(driver)
    db.session.flush()

    # Create driver details - KEY FIX: Use correct attribute names
    driver_details = DriverDetails(
        did=driver.id,  # This maps to driver_id in the model
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=500,
        withdrawable=0,
        rating_ride=0
    )
    db.session.add(driver_details)
    db.session.flush()

    # Create strike reasons
    reason1 = StrikeReason(brief="Late", strike_weight=1, cool_down_period=2, fine=100,created_by=user_id)
    reason2 = StrikeReason(brief="Damage", strike_weight=3, cool_down_period=4, fine=300,created_by=user_id)
    db.session.add_all([reason1, reason2])
    db.session.flush()

    # Create strike
    now = datetime.utcnow()
    strike = DriverStrike(
        driver_id=driver.id,
        reason=reason1.id,
        remarks="Initial",
        starts_at=now,
        expires_at=now + timedelta(days=reason1.cool_down_period),
        strike_by=user.id,
        fine=reason1.fine,
        cool_down_period=reason1.cool_down_period,
        strike_weight=reason1.strike_weight
    )
    db.session.add(strike)
    db.session.commit()

    return strike, driver, reason2 , driver_details

def test_modify_strike_success(client, admin_login):
    auth_headers, admin = admin_login

    # Create driver
    user = Users(
        mobile="**********", 
        fname='Test', 
        lname='Driver',
        email='<EMAIL>',
        pwd='password',
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()
    
    driver = Drivers(
        curUser=user,
        licNo="DL12345",
        licDoc="license.jpg",
        pic="driver.jpg",
        perma=False,
        approved=Drivers.APPROVED,
        total_strike_count=0  # Start with 0 strikes
    )
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    # Create driver details for wallet
    driver_details = DriverDetails(
        did=driver.id,
        wallet=500,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        withdrawable=0,
        rating_ride=0
    )
    db.session.add(driver_details)

    # Create original strike reason
    original_reason = StrikeReason(
        brief="Late arrival",
        strike_weight=1,
        cool_down_period=3,
        fine=100,
        created_by=admin
    )
    db.session.add(original_reason)
    db.session.commit()

    strike_form_data = {
        'driver_id': str(driver_id),
        'reason_id': str(original_reason.id),
        'remarks': 'Initial remark',
        'region': '1'
    }

    create_response = client.post(
        '/api/admin/driver/driver_strikes',
        data=strike_form_data,
        headers=auth_headers
    )
    assert create_response.status_code == 201
    strike_data = create_response.get_json()
    strike_id = strike_data['strike_id']

    # Create new strike reason for modification
    new_reason = StrikeReason(
        brief="Rude behavior",
        strike_weight=2,  # Different weight
        cool_down_period=5,
        fine=500,
        created_by=admin

    )
    db.session.add(new_reason)
    db.session.commit()
    new_reason_id=new_reason.id
    # Prepare modification data
    modify_form_data = {
        "strike_id": strike_id,
        "driver_id": str(driver_id),
        "reason_id": str(new_reason_id),
        "remarks": "Updated remarks"
    }

    # Make modification request
    response = client.post('/api/admin/driver/modify_strike', 
                         data=modify_form_data, 
                         headers=auth_headers)
    data = response.get_json()

    # Verify response
    new_reason=StrikeReason.query.get(new_reason_id)
    assert response.status_code == 200
    assert data["message"] == "Strike updated and reflowed"
    assert data["driver_strike_count"] == new_reason.strike_weight
    assert data["driver_banned"] is False 
    assert data["success"] == 1

    # Verify database updates
    updated_strike = DriverStrike.query.get(strike_id)
    assert updated_strike.reason == new_reason_id
    assert updated_strike.remarks == "Updated remarks"
    
    driver = Drivers.query.get(driver_id)
    assert driver.total_strike_count == new_reason.strike_weight
    assert driver.approved == Drivers.APPROVED  # Should not be banned

    # Verify wallet deduction (original strike fine)
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert driver_details.wallet == 0  # 500 - 100 + 100 - 500 (original fine)

def test_modify_strike_missing_fields(client, admin_login):
    auth_headers, _ = admin_login

    response = client.post('/api/admin/driver/modify_strike', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert "strike_id" in response.get_json()["error"]


def test_modify_strike_invalid_reason(client, admin_login):
    auth_headers, admin = admin_login
    strike, driver, _ , _ =  create_strike_for_test(admin)

    form_data = {
        "strike_id": str(strike.id),
        "driver_id": str(driver.id),
        "reason_id": "99999",  # Invalid
    }

    response = client.post('/api/admin/driver/modify_strike', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()["error"] == "Invalid strike reason"


def test_modify_strike_no_unresolved(client, admin_login):
    auth_headers, admin = admin_login
    strike, driver, new_reason ,_= create_strike_for_test(admin)

    strike.resolved = True
    db.session.commit()

    form_data = {
        "strike_id": str(strike.id),
        "driver_id": str(driver.id),
        "reason_id": str(new_reason.id)
    }

    response = client.post('/api/admin/driver/modify_strike', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()["error"] == "No unresolved strikes found for driver"


def test_modify_strike_not_found_in_set(client, admin_login):
    auth_headers, admin = admin_login
    strike, driver, new_reason , _= create_strike_for_test(admin)

    other_strike = DriverStrike(
        driver_id=driver.id,
        reason=new_reason.id,
        remarks="Resolved",
        starts_at=datetime.utcnow(),
        expires_at=datetime.utcnow() + timedelta(days=1),
        strike_by=driver.user,
        resolved=True,
        fine=new_reason.fine,
        cool_down_period=new_reason.cool_down_period,
        strike_weight=new_reason.strike_weight
    )
    db.session.add(other_strike)
    db.session.commit()

    form_data = {
        "strike_id": str(other_strike.id),
        "driver_id": str(driver.id),
        "reason_id": str(new_reason.id)
    }

    response = client.post('/api/admin/driver/modify_strike', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()["error"] == "Target strike not found for driver"
def test_modify_strike_reflows_cooldown_and_ban(client, admin_login):
    auth_headers, admin = admin_login  # Capture admin_user_id
    
    # Setup user and driver
    user = Users(
        mobile="9090909090",
        fname="Strike",
        lname="Tester",
        email="<EMAIL>",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()
    
    driver = Drivers(
        curUser=user,
        licNo="LICBAN123",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED
    )
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    driver_details = DriverDetails(
        did=driver_id,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=500,  # Initial wallet balance
        withdrawable=0,
        rating_ride=0
    )
    db.session.add(driver_details)
    db.session.commit()  # Commit after creating driver_details
    
    # Create strike reasons with fines
    reason1 = StrikeReason(brief="Late", strike_weight=1, cool_down_period=2, fine=200,created_by=admin)
    reason2 = StrikeReason(brief="Crash", strike_weight=5, cool_down_period=5, fine=500,created_by=admin)
    db.session.add_all([reason1, reason2])
    db.session.commit()
    r1_id=reason1.id
    r2_id=reason2.id
    # Create strikes via API to trigger fine deductions
    strike_data1 = {
        "driver_id": str(driver_id),
        "reason_id": str(r1_id),
        "remarks": "s1",
        "region": "-1"
    }
    response1 = client.post('/api/admin/driver/driver_strikes', data=strike_data1, headers=auth_headers)
    assert response1.status_code == 201
    s1_id = response1.get_json()["strike_id"]
    
    strike_data2 = {
        "driver_id": str(driver_id),
        "reason_id": str(r1_id),
        "remarks": "s2",
        "region": "-1"
    }
    response2 = client.post('/api/admin/driver/driver_strikes', data=strike_data2, headers=auth_headers)
    assert response2.status_code == 201
    s2_id = response2.get_json()["strike_id"]
    
    # Refresh objects after API calls
    driver = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    assert driver.approved == Drivers.APPROVED
    assert driver_details.wallet == 100  # 500 - 200 - 200
    
    # Modify s2 to reason2 (Crash) via API
    modify_data = {
        "strike_id": str(s2_id),
        "driver_id": str(driver_id),
        "reason_id": str(r2_id),
        "remarks": "Major violation"
    }
    response = client.post('/api/admin/driver/modify_strike', data=modify_data, headers=auth_headers)
    assert response.status_code == 200
    
    res = response.get_json()
    assert res["driver_banned"] is True, "Driver should be banned due to weight > threshold"
    assert res["driver_strike_count"] == 6, "Strike count should be 5 (Crash) + 1 (Late)"
    
    # Refresh objects after modification
    driver = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    s1 = DriverStrike.query.get(s1_id)
    s2 = DriverStrike.query.get(s2_id)
    
    # Confirm driver banned
    assert driver.approved == Drivers.BANNED
    # assert driver_details.wallet == -200  # 100 + 200 (reversal) - 500 (new fine)
    reason1=StrikeReason.query.get(r1_id)
    reason2=StrikeReason.query.get(r2_id)
    # Confirm reflowed strike timings
    assert s1.starts_at < s2.starts_at, "s1 should start before s2"
    assert s1.expires_at == s1.starts_at + timedelta(days=reason1.cool_down_period)
    assert s2.starts_at == s1.expires_at, "s2 should start right after s1 ends"
    assert s2.expires_at == s2.starts_at + timedelta(days=reason2.cool_down_period)

def test_get_strike_reasons(client, admin_login):
    auth_headers, admin = admin_login
    # Setup: Add some active reasons
    StrikeReason.query.delete()
    r1 = StrikeReason(brief="Reason 1", strike_weight=1, cool_down_period=3, active=True,fine=100,created_by=admin)
    r2 = StrikeReason(brief="Reason 2", strike_weight=2, cool_down_period=5, active=True,fine=200,created_by=admin)
    r3 = StrikeReason(brief="Inactive Reason", strike_weight=3, cool_down_period=1, active=False,fine=300,created_by=admin)
    db.session.add_all([r1, r2, r3])
    db.session.commit()
    r1_id=r1.id
    r2_id=r2.id
    r3_id=r3.id
    response = client.get(
        "/api/admin/driver/strike_reasons",
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["count"] == 2
    ids = [reason["id"] for reason in data["reasons"]]
    assert r1_id in ids and r2_id in ids and r3_id not in ids

def test_create_strike_reason_success(client, admin_login):
    auth_headers, _ = admin_login

    response = client.post(
        "/api/admin/driver/strike_reasons",
        data={"brief": "Test brief", "strike_weight": "3", "cool_down_period_days": "4","fine": "100"},
        headers=auth_headers
    )
    assert response.status_code == 201
    data = response.get_json()
    assert data["message"] == "Strike reason created successfully"
    assert data["reason"]["brief"] == "Test brief"
    assert data["reason"]["strike_weight"] == 3
    assert data["reason"]["cool_down_period_days"] == 4

def test_update_strike_reason_not_found(client, admin_login):
    auth_headers, _ = admin_login
    response = client.patch(
        "/api/admin/driver/strike_reasons/999999",
        data={"brief": "New Brief", "strike_weight": "3", "cool_down_period_days": "4"},
        headers=auth_headers
    )
    assert response.status_code == 404
    assert "not found" in response.get_json()["error"].lower()

def test_update_strike_reason_success(client, admin_login):
    """Test successful update of strike reason"""
    auth_headers, admin = admin_login
    # Make user superadmin
    db.session.commit()

    old_reason = StrikeReason(
        brief="Old", 
        strike_weight=2, 
        cool_down_period=3, 
        fine=100,
        active=True,
        created_by=admin
    )
    db.session.add(old_reason)
    db.session.commit()
    old_r_id=old_reason.id
    response = client.patch(
        f"/api/admin/driver/strike_reasons/{old_reason.id}",
        json={
            "brief": "New Brief", 
            "strike_weight": 3, 
            "cool_down_period_days": 4,
            "fine": 150,
            "remark": "Updating test reason"
        },
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["message"] == "Strike reason updated successfully"
    assert data["reason"]["brief"] == "New Brief"
    assert data["reason"]["strike_weight"] == 3
    assert data["reason"]["cool_down_period_days"] == 4
    assert data["reason"]["fine"] == 150
    assert data["changes"] == 4  # All fields changed

    # Verify old reason is still active (no versioning in current implementation)
    old_reason=StrikeReason.query.get(old_r_id)
    assert old_reason.active is True

def test_update_strike_reason_validation(client, admin_login):
    """Test validation of strike reason fields"""
    auth_headers, admin = admin_login
    # Make user superadmin
    db.session.commit()

    reason = StrikeReason(
        brief="Brief", 
        strike_weight=1, 
        cool_down_period=1, 
        fine=50,
        active=True,
        created_by=admin
    )
    db.session.add(reason)
    db.session.commit()
    r_id = reason.id
    # Test invalid strike weight (too high)
    response = client.patch(
        f"/api/admin/driver/strike_reasons/{r_id}",
        json={
            "strike_weight": 10,
            "cool_down_period_days": 0,
            "fine": -50
        },
        headers=auth_headers
    )
    assert response.status_code == 400
    json_data = response.get_json()
    assert "strike_weight must be at most 5" in json_data["error"].lower()

    # Test invalid cool down period (too low)
    response = client.patch(
        f"/api/admin/driver/strike_reasons/{r_id}",
        json={
            "strike_weight": 2,
            "cool_down_period_days": 0,
            "fine": 50
        },
        headers=auth_headers
    )
    assert response.status_code == 400
    json_data = response.get_json()
    assert "cool_down_period_days must be at least 1" in json_data["error"].lower()

    # Test invalid fine (negative)
    response = client.patch(
        f"/api/admin/driver/strike_reasons/{r_id}",
        json={
            "strike_weight": 2,
            "cool_down_period_days": 1,
            "fine": -50
        },
        headers=auth_headers
    )
    assert response.status_code == 400
    json_data = response.get_json()
    assert "fine must be at least 0" in json_data["error"].lower()


def test_update_strike_reason_not_found(client, admin_login):
    """Test updating non-existent strike reason"""
    auth_headers, admin = admin_login
    # Make user superadmin
    db.session.commit()

    response = client.patch(
        "/api/admin/driver/strike_reasons/9999",
        json={"brief": "New Brief"},
        headers=auth_headers
    )
    assert response.status_code == 404
    assert "Strike reason not found" in response.get_json()["error"]

def test_update_strike_reason_no_changes(client, admin_login):
    """Test updating with no actual changes"""
    auth_headers, admin = admin_login
    # Make user superadmin
    db.session.commit()

    reason = StrikeReason(
        brief="Test", 
        strike_weight=1, 
        cool_down_period=1, 
        fine=100,
        active=True,
        created_by=admin
    )
    db.session.add(reason)
    db.session.commit()
    reason_id = reason.id

    response = client.patch(
        f"/api/admin/driver/strike_reasons/{reason.id}",
        json={
            "brief": "Test",  # Same as current
            "strike_weight": 1,
            "cool_down_period_days": 1,
            "fine": 100
        },
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["message"] == "No changes detected"
def test_update_strike_reason_log_created(client, admin_login):
    """Test that changes are properly logged"""
    auth_headers, admin = admin_login
    StrikeReasonLog.query.delete()
    db.session.commit()

    reason = StrikeReason(
        brief="Old", 
        strike_weight=1, 
        cool_down_period=1, 
        fine=100,
        active=True,
        created_by=admin
    )
    db.session.add(reason)
    db.session.commit()
    reason_id = reason.id

    response = client.patch(
        f"/api/admin/driver/strike_reasons/{reason_id}",
        json={
            "brief": "New",
            "strike_weight": 2,
            "remark": "Testing logs"
        },
        headers=auth_headers
    )
    assert response.status_code == 200

    logs = StrikeReasonLog.query.filter_by(reason_id=reason_id).all()
    # Debug: Print actual log changes
    for log in logs:
        print(f"Change: {log.changes}, From: {log.change_from}, To: {log.change_to}, Remark: {log.remark}")

    # Use robust check
    assert {log.changes for log in logs} == {"brief", "strike_weight"}

    
def test_delete_strike_reason_not_found(client, admin_login):
    auth_headers, _ = admin_login

    response = client.patch(
        "/api/admin/driver/strike_reasons/999999",
        headers=auth_headers
    )
    assert response.status_code == 404
    assert "not found" in response.get_json()["error"].lower()
def test_get_driver_strike_history_success(client, admin_login):
    auth_headers, admin = admin_login

    # Setup user and driver
    user = Users(mobile="4445556666", fname='History', lname='Driver', email='<EMAIL>', pwd='pass', role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()
    user_id=user.id
    driver = Drivers(curUser=user, licNo="HIST123", licDoc="doc2.jpg", pic="dp2.jpg", perma=False, approved=Drivers.APPROVED)
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    reason = StrikeReason(brief="Rude behavior", strike_weight=1, cool_down_period=1,created_by=admin)
    db.session.add(reason)
    db.session.flush()

    strike = DriverStrike(driver_id=driver.id,  fine=reason.fine,
        cool_down_period=reason.cool_down_period,strike_weight=reason.strike_weight,reason=reason.id, starts_at=datetime.utcnow(), expires_at=datetime.utcnow() + timedelta(days=1), remarks="First warning", strike_by=user.id)
    db.session.add(strike)
    db.session.commit()

    response = client.get(f'/api/admin/driver/driver_strikes_history/{driver.id}', headers=auth_headers)
    user = Users.query.get(user_id)
    assert response.status_code == 200
    res = response.get_json()
    assert res["driver_id"] == driver_id
    assert res["no_of_strikes"] == 1
    assert res["strikes"][0]["strike_by"] == f"{user.fname} {user.lname}"


def test_get_driver_strike_history_driver_not_found(client, admin_login):
    auth_headers, _ = admin_login

    response = client.get('/api/admin/driver/driver_strikes_history/999999', headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()["error"] == "Driver not found"
def test_get_driver_strike_details_success(client, admin_login):
    auth_headers, admin = admin_login

    # Setup user and driver
    user = Users(mobile="1112223333", fname='Test', lname='Driver', email='<EMAIL>', pwd='pass', role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="TEST123", licDoc="doc.jpg", pic="dp.jpg", perma=False, approved=Drivers.APPROVED)
    db.session.add(driver)
    db.session.flush()
    driver_id = driver.id

    # Strike reason
    reason = StrikeReason(brief="Misconduct", strike_weight=2, cool_down_period=2,created_by=admin)
    db.session.add(reason)
    db.session.flush()

    # Active strike
    now = datetime.utcnow()
    strike = DriverStrike(driver_id=driver.id, reason=reason.id, fine=reason.fine,cool_down_period=reason.cool_down_period,strike_weight=reason.strike_weight, starts_at=now, expires_at=now + timedelta(days=2), remarks="Warning", strike_by=user.id)
    db.session.add(strike)
    db.session.commit()
    driver_id = driver.id
    strike_id = strike.id
    response = client.get(f'/api/admin/driver/strike_details/{driver.id}', headers=auth_headers)

    assert response.status_code == 200
    data = response.get_json()
    driver = db.session.query(Drivers).filter_by(id=driver_id).first()
    strile = db.session.query(DriverStrike).filter_by(id=strike_id).first()
    assert "driver" in data
    assert "active_strikes" in data
    assert data["driver"]["driver_id"] == driver_id
    assert data["active_strikes"][0]["strike_id"] == strike_id


def test_get_driver_strike_details_not_found(client, admin_login):
    auth_headers, _ = admin_login

    response = client.get('/api/admin/driver/strike_details/999999', headers=auth_headers)
    assert response.status_code == 404
    assert response.get_json()["error"] == "Driver not found"
