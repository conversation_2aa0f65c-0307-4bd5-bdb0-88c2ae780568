from datetime import datetime,timedelta
from models.models import db,Drivers, Users, StrikeReason,DriverStrike,DriverDetails,DriverSelfies,TripStartPic, Bookings, Trip
from conftest import create_b2b_booking
import random 

def test_label_uniform_invalid_main_class(client, admin_login):
    auth_headers, _ = admin_login
    # Setup user and driver
    user = Users(
        mobile = f"9{random.randint(10000000, 99999999)}",
        fname="Strike",
        lname="Tester",
        email = f"test_{random.randint(1000,9999)}@example.com",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()
    
    driver = Drivers(
        curUser=user,
        licNo="LICBAN123",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED
    )
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    driver_details = DriverDetails(
        did=driver.id,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=500,  # Initial wallet balance
        withdrawable=0,
        rating_ride=0
    )
    db.session.add(driver_details)
    db.session.commit()  # Commit after creating driver_details

    # Create strike reasons with fines
    reason1 = StrikeReason(brief="Late", strike_weight=1, cool_down_period=2, fine=200)
    data = {
        "driver_id": driver_id,
        "book_id": 4989,
        "img_main_class": 999,  # Invalid
        "img_color_class": 1,
        "selfie": "img.jpg",
        "fine_waived": "false",
        "region": -1
    }
    res = client.post("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, data=data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert "Invalid main class" in res.json['message']



def test_label_uniform_success_not_company_tshirt_not_fine_waived(client, admin_login):
    auth_headers, admin = admin_login
    user = Users(
        mobile = f"9{random.randint(10000000, 99999999)}",
        fname="Strike",
        lname="Tester",
        email = f"test_{random.randint(1000,9999)}@example.com",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()

    driver = Drivers(
        curUser=user,
        licNo="LICBAN123",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED,
        
    )
    db.session.add(driver)
    db.session.flush()
    driver_id=driver.id
    driver_details = DriverDetails(
        did=driver.id,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=100,  # Initial wallet balance
        withdrawable=0,
        rating_ride=0
    )
    reason1 = StrikeReason(brief="Company T-shirt Not Wearing", strike_weight=1, cool_down_period=10, fine=50,created_by=admin)
    
    db.session.add_all([driver_details, reason1])
    db.session.commit()  # Commit after creating driver_details
    data = {
        "driver_id": driver_id,
        "book_id":2125,
        "img_main_class": DriverSelfies.NOT_COMPANY_TSHIRT,
        "img_color_class": "",
        "selfie": "img.jpg",
        "fine_waived": "false",
        "region": -1
    }
    print("Reason ID:", reason1.id,flush=True)
    res = client.post("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, data=data)
    print("Response:", res.json, flush=True)
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    driver = Drivers.query.get(driver_id)

    assert driver.total_strike_count == 1
    assert driver_details.wallet == 50  
    assert res.status_code == 200
    assert res.json['success'] == 1
    
def test_label_uniform_success_not_company_tshirt_fine_waived(client, admin_login):
    auth_headers, admin = admin_login
    user = Users(
        mobile = f"9{random.randint(10000000, 99999999)}",
        fname="Strike",
        lname="Tester",
        email = f"test_{random.randint(1000,9999)}@example.com",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()

    driver = Drivers(
        curUser=user,
        licNo="LICBAN123",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED,
        
    )
    db.session.add(driver)
    db.session.flush()

    driver_details = DriverDetails(
        did=driver.id,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=100,  # Initial wallet balance
        withdrawable=0,
        rating_ride=0
    )
    reason1 = StrikeReason(brief="Company T-shirt Not Wearing", strike_weight=1, cool_down_period=10, fine=50,created_by=admin)
    
    db.session.add_all([driver_details, reason1])
    db.session.commit()  # Commit after creating driver_details
    data = {
        "driver_id": driver.id,
        "book_id":2125,
        "img_main_class": DriverSelfies.NOT_COMPANY_TSHIRT,
        "img_color_class": "",
        "selfie": "img.jpg",
        "fine_waived": "true",
        "region": -1
    }
    driver_id = driver.id
    res = client.post("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, data=data)
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    driver = Drivers.query.get(driver_id)
    assert driver.total_strike_count == 0
    assert driver_details.wallet == 100  
    assert res.status_code == 200
    assert res.json['success'] == 1

def test_label_uniform_driver_not_found(client, admin_login):
    auth_headers, _ = admin_login

    data = {
        "driver_id": 999999,
        "book_id": 32443,
        "img_main_class": DriverSelfies.COMPANY_TSHIRT,
        "img_color_class": DriverSelfies.COLOR_BLUE,
        "selfie": "img.jpg",
        "fine_waived": "false",
        "region": -1
    }
    res = client.post("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, data=data)
    assert res.status_code == 404
    assert res.json['message'] == "Driver not found"



def test_missing_parameters(client, admin_login):
    """Test missing required parameters"""
    auth_headers, _ = admin_login
    params = {"region": -1}  # Missing is_b2c and date

    res = client.get("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers,query_string=params)
    assert res.status_code == 400
    assert res.json == {"success": -1, "message": "Incomplete form details"}


def test_invalid_b2c(client, admin_login):
    """Test invalid is_b2c value"""
    auth_headers, _ = admin_login
    params = {"is_b2c": "invalid", "date": "2023-01-01","region": -1}
    res = client.get("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, query_string=params)
    assert res.status_code == 400
    assert res.json == {"success": -1, "message": "Invalid is_b2c value"}


def test_invalid_type(client, admin_login):
    """Test invalid type value"""
    auth_headers, _ = admin_login
    params = {"is_b2c": "1", "date": "2023-01-01", "type": "99", "regions": -1}
    res = client.get("/api/admin/dues_credit/label_driver_uniform", headers=auth_headers, query_string=params)
    assert res.status_code == 400
    assert res.json == {"success": -1, "message": "Invalid type value"}


def test_get_label_uniform_b2c_tagged_success(client, admin_login):
    auth_headers, user = admin_login

    user = Users(mobile = f"9{random.randint(10000000, 99999999)}", fname="B2C", lname="User",  email = f"test_{random.randint(1000,9999)}@example.com", pwd="pwd", role=Users.ROLE_DRIVER, region=1, enabled=True)
    db.session.add(user)
    db.session.flush()

    driver = Drivers(curUser=user, licNo="LIC123", licDoc="doc.jpg", pic="dp.jpg", approved=Drivers.APPROVED , perma=False)
    db.session.add(driver)
    db.session.flush()

    # Create a valid booking instance for the test
    now = datetime.utcnow()
    booking = Bookings(
        user=None,
        skey=None,
        driver=driver.id,
        lat=0.0,
        long=0.0,
        starttime=now.time(),
        startdate=now.date(),
        dur=timedelta(hours=1),
        endtime=(now + timedelta(hours=1)).time(),
        enddate=(now + timedelta(hours=1)).date(),
        estimate=100.0,
        pre_tax=100,
        loc="Test Location",
        car_type=1,
        type=10,
        days=0,
        comment="",
        payment_type=0,
        region=1,
        insurance=0,
        insurance_cost=0,
        insurance_num=0,
        user_rating=0,
        driver_rating=0
    )
    db.session.add(booking)
    db.session.flush()

    trip = Trip(
        book=booking.id,
        starttime=datetime.utcnow(),
        lat=0.0,
        lng=0.0,
        status=Trip.TRIP_STARTED
    )
    db.session.add(trip)

    selfie = TripStartPic(
        book_id=booking.id,
        car_left=None,
        car_right=None,
        car_back=None,
        car_front=None,
        selfie="selfie.jpg",
        extra1=None,
        extra2=None,
        extra3=None,
        extra4=None
    )
    db.session.add(selfie)

    driver_selfie = DriverSelfies(
        book_id=booking.id,
        img_main_class=DriverSelfies.NOT_COMPANY_TSHIRT,
        img_color_class=None,
        fine_waived=False,
        selfie="selfie.jpg",
        labeled_by= user.id,
        driver_id=driver.id,
    )
    db.session.add(driver_selfie)
    db.session.commit()

    date_str = datetime.utcnow().strftime("%Y-%m-%d")
    res = client.get(
        f"/api/admin/dues_credit/label_driver_uniform?is_b2c=1&regions=1&date={date_str}&type=2",
        headers=auth_headers
    )

    assert res.status_code == 200
    assert res.json['success'] == 1

def create_driver_with_selfie(db, admin_id , wallet_amount=1000):
    """Helper function to create driver with selfie record"""
    user = Users(
        mobile = f"9{random.randint(10000000, 99999999)}",
        fname="Test",
        lname="Driver",
        email = f"test_{random.randint(1000,9999)}@example.com",
        pwd="password",
        role=Users.ROLE_DRIVER,
        region=1,
        enabled=True
    )
    db.session.add(user)
    db.session.flush()
    
    driver = Drivers(
        curUser=user,
        licNo="LIC123",
        licDoc="doc.png",
        pic="pic.png",
        perma=False,
        approved=Drivers.APPROVED
    )
    db.session.add(driver)
    db.session.flush()
    driver_details = DriverDetails(
        did=driver.id,
        ride=0,
        hour=0,
        rating=0,
        earning=0,
        owed=0,
        wallet=wallet_amount,  
        withdrawable=0,
        rating_ride=0
   )
   
    db.session.add(driver_details)
    
    # Create strike reason with created_by from admin_login
    reason = StrikeReason(
        brief="Uniform Violation",
        strike_weight=1,
        cool_down_period=24,
        fine=200,
        created_by=admin_id
    )
    db.session.add(reason)
    db.session.flush()
    
    selfie = DriverSelfies(
        driver_id=driver.id,
        book_id=123,
        img_main_class=DriverSelfies.COMPANY_TSHIRT,
        img_color_class=DriverSelfies.COLOR_YELLOW,
        fine_waived=None,
        strike_id=None,
        labeled_by=admin_id,
        selfie='test.jpg'
        
        
    )
    db.session.add(selfie)
    db.session.commit()
    
    return user, driver, selfie, reason

def test_edit_label_incomplete_form(client, admin_login):
    """Test with missing form parameters"""
    auth_headers, _ = admin_login
    data = {"ds_id": 1, "strike_id": 1,"regions":-1}
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert "Incomplete form details" in res.json['message']

def test_edit_label_invalid_main_class(client, admin_login):
    """Test with invalid main class value"""
    auth_headers, admin = admin_login
    user_id, driver_id, selfie_id, reason_id = create_driver_with_selfie(db, admin)
    
    data = {
        "ds_id": selfie_id,
        "strike_id": "",
        "img_main_class": "999",
        "img_color_class": str(DriverSelfies.COLOR_YELLOW),
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert "Invalid main class" in res.json['message']

def test_edit_label_invalid_color_class(client, admin_login):
    """Test with invalid color when main class is valid"""
    auth_headers, admin = admin_login
    _,_, selfie,_ = create_driver_with_selfie(db, admin)
    
    data = {
        "ds_id": selfie.id,
        "strike_id": "",
        "img_main_class": str(DriverSelfies.COMPANY_TSHIRT),
        "img_color_class": "99",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert "Invalid color class" in res.json['message']

def test_edit_label_selfie_not_found(client, admin_login):
    """Test with non-existent selfie ID"""
    auth_headers, _ = admin_login
    data = {
        "ds_id": "9999",
        "strike_id": "",
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    assert res.status_code == 404
    assert res.json['success'] == -1
    assert "Driver selfie not found" in res.json['message']

def test_edit_label_reversal_violation_to_valid(client, admin_login):
    """Test reversal when changing from violation to valid uniform"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason= create_driver_with_selfie(db, admin)
    # Create initial strike
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id

    strike_data1 = {
        "driver_id": str(driver.id),
        "reason_id": str(reason_id),
        "remarks": "s1",
        "region": "-1"
    }
    strike = client.post('/api/admin/driver/driver_strikes', data=strike_data1, headers=auth_headers)
    strike_id=strike.get_json()["strike_id"]
    # Update selfie to violation state
    selfie = DriverSelfies.query.get(selfie_id)
    selfie.img_main_class = DriverSelfies.NOT_COMPANY_TSHIRT
    selfie.fine_waived = False
    selfie.strike_id = strike_id
    db.session.commit()
    # Get driver's initial wallet
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    initial_wallet = driver_details.wallet
    
    # Change to valid uniform (should trigger reversal)
    data = {
        "ds_id": selfie.id,
        "strike_id": str(strike_id),
        "img_main_class": str(DriverSelfies.COMPANY_TSHIRT),
        "img_color_class": str(DriverSelfies.COLOR_BLUE),
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    assert "Selfie labeled successfully" in res.json['message']
    
    # Verify selfie update
    updated_selfie = DriverSelfies.query.get(selfie_id)
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    reason = StrikeReason.query.get(reason_id)
    assert driver_details.wallet == initial_wallet + reason.fine 
    assert updated_selfie.img_main_class == DriverSelfies.COMPANY_TSHIRT
    assert updated_selfie.img_color_class == DriverSelfies.COLOR_BLUE
    assert updated_selfie.fine_waived is None  # Should be None for valid uniform
    assert updated_selfie.labeled_by == admin

def test_edit_label_reversal_waiver_applied(client, admin_login):
    """Test reversal when waiver is applied to existing violation"""
    auth_headers, admin = admin_login
    user, driver,selfie, reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id
    # Create initial strike
    strike_data1 = {
        "driver_id": str(driver_id),
        "reason_id": str(reason_id),
        "remarks": "s1",
        "region": "-1"
    }
    strike = client.post('/api/admin/driver/driver_strikes', data=strike_data1, headers=auth_headers)
    strike_id=strike.get_json()["strike_id"]
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    initial_wallet = driver_details.wallet
    # Update selfie to violation without waiver
    selfie = DriverSelfies.query.get(selfie_id)
    selfie.img_main_class = DriverSelfies.NOT_COMPANY_TSHIRT
    selfie.fine_waived = False
    selfie.strike_id = strike_id
    db.session.commit()
    
    # Apply waiver (should trigger reversal)
    data = {
        "ds_id": selfie.id,
        "strike_id": str(strike_id),
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "true",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify selfie updated
    updated_selfie = DriverSelfies.query.get(selfie_id)
    reason = StrikeReason.query.get(reason_id)
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    assert updated_selfie.img_main_class == DriverSelfies.NOT_COMPANY_TSHIRT
    assert updated_selfie.fine_waived is True
    assert updated_selfie.labeled_by == admin
    assert driver_details.wallet == initial_wallet + reason.fine 

def test_edit_label_new_strike_valid_to_violation(client, admin_login):
    """Test new strike when changing from valid to violation"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin,1000)
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    initial_wallet = driver_details.wallet
    # Change to violation (should trigger new strike)
    data = {
        "ds_id": selfie_id,
        "strike_id": "",
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    updated_selfie = DriverSelfies.query.get(selfie_id) 
    reason = StrikeReason.query.get(reason_id)
    assert updated_selfie.img_main_class == DriverSelfies.NOT_COMPANY_TSHIRT
    assert updated_selfie.img_color_class is None
    assert updated_selfie.fine_waived is False
    assert updated_selfie.labeled_by == admin
    assert driver_details.wallet == initial_wallet - reason.fine
    

def test_edit_label_new_strike_waiver_removed(client, admin_login):
    """Test new strike when waiver is removed from violation"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin,1000)
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    initial_wallet = driver_details.wallet
    
    # Update selfie to violation with waiver
    selfie = DriverSelfies.query.get(selfie_id)
    selfie.img_main_class = DriverSelfies.NOT_COMPANY_TSHIRT
    selfie.fine_waived = True
    selfie.strike_id = None
    db.session.commit()
    
    # Remove waiver (should trigger new strike)
    data = {
        "ds_id": selfie_id,
        "strike_id": " ",
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify selfie updated
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    reason = StrikeReason.query.get(reason_id)
    updated_selfie = DriverSelfies.query.get(selfie_id)
    assert updated_selfie.img_main_class == DriverSelfies.NOT_COMPANY_TSHIRT
    assert updated_selfie.fine_waived is False
    assert updated_selfie.labeled_by == admin
    assert driver_details.wallet == initial_wallet - reason.fine

def test_edit_label_violation_to_violation_no_waiver_change(client, admin_login):
    """Test changing between violation types without waiver change"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id
    # Create initial strike
    strike_data1 = {
        "driver_id": str(driver_id),
        "reason_id": str(reason_id),
        "remarks": "s1",
        "region": "-1"
    }
    strike = client.post('/api/admin/driver/driver_strikes', data=strike_data1, headers=auth_headers)
    strike_id=strike.get_json()["strike_id"]
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    wallet_after_strike = driver_details.wallet
    
    # Set initial violation
    selfie = DriverSelfies.query.get(selfie_id)
    selfie.img_main_class = DriverSelfies.NOT_COMPANY_TSHIRT
    selfie.fine_waived = False
    selfie.strike_id = strike_id
    db.session.commit()
    
    # Change to different violation type (no reversal or new strike)
    data = {
        "ds_id": selfie_id,
        "strike_id": str(strike_id),
        "img_main_class": str(DriverSelfies.NOT_A_PERSON),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify selfie updated but no strike changes
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    updated_selfie = DriverSelfies.query.get(selfie_id)
    assert updated_selfie.img_main_class == DriverSelfies.NOT_A_PERSON
    assert updated_selfie.fine_waived is False
    assert updated_selfie.labeled_by == admin
    assert updated_selfie.strike_id == strike_id
    assert wallet_after_strike == driver_details.wallet

def test_edit_label_valid_to_valid_color_change(client, admin_login):
    """Test valid uniform to valid uniform with color change"""
    auth_headers, admin = admin_login
    user, driver, selfie,reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    driver_id = driver.id
    reason_id = reason.id
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    intial_wallet = driver_details.wallet
    # Change to different valid color
    data = {
        "ds_id": selfie_id,
        "strike_id": "",
        "img_main_class": str(DriverSelfies.COMPANY_TSHIRT),
        "img_color_class": str(DriverSelfies.COLOR_BLUE),
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify selfie updated
    updated_selfie = DriverSelfies.query.get(selfie_id)
    driver_details = DriverDetails.query.filter_by(driver_id=driver_id).first()
    assert updated_selfie.img_main_class == DriverSelfies.COMPANY_TSHIRT
    assert updated_selfie.img_color_class == DriverSelfies.COLOR_BLUE
    assert updated_selfie.fine_waived is None  # Should be None for valid uniform
    assert updated_selfie.strike_id is None  # Should be None for empty strike_id
    assert updated_selfie.labeled_by == admin
    assert intial_wallet == driver_details.wallet

def test_edit_label_valid_uniform_waiver_ignored(client, admin_login):
    """Test that waiver is ignored (set to None) for valid uniform"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    # Try to set waiver on valid uniform
    data = {
        "ds_id": selfie_id,
        "strike_id": "",
        "img_main_class": str(DriverSelfies.COMPANY_TSHIRT),
        "img_color_class": str(DriverSelfies.COLOR_YELLOW),
        "fine_waived": "true",
        "regions":-1 
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify waiver is None for valid uniform
    updated_selfie = DriverSelfies.query.get(selfie_id)
    assert updated_selfie.img_main_class == DriverSelfies.COMPANY_TSHIRT
    assert updated_selfie.fine_waived is None  # Should be None regardless of input
    assert updated_selfie.labeled_by == admin

def test_edit_label_strike_id_handling(client, admin_login):
    """Test strike_id handling with different input values"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    # Test with "null" string
    data = {
        "ds_id":selfie_id,
        "strike_id": "null",
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 200
    assert res.json['success'] == 1
    
    # Verify strike_id is None
    updated_selfie = DriverSelfies.query.get(selfie_id)
    assert updated_selfie.strike_id is not None

def test_edit_label_server_error(client, admin_login, mocker):
    """Test database error during update"""
    auth_headers, admin = admin_login
    user, driver, selfie, reason = create_driver_with_selfie(db, admin)
    selfie_id = selfie.id
    # Force database error
    mocker.patch.object(db.session, 'commit', side_effect=Exception("DB error"))
    
    data = {
        "ds_id": selfie_id,
        "strike_id": "",
        "img_main_class": str(DriverSelfies.NOT_COMPANY_TSHIRT),
        "img_color_class": "",
        "fine_waived": "false",
        "regions":-1
    }
    res = client.patch("/api/admin/dues_credit/label_driver_uniform", 
                       headers=auth_headers, 
                       data=data)
    
    assert res.status_code == 500
    assert res.json['success'] == -2
    assert "Internal server error" in res.json['message']