# # FROM tiangolo/uwsgi-nginx:python3.11

# # # Install requirements
# # COPY requirements.txt /tmp/requirements.txt
# # RUN pip install --upgrade -r /tmp/requirements.txt
# # # RUN pip install -r /tmp/requirements.txt

# # # URL under which static (not modified by Python) files will be requested
# # # They will be served by Nginx directly, without being handled by uWSGI


# # # Add demo app
# # COPY . /app
# # WORKDIR /app
# # COPY nginx.conf /etc/nginx/nginx.conf
# # COPY conf.d/nginx.conf /etc/nginx/conf.d/nginx.conf

# # # Make /app/* available to be imported by Python globally to better support several use cases like Alembic migrations.
# # ENV PYTHONPATH=/app
# # RUN export FLASK_ENV=testing && pytest /app/tests/test_login/test_user_login.py --disable-warnings > /app/logs/pytest.log 2>&1 || (python /app/util/send_pytest_report.py && true)


# # # Move the base entrypoint to reuse it
# # RUN mv /entrypoint.sh /uwsgi-nginx-entrypoint.sh
# # # Copy the entrypoint that will generate Nginx additional configs
# # COPY entrypoint.sh /entrypoint.sh
# # RUN chmod +x /entrypoint.sh

# # ENTRYPOINT ["/entrypoint.sh"]

# # # Run the start script provided by the parent image tiangolo/uwsgi-nginx.
# # # It will check for an /app/prestart.sh script (e.g. for migrations)
# # # And then will start Supervisor, which in turn will start Nginx and uWSGI
# # CMD ["/start.sh"]

# FROM tiangolo/uwsgi-nginx:python3.11

# ENV UWSGI_CHEAPER 4
# ENV UWSGI_PROCESSES 64
# ENV NGINX_WORKER_PROCESSES auto

# # Upgrade pip to ensure compatibility with the latest packages
# RUN pip install --upgrade pip

# # Install the packages directly from requirements.txt
# COPY requirements.txt /tmp/requirements.txt
# RUN pip install -r /tmp/requirements.txt

# # Add the rest of your configurations here
# COPY . /app
# WORKDIR /app
# COPY nginx.conf /etc/nginx/nginx.conf
# COPY conf.d/nginx.conf /etc/nginx/conf.d/nginx.conf

# ENV PYTHONPATH=/app
# RUN export FLASK_ENV=testing && pytest /app/tests/test_login/test_user_login.py --disable-warnings > /app/logs/pytest.log 2>&1 || (python /app/util/send_pytest_report.py && true)

# RUN mv /entrypoint.sh /uwsgi-nginx-entrypoint.sh
# COPY entrypoint.sh /entrypoint.sh
# RUN chmod +x /entrypoint.sh

# ENTRYPOINT ["/entrypoint.sh"]
# CMD ["/start.sh"]


# Use a lightweight Python image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Copy the application code
COPY . .

# Install dependencies
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir -r requirements.txt

ENV PYTHONPATH=/app
RUN apt-get update && \
    apt-get install -y redis-server && \
    redis-server --daemonize yes && \
    sleep 2 && \
    redis-cli ping && \
    export FLASK_ENV=testing && \
    pytest /app/tests --disable-warnings > /app/logs/pytest.log 2>&1 ; (python /app/reports/pytest/send_pytest_report.py && true) && \
    apt-get remove -y redis-server && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/*
# Expose the application port
EXPOSE 8000

# Run Gunicorn with Gevent
#CMD ["python", "main.py"]
# CMD ["gunicorn", "-w", "4", "-k", "geventwebsocket.gunicorn.workers.GeventWebSocketWorker", "-b", "0.0.0.0:80", "main:app", "--log-level", "debug", "--access-logfile", "-", "--error-logfile", "-"]
CMD ["gunicorn", "-w", "4", "--threads", "8", "-k", "geventwebsocket.gunicorn.workers.GeventWebSocketWorker", "-b", "0.0.0.0:80", "--timeout", "120", "--keep-alive", "3", "--log-level", "debug", "--access-logfile", "-", "--error-logfile", "-", "main:app"]