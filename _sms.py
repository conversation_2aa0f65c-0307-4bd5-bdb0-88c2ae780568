#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import http.client
from datetime import timed<PERSON>ta
from flask import current_app as app
from _ops_message import send_slack_msg
import json
import re, requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import os
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from urllib.parse import urlencode
from urllib.parse import quote
from config import BaseConfig

MASTER_OTP='3487'
# Configuration
GUPSHUP_BASE_URL = "https://enterpriseapi.smsgupshup.com/GatewayAPI/rest"
GUPSHUP_USER_ID_TRANSACTIONAL = BaseConfig.GUPSHUP_USER_ID_TRANSACTIONAL
GUPSHUP_PASSWORD_TRANSACTIONAL = BaseConfig.GUPSHUP_PASSWORD_TRANSACTIONAL
KEY_BASE64_TRANSACTIONAL = BaseConfig.KEY_BASE64_TRANSACTIONAL
GUPSHUP_USER_ID_OTP = BaseConfig.GUPSHUP_USER_ID_OTP
GUPSHUP_PASSWORD_OTP = BaseConfig.GUPSHUP_PASSWORD_OTP
KEY_BASE64_OTP = BaseConfig.KEY_BASE64_OTP
PRINCIPAL_ENTITY_ID = BaseConfig.PRINCIPAL_ENTITY_ID
MASK="DRVFME"
COUNTRY_CODE_IN = '91'
MSG_SENDER_ID = 'DRVFME'
IST_OFFSET_TIMEDELTA = timedelta(hours=5, minutes=30)
SUPPORT_PH = ['']
TEMPLATE_ID_MAPPING = {
    "trip-allocated": "1207161718498465531",
    "trip-start":"1207161718541285038",
    "trip-stop": "1207161718524019073",
    "user-cancelled": "1207161718517667252",
    "d4m-cancelled": "1207161718470839793",
    "b2b-pickup-drop":"1207171836881518305",
    "d4m-cancelled-user-fine":"1207174774224480567",
    "d4m-cancelled-user-not-fine": "1207174678250741967",
    "d4m-cancelled-driver-fine": "1207174774635566624",
    "d4m-cancelled-driver-not-fine": "1207174774495624919",
    "d4m-strike-raise":"1207175007121055653",
    "d4m-strike-raise-ban":"1207175007149154489",
    "d4m-strike-resolved" :"1207175016074587869",
    "d4m-strike-resolved-rev-fine":"1207175016944841234",
    "D4M_Driver_DL_Expiry":"1207175585102690645",
    "D4M_Driver_Inactive":"1207175585158426687",
}

FLOWS = {
         "trip-start": "605f001bd962ab26413ae594",
         "trip-insured": "605f009db1174a473856c1b3",
         "trip-stop": "605f00f06f62d4337a086412",
         "user-cancelled": "605f01f8b184bc075b12a2e9",
         "trip-allocated": "605f03d64cc9d3649d643853",
         "d4m-cancelled": "605f044893bb912603036335",
         "landing-sms-ref": "60bb1f23663e1a65c2753068",
         "admin-driver-alloc": "63837731369a9b05970fa2c2",
         "b2b-pickup-drop": "666fd780d6fc0520c97b65f2",
         "b2b_allocate": "66794af9d6fc0556e72301a2",
         "affiliate-pickup": "",
         "b2b-pickup": "",
         "soft_booking_creation": "",
        }


def send_msg_flow(to, flow_id, payload):
    if not app.config['SEND_OPS_MSG']:
        return False
    to_send = str(COUNTRY_CODE_IN) + str(to)
    if len(to) == 12:
        to_send = str(to)
    if not re.match(r'^\d{12}$', to_send):
        send_slack_msg(1, str(to_send) + " is invalid number, so could not send: " + str(payload))
        return

    url = "https://api.msg91.com/api/v5/flow/"
    headers = {'authkey': app.config['OTP_AUTH_KEY'], 'content-type': "application/json" }

    # Use a dictionary comprehension to build the full_payload dictionary
    full_payload = {
        "flow_id": flow_id,
        "sender": MSG_SENDER_ID,
        "recipients": [
            {
                "mobiles": to_send,
                **payload  # Use the "unpacking" operator to add the payload items to the recipients dictionary
            }
        ]
    }

    response = requests.post(url, json=full_payload, headers=headers)
    if response.status_code != 200:
        return False
    return True

def send_otp_flow(to, country_code="91"):
    # if not SMS_ON_ACCEPT:
    #     return False
    to_send = str(country_code) + str(to)
    if len(to) == 12:
        to_send = str(to)
    if not re.match(r'^\d{12}$', to_send):
        send_slack_msg(1, str(to_send) + " is invalid number, so could not send: " + str(payload))
        return

    url = "https://control.msg91.com/api/sendotp.php"
    headers = {'authkey': app.config['OTP_AUTH_KEY'], 'content-type': "application/json" }

    # Use a dictionary comprehension to build the full_payload dictionary
    full_payload = {
        "authkey": app.config['OTP_AUTH_KEY'],
        "message": "<#> Your OTP for Drivers4Me is ##OTP##. ",
        "sender": "DRVFME",
        "DLT_TE_ID": "1207161519081388240",
        "mobile": to_send,
        "otp_length": "4"
    }
    response = requests.post(url, data=full_payload)
    print(response.text)
    if response.status_code != 200:
        return False
    return True

def send_msg(to, content, route=4):
    # No-op since this is broken
    print("Non-Flow SMS is not supported!")
    return False
    if not app.config['SEND_OPS_MSG']:
        return False
    to_send = str(COUNTRY_CODE_IN) + str(to)
    if len(to) == 12:
        to_send = str(to)

    if len(to_send) != 12:
        send_slack_msg(1, str(to_send) + " is invalid number, so could not send: " + str(content))
        return
    conn = http.client.HTTPConnection("api.msg91.com")
    uri = "/api/sendhttp.php?sender=" + MSG_SENDER_ID + "&route=" + str(route) + \
          "&mobiles=" + to_send + "&authkey=" + app.config['OTP_AUTH_KEY'] + \
          "&country=0&message=" + content
    conn.request("GET", uri.replace(" ", "%20"))
    res = conn.getresponse()
    if res.status != 200:
        return False
    else:
        return True

def send_email(fr, to, template="test"):

    conn = http.client.HTTPConnection("api.msg91.com")
    payload = '{ "authkey" : "' + app.config['OTP_AUTH_KEY'] + '", "template_id" : "test", "to" : "' + to + '", "from" :"' + fr + '", }'
    print(payload)
    headers = { 'content-type': "application/json" }
    conn.request("POST", "/api/v5/email", payload, headers)
    res = conn.getresponse()
    data = res.read()

    print(data.decode("utf-8"))
    if res.status != 200:
        return False
    else:
        return True

def decode_base64_key(key_base64: str) -> bytes:
    padding_needed = len(key_base64) % 4
    if padding_needed:
        key_base64 += '=' * (4 - padding_needed)
    return base64.urlsafe_b64decode(key_base64)
def encrypt_data(query_string: str) -> str:
    AES_KEY = decode_base64_key(KEY_BASE64_OTP)
    if len(AES_KEY) != 32:
        raise ValueError("AES_KEY must be 32 bytes long for AES-256 encryption.")
    
    aesgcm = AESGCM(AES_KEY)
    iv = os.urandom(12)  # 12 bytes for IV as required by Gupshup
    # Encrypt the query string; no additional authenticated data (AAD) is used.
    ciphertext = aesgcm.encrypt(iv, query_string.encode('utf-8'), None)
    
    # Concatenate IV and ciphertext (ciphertext includes the auth tag)
    encrypted_bytes = iv + ciphertext
    # Encode using URL-safe Base64 encoding.
    encrypted_payload = base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
    return encrypted_payload
def generate_otp_gupshup(phone_no: str, otp_length: int = 4, otp_type: str = "NUMERIC"):
    # Prepare the payload according to the API requirements.
    payload = {
        "password": GUPSHUP_PASSWORD_OTP,
        "method": "TWO_FACTOR_AUTH",
        "v": "1.1",
        "phone_no": phone_no,
        "msg": "Your OTP Verification Number for Drivers4Me is %code%",
        "format": "text",
        "otpCodeLength": otp_length,
        "otpCodeType": otp_type,
    }
    
    # Convert payload to a URL-encoded query string.
    query_string = urlencode(payload)
    print("string 2: ",query_string, flush=True)
    
    # Encrypt the query string as required.
    encrypted_payload = encrypt_data(query_string)
    
    # Build the final request URL.
    request_url = f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&encrdata={encrypted_payload}"
    #request_url = f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&{query_string}"
    print("string: ",request_url, flush=True)
    # Send the GET request to the Gupshup API.
    response = requests.get(request_url)
    print("Response: ",response.text,flush=True)
    return response.text

def validate_otp(mobile, otp):
    try:
        if otp == MASTER_OTP:
            return True
        conn = http.client.HTTPConnection("control.msg91.com")
        headers = {'content-type': "application/x-www-form-urlencoded"}
        mob_str = COUNTRY_CODE_IN + str(mobile)
        content = f"/api/verifyRequestOTP.php?authkey={app.config['OTP_AUTH_KEY']}&mobile={mob_str}&otp={otp}"
        conn.request("POST", content, "", headers)
        res = conn.getresponse()
        data = res.read()
        try:
            resp_arr = json.loads(data.decode("utf-8"))
            return resp_arr['type'] == 'success'
        except Exception:
            return False
    except Exception as e:
        return False
    
# Verify OTP
def verify_otp_gupshup(phone_no: str, otp_code: str):
    # Master override
    if otp_code == MASTER_OTP:
        return {"status": "success", "message": "OTP verified successfully"}

    # Prepare Gupshup request
    payload = {
        "password": GUPSHUP_PASSWORD_OTP,
        "method": "TWO_FACTOR_AUTH",
        "v": "1.1",
        "phone_no": phone_no,
        "otp_code": otp_code,
    }
    query_string = urlencode(payload)
    encrypted_payload = encrypt_data(query_string)
    response = requests.get(
        f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&encrdata={encrypted_payload}"
    )

    print("Response:", response.text, flush=True)

    # Check Gupshup’s reply
    if "success" in response.text:
        return {"status": "success", "success": 1, "message": "OTP verified successfully"}

    # On Gupshup error, fall back to validate_otp
    is_valid = validate_otp(phone_no, otp_code)
    if is_valid:
        return {
            "status": "success",
            "success": 1,
            "message": "OTP verified successfully "
        }
    else:
        # Determine the error message
        if "OTP expired" in response.text:
            msg = "OTP expired"
        elif "invalid OTP" in response.text:
            msg = "Invalid OTP"
        else:
            msg = "Unknown error"
        return {"status": "error", "success": 0, "message": msg}
  
    
def send_bulk_message_gupshup(
    phone_numbers: list[str],
    message: str,
    msg_type: str = "TEXT",
    auth_scheme: str = "plain",
    v: str = "1.1",
    response_format: str = "text",
    **optional_params
) -> str:
    """
    Send an SMS to one or more recipients via GupShup Enterprise API.
    Args:
        phone_numbers (list[str]): List of phone numbers in international format.
        message (str): Message text.
        msg_type (str): Message type (e.g. TEXT, Unicode_text).
        auth_scheme (str): Authentication scheme (default is 'plain').
        v (str): API version.
        response_format (str): Format of API response.
        **optional_params: Additional optional API parameters.
    Returns:
        str: API response.
    """
    # Join phone numbers with commas for bulk sending
    recipients = ",".join(phone_numbers)
        # Base payload
    payload = {
        "userid": GUPSHUP_USER_ID_TRANSACTIONAL,
        "password": GUPSHUP_PASSWORD_TRANSACTIONAL,
        "send_to": recipients,
        "msg": message,
        "msg_type": msg_type,
        "method": "sendMessage",
        "auth_scheme": auth_scheme,
        "v": v,
        "format": response_format,
    }
    # Merge in any optional parameters
    payload.update(optional_params)
    # Encode the query and make the GET request
    query_string = urlencode(payload)
    request_url = f"{GUPSHUP_BASE_URL}?{query_string}"
    print(f"Request URL: {request_url}")
    response = requests.get(request_url)
    print(f"GupShup response: {response.text}")
    return response.text