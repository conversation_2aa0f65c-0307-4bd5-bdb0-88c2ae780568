#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra# === Standard Library ===

# === Standard Library ===

import http.client
from datetime import timedelta,datetime
from flask import current_app as app
from flask_jwt_extended import create_access_token
import base64
import os
import json
import re, requests
import hashlib

# === Third-Party Libraries ===

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

# === Application (Internal) Imports ===

from db_config import db
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from utils.auth.login_utils import create_token
from utils.response_utils import standard_response
from utils.profile.profile_utils import account_enabled
from utils.auth.login_params import ExpiryTime,TokenValidity
from models.models import Users, UserToken, AdminUserLog, Drivers, UserFCM
from urllib.parse import urlencode
from urllib.parse import quote
from config import BaseConfig


"""
Refreshes the access token for an authenticated user using a valid refresh token.

Parameters:
    auth_header (str): The `Authorization` header containing the Bearer refresh token.
    current_user (int): The ID of the currently authenticated user.
    user_agent (str): User-Agent header of the request (used for logging or tracking purposes).

Returns:
    dict: A standardized JSON response containing a new access token if refresh is successful.

Raises:
    ValueError: If the Authorization header is missing or incorrectly formatted.
    LookupError: If the user is not found or is restricted.
    ExpiredTokenError: If the refresh token has expired.
    Exception: For any other unexpected error in token handling.
"""

def handle_token_refresh(auth_header: str, current_user: int) -> dict:
    if not auth_header:
        return standard_response(
            success=-1,
            status=400,
            message="Authorization header is missing.",
            response_status="error"
        )

    if not auth_header.startswith("Bearer "):
        return standard_response(
            success=-2,
            status=400,
            message="Invalid token format. Use 'Bearer <token>'.",
            response_status="error"
        )

    try:
        refresh_token = auth_header.split("Bearer ")[1].strip()
    except IndexError:
        return standard_response(
            success=-2,
            status=400,
            message="Invalid token format. Use 'Bearer <token>'.",
            response_status="error"
        )

    if not account_enabled(current_user):
        return standard_response(
            success=-4,
            status=401,
            message="User restricted",
            response_status="error"
        )

    user = Users.query.filter(Users.id == current_user).first()
    if not user:
        return standard_response(
            success=-4,
            status=401,
            message="User not found"
        )

    token_hash = hashlib.sha512(refresh_token.encode("utf-8")).hexdigest()
    user_token = UserToken.query.filter(
        UserToken.user_id == user.id,
        UserToken.refresh == token_hash
    ).order_by(UserToken.timestamp.desc()).first()

    if not user_token:
        return standard_response(
            success=-4,
            status=401,
            message="Invalid refresh token. Please login again.",
            response_status="error"
        )

    now = datetime.utcnow()
    if user_token.expiry < now:
        return standard_response(
            success=-3,
            status=401,
            message="Refresh token has expired. Please login again.",
            response_status="error"
        )

    # Create new access token
    expires_access = timedelta(days=365)
    identity_with_claims = {
        'id': user.id,
        'roles': user.role,
        'region': user.region,
        'name': f'{user.fname} {user.lname}',
    }
    access_token = create_access_token(
        identity=user.id,
        additional_claims=identity_with_claims,
        expires_delta=expires_access
    )

    return standard_response(
        success=1,
        status=200,
        message="Access token refreshed successfully.",
        data={
            "access_token": access_token,
            "refresh_expiry": user_token.expiry.isoformat()
        },
        response_status="success"
    )

"""
Checks if a mobile number is already registered in the system.

Parameters:
    mobile (str): The 10-digit mobile number to verify.

Returns:
    dict: A standardized JSON response indicating whether the number is registered,
          and if it was created by an admin or a user.

Raises:
    Exception: If a database or query failure occurs.
"""

def check_mobile_existence(mobile: str) -> dict:
    user = Users.query.filter_by(mobile=mobile).first()
    if not user:
        return standard_response(
            success=-2,
            status=404,
            message="Mobile number not found.",
            response_status="error"
        )

    admin_log = AdminUserLog.query.filter_by(user=user.id).first()
    if admin_log and admin_log.action == AdminUserLog.USER_CREATED:
        return standard_response(
            success=2,
            status=200,
            message="Mobile number is registered by admin. Re-registration required.",
            data={"registered_by": "admin"},
            response_status="success"
        )

    return standard_response(
        success=1,
        status=200,
        message="Mobile number is registered.",
        data={"registered_by": "user"},
        response_status="success"
    )    

"""
Checks if a given mobile number is associated with an existing user,
and whether that user is registered as a driver.

Parameters:
    mobile (str): The 10-digit mobile number to check.
    countrycode (str): The country code associated with the mobile number.

Returns:
    dict: A standardized JSON response with one of the following values in `data.exists`:
        - 0: User does not exist.
        - -1: User exists but is banned.
        - 1: User exists but is not a driver.
        - 2: User exists and is registered as a driver.

Raises:
    Exception: If database query or join fails.
"""

def check_driver_existence(mobile: str, countrycode: str) -> dict:
    # Outer join Users to Drivers
    result = Users.query.outerjoin(Drivers, Users.id == Drivers.user)
    result = result.add_entities(Drivers).filter(Users.mobile == mobile).first()

    if not result:
        return standard_response(
            success=1,
            status=200,
            message='The account does not exist.',
            data={'exists': 0},
            response_status="success"
        )

    user, driver = result
    # Check banned status
    if not account_enabled(user.id):
        return standard_response(
            success=1,
            status=200,
            message='The user is banned from the system.',
            data={'exists': -1},
            response_status="success"
        )

    if driver:
        return standard_response(
            success=1,
            status=200,
            message='The account is registered as a driver.',
            data={'exists': 2},
            response_status="success"
        )
    else:
        return standard_response(
            success=1,
            status=200,
            message='The account is registered as a regular user.',
            data={'exists': 1},
            response_status="success"
        )

def store_or_update_fcm_token(user_id: int, fcm_token: str, device: str) -> dict:
    """
    Stores or updates the FCM token for a given user and device.

    Args:
        user_id (int): ID of the authenticated user.
        fcm_token (str): The FCM token to be stored.
        device (str): Device type ('ios' or 'android').

    Returns:
        dict: A standard response with success or error info.
    """
    if not fcm_token or not device:
        return {
            "success": -3,
            "status": "error",
            "message": "Missing or invalid request parameters. 'fcm_token' and 'device' are required."
        }

    if device not in ['ios', 'android']:
        return {
            "success": -5,
            "status": "error",
            "message": "Invalid device type. Must be 'ios' or 'android'."
        }

    if not isinstance(fcm_token, str) or not fcm_token.strip():
        return {
            "success": -4,
            "status": "error",
            "message": "Invalid FCM token format."
        }

    try:
        existing_entry = db.session.query(UserFCM).filter_by(user_id=user_id, device=device).first()

        if existing_entry:
            if existing_entry.token == fcm_token:
                return {
                    "success": -7,
                    "status": "error",
                    "message": "This device is already registered with the given FCM token."
                }
            existing_entry.token = fcm_token
        else:
            new_entry = UserFCM(user_id=user_id, device=device, token=fcm_token)
            db.session.add(new_entry)

        db.session.commit()
        return {
            "success": 1,
            "status": "success",
            "message": "FCM token added successfully."
        }

    except Exception:
        db.session.rollback()
        return {
            "success": -6,
            "status": "error",
            "message": "Unable to save FCM token. Please try again later."
        }