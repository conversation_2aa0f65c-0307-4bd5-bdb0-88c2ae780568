import sys
sys.path.append("/app/")
import os, time

from send_fcm_message import send_fcm_msg_from_config

TIME_TO_SLEEP_MIN = 15

def send_fcm_message_from_files(last_cycle_store):
    new_cycle_store = []
    for file in os.listdir("fcm_config"):
        if file.endswith(".ini"):
            if file not in last_cycle_store:
                print("Sending message in file", file)
                try:
                    res = send_fcm_msg_from_config(os.path.join("fcm_config", file))
                    if not res:
                        raise Exception("Timestamp not matching")
                    new_cycle_store.append(file)
                except Exception as e:
                    print("Could not send, reason", str(e))
            else:
                print("Skipping", file, "as sent in last cycle")
    return new_cycle_store


if __name__ == '__main__':
    last_cycle_store = []
    while True:
        print("Checking all scheduled FCM messages")
        last_cycle_store = send_fcm_message_from_files(last_cycle_store)
        time.sleep(TIME_TO_SLEEP_MIN * 60)