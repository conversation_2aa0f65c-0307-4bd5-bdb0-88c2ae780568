from datetime import datetime, timedelta

from utils.time_utils import convert_to_local_time
from services.customer_app.booking_service import create_search_entry
from models.models import Users, db, DriverSearch, Coupons
from conftest import unique_user_data, create_user_and_driver

# def test_register_search_success(client, customer_login):
#     auth_headers, _ = customer_login()

#     future_time = convert_to_local_time(datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "pickup_loc": "Kolkata Pickup Point",
#         "pickup_time": future_time,
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 200
#     assert json_data["success"] == 1
#     assert "search_id" in json_data["data"]
#     assert "estimate" in json_data["data"]
    
    
# def test_register_search_success_immediate(client, customer_login):
#     auth_headers, _ = customer_login()

#     future_time = convert_to_local_time(datetime.utcnow() + timedelta(minutes=3)).strftime("%Y-%m-%d %H:%M:%S")

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Kolkata Pickup Point",
#         "drop_loc": "Kolkata Drop Point",
#         "pickup_time": future_time,
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 1,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 200
#     assert json_data["success"] == 1
#     assert "search_id" in json_data["data"]
#     assert "estimate" in json_data["data"]

def test_register_search_success_night(client, customer_login):
    auth_headers, _ = customer_login()

    now = datetime.utcnow()
    pickup_time = datetime.combine(now.date() + timedelta(days=1), datetime.strptime("23:30", "%H:%M").time())  # 11:30 PM next day
    pickup_time_local = convert_to_local_time(pickup_time).strftime("%Y-%m-%d %H:%M:%S")

    form_data = {
        "pickup_lat": "22.5726",
        "pickup_long": "88.3639",
        "pickup_loc": "Kolkata Pickup Point",
        "pickup_time": pickup_time_local,
        "duration": "0d 2h 0m",
        "car_type": 1,
        "gear_type": 0,
        "booking_type": 1,
        "ninsurance": 1,
        "is_immediate": 0,
        "region": 0,
        "source": "test_source",
        "coupon_code": ""
    }

    response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data["success"] == 1
    assert "search_id" in json_data["data"]
    assert "estimate" in json_data["data"]
    assert json_data["data"]["estimate"]["fare_breakdown"]["night_charge"] > 0
    assert json_data["data"]["night_flag"] == 1
        
    
# def test_register_search_success_oneway(client, customer_login):
#     auth_headers, _ = customer_login()

#     future_time = convert_to_local_time(datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Kolkata Pickup Point",
#         "drop_loc": "Kolkata Drop Point",
#         "pickup_time": future_time,
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 3,
#         "ninsurance": 1,
#         "is_immediate": 1,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 200
#     assert json_data["success"] == 1
#     assert "search_id" in json_data["data"]
#     assert "estimate" in json_data["data"]
#     assert json_data["data"]["estimate"]['distance_km'] > 0
    
    
# def test_register_search_success_coupon(client, customer_login):
#     auth_headers, _ = customer_login()
#     coupon_code = "TESTCOUPON123"
#     coupon = Coupons(coupon_code, 200, 0, datetime.utcnow() + timedelta(days=30), percent_off=10, max_off=50, redeem_limit=1)
#     db.session.add(coupon)
#     coupon.state = Coupons.LABEL_ACTIVE
#     db.session.commit()
#     future_time = convert_to_local_time(datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Kolkata Pickup Point",
#         "drop_loc": "Kolkata Drop Point",
#         "pickup_time": future_time,
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 3,
#         "ninsurance": 1,
#         "is_immediate": 1,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": "TESTCOUPON123"
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()
#     print(json_data,flush=True)
#     assert response.status_code == 200
#     assert json_data["success"] == 1
#     assert "search_id" in json_data["data"]
#     assert "estimate" in json_data["data"]
#     assert json_data["data"]["estimate"]["fare_breakdown"]["coupon_discount"]  == 50

# def test_register_search_missing_fields(client, customer_login):
#     auth_headers, _ = customer_login()

#     # Missing required fields like pickup_time, car_type etc.
#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "region": 0
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 422
#     assert json_data["success"] == -3
#     assert isinstance(json_data["message"], list)
    
# def test_register_search_invalid_jwt(client):
#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Loc A",
#         "drop_loc": "Loc B",
#         "pickup_time": (datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "unauth_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data)  # No headers
#     json_data = response.get_json()

#     assert response.status_code == 401
#     assert json_data["success"] == -1

# def test_register_search_account_disabled(client, customer_login):
#     auth_headers, user_id = customer_login()

#     user = db.session.query(Users).filter(Users.id == user_id).first()
#     user.enabled = False
#     db.session.commit()

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Loc A",
#         "drop_loc": "Loc B",
#         "pickup_time": (datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 403
#     assert json_data["success"] == -2
    
# def test_register_search_past_pickup_time(client, customer_login):
#     auth_headers, _ = customer_login()

#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Old Loc A",
#         "drop_loc": "Old Loc B",
#         "pickup_time": (datetime.utcnow() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }

#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert response.status_code == 400
#     assert json_data["success"] == -8
    
# def test_invalid_duration_format(client, customer_login):
#     auth_headers, _ = customer_login()
#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Loc A",
#         "drop_loc": "Loc B",
#         "pickup_time": convert_to_local_time(datetime.utcnow() + timedelta(hours=2)).strftime("%Y-%m-%d %H:%M:%S"),
#         "duration": "2 hours",  # INVALID
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": ""
#     }
#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()
    
#     assert response.status_code == 400
#     assert json_data["success"] == -2
#     assert "Invalid duration format" in json_data["message"]
    
# def test_invalid_coupon_code(client, customer_login):
#     auth_headers, _ = customer_login()
#     form_data = {
#         "pickup_lat": "22.5726",
#         "pickup_long": "88.3639",
#         "drop_lat": "22.5740",
#         "drop_long": "88.3700",
#         "pickup_loc": "Loc A",
#         "drop_loc": "Loc B",
#         "pickup_time": convert_to_local_time(datetime.utcnow() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
#         "duration": "0d 2h 0m",
#         "car_type": 1,
#         "gear_type": 0,
#         "booking_type": 1,
#         "ninsurance": 1,
#         "is_immediate": 0,
#         "region": 0,
#         "source": "test_source",
#         "coupon_code": "FAKECODE123"
#     }
#     response = client.post("/api/booking/search", data=form_data, headers=auth_headers)
#     json_data = response.get_json()

#     assert json_data["success"] == -14
#     assert "coupon" in json_data["message"].lower()
    
def test_confirm_booking_success(client, customer_login):
    auth_headers, user_id = customer_login()
    search_entry = create_search_entry(
        search_id="search",
        user_id=user_id,
        car_type=1,
        gear_type=0,
        pickup_lat=22.5726,
        pickup_long=88.3639,
        pickup_time=datetime.utcnow(),
        dur=1.0,  # assuming dur is float hours
        now=datetime.utcnow(),  # ✅ fix: pass actual datetime
        book_type=0,
        days=1,
        ninsurance=0,
        region=0,
        source="test_source",
        coupon_id=None,
        pickup_loc="Pickup Location",
        drop_lat=22.5850,
        drop_long=88.3700,
        drop_loc="Drop Location"
    )
    db.session.add(search_entry)
    db.session.commit()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    payload = {
     "search_id": search_entry.id, 
        "payment_type": 0,
        "is_immediate": 0,
    }
    response = client.post("/api/booking/confirm", data=payload, headers=auth_headers)
    json_data = response.get_json()
    print(json_data)

    assert response.status_code == 200
    assert json_data["success"] == 1
    assert "reference_id" in json_data["data"]
    assert "estimate" in json_data["data"]
    
def test_confirm_booking_invalid_search_id(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post("/api/booking/confirm", data={
        "search_id": 'sdsds',
        "payment_type": 1,
        "is_immediate": 0,
    }, headers=auth_headers)

    json_data = response.get_json()
    assert response.status_code == 400
    assert json_data["success"] == -7
    
def test_confirm_booking_validation_error(client, customer_login):
    auth_headers, _ = customer_login()
    # Missing required fields like 'payment_type'
    response = client.post("/api/booking/confirm", data={}, headers=auth_headers)
    json_data = response.get_json()
    print(json_data)
    assert response.status_code == 422
    assert json_data["success"] == -5
    
    
def test_confirm_booking_expired_search(client, customer_login):
    auth_headers, user = customer_login()

    # Create an old search entry (older than CONFIRM_THRESHOLD_SEARCH)
    old_time = datetime.utcnow() - timedelta(minutes=11)
                                             

    search = DriverSearch(
        id="old_search_id",
        user=user,
        reflat=22.57,
        reflong=88.36,
        reloc="Old Pickup",
        destlat=22.58,
        destlong=88.37,
        destloc="Old Drop",
        time=old_time,
        car_type=1,
        gear_type=0,
        type=1,
        insurance_num=True,
        region=0,
        source="test",
        date=old_time.date(),
        timestamp=old_time,
        dur=timedelta(hours=1)
    )
    db.session.add(search)
    db.session.commit()

    form_data = {"search_id": search.id}
    response = client.post("/api/booking/confirm", data=form_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 400
    assert json_data["success"] == -8
    assert json_data["message"] == "Booking window expired"
    
def test_confirm_booking_user_negative_credit(client, customer_login):
    auth_headers, user = customer_login()

    # Set user credit negative
    user_entry = db.session.query(Users).filter_by(id=user).first()
    user_entry.credit = -50
    db.session.commit()

    # Create a valid search
    now = datetime.utcnow()
    search = DriverSearch(
        id="neg_credit_search_id",
        user=user,
        reflat=22.57,
        reflong=88.36,
        reloc="Pickup",
        destlat=22.58,
        destlong=88.37,
        destloc="Drop",
        time=now,
        car_type=1,
        gear_type=0,
        type=1,
        insurance_num=True,
        region=0,
        source="test",
        date=now.date(),
        timestamp=now,
        dur=timedelta(hours=1)
    )
    db.session.add(search)
    db.session.commit()

    form_data = {"search_id": search.id}
    response = client.post("/api/booking/confirm", data=form_data, headers=auth_headers)
    json_data = response.get_json()
    print(json_data)

    assert response.status_code == 400
    assert json_data["success"] == -9
    assert "not enough credits" in json_data["message"].lower()