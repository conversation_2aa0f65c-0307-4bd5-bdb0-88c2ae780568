from pydantic import BaseModel, field_validator,constr,ValidationInfo
from typing import Optional

class AddDriverDuePayload(BaseModel):
    driver_id: int
    amount: int
    due_type: str
    trans_id: Optional[str] = None
    remark: Optional[str] = ""
    due_type_reason: Optional[str] = ""

    @field_validator("due_type_reason")
    @classmethod
    def validate_due_type_reason(cls, v, info: ValidationInfo):
        due_type = info.data.get("due_type")
        if due_type in ["Fine", "Gift"] and not v:
            raise ValueError(f"{due_type} reason is mandatory for {due_type}")
        return v

    @field_validator("amount")
    @classmethod
    def validate_amount_positive(cls, v):
        if v < 0:
            raise ValueError("Amount should be positive")
        return v
    
class SearchDriverByMobileParams(BaseModel):
    mobile: constr(strip_whitespace=True, min_length=10, max_length=10)
    
class DriverDueLogPayload(BaseModel):
    mobile: constr(strip_whitespace=True, min_length=10, max_length=10)
    
class DuesDriverListPayload(BaseModel):
    region: Optional[int] = -1
    search_query: Optional[str] = None
    filter_by: Optional[str] = None
    sort_by: Optional[str] = None
    l_limit: Optional[int] = 0
    u_limit: Optional[int] = 50
    
FILTER_BY_AVAILABLE = 1
FILTER_BY_NOT_AVAILABLE = 2

SORT_BY_DRIVER_ID_ASC = 1
SORT_BY_DRIVER_ID_DESC = 2
SORT_BY_SCORE_DESC = 3
SORT_BY_SCORE_ASC = 4
SORT_BY_RATING_DESC = 5
SORT_BY_RATING_ASC = 6

