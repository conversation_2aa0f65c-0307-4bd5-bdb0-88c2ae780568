tags:
  - User
summary: Register a new customer
description: |
  Registers a new customer using mobile number, password, first name, and a token for verification. 
  Optionally accepts email and last name. Handles existing mobile/email cases and admin-registered users.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Customer's mobile number
  - name: pwd
    in: formData
    type: string
    required: true
    description: Password to set for the user
  - name: fname
    in: formData
    type: string
    required: true
    description: First name of the customer
  - name: token_and_secret
    in: formData
    type: string
    required: true
    description: Token and secret used to validate mobile number
  - name: lname
    in: formData
    type: string
    required: false
    description: Last name of the customer
  - name: email
    in: formData
    type: string
    required: false
    description: Email address of the customer
  - name: countrycode
    in: formData
    type: string
    required: false
    description: Country code (default is "91")

responses:
  200:
    description: Customer registered successfully
    schema:
      type: object
      properties:
        response:
          type: integer
          example: 0
        msg:
          type: string
          example: "Success"

  400:
    description: Validation error or missing required fields
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Missing required fields: mobile, pwd, fname."

  401:
    description: Invalid token or secret
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid token or secret. Verification failed."

  403:
    description: Token has expired
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -8
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "The token has expired. Please request a new password reset link."

  409:
    description: Mobile or email already registered
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -10
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Email is already registered."

  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -9
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
