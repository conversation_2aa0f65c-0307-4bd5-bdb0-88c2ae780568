#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  tnc.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from flask import Blueprint, render_template, request, jsonify, Response
from _sms import send_msg
from flasgger import swag_from
import re, datetime, uuid, pytz, json
from _utils import get_safe, complete,convert_datetime_to_utc
from booking_params import BookingParams
from book_ride import _get_estimate
import sh, requests, html

website = Blueprint('website', __name__)

@website.route('/contact-us', methods=['GET', 'POST'])
def contact_page():
    return render_template('contactus.html')

@website.route('/delete-account', methods=['GET', 'POST'])
def delete_account():
    return render_template('deleteAccount.html')


@website.route('/terms-and-condition', methods=['GET', 'POST'])
def tnc_page():
    return render_template('tnc.html')


@website.route('/frequently-asked-questions', methods=['GET', 'POST'])
def faq_page():
    return render_template('faq.html')


@website.route('/acko_tnc', methods=['GET', 'POST'])
def acko_tnc_page():
    return render_template('acko_tnc.html')


@website.route('/login', methods=['GET', 'POST'])
def login_page():
    return render_template('login.html')


@website.route('/', methods=['GET', 'POST'])
def main():
    return render_template('homepage.html')


@website.route('/.well-known/assetlinks.json', methods=['GET', 'POST'])
def assetlinks():
    js = [{
        "relation": [
          "delegate_permission/common.handle_all_urls"
        ],
        "target": {
          "namespace": "android_app",
          "package_name": "com.drivers4me",
          "sha256_cert_fingerprints": [
            "E5:FD:0E:83:C8:B7:B7:C7:BA:D1:2E:C8:0F:CB:11:F2:7D:B9:32:9C:EB:65:58:F8:51:6A:1B:72:5E:81:8C:2A"
          ]
        }
      }
    ]
    return Response(json.dumps(js),  mimetype='application/json')


@website.route('/pa-info', methods=['GET', 'POST'])
def painfo():
    with open('/proc/meminfo') as f:
        meminfo = f.read().split("\n")
    with open('/proc/cpuinfo') as f:
        cpuinfo = f.read().replace("\t", "").split("\n")
    key_idx = 0
    ps_data = {}
    ps = sh.ps('aux').split("\n")
    for l in ps:
        ps_data[str(key_idx)] = l
        key_idx += 1
    return jsonify({"meminfo": meminfo, "cpuinfo": cpuinfo,
                    "ps": ps_data})

@website.route('/book', methods=['GET', 'POST'])
def book():
    return render_template('booking.html')

@website.route('/trips', methods=['GET', 'POST'])
def trip():
    return render_template('bookings-section.html')


@website.route('/profile', methods=['GET', 'POST'])
def profile():
    return render_template('user-profile.html')

@website.route('/api/get_estimate', methods=['GET', 'POST'])
@swag_from('/app/swagger_docs/booking_admin/get_booking_estimate.yml')
def get_estimate():
    search_id = uuid.uuid4().urn[9:]
    user = 1 # HACK
    
    source = request.form.get("source","frontpage")
    insurance = request.form.get("insurance", 0, type = int)
    ninsurance = request.form.get("ninsurance", 1, type=int)

    if not complete(request.form, ['src_lat', 'src_lng', 'car_type', 'dur', 'time']):
        return jsonify({'id': -1}), 201

    book_type = int(get_safe(request.form, 'type', BookingParams.TYPE_ROUNDTRIP))
    reflat = request.form['src_lat']
    reflong = request.form['src_lng']
    region = int(get_safe(request.form, 'region', 0))
    time = convert_datetime_to_utc(request.form['time'], tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
    dur = request.form['dur']
    
    try:
        dest_lat = request.form['dest_lat']
        dest_long = request.form['dest_lng']
        dest_loc = ""
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False
    car_type = request.form['car_type']
    return _get_estimate(user, search_id, book_type, car_type, time, dur, reflat, reflong, dest_lat, dest_long, dest_loc, dest_exists, insurance, ninsurance,
                         city=region, source=source)

@website.route('/trip/map/<book_code>', methods=['GET', 'POST'])
def trip_map(book_code):
    if book_code == "FDTO61":
        return render_template('maps2890.html')
    elif book_code == "4GVKUV":
        return render_template('maps8233.html')
    elif book_code == "7RY9H3":
        return render_template('maps7RY9H3.html')
    else:
        return jsonify({"success": -1, "error": {
                "message": "Not enough permissions to view this page"}
                }), 401

@website.route('/website/contact_us', methods=['POST'])
def contact_us_api():
    if not complete(request.form, ['mobile', 'name', 'from', 'to']):
        return jsonify({'id': -1}), 201
    try:
        mobile = str(int(get_safe(request.form, 'mobile', 0)))
    except Exception:
        mobile = "0"
    name = html.escape(str(get_safe(request.form, 'name', "Unknown"))).replace("&", "and").replace("=","equals").replace("#", "hash")
    frm = html.escape(str(get_safe(request.form, 'from', "Unknown")))
    to = html.escape(str(get_safe(request.form, 'to', "Unkwown")))
    url = "https://docs.google.com/forms/d/e/1FAIpQLSc6w1UlBayugJ-IcUMCHAfjpUnbtRcL3qAGGJJMy-ZLGms9xQ/formResponse?usp=pp_url&entry.121050079=" + \
           name + "&entry.1707538659=" + mobile + "&entry.753792398=" + frm + "&entry.611516274=" + to
    req = requests.get(url)
    if req.status_code != 200:
        return jsonify({"success": -1, "url": url, "resp": str(req.content)})
    return jsonify({"success": 1, "url": url})
