#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from datetime import datetime

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from flasgger import swag_from
from flask import current_app as app
from sqlalchemy.orm import aliased
from sqlalchemy.sql import func, case, text, cast
from sqlalchemy.types import Numeric
from sqlalchemy import or_, and_, not_, distinct
from pydantic import ValidationError
import traceback

from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from utils.response_utils import standard_response
from  utils.time_utils import convert_to_utc
from utils.bookings.booking_params import Regions
from schemas.admin_dashboard.analytics_schemas import TransactionSummaryPayload, TransactionSummaryCustomerAdminValidator, \
    TransactionSummaryDriverAdminValidator, DriverInventoryCountValidator, BookingSummaryAdminPayload, \
        AnalyticsDataValidator, LiveRoutesValidator, DeleteRouteDocValidator
        
from services.admin_dashboard.analytics_admin_service import get_customer_transaction_summary_admin, get_driver_transaction_summary_admin, \
    get_driver_inventory_count,booking_summary_admin_service, get_live_routes_data, delete_ongoing_route_doc, get_transaction_summary, \
        fetch_locations, parse_regions
        
analytics_admin = Blueprint('analytics_admin', __name__)
    

@analytics_admin.route('/api/admin/transaction_summary', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary.yml')
def transaction_summary():
    """
    Retrieve a general transaction summary for admin analytics.

    Parses and validates the incoming form data using `TransactionSummaryPayload`,
    and returns financial metrics like bookings, revenue, and cancellations.

    Args:
        None (uses request.form)

    Returns:
        Response: JSON object with success/failure, status code, message, and transaction summary data.
    """
    try:
        payload_dict = request.form.to_dict()
        payload = TransactionSummaryPayload(**payload_dict)
        result, status = get_transaction_summary(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved transaction summary",
                data=result,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=0,
                status=result['status'],
                message=result['message'],
                response_status="error"
            )), status
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Missing or invalid date values",
            response_status="error",
            data={'error': error_details}
        )), 422
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_admin.route('/api/admin/transaction_summary_customer_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary_customer_admin.yml')
def transaction_summary_customer_admin():
    """
    Retrieve customer-specific transaction summary for admin analytics.

    Validates the incoming form data with `TransactionSummaryCustomerAdminValidator`
    and returns customer-based transaction analytics.

    Args:
        None (uses request.form)

    Returns:
        Response: JSON response with customer transaction summary or validation error.
    """
    try:
        payload = TransactionSummaryCustomerAdminValidator(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Validation Error",
            data={'error': error_details},
            response_status="error"
        )), 422
    try:
        result = get_customer_transaction_summary_admin(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved transaction summary",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary_customer_admin: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500


@analytics_admin.route('/api/admin/transaction_summary_driver_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary_driver_admin.yml')
def transaction_summary_driver_admin():
    """
    Retrieve driver-specific transaction summary for admin analytics.

    Validates incoming form data using `TransactionSummaryDriverAdminValidator`
    and returns driver-based transaction analytics.

    Args:
        None (uses request.form)

    Returns:
        Response: JSON object containing driver transaction summary or appropriate error messages.
    """
    try:
        payload = TransactionSummaryDriverAdminValidator(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Validation Error",
            data={'error': error_details},
            response_status="error"
        )), 422

    try:
        result = get_driver_transaction_summary_admin(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved transaction summary",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary_driver_admin: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
       
@analytics_admin.route('/api/admin/driver_inventory_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/driver_inventory_count.yml')
def driver_inventory_count():
    """
    Get inventory statistics for drivers such as vehicle availability or assignment status.

    Validates form data using `DriverInventoryCountValidator`.

    Args:
        None (uses request.form)

    Returns:
        Response: JSON object with driver inventory count data or validation/error details.
    """
    try:
        payload = DriverInventoryCountValidator(**request.form.to_dict())
        result = get_driver_inventory_count(payload)
        return jsonify(standard_response(success=1, status=200, data=result, message="Success")), 200

    except ValueError as ve:
        return jsonify(standard_response(success=0, status=400, message=str(ve))), 400
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Validation Error",
            data={'error': error_details},
            response_status="error"
        )), 422

    except Exception as e:
        app.logger.exception(f"Unexpected error in driver_inventory_count: {e}")
        return jsonify(standard_response(success=-99, status=500, message="Internal Server Error")), 500
    

@analytics_admin.route('/api/admin/booking_summary_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
def booking_summary_admin():
    """
    Retrieve summarized booking analytics for admin dashboard.

    Validates form input using `BookingSummaryAdminPayload` and returns key
    booking metrics such as total, completed, and canceled bookings.

    Args:
        None (uses request.form)

    Returns:
        Response: JSON object with booking summary or error details.
    """
    try:
        payload = BookingSummaryAdminPayload(**request.form.to_dict())
        result, status_code = booking_summary_admin_service(payload)
        if status_code == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved booking summary",
                data=result,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=0,
                status=status_code,
                message=result['message'],
                response_status="error"
            )), status_code
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Missing or invalid parameters",
            response_status="error",
            data={'error': error_details}
        )), 422
    except Exception as e:
        app.logger.exception(f"Unexpected error in booking_summary_admin: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_admin.route('/api/admin/analytics_data', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def analytics_data():
    """
    Fetches analytics data such as trip counts or heatmaps for given date ranges and regions.

    Accepts GET query parameters validated by `AnalyticsDataValidator`, and
    compares data across current and previous periods.

    Args:
        None (uses request.args):
            - data_type (str): Type of analytics data to fetch.
            - from_date (str): Start of current date range.
            - to_date (str): End of current date range.
            - from_date_previous (str, optional): Start of previous date range.
            - to_date_previous (str, optional): End of previous date range.
            - region (list[str], optional): Region filter.

    Returns:
        Response: JSON object containing analytics data for one or two date periods.
    """

    try:
        payload = AnalyticsDataValidator(**request.args)
        regions = parse_regions(payload.region)

        # Convert current period to datetime
        from_date_str, from_time_str = convert_to_utc(payload.from_date, "00:00:00")
        to_date_str, to_time_str = convert_to_utc(payload.to_date, "23:59:59")
        from_date = datetime.strptime(f"{from_date_str} {from_time_str}", '%Y-%m-%d %H:%M:%S')
        to_date = datetime.strptime(f"{to_date_str} {to_time_str}", '%Y-%m-%d %H:%M:%S')

        # Optional previous period
        from_date2 = to_date2 = None
        if payload.from_date_previous and payload.to_date_previous:
            fd2_str, ft2_str = convert_to_utc(payload.from_date_previous, "00:00:00")
            td2_str, tt2_str = convert_to_utc(payload.to_date_previous, "23:59:59")
            from_date2 = datetime.strptime(f"{fd2_str} {ft2_str}", '%Y-%m-%d %H:%M:%S')
            to_date2 = datetime.strptime(f"{td2_str} {tt2_str}", '%Y-%m-%d %H:%M:%S')

            if from_date2 > from_date or to_date2 > to_date:
                return jsonify(standard_response(
                    success=-4,
                    status=400,
                    message="Previous dates cannot be greater than current dates",
                    response_status="error"
                )), 400

        # Fetch data
        locations1 = fetch_locations(payload.data_type, from_date, to_date, regions)
        locations2 = fetch_locations(payload.data_type, from_date2, to_date2, regions) if from_date2 and to_date2 else []

        if locations1 is None or (from_date2 and to_date2 and locations2 is None):
            return jsonify(standard_response(
                success=-2,
                status=400,
                message="Invalid data type",
                response_status="error"
            )), 400
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved analytics data",
            data=[locations1, locations2],
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in analytics_data: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
@analytics_admin.route("/api/admin/analytics/live_routes/<int:status_min>", methods=["GET"])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def route_docs_with_status_filter(status_min):
    """
    Retrieve all ongoing or completed trips based on status threshold.

    Uses a path parameter to filter route documents with a minimum status value.

    Args:
        status_min (int): Minimum trip status (e.g., 1 for active trips, 3+ for completed).

    Returns:
        Response: JSON object with a list of live routes or message indicating no active trips.
    """
    try:
        # Validate path param
        payload = LiveRoutesValidator(status_min=status_min)
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Validation Error",
            response_status="error",
            data={'error': error_details}
        )), 422

    try:
        data= get_live_routes_data(payload.status_min)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="No active trips found" if not data else "Successfully retrieved live routes",
            data=data,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in route_docs_with_status_filter: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
@analytics_admin.route("/api/admin/analytics/delete_route", methods=["DELETE"])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def delete_booking_route_doc():
    """
    Delete a route document (trip) based on booking and driver ID.

    Parses and validates JSON payload using `DeleteRouteDocValidator`.

    Args:
        None (uses request.json):
            - book_id (str): Booking ID to delete.
            - driver_id (str): Associated driver ID.

    Returns:
        Response: JSON response indicating success or failure of deletion.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Request JSON missing",
                response_status="error"
            )), 400

        try:
            validated = DeleteRouteDocValidator(**data)
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Validation error",
                response_status="error",
                data={'error': error_details}
            )), 422

        response, status_code = delete_ongoing_route_doc(
            validated.book_id,
            validated.driver_id
        )
        if status_code == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully deleted route document",
                data=response,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=response['success'],
                status=status_code,
                message=response['message'],
                response_status="error"
            )), status_code

    except Exception:
        error_details = traceback.format_exc()
        app.logger.exception(f"Unexpected error in delete_booking_route_doc: {error_details}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500