#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from pydantic import BaseModel, field_validator, constr
import re

class GenerateOtpPayload(BaseModel):
    mobile: str

    @field_validator("mobile")
    @classmethod
    def validate_mobile(cls, v):
        if not re.fullmatch(r'\d{10}', v):
            raise ValueError("Invalid mobile number")
        return v
    
    
class ValidateAdminOtpPayload(BaseModel):
    mobile: str
    otp: str

    @field_validator("mobile")
    @classmethod
    def valid_mobile(cls, v):
        if not re.fullmatch(r'\d{10}', v):
            raise ValueError("Invalid mobile number")
        return v

    @field_validator("otp")
    @classmethod
    def valid_otp(cls, v):
        if not re.fullmatch(r'\d{4,6}', v):
            raise ValueError("Invalid OTP format")
        return v
    
class AdminLoginPayload(BaseModel):
    mobile: str
    pwd: str

    @field_validator('mobile', 'pwd')
    @classmethod
    def not_empty(cls, v):
        if not v.strip():
            raise ValueError("Field cannot be empty")
        return v
    
class ValidatePasswordChangeOtpPayload(BaseModel):
    mobile: constr(min_length=10, max_length=10)
    otp: constr(min_length=1)
    
class AdminPasswordChangePayload(BaseModel):
    mobile: constr(min_length=10, max_length=10)
    new_pwd: constr(min_length=1)
    
class AdminUserDeletePayload(BaseModel):
    user_id: int
    
    @field_validator("user_id")
    @classmethod
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValueError("Invalid user ID")
        return v