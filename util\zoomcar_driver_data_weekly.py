import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str_1 = (datetime.now() - timedelta(days=8)).date().strftime("%d%m%Y")
date_str_2 = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathOK = D4M_UTIL_PATH + 'output/kolkata-olx-driver.csv'
subjectOK = "OLX Driver Payment - Kolkata - " + date_str_1 + " to " + date_str_2
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
send_mail(from_addr, to_addr_list, subjectOK, content, filepathOK)