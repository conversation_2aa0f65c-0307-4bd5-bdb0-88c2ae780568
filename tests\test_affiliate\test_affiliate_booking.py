import pytest
from unittest.mock import patch, MagicMock
from flask import url_for
from affiliate_b2b.affiliate_models import AffiliateRep, Affiliate, AffiliateToken, AffiliateDriverSearch, AffiliatePriceMapping, AffBookingLogs
from models import Bookings, Trip, BookingAlloc, BookingCancelled,Drivers
from db_config import db
from datetime import datetime, date, time, timedelta
import pytz
from flask import jsonify
from booking_params import BookingParams
from collections import namedtuple
from conftest import create_b2b_booking, create_new_driver, create_master_affiliate, unique_user_data, create_user_and_driver

""" Test cases for api: /api/affiliate/search """

VALID_TIME = (datetime.now(pytz.utc) + timedelta(hours=2)).strftime("%d/%m/%Y %H:%M:%S")

@pytest.fixture
def form_data():
    return {
        'aff_id': 1,
        'trans_type': 3,
        'trip_type': 3,
        'city': 0,
        'reflat': '12.9716',
        'reflong': '77.5946',
        'dur': '2:00:00',
        'time': VALID_TIME
    }

@pytest.fixture
def affiliate_price_mapping(affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    """Fixture to create a test entry in AffiliatePriceMapping table."""
    mapping = AffiliatePriceMapping(
        aff_id=aff_rep.affiliate_id,
        mapped_region=0,
        mapped_trip_type=3,
        mapped_price_affiliate=aff_rep.affiliate_id,
        mapped_price_affiliate_name="Master_Client"
    )
    db.session.add(mapping)
    db.session.commit()
    return mapping

@pytest.fixture()
def create_booking(affiliate_rep_login, create_driver_details):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    driver = create_driver_details
    current_date = datetime.utcnow().date()
    startdate = current_date + timedelta(days=2)
    enddate = current_date + timedelta(days=2)

    starttime = time(10, 30)
    endtime = time(12, 30)

    # Sample booking data
    search_id = '12345'
    booking = Bookings(None, search_id, driver['driver_id'], 22.1, 88.1, starttime, startdate, '01:30:00', endtime, enddate,       
                                    200, 200, 'Kolkata', 3, type=BookingParams.TYPE_B2B)
    aff_id = aff_rep.affiliate_id
    booking.code = 'BOOK123'
    
    db.session.add(booking)
    db.session.commit()
  
    booking_alloc = BookingAlloc(booking.id, driver['driver_id'], aff_rep.id)
    aff_book_logs = AffBookingLogs(aff_id, mapped_by=aff_id, raised_by=aff_id, rep_id=aff_rep.id, book_id=booking.id, mapped_wallet=aff_id)

    db.session.add(aff_book_logs)
    db.session.add(booking_alloc)
    db.session.commit()

    return booking, booking_alloc, aff_book_logs

@pytest.fixture()
def create_trip(affiliate_rep_login, create_booking):
    auth_headers, aff_rep = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking

    # Define trip start time as a datetime (combining booking start date and time)
    start_date = booking.startdate
    start_time = booking.starttime
    trip_start_dt = datetime.combine(start_date, start_time)

    trip = Trip(
        book=booking.id,
        starttime=trip_start_dt,
        lat=22.1,
        lng=88.1,
        status=Trip.TRIP_INIT
    )

    db.session.add(trip)
    db.session.commit()

    return trip


def test_affiliate_search_unauthorized(client):
    res = client.post('/api/affiliate/search', data={})
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_affiliate_search_missing_fields(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    res = client.post('/api/affiliate/search', headers=auth_headers, data={})
    assert res.status_code == 201
    assert res.json['success'] == -1
    assert 'Incomplete' in res.json['message']


def test_affiliate_search_account_disabled(client, affiliate_rep_login, form_data):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    form_data['aff_id'] = str(aff_rep.affiliate_id)
    
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()
    affiliate.enabled = False
    db.session.commit()

    res = client.post('/api/affiliate/search', headers=auth_headers, data=form_data)
    json_data = res.get_json()
    assert res.status_code == 403
    assert res.json['success'] == -1
    assert 'Affiliate account is not enabled' in res.json['message']


def test_affiliate_search_unauthorized_access(client, affiliate_rep_login, form_data):
    auth_headers, aff_rep = affiliate_rep_login
    form_data['aff_id'] = "5"

    res = client.post('/api/affiliate/search', headers=auth_headers, data=form_data)
    json_data = res.get_json()
    print("Response", json_data)
    assert res.status_code == 403
    assert res.json['success'] == -3
    assert 'Unauthorized access' in res.json['message']

def test_affiliate_search_success(client, affiliate_rep_login, form_data, affiliate_price_mapping):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data['aff_id'] = str(rep.affiliate_id)

    # Patch the real function
    with patch('affiliate_b2b.affiliate_booking.get_affiliate_estimate') as mock_estimate:
        # Mock the Flask response
        fake_response_data = {
            'success': 1,
            'cust_total_fare': 100,
            # include other keys if your route uses them
        }
        mock_response = client.application.response_class(
            response=jsonify(fake_response_data).get_data(as_text=True),
            status=200,
            mimetype='application/json'
        )

        # Mock a single model instance (not a list)
        mock_model_instance = MagicMock()
        mock_model_instance._sa_instance_state = MagicMock()

        mock_estimate.return_value = (mock_response, mock_model_instance)

        res = client.post('/api/affiliate/search', headers=auth_headers, data=form_data)
        assert 'estimates' in res.json
        assert len(res.json['estimates']) > 0
        assert 'cust_total_fare' in res.json['estimates'][0]
        assert res.json['estimates'][0]['cust_total_fare'] == 100


def test_affiliate_search_with_return_trip(client, affiliate_rep_login, form_data):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data['aff_id'] = str(rep.affiliate_id)
    form_data['is_return'] = '1'
    form_data['return_time'] = VALID_TIME
    form_data['return_dur'] = '2:00:00'
    form_data['dest_lat'] = '12.2958'
    form_data['dest_long'] = '76.6394'

    with patch('affiliate_b2b.affiliate_booking.get_affiliate_estimate') as mock_estimate:
        # Mock the Flask response
        fake_response_data = {
            'success': 1,
            'cust_total_fare': 100,
            # include other keys if your route uses them
        }
        mock_response = client.application.response_class(
            response=jsonify(fake_response_data).get_data(as_text=True),
            status=200,
            mimetype='application/json'
        )

        # Mock a single model instance (not a list)
        mock_model_instance = MagicMock()
        mock_model_instance._sa_instance_state = MagicMock()

        mock_estimate.return_value = (mock_response, mock_model_instance)

        res = client.post('/api/affiliate/search', headers=auth_headers, data=form_data)
        assert res.status_code == 200
        assert res.json['success'] == 1
        assert len(res.json['estimates']) > 1

def test_affiliate_search_db_error_on_bulk_save(client, affiliate_rep_login, form_data):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data['aff_id'] = str(rep.affiliate_id)

    with patch('affiliate_b2b.affiliate_booking.get_affiliate_estimate') as mock_estimate:
        # Mock the Flask response
        fake_response_data = {
            'success': 1,
            'cust_total_fare': 100,
            # include other keys if your route uses them
        }
        mock_response = client.application.response_class(
            response=jsonify(fake_response_data).get_data(as_text=True),
            status=200,
            mimetype='application/json'
        )

        # Mock a single model instance (not a list)
        mock_model_instance = MagicMock()
        mock_model_instance._sa_instance_state = MagicMock()

        mock_estimate.return_value = (mock_response, mock_model_instance)
        with patch('models.db.session.bulk_save_objects', side_effect=Exception("DB Fail")):
            res = client.post('/api/affiliate/search', headers=auth_headers, data=form_data)
            json_data = res.get_json()
            print("Response", json_data)
            assert res.status_code == 500  # Still returns 200 as per current cod
            assert res.json['success'] == -1


""" Test cases for api: /api/affiliate/book """

@pytest.fixture
def valid_booking_form_data():
    return {
        'aff_id': 1,
        'search_ids': '101',
        'loc': 'Bangalore',
        'remark': 'Urgent',
        'trip_name': 'Conference',
        'trip_type': 3,
        'vehicle_no': 'KA01AB1234,KA02CD5678',
        'source_spoc_name': 'John Doe',
        'source_spoc_contact': '**********',
        'dest_lat': '12.9716',
        'dest_long': '77.5946',
        'dest_loc': 'MG Road',
        'priority': '0',
        'release': 1
    }


def test_affiliate_book_unauthorized(client):
    res = client.post('/api/affiliate/book', data={})
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'


def test_affiliate_book_account_disabled(client, affiliate_rep_login, form_data):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    form_data['aff_id'] = str(aff_rep.affiliate_id)

    affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()
    affiliate.enabled = False
    db.session.commit()

    res = client.post('/api/affiliate/book', headers=auth_headers, data=form_data)
    json_data = res.get_json()
    assert res.status_code == 403
    assert res.json['success'] == -1
    assert 'Affiliate account is not enabled' in res.json['message']

def test_affiliate_book_unauthorized_affiliate(client, affiliate_rep_login, valid_booking_form_data):
    auth_headers, _ = affiliate_rep_login
    valid_booking_form_data['aff_id'] = '999999'  # Not allowed

    res = client.post('/api/affiliate/book', headers=auth_headers, data=valid_booking_form_data)
   
    assert res.status_code == 403
    assert res.json['success'] == -3
    assert 'Unauthorized access' in res.json['message']


def test_affiliate_book_incomplete_form(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    res = client.post('/api/affiliate/book', headers=auth_headers, data={})
    assert res.status_code == 201
    assert res.json['success'] == -1
    assert 'Incomplete form details' in res.json['message']


def test_affiliate_book_invalid_search_id(client, affiliate_rep_login, valid_booking_form_data):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    valid_booking_form_data['aff_id'] = str(rep.affiliate_id)
    valid_booking_form_data['search_ids'] = '929293'

    res = client.post('/api/affiliate/book', headers=auth_headers, data=valid_booking_form_data)
    assert res.status_code == 400
    assert res.json['success'] == 0
    assert 'Search ID' in res.json['message']

def test_affiliate_book_no_search_id(client, affiliate_rep_login, valid_booking_form_data):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    valid_booking_form_data['aff_id'] = str(rep.affiliate_id)
    valid_booking_form_data['search_ids'] = ''
    res = client.post('/api/affiliate/book', headers=auth_headers, data=valid_booking_form_data)

    assert res.status_code == 200
    assert res.json['success'] == -1
    assert 'No search id' in res.json['message']

def test_affiliate_book_insufficient_balance(client, affiliate_rep_login, valid_booking_form_data):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    valid_booking_form_data['aff_id'] = str(aff_rep.affiliate_id)
    valid_booking_form_data['search_ids'] = '101'
    print('Form Data', valid_booking_form_data)
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()
    affiliate.wallet = -10000

    search = AffiliateDriverSearch('101', aff_rep.affiliate_id, aff_rep.id, 3, 22.5726, 88.3639, 22.3072, 73.1812,
            time=time(10, 30), date=date.today(), dur=time(2, 0), timestamp=datetime.now(), estimate=250, source='1-2')
    db.session.add(search)
    db.session.commit()

    res = client.post('/api/affiliate/book', headers=auth_headers, data=valid_booking_form_data)
    json_data = res.get_json()
    print("Response", json_data)
    assert res.status_code == 200
    assert res.json['success'] == -2
    assert 'Not enough credits' in res.json['message']


def test_affiliate_book_success(client, affiliate_rep_login, valid_booking_form_data):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    valid_booking_form_data['aff_id'] = str(aff_rep.affiliate_id)

    search = AffiliateDriverSearch('101', aff_rep.affiliate_id, aff_rep.id, 3, 22.5726, 88.3639, 22.3072, 73.1812,
            time=time(10, 30), date=date.today(), dur=time(2, 0), timestamp=datetime.now(), estimate=250, source='1-2')
    db.session.add(search)
    try:
        db.session.commit()
    except Exception as e:
        print("Exception in creating search:", e)
        db.session.rollback()


    with patch('affiliate_b2b.affiliate_booking.create_book', return_value=('BOOK123', {}, 200)):
        res = client.post('/api/affiliate/book', headers=auth_headers, data=valid_booking_form_data)
        json_data = res.get_json()
        print("Resp", json_data, flush=True)
        assert res.status_code == 200
        assert res.json['success'] == 1
        assert 'rem_limit' in res.json


""" Test cases for api: /api/affiliate/spoc_address_list """

def test_spoc_address_list_unauthorized(client):
    data = {'aff_id': 1, 'region': 1}
    res = client.post('/api/affiliate/spoc_address_list', data=data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_spoc_address_list_incomplete_form(client,affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    form_data = {
        'aff_id': aff_rep.affiliate_id
    }
    response = client.post('/api/affiliate/spoc_address_list', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.get_json()['success'] == -2

def test_get_spoc_address_list_success(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    data = {'aff_id': aff_rep.affiliate_id, 'region': 0}

    response = client.post('/api/affiliate/spoc_address_list', data=data, headers=auth_headers)

    assert response.status_code == 200
    res_json = response.get_json()
    assert res_json['success'] == 1
    assert 'addresses' in res_json
    assert 'spocs' in res_json


""" Test cases for api: /api/affiliate/booking/details """


def test_booking_details_unauthorized(client):
    data = {
        'booking_id': 1
    }
    res = client.post('/api/affiliate/booking/details', data=data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_booking_details_incomplete_form(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    form_data = {}
    response = client.post('/api/affiliate/booking/details', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.get_json()['success'] == -1


def test_booking_details_booking_not_found(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    
    form_data = {
        'booking_id': 2222
    }
    response = client.post('/api/affiliate/booking/details', data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 400
    assert json_data['success'] == -2
    assert json_data['message'] == 'Booking does not exist'


def test_booking_details_exist(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    response_data = create_b2b_booking(client, auth_headers, aff_rep.affiliate_id, aff_rep.id)('1011')
    assert response_data['success'] == 1
    booking = db.session.query(Bookings).filter(Bookings.search_key == '1011').first()
    data = unique_user_data()
    user, driver = create_user_and_driver(data)
    booking.driver = driver.id
    db.session.commit()
    booking_id = booking.id
    form_data = {
        'booking_id': booking.id
    }
    response = client.post('/api/affiliate/booking/details', data=form_data, headers=auth_headers)
    json_data = response.get_json()
    print("Response", json_data)
    assert response.status_code == 200
    assert json_data['success'] == 1
    assert json_data['booking_id'] == booking_id
    assert 'data' in json_data
    assert 'basic_details' in json_data['data']
    assert json_data['data']['basic_details']['book_id'] == booking_id


""" Test cases for api: /api/affiliate/booking/details_update """


def test_booking_details_update_unauthorized(client):
    data = {
        'booking_id': 1
    }
    res = client.post('/api/affiliate/booking/details_update', data=data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_booking_details_update_incomplete_form(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    form_data = {}
    response = client.post('/api/affiliate/booking/details_update', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.get_json()['success'] == -1


def test_booking_details_update_invalid_format_booking_id(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    
    form_data = {
        "booking_id": 'INVALID'
    }
    response = client.post('/api/affiliate/booking/details_update', data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 400
    assert json_data['success'] == -1
    assert json_data['message'] == 'Invalid booking ID'

def test_booking_details_update_booking_not_found(client, affiliate_rep_login):
    auth_headers, aff_rep = affiliate_rep_login
    
    form_data = {
        'booking_id': 2222
    }
    response = client.post('/api/affiliate/booking/details_update', data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 400
    assert json_data['success'] == -1
    assert json_data['message'] == 'No booking found'

def test_booking_details_update_success(client, affiliate_rep_login):
    affiliate_driver = db.session.query(AffiliateDriverSearch).all()
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    response_data = create_b2b_booking(client, auth_headers, aff_rep.affiliate_id, aff_rep.id)('1011')
    assert response_data['success'] == 1
    booking = db.session.query(Bookings).filter(Bookings.search_key == '1011').first()

    form_data = {
        'booking_id': booking.id,
        "vehicle_no": "WB303034"
    }
    response = client.post('/api/affiliate/booking/details_update', data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 200
    assert json_data['success'] == 1
    assert json_data['message'] == 'Booking details updated successfully'


""" Test cases for api: /api/affiliate/booking/add_remark """

# Test: Unauthorized access
def test_add_remark_unauthorized(client):

    form_data = {
        'booking_id': '1', 
        'remark': 'Test'
    }
    res = client.post('/api/affiliate/booking/add_remark', data=form_data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

# Test: Incomplete form
def test_add_remark_incomplete_form(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    form_data = {
        'booking_id': '1'
    }
    res = client.post('/api/affiliate/booking/add_remark', headers=auth_headers, data=form_data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert res.json['message'] == 'Incomplete form'


# Test: Booking does not exist
def test_add_remark_booking_not_found(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query:
        mock_query.return_value.outerjoin.return_value.filter.return_value.first.return_value = None

        res = client.post('/api/affiliate/booking/add_remark', headers=auth_headers, data={
            'booking_id': '999',
            'remark': 'No such booking'
        })

        assert res.status_code == 400
        assert res.json['success'] == -2
        assert res.json['message'] == 'Booking does not exist'


# Test: Booking found and remark updated
def test_add_remark_success(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    booking = MagicMock()
    aff_log = MagicMock()
    aff_log.aff_id = 123
    booking.region = 1

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table') as mock_send:
        mock_query.return_value.outerjoin.return_value.filter.return_value.first.return_value = (booking, aff_log)

        res = client.post('/api/affiliate/booking/add_remark', headers=auth_headers, data={
            'booking_id': '1',
            'remark': 'Updated remark'
        })

        assert res.status_code == 200
        assert res.json['success'] == 1
        assert res.json['message'] == 'Remark saved successfully'
        assert aff_log.comment == 'Updated remark'
        mock_send.assert_called_once_with(1, channel='table', dest_aff_id=123, booking_region=1)


# Test: Exception during DB commit
def test_add_remark_db_exception(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    booking = MagicMock()
    aff_log = MagicMock()
    aff_log.aff_id = 123
    booking.region = 1

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.db.session.commit', side_effect=Exception("DB Error")), \
         patch('affiliate_b2b.affiliate_booking.db.session.rollback') as mock_rollback:

        mock_query.return_value.outerjoin.return_value.filter.return_value.first.return_value = (booking, aff_log)

        res = client.post('/api/affiliate/booking/add_remark', headers=auth_headers, data={
            'booking_id': '1',
            'remark': 'Cause error'
        })

        assert res.status_code == 500
        assert res.json['success'] == -3
        assert 'DB Error' in res.json['message']

""" Test cases for api: /api/affiliate/driver_list """

# Unauthorized access
def test_driver_list_unauthorized(client):
    res = client.post('/api/affiliate/driver_list', data={'booking_id': '1'})
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

# Incomplete form
def test_driver_list_incomplete_form(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    res = client.post('/api/affiliate/driver_list', headers=headers, data={})
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert res.json['message'] == 'Incomplete form'

# Booking not found
def test_driver_list_booking_not_found(client, affiliate_rep_login):
    headers, aff_rep = affiliate_rep_login
   
    res = client.post('/api/affiliate/driver_list', headers=headers, data={'booking_id': '999'})
    assert res.status_code == 404
    assert res.json['success'] == -1
    assert res.json['message'] == 'Booking not found'

# Successful driver list with default sorting (desc) and availability on
def test_driver_list_success_default(client, affiliate_rep_login):
    headers, claims = affiliate_rep_login

    booking = MagicMock()
    booking.region = 1

    mock_drivers = [
        (1, 'John', 'Doe', '**********', 4.5, 'pic1.jpg', 'DL123', 1, 100, 90),
        (2, 'Alice', 'Smith', '8765432109', 4.0, 'pic2.jpg', 'DL456', 1, 80, 70),
    ]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        # Booking query: get
        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        # Driver query chain
        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        # Setup side effect for query
        def query_side_effect(*args, **kwargs):
            # If querying Bookings, it will usually be called with a single model
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect

        # Mock exists().where().correlate()
        mock_subquery = MagicMock(name="has_booking")
        mock_exists.return_value.where.return_value.correlate.return_value = mock_subquery

        mock_aff_config.return_value = {"driverAvailVisEnabled": 1}

        res = client.post('/api/affiliate/driver_list', headers=headers, data={'booking_id': '1'})
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert len(json_data['data']) == 2
        assert json_data['data'][0]['name'] == 'John Doe'

def test_driver_list_search_by_first_name(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    booking = MagicMock()
    booking.region = 1

    mock_drivers = [
        (3, 'Robert', 'Johnson', '7654321098', 4.7, 'pic3.jpg', 'DL789', 1, 50, 40),
    ]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        def query_side_effect(*args, **kwargs):
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect
        mock_aff_config.return_value = {"driverAvailVisEnabled": 1}
        mock_exists.return_value.where.return_value.correlate.return_value = MagicMock()

        res = client.post('/api/affiliate/driver_list', headers=headers, data={
            'booking_id': '1',
            'search_query': 'Robert'
        })
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert len(json_data['data']) == 1
        assert json_data['data'][0]['name'] == 'Robert Johnson'

def test_driver_list_search_by_mobile(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    booking = MagicMock()
    booking.region = 1

    mock_drivers = [
        (4, 'Emma', 'Williams', '7654320000', 4.2, 'pic4.jpg', 'DL321', 1, 30, 20),
    ]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        def query_side_effect(*args, **kwargs):
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect
        mock_aff_config.return_value = {"driverAvailVisEnabled": 1}
        mock_exists.return_value.where.return_value.correlate.return_value = MagicMock()

        res = client.post('/api/affiliate/driver_list', headers=headers, data={
            'booking_id': '1',
            'search_query': '7654320000'
        })
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert len(json_data['data']) == 1
        assert json_data['data'][0]['mobile'] == '7654320000'

def test_driver_list_sort_ascending(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    booking = MagicMock()
    booking.region = 1

    mock_drivers = [
        (2, 'Alice', 'Smith', '8765432109', 4.0, 'pic2.jpg', 'DL456', 1, 80, 70),
        (1, 'John', 'Doe', '**********', 4.5, 'pic1.jpg', 'DL123', 1, 100, 90),
    ]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        def query_side_effect(*args, **kwargs):
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect
        mock_aff_config.return_value = {"driverAvailVisEnabled": 1}
        mock_exists.return_value.where.return_value.correlate.return_value = MagicMock()

        res = client.post('/api/affiliate/driver_list', headers=headers, data={
            'booking_id': '1',
            'sort_by': '1'
        })
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert json_data['data'][0]['name'] == 'Alice Smith'

def test_driver_list_availability_off(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    booking = MagicMock()
    booking.region = 1

    mock_drivers = [
        (1, 'Unavailable', 'Driver', '0000000000', 3.0, 'pic.jpg', 'DL999', 0, 10, 5),
    ]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        def query_side_effect(*args, **kwargs):
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect
        mock_aff_config.return_value = {"driverAvailVisEnabled": 0}
        mock_exists.return_value.where.return_value.correlate.return_value = MagicMock()

        res = client.post('/api/affiliate/driver_list', headers=headers, data={'booking_id': '1'})
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert len(json_data['data']) == 1
        assert json_data['data'][0]['driver_avl'] == 0

def test_driver_list_pagination_has_more(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    booking = MagicMock()
    booking.region = 1

    mock_drivers = [(i, f'F{i}', f'L{i}', f'900000000{i}', 4.0, 'pic.jpg', f'DL{i}', 1, 10, 5) for i in range(1, 27)]

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.AffiliateCollections.affiliates_details.find_one') as mock_aff_config, \
         patch('affiliate_b2b.affiliate_booking.exists') as mock_exists:

        mock_booking_query = MagicMock()
        mock_booking_query.get.return_value = booking

        mock_driver_query = MagicMock()
        mock_driver_query.join.return_value = mock_driver_query
        mock_driver_query.filter.return_value = mock_driver_query
        mock_driver_query.order_by.return_value = mock_driver_query
        mock_driver_query.offset.return_value = mock_driver_query
        mock_driver_query.limit.return_value = mock_driver_query
        mock_driver_query.all.return_value = mock_drivers

        def query_side_effect(*args, **kwargs):
            if len(args) == 1 and getattr(args[0], '__name__', None) == 'Bookings':
                return mock_booking_query
            return mock_driver_query

        mock_query.side_effect = query_side_effect
        mock_aff_config.return_value = {"driverAvailVisEnabled": 1}
        mock_exists.return_value.where.return_value.correlate.return_value = MagicMock()

        res = client.post('/api/affiliate/driver_list', headers=headers, data={'booking_id': '1', 'page': '1', 'limit': '25'})
        json_data = res.get_json()

        assert res.status_code == 200
        assert json_data['success'] == 1
        assert json_data['has_more'] is True
        assert len(json_data['data']) == 25


""" Test cases for api: /api/affiliate/booking/cancel_charge """

def test_cancel_charge_unauthorized(client):
    form_data = {
        'book_code': 'XYZ', 
        'reason': '1'
    }
    res = client.post('/api/affiliate/booking/cancel_charge', data=form_data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_cancel_charge_incomplete_form(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    form_data = {
        'book_code': 'XYZ'
    }
    res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data=form_data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert res.json['message'] == 'Incomplete Form Details'

def test_cancel_charge_invalid_reason_format(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    form_data = {
        'book_code': 'XYZ', 
        'reason': 'abc'
    }
    res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data=form_data)
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert res.json['message'] == 'Invalid Data Format'

def test_cancel_charge_booking_not_found(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login
    form_data = {
        'book_code': 'INVALID', 
        'reason': '1'
    }

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query:
        mock_query.return_value.outerjoin.return_value.filter.return_value.first.return_value = None

        res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data=form_data)
        assert res.status_code == 404
        assert res.json['success'] == -1
        assert res.json['message'] == 'Booking not found'

def test_cancel_charge_trip_checked_in(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, _, aff_book_logs = create_booking
    booking_code = booking.code
    trip  = Trip(booking.id, datetime.now(), 22.1, 88.1, Trip.TRIP_START_PIC)
    db.session.add(trip)
    db.session.commit()

    # Setup Trip mock
    form_data = {
        'book_code': booking_code,
        'reason': '1'
    }

    res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data=form_data)
    json_data = res.get_json()
    assert res.status_code == 200
    assert res.json['success'] == -2
    assert res.json['message'] == 'Can not cancel after checkIn'


def test_cancel_charge_waiver_applied(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, _, aff_book_logs = create_booking

    mock_booking = MagicMock(
        driver=2,
        startdate=date.today(),
        starttime=datetime.now().time(),
        type='test'
    )
    mock_trip = None
    mock_last_alloc_time = datetime.utcnow() - timedelta(minutes=1)

    mock_affiliate = MagicMock()
    mock_affiliate.client_name = "TestClient"

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.BookingCancelled.WAIVER', new={1}), \
         patch('affiliate_b2b.affiliate_booking.BookingCancelled.FORGIVE_DELTA', new=timedelta(minutes=5)):

        # First query chain (booking, trip, time)
        mock_outerjoin = MagicMock()
        mock_outerjoin.filter.return_value.first.return_value = (
            mock_booking, mock_trip, mock_last_alloc_time
        )
        mock_query.return_value.outerjoin.return_value = mock_outerjoin

        # Second query chain (logs and affiliate)
        def join_side_effect(*args, **kwargs):
            mock_join = MagicMock()
            mock_join.filter.return_value.first.return_value = (
                aff_book_logs, mock_affiliate
            )
            return mock_join

        mock_query.return_value.join.side_effect = join_side_effect

        res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data={
            'book_code': booking.code,
            'reason': '1'
        })

        assert res.status_code == 200
        assert res.json['success'] == 1
        assert res.json['charge'] == [0, 0]

def test_cancel_charge_success_with_penalty(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, _, aff_book_logs = create_booking

    mock_booking = MagicMock(
        driver=2,
        startdate=date.today(),
        starttime=datetime.now().time(),
        type='test'
    )
    mock_trip = None
    mock_last_alloc_time = datetime.utcnow() - timedelta(minutes=10)

    mock_affiliate = MagicMock()
    mock_affiliate.client_name = "TestClient"

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch:

        # First query (booking, trip, last_alloc_time)
        mock_outerjoin = MagicMock()
        mock_outerjoin.filter.return_value.first.return_value = (
            mock_booking, mock_trip, mock_last_alloc_time
        )
        mock_query.return_value.outerjoin.return_value = mock_outerjoin

        # Second query (aff_booking_logs and affiliate)
        def join_side_effect(*args, **kwargs):
            mock_join = MagicMock()
            mock_join.filter.return_value.first.return_value = (
                aff_book_logs, mock_affiliate
            )
            return mock_join

        mock_query.return_value.join.side_effect = join_side_effect

        # Penalty charges mock
        mock_cancel_ch.return_value = ([50, 2], 1)

        res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data={
            'book_code': booking.code,
            'reason': '1'
        })

        assert res.status_code == 200
        assert res.json['success'] == 1
        assert res.json['charge'] == [50, 2]
     
def test_cancel_charge_driver_is_default(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, _, aff_book_logs = create_booking

    mock_booking = MagicMock(
        driver=1,  # Default driver
        startdate=date.today(),
        starttime=datetime.now().time(),
        type='test'
    )
    mock_trip = None
    mock_last_alloc_time = datetime.utcnow()

    mock_affiliate = MagicMock()
    mock_affiliate.client_name = "TestClient"

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query:

        # First query (booking, trip, last_alloc_time)
        mock_outerjoin = MagicMock()
        mock_outerjoin.filter.return_value.first.return_value = (
            mock_booking, mock_trip, mock_last_alloc_time
        )
        mock_query.return_value.outerjoin.return_value = mock_outerjoin

        # Second query (aff_booking_logs and affiliate)
        def join_side_effect(*args, **kwargs):
            mock_join = MagicMock()
            mock_join.filter.return_value.first.return_value = (
                aff_book_logs, mock_affiliate
            )
            return mock_join

        mock_query.return_value.join.side_effect = join_side_effect

        res = client.post('/api/affiliate/booking/cancel_charge', headers=headers, data={
            'book_code': booking.code,
            'reason': '1'
        })

        assert res.status_code == 200
        assert res.json['success'] == 1
        assert res.json['charge'] == [0, 0]


""" Test cases for api: /api/affiliate/booking/cancel """

def test_booking_cancel_unauthorized(client):
    form_data = {
        'book_code': 'XYZ', 
        'reason': '1'
    }
    res = client.post('/api/affiliate/booking/cancel', data=form_data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_booking_cancel_incomplete_form(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    res = client.post('/api/affiliate/booking/cancel', headers=headers, data={'book_code': 'BOOK123'})
    assert res.status_code == 400
    assert res.json['success'] == -1
    assert res.json['message'] == 'Incomplete Form Details'

def test_booking_cancel_invalid_reason(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    res = client.post('/api/affiliate/booking/cancel', headers=headers, data={'book_code': 'BOOK123', 'reason': 'invalid'})
    assert res.status_code == 400
    assert res.json['success'] == 0
    assert res.json['message'] == 'Invalid reason code.'


def test_booking_cancel_booking_not_found(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query:
        mock_filter = MagicMock()
        mock_filter.first.return_value = None

        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
        mock_outerjoin.filter.return_value = mock_filter

        mock_query.return_value = mock_outerjoin

        res = client.post('/api/affiliate/booking/cancel', headers=headers, data={'book_code': 'INVALID', 'reason': '1'})

        assert res.status_code == 404
        assert res.json['success'] == -1
        assert res.json['message'] == 'Booking not found'

def test_booking_cancel_already_cancelled_trip(client, affiliate_rep_login, create_booking, create_trip):
    headers, aff_rep = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip
    
    booking.valid = -1
    db.session.commit()
    # Set up mock wallet object
    mock_wallet_obj = MagicMock()
    mock_wallet_obj.wallet = 1000
    mock_wallet_obj.id = 42


    with patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute, \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_affiliate'), \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_update_of_booking'), \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table'):

        # Mock the query chain to return our custom result
        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
       
        # Set return values for penalty and wallet computation
        mock_cancel_ch.return_value = ([50, 10], 2)  # (user_penalty, driver_penalty), penalty_flag
        mock_wallet_compute.return_value = (900, 800)  # new_user_wallet, new_driver_wallet

        # Prepare form data for cancellation
        form_data = {
            'book_code': booking.code,
            'reason': '12'
        }
        
        res = client.post('/api/affiliate/booking/cancel', headers=headers, data=form_data)
       
        assert res.status_code == 200
        assert res.json['success'] == 0
        assert 'already cancelled' in res.json['message'].lower()

def test_booking_cancel_affiliate_not_found(client, affiliate_rep_login, create_booking, create_trip):
    headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()
   
    # Set up mock wallet object
    mock_wallet_obj = MagicMock()
    mock_wallet_obj.wallet = 1000
    mock_wallet_obj.id = 42

    # Construct a namedtuple to simulate the DB query result
    ResultTuple = namedtuple(
        'ResultTuple',
        ['Bookings', 'AffBookingLogs', 'Affiliate', 'Trip', 'mapped_by_alias', 'mapped_wallet_alias']
    )
    mock_result = ResultTuple(
        Bookings=booking,
        AffBookingLogs=aff_book_logs,
        Affiliate=None,
        Trip=trip,
        mapped_by_alias=affiliate,
        mapped_wallet_alias=mock_wallet_obj
    )

    with patch('affiliate_b2b.affiliate_booking.db.session.query') as mock_query, \
         patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute, \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_affiliate'), \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_update_of_booking'), \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table'):

        # Mock the query chain to return our custom result
        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
        mock_outerjoin.filter.return_value.first.return_value = mock_result
        mock_query.return_value = mock_outerjoin

        # Set return values for penalty and wallet computation
        mock_cancel_ch.return_value = ([50, 10], 2)  # (user_penalty, driver_penalty), penalty_flag
        mock_wallet_compute.return_value = (900, 800)  # new_user_wallet, new_driver_wallet

        # Prepare form data for cancellation
        form_data = {
            'book_code': booking.code,
            'reason': '12'
        }
        
        res = client.post('/api/affiliate/booking/cancel', headers=headers, data=form_data)
       
        assert res.status_code == 404
        assert res.json['success'] == -1
        assert res.json['message'] == 'Affiliate not found'

def test_booking_cancel_after_checkin(client, affiliate_rep_login, create_booking, create_trip):
    headers, aff_rep = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip
    trip.status = Trip.TRIP_STOP_PIC
    db.session.commit()
   
    # Set up mock wallet object
    mock_wallet_obj = MagicMock()
    mock_wallet_obj.wallet = 1000
    mock_wallet_obj.id = 42


    with patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute, \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_affiliate'), \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_update_of_booking'), \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table'):

        # Mock the query chain to return our custom result
        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
    
        # Set return values for penalty and wallet computation
        mock_cancel_ch.return_value = ([50, 10], 2)  # (user_penalty, driver_penalty), penalty_flag
        mock_wallet_compute.return_value = (900, 800)  # new_user_wallet, new_driver_wallet

        # Prepare form data for cancellation
        form_data = {
            'book_code': booking.code,
            'reason': '12'
        }
        
        res = client.post('/api/affiliate/booking/cancel', headers=headers, data=form_data)
        
        assert res.status_code == 200
        assert res.json['success'] == -2
        assert res.json['message'] == 'Can not cancel after checkIn'

def test_booking_cancel_success_with_penalty(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, _ = create_booking
    booking.driver = 2
    mock_last_alloc_time = datetime.utcnow() - timedelta(minutes=20)
    booking_alloc.timestamp = mock_last_alloc_time
    db.session.commit()

    # Patch necessary functions and values
    with patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute, \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_affiliate'), \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_update_of_booking'), \
         patch('affiliate_b2b.affiliate_booking.live_update_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table'):

        # Mock the query chain to return our custom result
        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
       

        # Set return values for penalty and wallet computation
        mock_cancel_ch.return_value = ([50, 0], 2)  # (user_penalty, driver_penalty), penalty_flag
        mock_wallet_compute.return_value = (900, 800)  # new_user_wallet, new_driver_wallet

        # Prepare form data for cancellation
        form_data = {
            'book_code': booking.code,
            'reason': '12'
        }

        res = client.post('/api/affiliate/booking/cancel', headers=headers, data=form_data)
        json_data = res.get_json()
        assert json_data['success'] == 1
        assert json_data['penalty'] == [50, 0]

def test_booking_cancel_success_with_waiver_reason(client, affiliate_rep_login, create_booking, create_trip):
    headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip

    affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()

    mock_wallet_obj = MagicMock()
    mock_wallet_obj.wallet = 1000
    mock_wallet_obj.id = 42


    with patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute, \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_affiliate'), \
         patch('affiliate_b2b.affiliate_booking.send_notification_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_update_of_booking'), \
         patch('affiliate_b2b.affiliate_booking.live_update_to_channel'), \
         patch('affiliate_b2b.affiliate_booking.send_live_aff_booking_table'):

        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin

        mock_cancel_ch.return_value = ([0, 0], 0)  # penalty waived
        mock_wallet_compute.return_value = (1000, 1000)

        form_data = {
            'book_code': booking.code,
            'reason': '2'  # Assume '0' is a waiver reason code
        }

        res = client.post('/api/affiliate/booking/cancel', headers=headers, data=form_data)
        json_data = res.get_json()
        assert res.status_code == 200
        assert res.json['success'] == 1
        assert res.json['penalty'] == [0, 0]


""" Test cases for api: /api/affiliate/driver/allocate """

def test_driver_allocate_unauthorized(client):
    form_data = {
        'book_code': 'XYZ', 
        'driver_id': 1
    }
    res = client.post('/api/affiliate/driver/allocate', data=form_data)
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'

def test_driver_allocate_incomplete_form(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'driver_id': 1 
    })

    json_data = res.get_json()
    assert res.status_code == 400
    assert json_data['success'] == -1
    assert json_data['message'] == 'Incomplete Form Details'

def test_driver_allocate_invalid_driver_id_format(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': 'SOME_BOOK_CODE',
        'driver_id': 'abc'  # Invalid format
    })

    json_data = res.get_json()
    assert res.status_code == 400
    assert json_data['success'] == -1
    assert json_data['message'] == 'Invalid Format'

def test_driver_allocate_booking_not_found(client, affiliate_rep_login):
    headers, _ = affiliate_rep_login

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': 'INVALID_CODE',
        'driver_id': 1
    })

    json_data = res.get_json()
    assert res.status_code == 404
    assert json_data['success'] == -1
    assert json_data['message'] == 'Booking not found'

def test_driver_allocate_same_driver(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking = create_booking[0]

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': booking.code,
        'driver_id': booking.driver  # Same driver
    })

    json_data = res.get_json()
    assert res.status_code == 400
    assert json_data['success'] == -3
    assert json_data['message'] == 'Allocating same driver'

def test_driver_allocate_driver_not_found(client, affiliate_rep_login, create_booking, create_trip):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': booking.code,
        'driver_id': 333,
        'change': 1,
        'reason': 1
    })

    json_data = res.get_json()
    assert res.status_code == 404
    assert json_data['success'] == -2
    assert json_data['message'] == 'Driver Not Found'

def test_driver_allocate_success_with_change(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
   
    driver, driver_info = create_new_driver()
    with patch('affiliate_b2b.affiliate_booking.Price.get_cancel_ch') as mock_cancel_ch, \
         patch('affiliate_b2b.affiliate_booking.compute_driver_wallet') as mock_wallet_compute:
        # Mock the query chain to return our custom result
        mock_outerjoin = MagicMock()
        mock_outerjoin.outerjoin.return_value = mock_outerjoin
       
        # Set return values for penalty and wallet computation
        mock_cancel_ch.return_value = ([50, 10], 2) 
        mock_wallet_compute.return_value = (1000, 1000)
        res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
            'book_code': booking.code,
            'driver_id': driver.id,
            'change': 1,
            'reason': 2,
            'reason_details': 'User requested'
        })

        json_data = res.get_json()
        print("Response", json_data)
        assert res.status_code == 200
        assert json_data['success'] == 1
        assert json_data['message'] == 'Driver allocated successfully'


def test_driver_allocate_change_after_checkin(client, affiliate_rep_login, create_booking, create_trip):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    trip = create_trip
    trip.status = Trip.TRIP_REACHED_DEST
    db.session.commit()
    driver, driver_info = create_new_driver()

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': booking.code,
        'driver_id': driver.id,
        'change': 1,
        'reason': 1
    })

    json_data = res.get_json()
    assert res.status_code == 400
    assert json_data['success'] == -2
    assert json_data['message'] == 'Cannot change driver after check-in'


def test_driver_allocate_success_without_change(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    booking.valid = 0
    db.session.commit()
    new_driver, driver_info = create_new_driver()
    new_driver_id = new_driver.id
   

    res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
        'book_code': booking.code,
        'driver_id': new_driver_id
    })

    json_data = res.get_json()
    assert res.status_code == 200
    assert json_data['success'] == 1
    assert json_data['message'] == 'Driver allocated successfully'


def test_driver_allocate_db_error(client, affiliate_rep_login, create_booking):
    headers, _ = affiliate_rep_login
    booking, booking_alloc, aff_book_logs = create_booking
    driver, driver_info = create_new_driver()

    with patch('affiliate_b2b.affiliate_booking.db.session.commit', side_effect=Exception('DB error')):
        res = client.post('/api/affiliate/driver/allocate', headers=headers, data={
            'book_code': booking.code,
            'driver_id': driver.id
        })

        json_data = res.get_json()
        assert res.status_code == 500
        assert json_data['success'] == 0
        assert json_data['message'] == 'Database error'

