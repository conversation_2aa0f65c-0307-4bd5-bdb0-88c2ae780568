# Importing the Flask app instance
from main import app

# Importing necessary modules for date comparison
from datetime import datetime, date,timedelta

from sqlalchemy.sql import func,or_,and_

# Importing required database models and session
from models import Drivers, DriverInfo, DriverApprovalLog, db,Users,Bookings,Trip,DriverDetails

from _fcm import send_fcm_msg_driver
import _sms

# --- Main Function ---
def unapprove_expired_and_inactive_drivers():
    # Get today's date for comparison
    today = date.today()
    cutoff_date = datetime.now() - timedelta(days=60)

    try:
        # Query to find all drivers who are currently approved
        # but have a license expiration date before today
        expired_drivers = db.session.query(Drivers,Users).join(
            DriverInfo, Drivers.id == DriverInfo.driver_id
        ).join(Users, Drivers.user == Users.id).filter(
            Drivers.approved == Drivers.APPROVED,  # Driver is currently approved
            DriverInfo.license_exp < today         # Driver's license is expired
        ).all()
        
                
        inactive_drivers = (
            db.session.query(Drivers,Users).join(Users, Drivers.user == Users.id)
             .join(DriverInfo, DriverInfo.driver_id == Drivers.id)
             .join(DriverDetails, DriverDetails.driver_id == Drivers.id)
             .outerjoin(Bookings, Bookings.driver == Drivers.id)   # allow drivers with no bookings
            .outerjoin(Trip, Trip.book_id == Bookings.id)         # allow drivers with no trips
            .filter(Drivers.approved == Drivers.APPROVED)
            .filter(
                func.coalesce(DriverDetails.reactivation_ts, DriverInfo.timestamp) < cutoff_date
            ) 
            .group_by(Drivers.id, Users.id, DriverInfo.timestamp, DriverDetails.approval_ts)
              .having(
                or_(
                    func.max(Trip.endtime) == None,         # never done a trip
                    func.max(Trip.endtime) < cutoff_date    # last trip older than 60 days
                )
            )
            .all()
        )
    

        # Iterate through each expired driver and unapprove them
        for driver,user in expired_drivers:
            old_status = driver.approved  # Save old approval status
            driver.approved = Drivers.PENDING  # Set new status to unapproved

            # Create a new log entry to record the change
            log = DriverApprovalLog(
                driver=int(driver.id),
                changes="Approval",
                approval=Drivers.PENDING,
                editedby="Automatic System",  # Marked as system-generated
                change_from=str(old_status),
                change_to=str(Drivers.PENDING),
                remark="Unapproved due to expired license"
            )

            # Add the log entry to the session
            db.session.add(log)
            print(f"Unapproved (Pending) driver {driver.id} {user.get_name()} due to expired license")

            # Log each driver's unapproval for audit purposes
            fcm_title = "License Expired"
            fcm_smalltext = f"Your license has expired. Please update your license to continue driving with us."
            fcm_bigtext = (
                "Your account has been temporarily unapproved as your driving license has expired. Please update your renewed license"
            )
            send_fcm_msg_driver( driver.id,title=fcm_title,smalltext=fcm_smalltext,bigtext=fcm_bigtext)
            msg=f"Hi {user.get_name()}, Your account has been temporarily unapproved by Drivers4Me as your driving license has expired. Please update your renewed license to regain approval and continue driving with us."
            _sms.send_bulk_message_gupshup(phone_numbers=[user.mobile],
                                       message = msg,
                                       mask = _sms.MASK,
                                       dltTemplateId = _sms.TEMPLATE_ID_MAPPING['D4M_Driver_DL_Expiry'],
                                       principalEntityId = _sms.PRINCIPAL_ENTITY_ID
            )

        # Commit all the changes to the database
        db.session.commit()
        for driver,user in inactive_drivers:
            old_status = driver.approved  # Save old approval status
            driver.approved = Drivers.INACTIVE  # Set new status to unapproved
            
            log = DriverApprovalLog(
                driver=int(driver.id),
                changes="Approval",
                approval=Drivers.INACTIVE,
                editedby="Automatic System",  # Marked as system-generated
                change_from=str(old_status),
                change_to=str(Drivers.INACTIVE),
                remark="Inactive due to inactivity for 60 days"
            )
            db.session.add(log)
            db.session.commit()
            print(f"Marked driver {driver.id} {user.get_name()} as inactive due to inactivity for 60 days")
            fcm_title = "Account Inactive"
            fcm_smalltext = f"Your account has been marked inactive due to inactivity for 60 days."
            fcm_bigtext = (
                "Your account has been marked inactive due to inactivity for 60 days. Please come to office and pay reactivation fee to reactivate your account."
            )
            send_fcm_msg_driver( driver.id,title=fcm_title,smalltext=fcm_smalltext,bigtext=fcm_bigtext)
            days = "60 days" 
            msg=f"Hi {user.get_name()}, Your account has been marked inactive by Drivers4Me due to inactivity for {days}. Please come to office and pay reactivation fee to reactivate your account."
            _sms.send_bulk_message_gupshup(phone_numbers=[user.mobile],
                                       message = msg,
                                       mask = _sms.MASK,
                                       dltTemplateId = _sms.TEMPLATE_ID_MAPPING['D4M_Driver_Inactive'],
                                       principalEntityId = _sms.PRINCIPAL_ENTITY_ID
            )

        # Add the log entry to the session

    except Exception as e:
        # Roll back in case of any error during DB operations
        db.session.rollback()
        print(f"[ERROR] Failed to unapprove expired drivers: {e}")

# Execute the function within the Flask application context
if __name__ == '__main__':
    with app.app_context():
        unapprove_expired_and_inactive_drivers()
