from utils.bookings.booking_params import BookingParams, Regions
import math
import time as tm
from ..pricing.price_b2c import PriceCity, PriceCityOutstation
from ..pricing.price_b2b import PriceOneWay, PriceRound, PriceOutStation
from models.models import Trip, Bookings, BookPricing, db
from datetime import datetime, timedelta

price_map = { Regions.REGN_KOLKATA: "price_kolkata", Regions.REGN_HYDERABAD: "price_hyderabad", Regions.REGN_GUWAHATI: "price_guwahati",
                Regions.REGN_NAGPUR: "price_nagpur", Regions.REGN_PUNE: "price_pune", Regions.REGN_MUMBAI: "price_mumbai", Regions.REGN_DELHI: "price_delhi",
                Regions.REGN_CHENNAI: "price_chennai", Regions.REGN_BANGALORE: "price_bangalore", Regions.REGN_AHMEDABAD: "price_ahmedabad",
                Regions.REGN_SILIGURI: "price_siliguri", Regions.REGN_BHUBANESWAR: "price_bhubaneswar", Regions.REGN_PATNA: "price_patna",
                Regions.REGN_RANCHI: "price_ranchi", Regions.REGN_GURGAON: "price_gurgaon", Regions.REGN_NOIDA: "price_noida", Regions.REGN_JAIPUR: "price_jaipur",
                Regions.REGN_LUCKNOW: "price_lucknow", Regions.REGN_CHANDIGARH: "price_chandigarh", Regions.REGN_ALAMPUR: "price_alampur"
            }

class Price:
    # gst
    CGST = 0.025
    SGST = 0.025
    price_city = None

    # Hour ratio X => divide hour into X intervals (60/X duration).
    # For every extra interval charge the penalty
    HOUR_RATIO = 60

    @staticmethod
    def get_cgst():
        return PriceCity.get_cgst()

    @staticmethod
    def get_sgst():
        return PriceCity.get_sgst()

    @staticmethod
    def get_hour_ratio():
        return PriceCity.get_hour_ratio()

    @staticmethod
    def get_cancel_ch(curtime, starttime, city=0, has_trip=False, cancel_cat=0, is_ontrip=False, type=0, client_name=""):
        if type == BookingParams.TYPE_B2B:
            return PriceOneWay.get_cancel_ch(curtime, starttime, cancel_cat, has_trip, client_name)
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_cancel_ch(curtime, starttime, has_trip, cancel_cat, is_ontrip)

    @staticmethod
    def get_booking_ch(car_type, driver_id, booking_id, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_booking_ch(car_type, driver_id, booking_id)

    @staticmethod
    def get_booking_percent(booking_id, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_booking_percent(booking_id)

    @staticmethod
    def get_driver_charge(booking_id, city=0, booking = None, trip = None, book_pricing = None):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        book_percent = Price.price_city.get_booking_percent(booking_id, book = booking)
        driver_charge = price = due = 0
        if not trip:
            trip = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
        if not booking:
            booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        if not book_pricing:
            book_pricing = db.session.query(BookPricing).filter(BookPricing.book_id == booking_id).first()
        estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.get_hour_ratio() + \
                             math.ceil((booking.dur.minute * Price.get_hour_ratio()) / 60)
        try:
            if not trip and book_pricing and (booking.valid == Bookings.UNALLOCATED or booking.valid == Bookings.ALLOCATED):
                driver_charge = (book_pricing.estimate - book_pricing.insurance_ch - (book_pricing.estimate * (book_percent))) 
                due = book_pricing.insurance_ch + (book_pricing.estimate * (book_percent))
            elif (not trip and booking.valid < Bookings.ALLOCATED) or (trip and trip.status > Trip.TRIP_STARTED):
                driver_charge = (booking.estimate - booking.insurance_cost - (booking.estimate * (book_percent)))
                due = booking.insurance_cost + (booking.estimate * (book_percent))
            else:
                stop_time = datetime.utcnow()
                delta = stop_time - trip.starttime
                time_delta = convert_to_semihours(delta, Price.get_hour_ratio())
                if trip.endtime and trip.status == 0:
                    driver_charge = trip.price - (trip.net_rev if trip.net_rev else (booking.insurance_cost + trip.price * (book_percent)))
                    due = trip.net_rev
                else:
                    if booking.type not in [BookingParams.TYPE_OUTSTATION, BookingParams.TYPE_OUTSTATION_ONEWAY]:
                        price, pre_tax, cgst, sgst, total_due, ot_fare, night_fare = Price.get_trip_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=booking.estimate_pre_tax,
                                                    book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                                    trip_starttime=trip.starttime.time(), trip_stoptime=stop_time.time(),
                                                    startdate=trip.starttime.date(), enddate=stop_time.date(), insurance_ch=booking.insurance_cost,
                                                    city=booking.region)
                    else:
                        price, pre_tax, cgst, sgst, total_due, ot_fare, night_fare = PriceOutstation.get_trip_price(startdate=trip.starttime.date(), enddate=stop_time.date(),
                                                                book_delta=estimate_delta, real_delta=time_delta, est=booking.estimate_pre_tax, insurance_ch=booking.insurance_cost,
                                                                city=booking.region)
                    driver_charge = price - total_due
                    due = total_due
        except Exception as e:
            print(str(e), flush = True)
        return driver_charge, round(due)
    

    @staticmethod
    def get_booking_ch_new(booking_info, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_booking_ch_new(booking_info)

    @staticmethod
    def get_insurance_ch(booking_id, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_insurance_ch(booking_id)

    @staticmethod
    def get_dist_fare(dist, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_dist_fare(dist)

    @staticmethod
    def get_dist_fare_op(dist, op_level, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_dist_fare_op(dist, op_level)

    @staticmethod
    def get_op_level(startdate, starttime, enddate, endtime, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_op_level(startdate, starttime, enddate, endtime)

    @staticmethod
    def get_gst(base_price, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_gst(base_price)

    @staticmethod
    def get_price(type, dur, starttime, endtime, dist, car_type, startdate, enddate, loc_cluster, 
                  insurance_num=0, city=0, client_name="", client_trip_type=-1, is_immediate=False, driver_type=BookingParams.CLASSIC_DRIVER,coupon=None):
        if type == BookingParams.TYPE_B2B:
            if client_trip_type == BookingParams.TYPE_ONEWAY:
                price_oneway = PriceOneWay(client_name)
                return price_oneway.get_price(dur, starttime, endtime, dist, startdate, enddate)
            elif client_trip_type == BookingParams.TYPE_ROUNDTRIP:
                price_round = PriceRound(client_name)
                return price_round.get_price(dur, starttime, endtime, startdate, enddate)
            elif client_trip_type == BookingParams.TYPE_OUTSTATION:
                price_outstation = PriceOutStation(client_name)
                return price_outstation.get_price(dur, starttime, endtime, startdate, enddate)
            else:
                return None
        price_cl = price_map.get(city, "price_kolkata")
        price_city = PriceCity(price_cl)
        print("couponn",coupon)
        response = price_city.get_price(type, dur, starttime, endtime, dist, car_type, startdate, enddate, loc_cluster, insurance_num, is_immediate,coupon)
        return response
    
    @staticmethod
    def get_overtime_charge_gen(real_delta, book_delta, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_overtime_charge_gen(real_delta, book_delta)

    @staticmethod
    def get_overtime_charge_op(real_delta, book_delta, op_level, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_overtime_charge_op(real_delta, book_delta, op_level)

    @staticmethod
    def get_trip_price(book_id, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch=0, city=0, type=0, client_name="", client_trip_type=-1, driver_est=0):
        if type == BookingParams.TYPE_B2B:
            if client_trip_type == BookingParams.TYPE_ONEWAY:
                price_oneway = PriceOneWay(client_name)
                return price_oneway.get_trip_price(book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch)
            elif client_trip_type == BookingParams.TYPE_ROUNDTRIP:
                price_round = PriceRound(client_name)
                return price_round.get_trip_price(book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch)
            elif client_trip_type == BookingParams.TYPE_OUTSTATION:
                price_outstation = PriceOutStation(client_name)
                return price_outstation.get_trip_price(book_delta, real_delta, est, driver_est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch)
            else:
                return None
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_trip_price(book_id, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime, startdate, enddate, insurance_ch)

    @staticmethod
    def get_ot_rates(startdate, enddate, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        Price.price_city = PriceCity(price_cl)
        return Price.price_city.get_ot_rates(startdate, enddate)


class PriceOutstation:

    price_os = None

    @staticmethod
    def get_booking_ch(car_type, driver_id, booking_id, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_booking_ch(car_type, driver_id, booking_id)

    @staticmethod
    def get_booking_ch_new(booking_info, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_booking_ch_new(booking_info)

    @staticmethod
    def get_price(startdate, enddate, nofood, dur, car_type, type=BookingParams.TYPE_OUTSTATION, dist=0, insurance_num=0, city=0, is_immediate=False,coupon=None):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_price(startdate, enddate, nofood, dur, car_type, type, dist, insurance_num, is_immediate,coupon)

    @staticmethod
    def get_trip_price(book_delta, real_delta, est, startdate, enddate, insurance_ch=0, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_trip_price(book_delta, real_delta, est, startdate, enddate, insurance_ch)

    @staticmethod
    def get_ot_rates(startdate, enddate, city=0):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_ot_rates(startdate, enddate)

    @staticmethod
    def get_cancel_ch(curtime, starttime, city=0, has_trip=False, cancel_cat=0, is_ontrip=False):
        price_cl = price_map.get(city, "price_kolkata")
        PriceOutstation.price_os = PriceCityOutstation(price_cl)
        return PriceOutstation.price_os.get_cancel_ch(curtime, starttime, has_trip, cancel_cat, is_ontrip)

'''
Utility functions
'''

def starts_at_night(start_hour, city=0):
    price_cl = price_map.get(city, "price_kolkata")
    Price.price_city = PriceCity(price_cl)
    return Price.price_city.starts_at_night(start_hour)

def is_trip_night(start_hour, end_hour,is_immediate=False, city=0):
    price_cl = price_map.get(city, "price_kolkata")
    Price.price_city = PriceCity(price_cl)
    return Price.price_city.is_trip_night(start_hour, end_hour, is_immediate)

def is_trip_early_morn(start_hour, end_hour, city=0):
    price_cl = price_map.get(city, "price_kolkata")
    Price.price_city = PriceCity(price_cl)
    return Price.price_city.is_trip_early_morn(start_hour, end_hour)

def convert_to_semihours(dt, hr):
    semihours = dt.days * 24 * hr
    semihours = semihours + math.ceil((dt.seconds * hr) / 3600)
    return semihours