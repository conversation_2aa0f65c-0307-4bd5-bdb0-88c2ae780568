import pytest
from models import Users, Bookings, Trip
from flask import current_app as app
from db_config import db
import json
import datetime
from io import BytesIO
from booking_params import Rating
from flask_jwt_extended import create_access_token
from conftest import driver_bookings,driver_trip,unique_user_data,create_user_and_driver

def access_token_create(user_data):
    expires_access = datetime.timedelta(days=365)
    identity_with_claims = {
        'id': user_data.id,
        'roles': user_data.role,
        'region': user_data.region,
        'fname': user_data.fname,
        'lname': user_data.lname,
    }
    with app.app_context():
        access_token = create_access_token(identity=identity_with_claims, additional_claims=identity_with_claims, expires_delta=expires_access)
    return access_token

trip_states = [
    (Trip.TRIP_INIT, "TRIP_INIT"),
    (Trip.TRIP_REACHED_SRC, "TRIP_REACHED_SRC"),
    (Trip.TRIP_START_PIC, "TRIP_START_PIC"),
    (Trip.TRIP_STARTED, "TRIP_STARTED"),
    (Trip.TRIP_REACHED_DEST, "TRIP_REACHED_DEST"),
    (Trip.TRIP_STOP_PIC, "TRIP_STOP_PIC"),
    (Trip.TRIP_STOPPED, "TRIP_STOPPED")
]

#  API - /api/trip/state_change

def test_trip_state_change_invalid_role(client,driver_login):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    access_token=access_token_create(user)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/trip/state_change', headers=headers)

    assert response.status_code == 401
    assert response.json['success'] == -1


def test_trip_state_change_disabled(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.enabled=False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = client.post('/api/trip/state_change',headers=headers)
    assert response.status_code == 401
    assert response.json['success'] == -1


def test_trip_state_change_invalid_params(client,driver_login):
    state=driver_login
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    data = {
        'state': '-5',  # Invalid state
        'booking_id': '-1',
        'lat': '22.5726',
        'lng': '88.3639'
    }
    response = client.post('/api/trip/state_change', data=data,headers=headers)

    # Verify response
    assert response.status_code == 400
    response_data = json.loads(response.data)
    assert response_data["status"] == 400
    assert "error" in response_data
    assert response_data["error"]["message"] == "Invalid params"

def test_trip_state_change_invalid_booking(client,driver_login,mock_upload_pic):
    state=driver_login
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    current_time = datetime.datetime.utcnow()
    mock_upload_pic.side_effect = ['url_cleft', 'url_cright', 'url_cback', 'url_cfront', 'url_selfie']
    data = {
            'state': "5",
            'booking_id':"222" ,  # assuming a valid booking ID for testing
            'lat': '22.5726',   # sample latitude
            'lng': '88.3639',   # sample longitude
            'cleft': (BytesIO(b'mock car left pic'), 'car_left.jpg'),
            'cright': (BytesIO(b'mock car right pic'), 'car_right.jpg'),
            'cback': (BytesIO(b'mock car back pic'), 'car_back.jpg'),
            'cfront': (BytesIO(b'mock car front pic'), 'car_front.jpg'),
            'selfie': (BytesIO(b'mock selfie pic'), 'selfie.jpg'),
            'start_time':current_time,
            'stop_time':(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        }
    response = client.post('/api/trip/state_change', data=data, headers=headers)

    # Assert the response status code is 200 OK
    assert response.status_code == 200

    # Parse the response JSON
    response_data = response.get_json()

    # Assert the API response contains the expected trip state
    assert response_data["status"] == 200
    assert "data" in response_data
    assert response_data["data"]["success"] == False


def test_trip_state_change_incorrect_change(client,driver_login,mock_upload_pic):
    state=driver_login
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    trip_id=driver_trip(booking_id,0)
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    current_time = datetime.datetime.utcnow()
    mock_upload_pic.side_effect = ['url_cleft', 'url_cright', 'url_cback', 'url_cfront', 'url_selfie']
    data = {
            'state': "6",
            'booking_id':booking_id ,  # assuming a valid booking ID for testing
            'lat': '22.5726',   # sample latitude
            'lng': '88.3639',   # sample longitude
            'cleft': (BytesIO(b'mock car left pic'), 'car_left.jpg'),
            'cright': (BytesIO(b'mock car right pic'), 'car_right.jpg'),
            'cback': (BytesIO(b'mock car back pic'), 'car_back.jpg'),
            'cfront': (BytesIO(b'mock car front pic'), 'car_front.jpg'),
            'selfie': (BytesIO(b'mock selfie pic'), 'selfie.jpg'),
            'start_time':current_time,
            'stop_time':(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        }
    response = client.post('/api/trip/state_change', data=data, headers=headers)

    # Assert the response status code is 200 OK
    assert response.status_code == 400

    # Parse the response JSON
    response_data = response.get_json()

    # Assert the API response contains the expected trip state
    assert response_data["status"] == 400
    assert "error" in response_data
    assert response_data["error"]["message"] == "Invalid status requested"


@pytest.mark.parametrize("trip_state, state_name", trip_states)
def test_trip_state_change_success(client,driver_login,trip_state,state_name,mock_upload_pic):
    state=driver_login
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    if trip_state!=Trip.TRIP_INIT:
        trip_id=driver_trip(booking_id,trip_state+1)
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    current_time = datetime.datetime.utcnow()
    mock_upload_pic.side_effect = ['url_cleft', 'url_cright', 'url_cback', 'url_cfront', 'url_selfie']
    if trip_state == Trip.TRIP_START_PIC or trip_state == Trip.TRIP_STOP_PIC:  # TRIP_START_PIC or TRIP_STOP_PIC
        data = {
            'state': str(trip_state),
            'booking_id':booking_id ,
            'lat': '22.5726',
            'lng': '88.3639',
            'cleft': (BytesIO(b'mock car left pic'), 'car_left.jpg'),
            'cright': (BytesIO(b'mock car right pic'), 'car_right.jpg'),
            'cback': (BytesIO(b'mock car back pic'), 'car_back.jpg'),
            'cfront': (BytesIO(b'mock car front pic'), 'car_front.jpg'),
            'selfie': (BytesIO(b'mock selfie pic'), 'selfie.jpg'),
            'start_time':"12:30:00",
            'stop_time':(current_time + datetime.timedelta(hours=4)).strftime("%Y-%m-%d %H:%M:%S.%f"),
        }
    else:
        data = {
            'state': str(trip_state),
            'booking_id': booking_id,
            'lat': '22.5726',
            'lng': '88.3639',
            'start_time':current_time,
            'stop_time':(current_time + datetime.timedelta(hours=4)).strftime("%Y-%m-%d  %H:%M:%S.%f"),
        }
    response = client.post('/api/trip/state_change', data=data, headers=headers)
    response_data = response.get_json()
    assert response.status_code == 200

    # Assert the API response contains the expected trip state
    assert response_data["status"] == 200
    assert "data" in response_data
    if trip_state==Trip.TRIP_INIT:
        assert response_data["data"] == True
    else:
        assert response_data["data"]['success'] == True

# ---------------------------------

#  API - /api/trip/driver_rate -----------

@pytest.mark.parametrize("rating_value, expected_status_code, expected_success", [
    (4, 200, 1),  # Valid rating
    (0, 200, 1),    # Rating below the minimum (will be set to the default)
    (7, 200, 1),    # Rating above the maximum (will be capped)
    ('invalid', 201, -2),  # Invalid rating (non-numeric)
])
def test_driver_rate(client, driver_login, rating_value, expected_status_code, expected_success):
    state = driver_login
    user_id = state['user_id']
    access_token = state['access_token']
    driver_id = state['driver_id']

    booking = Bookings(
        user=user_id,
        skey='some_secret_key',
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.datetime.utcnow() + datetime.timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
    booking.valid=1
    db.session.add(booking)
    db.session.commit()

    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    data = {
        'book_id': booking.id,
        'driver_rating': rating_value
    }

    response = client.post('/api/trip/driver_rate', data=data, headers=headers)

    assert response.status_code == expected_status_code

    response_data = response.get_json()
    assert response_data['success'] == expected_success
    if expected_success == 1:
        booking = db.session.merge(booking)
        db.session.refresh(booking)  # Refresh the booking to get the updated value from the DB

        # Cap or default the rating based on your logic
        if isinstance(rating_value, (int, float)):
            if rating_value < Rating.RATING_DEFAULT:
                expected_rating_value = Rating.RATING_DEFAULT
            elif rating_value > Rating.RATING_HIGH:
                expected_rating_value = Rating.RATING_HIGH
            else:
                expected_rating_value = rating_value
        else:
            expected_rating_value = None  # Invalid rating, no update should happen

        # Assert that the rating was capped/adjusted correctly
        assert booking.driver_rating == expected_rating_value

# ---------------------------------

#  API - /api/trip/otp_validate -----------

@pytest.mark.parametrize("provided_otp, expected_status, expected_message", [
    ("123456", 200, None),           # Correct OTP scenario
    ("000000", 403, "OTP did not match"),  # Incorrect OTP scenario
])
def test_trip_otp_validate(client, driver_login, provided_otp, expected_status, expected_message):
    state = driver_login
    access_token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    booking = Bookings(
        user=user_id,
        skey='some_secret_key',
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.datetime.utcnow() + datetime.timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
    booking.valid=1
    booking.otp="123456"
    db.session.add(booking)
    db.session.commit()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    data = {
        'otp': provided_otp if provided_otp != "" else '123456',
        'booking_id': booking.id
    }

    response = client.post('/api/trip/otp_validate', data=data, headers=headers)

    # Assert the response status code is as expected
    assert response.status_code == expected_status

    # Assert correct error message is returned for error cases
    if expected_status != 200:
        response_data = response.get_json()
        assert response_data['error']['message'] == expected_message

    # For success case, we expect OTP to be regenerated
    if expected_status == 200:
        booking = db.session.merge(booking)
        db.session.refresh(booking)
        assert booking.otp != provided_otp  # OTP should be regenerated

# ---------------------------------