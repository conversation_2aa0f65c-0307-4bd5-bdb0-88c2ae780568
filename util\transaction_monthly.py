from main import app
import sys
sys.path.append("/app/")
import csv
import os
from db_config import db
from sqlalchemy import func,or_,not_, case
from models import Trip, Bookings, Users, UserTrans, DriverTrans,Drivers
from _email import send_mail_v2
from datetime import datetime, timedelta
from calendar import monthrange
from booking_params import Regions

FROM_ADDR = "<EMAIL>"
TO_ADDR = ["<EMAIL>","<EMAIL>", "<EMAIL>"]
MONTH = {
    1: "January",
    2: "February",
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    7: "July",
    8: "August",
    9: "September",
    10: "October",
    11: "November",
    12: "December"
}

def get_last_month():
    today = datetime.today()
    first_of_this_month = today.replace(day=1)
    last_month = first_of_this_month - timedelta(days=1)
    return last_month.strftime('%Y-%m')


def get_due_from_customer(month=None):
    try:
        query = (
            db.session.query(
                func.date_format(
                    func.convert_tz(Trip.starttime, '+00:00', '+05:30'),
                    '%Y-%m'
                ).label('date_ist'),
                Bookings.region.label('city'),
                func.sum(Trip.net_rev).label('total_due'),
                func.count(Trip.id).label('trip_count'),
                func.sum(Trip.price).label('trip_price')
            )
            .join(Bookings, Bookings.id == Trip.book_id)
            .filter(
                Bookings.type < 50
            )
        )

        # Apply month filter if provided
        if month:
            query = query.filter(
                func.date_format(
                    func.convert_tz(Trip.starttime, '+00:00', '+05:30'),
                    '%Y-%m'
                ) == month
            )

        # Group by and order by using column expressions
        results = (
            query.group_by(
                func.date_format(
                    func.convert_tz(Trip.starttime, '+00:00', '+05:30'),
                    '%Y-%m'
                ),
                Bookings.region
            )
            .order_by(
                func.date_format(
                    func.convert_tz(Trip.starttime, '+00:00', '+05:30'),
                    '%Y-%m'
                ),
                Bookings.region
            )
            .all()
        )

        output_file = f'due_data_{month if month else "all"}.csv'

        # Write to CSV
        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write Header
            csv_writer.writerow(['Date (IST)', 'City', 'Total Net Due', 'Trip Count', 'Trip Price'])

            # Write Data
            for row in results:
                city_name = Regions.REGN_NAME[row.city]
                csv_writer.writerow([row.date_ist, city_name, row.total_due, row.trip_count, row.trip_price])

        print(f'Data successfully exported to {output_file}', flush=True)
        return output_file

    except Exception as e:
        print(f"Error in export_due_to_csv: {str(e)}", flush=True)
        return None


def get_referral_amount_by_customer(month=None):
    try:
        query = (
            db.session.query(
                func.date_format(
                    func.convert_tz(UserTrans.timestamp, '+00:00', '+05:30'),
                    '%Y-%m'
                ).label('month_ist'),
                Bookings.region.label('city'),
                func.sum(
                    case(
                        (Bookings.user == None, 0),
                        else_=UserTrans.amount / 100
                    )
                ).label('total_referral_amount')
            )
            .outerjoin(Bookings, Bookings.user == UserTrans.user_id)
            .join(Users, UserTrans.user_id == Users.id)
            .filter(
                (UserTrans.method.like("Referral use%") | UserTrans.method.like("Referral redemption%")),
                ~Users.mobile.between('7000000000', '7000000050'),
                Bookings.type < 50 ,
            )
        )

        # Apply month filter if provided
        if month:
            query = query.filter(
                func.date_format(
                    func.convert_tz(UserTrans.timestamp, '+00:00', '+05:30'),
                    '%Y-%m'
                ) == month
            )

        results = query.group_by('month_ist', Bookings.region).order_by(Bookings.region, func.sum(UserTrans.amount / 100).desc()).all()

        output_file = f'referral_data_{month if month else "all"}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write Header
            csv_writer.writerow(['Month (IST)', 'City', 'Total Referral Amount'])

            # Write Data
            for row in results:
                city_name = Regions.REGN_NAME[row.city]
                csv_writer.writerow([row.month_ist, city_name, row.total_referral_amount])

        print(f'Referral data exported to {output_file}', flush=True)
        return output_file

    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


excluded_driver_ids = [1, 2]
excluded_user_ids = [1]


def get_region_wise_fine_amount_driver(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist
        results = db.session.query(
            Users.region,
            # Aggregate the amounts for each region
            func.abs(func.sum(DriverTrans.amount/100).label('total_amount'))
        ).select_from(Drivers).join(
            # Define the join condition explicitly
            DriverTrans, DriverTrans.driver_id == Drivers.id
        ).join(Users, Users.id == Drivers.user).filter(
            DriverTrans.method == 'Admin Fine',
            DriverTrans.amount < 0,
            DriverTrans.timestamp >= start_of_month,
            DriverTrans.timestamp <= end_of_month,
            Drivers.id.notin_(excluded_driver_ids)
        ).group_by(
            Users.region  # Group by the latest region
        ).all()
        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_fine_driver_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])
            month_year = f"{month}-{year}"  # Concatenate month and year
            # Write data
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error in driver fine: {str(e)}", flush=True)
        return None


def get_region_wise_gift_amount_driver(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)

        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist

        results = db.session.query(
            Users.region,
            # Aggregate the amounts for each region
            func.sum(DriverTrans.amount/100).label('total_amount')
        ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
            # Define the join condition explicitly
            DriverTrans, DriverTrans.driver_id == Drivers.id
        ).filter(
            DriverTrans.method == 'Gift',
            DriverTrans.amount > 0,
            DriverTrans.timestamp >= start_of_month,
            DriverTrans.timestamp <= end_of_month,
            Drivers.id.notin_(excluded_driver_ids)
        ).group_by(
            Users.region  # Group by the latest region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_gift_driver_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])
            month_year = f"{month}-{year}"  # Concatenate month and year
            # Write data
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_automatic_incentive_driver(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist

        results = db.session.query(
            Users.region,
            # Aggregate the amounts for each region
            func.sum(DriverTrans.amount/100).label('total_amount')
        ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
            # Define the join condition explicitly
            DriverTrans, DriverTrans.driver_id == Drivers.id
        ).filter(
            or_(
                DriverTrans.method == 'Daily bonus',
                DriverTrans.method == 'Weekly bonus',
            ),
            DriverTrans.amount > 0,
            DriverTrans.timestamp >= start_of_month,
            DriverTrans.timestamp <= end_of_month,
            Drivers.id.notin_(excluded_driver_ids)
        ).group_by(
            Users.region  # Group by the latest region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_automatic_inc_driver_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])
            month_year = f"{month}-{year}"  # Concatenate month and year
            # Write data
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_others_charges_driver(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist

        results = db.session.query(
            Users.region,
            # Aggregate the amounts for each region
            func.sum(func.abs(DriverTrans.amount / 100)).label('total_amount')
        ).select_from(Drivers).join(
            # Define the join condition explicitly
            DriverTrans, DriverTrans.driver_id == Drivers.id
        ).join(Users, Users.id == Drivers.user).filter(
            or_(
                DriverTrans.method == 'T-Shirt',
                DriverTrans.method == 'Registration',
                DriverTrans.method == 'Reactivation',
            ),
            DriverTrans.timestamp >= start_of_month,
            DriverTrans.timestamp <= end_of_month,
            Drivers.id.notin_(excluded_driver_ids)
        ).group_by(
            Users.region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_other_charge_driver_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])

            # Write data
            month_year = f"{month}-{year}"  # Concatenate month and year
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_gift_customer_amount(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist
        results = db.session.query(
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                '0'  # Default value if no booking is found
            ).label('latest_region'),
            # Aggregate the amounts for each region
            func.sum(UserTrans.amount/100).label('total_amount')
        ).select_from(Users).join(
            # Define the join condition explicitly
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.method == 'Gift',
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount > 0,
            UserTrans.timestamp >= start_of_month,
            UserTrans.timestamp <= end_of_month,
            Users.id.notin_(excluded_user_ids)
        ).group_by(
            'latest_region'  # Group by the latest region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_gift_customer_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])

            # Write data
            month_year = f"{month}-{year}"  # Concatenate month and year
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")

        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_fine_customer_amount(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist
        results = db.session.query(
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                '0'  # Default value if no booking is found
            ).label('latest_region'),
            # Aggregate the amounts for each region
            func.abs(func.sum(UserTrans.amount/100).label('total_amount'))
        ).select_from(Users).join(
            # Define the join condition explicitly
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.method == 'Fine',
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount < 0,
            UserTrans.timestamp >= start_of_month,
            UserTrans.timestamp <= end_of_month,
            Users.id.notin_(excluded_user_ids)
        ).group_by(
            'latest_region'  # Group by the latest region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_fine_customer_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])

            # Write data
            month_year = f"{month}-{year}"  # Concatenate month and year
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")

        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_cancel_customer_amount(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)

        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist
        results = db.session.query(
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                '0'  # Default value if no booking is found
            ).label('latest_region'),
            # Aggregate the amounts for each region
            -func.sum(UserTrans.amount/100).label('total_amount')
        ).select_from(Users).join(
            # Define the join condition explicitly
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            or_(
                UserTrans.method.like('Cancellation charges for%'),
                # Include amounts for Reversal: Booking%
                UserTrans.method.like('Cancellation reversal for%')
            ),
            UserTrans.timestamp >= start_of_month,
            UserTrans.timestamp <= end_of_month,
            Users.id.notin_(excluded_user_ids)
        ).group_by(
            'latest_region'  # Group by the latest region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_cancel_customer_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])
            month_year = f"{month}-{year}"  # Concatenate month and year
            # Write data
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        print(f"Data successfully exported to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def get_region_wise_cancel_driver_amount(month, year):
    try:
        # First day of the month ist
        start_of_month = datetime(year, month, 1) + timedelta(seconds=330*60)
        # Get the last day of the month
        end_of_month_day = monthrange(year, month)[1]
        end_of_month = datetime(year, month, end_of_month_day, 23, 59, 59) + \
            timedelta(seconds=330*60)   # Last moment of the month ist

        results = db.session.query(
            Users.region,
            # Aggregate the amounts for each region
        -func.sum(DriverTrans.amount/100).label('total_amount')
        ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
            # Define the join condition explicitly
            DriverTrans, DriverTrans.driver_id == Drivers.id
        ).filter(
            or_(
                DriverTrans.method.like('Cancellation: Booking%'),
                # Include amounts for Reversal: Booking%
                DriverTrans.method.like('Reversal: Booking%')
            ),
            DriverTrans.timestamp >= start_of_month,
            DriverTrans.timestamp <= end_of_month,
            Drivers.id.notin_(excluded_driver_ids)
        ).group_by(
            Users.region
        ).all()

        region_map = {
            Regions.REGN_NAME[int(reg_code)]: int(amount)
            for reg_code, amount in results
        }
        output_file = f'region_wise_cancel_driver_{month}_{year}.csv'

        with open(output_file, mode='w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)

            # Write header
            csv_writer.writerow(['Month-Year', 'Region', 'Total Amount'])

            # Write data
            month_year = f"{month}-{year}"  # Concatenate month and year
            for region, total_amount in region_map.items():
                csv_writer.writerow([month_year, region, total_amount])

        return output_file
    except Exception as e:
        print(f"Error in export_referral_to_csv: {str(e)}", flush=True)
        return None


def send_mail(month, year, files):
    try:
        # Email subject and content
        subject = f"Transaction - {MONTH.get(month) if month else 'All Data'} {year}"
        content = f"Attached is the transaction report for {MONTH.get(month) if month else 'all months'} {year}.\n\n-- Drivers4Me Team"

        # Email recipients
        if not TO_ADDR:
            print("No recipients specified. Email not sent.")
        else:
            print(f"Sending email to: {', '.join(TO_ADDR)}")

            # Attach file and send email
            send_mail_v2(FROM_ADDR, TO_ADDR, subject, content, files)

        for file_path in files:
            if os.path.exists(file_path):
                os.remove(file_path)

        print("Email sent and files cleaned up.", flush=True)

    except Exception as e:
        print(f"Error in send_mail: {str(e)}", flush=True)


if __name__ == '__main__':
    with app.app_context():
        month = None
        if len(sys.argv) > 1:
            month = sys.argv[1]
        else:
            month = get_last_month()

        files = []
        try:
            due_file = get_due_from_customer(month)
            if due_file:
                files.append(due_file)


            referral_file = get_referral_amount_by_customer(month)
            if referral_file:
                files.append(referral_file)

            year, month = month.split('-')
            # Convert them to integers
            month = int(month)
            year = int(year)

            driver_fine_file = get_region_wise_fine_amount_driver(month, year)
            if driver_fine_file:
                files.append(driver_fine_file)

            driver_gift_file = get_region_wise_gift_amount_driver(month, year)
            if driver_gift_file:
                files.append(driver_gift_file)

            driver_aut_inc_file = get_region_wise_automatic_incentive_driver(
                month, year)
            if driver_aut_inc_file:
                files.append(driver_aut_inc_file)

            driver_oth_char_file = get_region_wise_others_charges_driver(
                month, year)
            if driver_oth_char_file:
                files.append(driver_oth_char_file)

            customer_fine_file = get_region_wise_fine_customer_amount(
                month, year)
            if customer_fine_file:
                files.append(customer_fine_file)

            customer_gift_file = get_region_wise_gift_customer_amount(
                month, year)
            if customer_gift_file:
                files.append(customer_gift_file)

            customer_cancel_file = get_region_wise_cancel_customer_amount(
                month, year)
            if customer_cancel_file:
                files.append(customer_cancel_file)

            driver_cancel_file = get_region_wise_cancel_driver_amount(
                month, year)
            if driver_cancel_file:
                files.append(driver_cancel_file)
        except Exception as e:
            print(f"Failed to export data for {month}: {str(e)}")

        if files:
            send_mail(month, year, files)
