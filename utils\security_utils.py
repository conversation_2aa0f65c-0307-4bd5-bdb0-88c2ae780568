#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  security_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import uuid
import hashlib
from random import randint
import datetime

from redis_config import execute_with_fallback

def gen_otp(n):
    return ''.join(["{}".format(randint(0, 9)) for _ in range(0, n)])

def get_salt():
    return uuid.uuid4().hex


def get_pwd(pwd, salt):
    return hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()

def add_token_to_blacklist(token_hash, expires):
    ttl_seconds = int((expires - datetime.datetime.utcnow()).total_seconds())
    if ttl_seconds > 0:
        execute_with_fallback('set', f"blacklist:{token_hash}", "reason", ex=ttl_seconds)
    # redis_client.set(f"blacklist:{token_hash}", "reason", ex=int(expires.timestamp()))