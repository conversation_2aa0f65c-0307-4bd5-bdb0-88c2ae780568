import math
from datetime import datetime,timezone, timedelta
import requests

from fuzzywuzzy import fuzz
from sqlalchemy.sql import func, or_,desc,asc,and_,exists, case
from flask import current_app as app


from models.models import db, Drivers, Users, DriverDetails, DriverInfo, Bookings, Trip , BookDest,\
    DriverApprovalLog, DriverSkill, DriverLoc, DriverVerify, DriverBank, DriverIdle, DriverGLIDDocDetails,\
    DriverGLDrivLicDetails, DriverGLBankDetails, DriverGLPanDetails, DriverRegion,MobileChange
from utils.distance_utils import EARTH_RADIUS,mapmyindia_url
from utils.bookings.booking_params import Regions,BookingParams
from utils.time_utils import convert_to_utc, convert_to_local_time
from utils.metric_utils import cm_to_feet_and_inches, feet_and_inches_to_cm
from schemas.admin_dashboard.driver_schemas import DriverApprovalLogParams, \
    IS_GLOBAL_DRIVER_LICENCE, IS_GLOBAL_DRIVER_MOBILE, SORT_BY_TIMESTAMP_ASC, SORT_BY_TIMESTAMP_DESC, \
    SORT_BY_RATING_ASC, SORT_BY_RATING_DESC,SORT_BY_DRIVER_TIMESTAMP_DESC, \
        SORT_BY_DRIVER_TIMESTAMP_ASC, SORT_BY_DRIVER_RATING_DESC, SORT_BY_DRIVER_RATING_ASC, \
            DashDriverSearchParams, DriverTripsLogPayload
from services.socket.socketio_app import live_update_to_channel
from utils.s3_utils import upload_pic


def _base_driver_query():
    """
    Constructs the base SQLAlchemy query for fetching driver data with user and driver details.

    Returns:
        sqlalchemy.orm.query.Query: Base query with selected driver fields and outer joins.
    """
    return db.session.query(
        Drivers.id.label('driver_id'),
        Users.mobile.label('mobile'),
        func.concat(Users.fname, ' ', Users.lname).label('name'),
        DriverInfo.pres_region.label('location'),
        DriverInfo.pres_addr_lat.label('lat'),
        DriverInfo.pres_addr_lng.label('lng'),
        Drivers.rating.label('rating'),
        DriverDetails.rating_count.label('rating_count'),
        Drivers.approved.label('driver_approved'),
        Drivers.available.label('driver_available'),
        Users.reg.label('timestamp'),
        Drivers.pic.label('driver_pic'),
        Users.region.label('region'),
        Users.label_bv.label('label')
    ).outerjoin(Users, Drivers.user == Users.id) \
     .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id) \
     .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)


def _apply_global_search(query, payload):
    """
    Applies global search filters to the query based on license number or mobile number.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Search parameters with global flags.

    Returns:
        Query: Updated query with global search filters applied.
    """
    if payload.is_global and payload.search_query:
        if payload.is_global == IS_GLOBAL_DRIVER_LICENCE:
            return query.filter(Drivers.licenseNo == payload.search_query)
        elif payload.is_global == IS_GLOBAL_DRIVER_MOBILE:
            return query.filter(Users.mobile == int(payload.search_query))
    return query


def _apply_text_search(query, payload):
    """
    Applies keyword-based search on driver name, ID, mobile, license, or region.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Search parameters with search query.

    Returns:
        Query: Updated query with text-based filtering applied.
    """
    if not payload.search_query:
        return query

    q = payload.search_query.strip()

    if q.startswith('#'):
        return query.filter(DriverInfo.pres_region.ilike(f"%{q[1:]}%"))

    try:
        search_id = int(q)
        return query.filter(or_(Drivers.id == search_id, Users.mobile == search_id))
    except ValueError:
        name_parts = q.split()
        if len(name_parts) >= 2:
            conditions = []
            for i in range(len(name_parts)):
                for j in range(i + 1, len(name_parts)):
                    conditions.append(
                        and_(
                            Users.fname.ilike(f"%{name_parts[i]}%"),
                            Users.lname.ilike(f"%{name_parts[j]}%")
                        )
                    )
                    conditions.append(
                        and_(
                            Users.fname.ilike(f"%{name_parts[j]}%"),
                            Users.lname.ilike(f"%{name_parts[i]}%")
                        )
                    )
            return query.filter(or_(*conditions))
        else:
            return query.filter(
                or_(
                    Users.fname.ilike(f"%{q}%"),
                    Users.lname.ilike(f"%{q}%"),
                    Drivers.licenseNo == q
                )
            )


def _apply_region_filter(query, payload):
    """
    Filters query by region if applicable (non-global view and region specified).

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload containing region information.

    Returns:
        Query: Updated query with region filtering applied.

    Raises:
        ValueError: If the region string is invalid.
    """
    if not payload.is_global and payload.region and payload.region != Regions.ALL_REGIONS_ACCESS:
        try:
            regions = [int(value) for value in payload.region.split(',')]
            return query.filter(Users.region.in_(regions))
        except Exception:
            raise ValueError("Error in search regions string")
    return query


def _apply_status_filters(query, payload):
    """
    Filters query based on driver availability, approval status, and rating thresholds.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload containing status, approval, and rating filters.

    Returns:
        Query: Updated query with status filters applied.
    """
    if payload.status:
        status_vals = [int(x) for x in payload.status.split(',')]
        query = query.filter(Drivers.available.in_(status_vals))
    if payload.approval:
        approval_vals = [int(x) for x in payload.approval.split(',')]
        query = query.filter(Drivers.approved.in_(approval_vals))
    if payload.rating_gt:
        query = query.filter(Drivers.rating >= float(payload.rating_gt))
    if payload.rating_lt:
        query = query.filter(Drivers.rating <= float(payload.rating_lt))
    return query


def _apply_timestamp_filters(query, payload):
    """
    Applies registration timestamp filters on the user registration field.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload containing timestamp range.

    Returns:
        Query: Updated query with timestamp filters.
    """
    if payload.timestamp_gt:
        query = query.filter(Users.reg >= payload.timestamp_gt)
    if payload.timestamp_lt:
        query = query.filter(Users.reg <= payload.timestamp_lt)
    return query


def _apply_label_filter(query, payload):
    """
    Filters the query by a driver label if provided.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload with label_bv filter.

    Returns:
        Query: Updated query with label filter applied.
"""
    if payload.label:
        query = query.filter(Users.label_bv == int(payload.label))
    return query


def _get_ongoing_driver_ids():
    """
    Fetches a set of driver IDs currently on an ongoing trip (i.e., trip with no end time).

    Returns:
        set: Set of driver IDs on ongoing trips.
    """
    ongoing = db.session.query(Bookings.driver) \
        .join(Trip, Trip.book_id == Bookings.id) \
        .filter(Trip.endtime.is_(None)) \
        .distinct().all()
    return set(row.driver for row in ongoing)


def _apply_ongoing_filter(query, payload, ongoing_driver_ids):
    """
    Filters query to include/exclude drivers based on ongoing trip status.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload with ongoing flags.
        ongoing_driver_ids (set): Set of drivers with ongoing trips.

    Returns:
        Query: Updated query with ongoing trip filters.

    Raises:
        ValueError: If payload.ongoing contains invalid values.
    """
    if payload.ongoing:
        try:
            values = [int(x) for x in payload.ongoing.split(',')]
            conditions = []
            if 1 in values:
                conditions.append(Drivers.id.in_(ongoing_driver_ids))
            if 0 in values:
                conditions.append(~Drivers.id.in_(ongoing_driver_ids))
            if conditions:
                query = query.filter(or_(*conditions))
        except ValueError:
            raise ValueError("Invalid ongoing value(s) (must be 0 or 1)")
    return query


def _apply_location_radius_filter(query, payload):
    """
    Filters drivers within a radius (in kilometers) from a given latitude/longitude.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload containing location and radius.

    Returns:
        Query: Updated query filtered by geographic distance.

    Raises:
        ValueError: If lat/long/radius values are invalid.
    """
    if payload.lat and payload.long and payload.radius_km:
        try:
            lat1 = float(payload.lat)
            long1 = float(payload.long)
            radius = float(payload.radius_km)
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid lat/long/radius: {e}")

        degrees_to_radians = math.pi / 180.0
        lat2 = func.coalesce(DriverInfo.pres_addr_lat, 0)
        long2 = func.coalesce(DriverInfo.pres_addr_lng, 0)

        phi1 = (90.0 - lat1) * degrees_to_radians
        phi2 = (90.0 - lat2) * degrees_to_radians
        theta1 = long1 * degrees_to_radians
        theta2 = long2 * degrees_to_radians

        cos_value = (
            func.sin(phi1) * func.sin(phi2) * func.cos(theta1 - theta2) +
            func.cos(phi1) * func.cos(phi2)
        )
        arc = func.acos(func.least(func.greatest(cos_value, -1.0), 1.0))
        distance_expr = arc * EARTH_RADIUS

        query = query.filter(distance_expr <= radius)
    return query


def _apply_sorting(query, payload):
    """
    Applies sorting to the query based on payload's sort_by and sort_order values.

    Args:
        query (Query): SQLAlchemy query object.
        payload (DashDriverSearchParams): Payload containing sorting preferences.

    Returns:
        Query: Updated query with applied sorting.
    """
    payload.sort_by, payload.sort_order = (
        ('timestamp', 'desc') if payload.sort_by == str(SORT_BY_DRIVER_TIMESTAMP_DESC) else
        ('timestamp', 'asc') if payload.sort_by == str(SORT_BY_DRIVER_TIMESTAMP_ASC) else
        ('driver_rating', 'desc') if payload.sort_by == str(SORT_BY_DRIVER_RATING_DESC) else
        ('driver_rating', 'asc') if payload.sort_by == str(SORT_BY_DRIVER_RATING_ASC) else
        (payload.sort_by, payload.sort_order)
    )

    if payload.sort_by == 'timestamp':
        return query.order_by(desc(Users.reg) if payload.sort_order == 'desc' else asc(Users.reg))
    elif payload.sort_by == 'driver_rating':
        return query.order_by(
            desc(Drivers.rating) if payload.sort_order == 'desc' else asc(Drivers.rating),
            desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count)
        )
    elif payload.sort_by == 'driver_rating_count':
        return query.order_by(desc(DriverDetails.rating_count) if payload.sort_order == 'desc' else asc(DriverDetails.rating_count))
    elif payload.sort_by == 'driver_ride_count':
        return query.order_by(desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count))

    return query


def _build_response(results, starting_from, ongoing_driver_ids):
    """
    Formats the query results into a list of driver dictionaries.

    Args:
        results (list): Query result rows.
        starting_from (int): Offset to determine if we're at the beginning.
        ongoing_driver_ids (set): Set of driver IDs currently on a trip.

    Returns:
        tuple: (response list, latest timestamp string if any)
    """
    response = []
    latest_timestamp = None

    for result in results:
        response.append({
            'driver_id': result.driver_id,
            'mobile': result.mobile,
            'name': result.name,
            'location': result.location,
            'region': result.region,
            'rating': result.rating,
            'rating_count': result.rating_count,
            'approval': result.driver_approved,
            'status': result.driver_available,
            'image': result.driver_pic,
            'timestamp': result.timestamp,
            'label': result.label,
            'ongoing': result.driver_id in ongoing_driver_ids
        })
        if not int(starting_from) and (latest_timestamp is None or result.timestamp > latest_timestamp):
            latest_timestamp = result.timestamp

    last_entry = latest_timestamp.strftime('%Y-%m-%d %H:%M:%S') if latest_timestamp else None
    return response, last_entry

def driver_search_service(payload: DashDriverSearchParams):
    """
    Performs complex search/filter/sort operations to fetch drivers for dashboard listing.

    Args:
        payload (DashDriverSearchParams): Payload containing all filters, search, and pagination details.

    Returns:
        tuple: (dict with success, count, lastentry, data), HTTP status code
    """
    try:
        starting_from = payload.starting_from
        no_of_logs = payload.no_of_logs

        if payload.is_global and not payload.search_query:
            return {'success': 1, 'count': 1, 'lastentry': None, 'data': []}, 200

        query = _base_driver_query()

        query = _apply_global_search(query, payload)
        query = _apply_text_search(query, payload)
        query = _apply_region_filter(query, payload)
        query = _apply_status_filters(query, payload)
        query = _apply_timestamp_filters(query, payload)
        query = _apply_label_filter(query, payload)
        ongoing_driver_ids = _get_ongoing_driver_ids()
        query = _apply_ongoing_filter(query, payload, ongoing_driver_ids)
        query = _apply_location_radius_filter(query, payload)

        query = _apply_sorting(query, payload)
        query = query.order_by(desc(DriverDetails.driver_id))

        count = db.session.query(func.count()).select_from(query.subquery()).scalar()
        query = query.offset(starting_from).limit(no_of_logs)
        results = query.all()

        response, last_entry = _build_response(results, starting_from, ongoing_driver_ids)

        return {'success': 1, 'count': count, 'lastentry': last_entry, 'data': response}, 200

    except Exception as e:
        return {'success': -99, 'error': f'Error in driver search: {str(e)}'}, 500


def fetch_single_driver_profile(driver_id: int) -> dict:
    """
    Fetches detailed information of a single driver, used for admin driver profile view.

    Args:
        driver_id (int): Unique driver ID.

    Returns:
        dict: Dictionary of driver details, or empty if not found.
    """
    query = db.session.query(
        Drivers.id.label('driver_id'),
        Users.mobile.label('mobile'),
        func.concat(Users.fname, ' ', Users.lname).label('name'),
        DriverInfo.pres_region.label('location'),
        Drivers.rating.label('rating'),
        DriverDetails.rating_count.label('rating_count'),
        Drivers.approved.label('driver_approved'),
        Drivers.available.label('driver_available'),
        Users.reg.label('timestamp'),
        Drivers.pic.label('driver_pic'),
        Users.region.label('region'),
        Users.label_bv.label('label')
    ).filter(Drivers.id == driver_id).outerjoin(Users, Drivers.user == Users.id)\
     .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id)\
     .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)

    result = query.first()

    if not result:
        return {}

    ongoing_driver_ids = db.session.query(Bookings.driver).join(Trip, Trip.book_id == Bookings.id)\
        .filter(Trip.endtime.is_(None)).distinct().all()
    ongoing_driver_ids = set(row.driver for row in ongoing_driver_ids)

    return {
        'driver_id': result.driver_id,
        'mobile': result.mobile,
        'name': result.name,
        'location': result.location,
        'region': result.region,
        'rating': result.rating,
        'rating_count': result.rating_count,
        'approval': result.driver_approved,
        'status': result.driver_available,
        'image': result.driver_pic,
        'timestamp': result.timestamp,
        'label': result.label,
        'ongoing': result.driver_id in ongoing_driver_ids
    }
       

def fetch_driver_trip_logs(payload: DriverTripsLogPayload , tz: str):
    """
    Fetches a paginated and filtered list of a driver's completed trip logs, 
    including stats like total/completed/cancelled trips and average rating.

    Args:
        payload (DriverTripsLogPayload): Filter parameters including sort, date, search query.
        tz (str): Timezone string for formatting time.

    Returns:
        dict: Trip logs and statistics.
    """
    driver_id = payload['driver_id']
    sort_by = payload['sort_by']
    offset = payload['offset']
    limit = payload['limit']
    start_date = payload.get('start_date')
    end_date = payload.get('end_date')
    search_query = payload.get('search_query', '')
    book_type = payload.get('book_type')

    query = db.session.query(
        Bookings.user_rating.label('user_rating'),
        Bookings.estimate.label('book_estimate_price'),
        Bookings.id.label('book_ref'),
        Bookings.type.label('book_type'),
        Bookings.loc.label('book_loc_name'),
        Bookings.insurance_cost.label('insurance_cost'),
        Bookings.code.label('book_code'),
        Bookings.valid.label('book_valid'),
        Trip.starttime.label('trip_start'),
        Trip.price.label('trip_price'),
        Trip.status.label('trip_status'),
        Trip.due.label('trip_due'),
        BookDest.name.label('dest_book_name')
    ).outerjoin(BookDest, Bookings.id == BookDest.book_id
    ).outerjoin(Trip, Bookings.id == Trip.book_id
    ).filter(Bookings.driver == driver_id)

    if start_date:
        start_utc_date, start_utc_time = convert_to_utc(str(start_date), "00:00:00", tz)
        start_utc = datetime.strptime(f"{start_utc_date} {start_utc_time}", '%Y-%m-%d %H:%M:%S')
        query = query.filter(Trip.starttime >= start_utc)

    if end_date:
        end_utc_date, end_utc_time = convert_to_utc(str(end_date), "00:00:00", tz)
        end_utc = datetime.strptime(f"{end_utc_date} {end_utc_time}", '%Y-%m-%d %H:%M:%S') + timedelta(days=1)
        query = query.filter(Trip.starttime < end_utc)

    if book_type is not None:
        if book_type == 1:
            query = query.filter(Bookings.type >= BookingParams.TYPE_C24)
        else:
            query = query.filter(Bookings.type < BookingParams.TYPE_C24)

    if search_query:
        try:
            query = query.filter(
                or_(
                    Bookings.id.ilike(f'%{search_query}%'),
                    Bookings.id == int(search_query)
                )
            )
        except ValueError:
            query = query.filter(
                or_(
                    Bookings.code == search_query,
                    Bookings.code.ilike(f'%{search_query}%'),
                )
            )

    total_trips = query.count()
    completed_trips = query.filter(Trip.status == 0).count()
    invalid_trips = query.filter(Bookings.valid.in_([Bookings.CANCELLED_USER, Bookings.CANCELLED_DRIVER, Bookings.CANCELLED_D4M])).count()
    avg_rating = db.session.query(func.avg(Bookings.user_rating)).filter(
        Bookings.valid == 1,
        Bookings.user_rating != 0,
        Bookings.driver == driver_id
    ).scalar()

    if sort_by == SORT_BY_TIMESTAMP_ASC:
        query = query.order_by(asc(Trip.starttime))
    elif sort_by == SORT_BY_TIMESTAMP_DESC:
        query = query.order_by(desc(Trip.starttime))
    elif sort_by == SORT_BY_RATING_ASC:
        query = query.order_by(asc(Bookings.user_rating))
    elif sort_by == SORT_BY_RATING_DESC:
        query = query.order_by(desc(Bookings.user_rating))

    query = query.filter(Trip.status == 0, Bookings.valid == 1).offset(offset).limit(limit)

    results = query.all()

    trip_logs = [
        {
            'rating': r.user_rating,
            'est_fare': r.book_estimate_price,
            'book_id': r.book_ref,
            'book_type': r.book_type,
            'loc_start': r.book_loc_name or 'N/A',
            'insurance_cost': r.insurance_cost,
            'book_code': r.book_code,
            'book_valid': r.book_valid,
            'time': convert_to_local_time(r.trip_start, tz).strftime('%a, %d %b %Y, %I:%M %p'),
            'est_final': r.trip_price,
            'trip_due': r.trip_due,
            'trip_status': r.trip_status,
            'loc_end': r.dest_book_name or 'N/A'
        }
        for r in results
    ]

    return {
        'data': trip_logs,
        'total_trips': total_trips,
        'completed_trips': completed_trips,
        'cancelled_trips': invalid_trips,
        'avg_user_rating': avg_rating or 0
    }
    
    
def get_all_driver_approval_logs_service(payload: DriverApprovalLogParams, tz: str):
    query = db.session.query(DriverApprovalLog).filter(DriverApprovalLog.driver == payload.driver_id)

    # Date Filter
    if payload.fromdate and payload.todate:
        from_date_utc_date, from_date_utc_time = convert_to_utc(payload.fromdate, "00:00:00", tz)
        to_date_utc_date, to_date_utc_time = convert_to_utc(payload.todate, "00:00:00", tz)

        from_dt = datetime.strptime(f"{from_date_utc_date} {from_date_utc_time}", "%Y-%m-%d %H:%M:%S")
        to_dt = datetime.strptime(f"{to_date_utc_date} {to_date_utc_time}", "%Y-%m-%d %H:%M:%S") + timedelta(days=1)

        query = query.filter(
            DriverApprovalLog.timestamp >= from_dt,
            DriverApprovalLog.timestamp < to_dt
        )

    # Search
    if payload.search_query:
        q = f"%{payload.search_query}%"
        query = query.filter(or_(
            DriverApprovalLog.changes.ilike(q),
            DriverApprovalLog.change_from.ilike(q),
            DriverApprovalLog.change_to.ilike(q),
            DriverApprovalLog.editedby.ilike(q),
            DriverApprovalLog.remark.ilike(q),
        ))

    # Filters
    if payload.approval:
        approval_list = [int(a) for a in payload.approval.split(',')]
        query = query.filter(DriverApprovalLog.approval.in_(approval_list))
    if payload.editedby:
        query = query.filter(DriverApprovalLog.editedby.ilike(f"%{payload.editedby}%"))
    if payload.change_from:
        query = query.filter(DriverApprovalLog.change_from.ilike(f"%{payload.change_from}%"))
    if payload.change_to:
        query = query.filter(DriverApprovalLog.change_to.ilike(f"%{payload.change_to}%"))

    # Sorting
    sort_attr = getattr(DriverApprovalLog, payload.sortby or "timestamp", DriverApprovalLog.timestamp)
    query = query.order_by(sort_attr.asc() if payload.sorttype == '1' else sort_attr.desc())

    # Pagination
    logs = query.offset(payload.offset).limit(payload.limit).all()

    # Format Response
    return [{
        'id': log.id,
        'driver': log.driver,
        'changes': log.changes,
        'approval': log.approval,
        'editedby': log.editedby,
        'change_from': log.change_from,
        'change_to': log.change_to,
        'remark': log.remark,
        'timestamp': convert_to_local_time(log.timestamp, tz).strftime('%a, %d %b %Y, %I:%M %p')
    } for log in logs]
    

def get_driver_details_service(driver_id: int = None, driver_user_id: int = None) -> dict:
    """
    Fetch detailed information about a driver based on their unique driver ID or user ID.

    Args:
        driver_id (int, optional): Unique driver ID.
        driver_user_id (int, optional): Unique user ID associated with the driver.  

    Returns:
        dict: Dictionary containing driver details.
    """
    query = db.session.query(
        Drivers,
        Users,
        DriverDetails,
        DriverSkill,
        DriverLoc,
        DriverVerify,
        DriverInfo,
        DriverBank,
        DriverIdle,
        DriverGLIDDocDetails,
        DriverGLDrivLicDetails,
        DriverGLBankDetails,
        DriverGLPanDetails
    ).outerjoin(Users, Drivers.user == Users.id)\
     .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id)\
     .outerjoin(DriverSkill, Drivers.id == DriverSkill.driver_id)\
     .outerjoin(DriverLoc, Drivers.id == DriverLoc.driver_id)\
     .outerjoin(DriverVerify, Drivers.id == DriverVerify.driver_id)\
     .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)\
     .outerjoin(DriverBank, Drivers.id == DriverBank.driver_id)\
     .outerjoin(DriverIdle, Drivers.id == DriverIdle.driver_id)\
     .outerjoin(DriverGLIDDocDetails, Drivers.id == DriverGLIDDocDetails.driver_id)\
     .outerjoin(DriverGLDrivLicDetails, Drivers.id == DriverGLDrivLicDetails.driver_id)\
     .outerjoin(DriverGLBankDetails, Drivers.id == DriverGLBankDetails.driver_id)\
     .outerjoin(DriverGLPanDetails, Drivers.id == DriverGLPanDetails.driver_id)

    if driver_id:
        query = query.filter(Drivers.id == driver_id)
    elif driver_user_id:
        query = query.filter(Users.id == driver_user_id)

    result = query.first()

    if not result:
        return {
            "status": "error",
            "code": -1,
            "message": "Driver not found",
            "data": None
        }

    (
        driver, user, details, skill, location, verification, info, bank, idle,
        id_ver_details, dl_ver_details, bank_ver_details, pan_ver_details
    ) = result

    height_cm = info.extras.get("height", 0) if info else 0
    driver_height_feet, driver_height_inches = cm_to_feet_and_inches(height_cm)

    return {
    "status": "success",
    "code": 1,
    "message": "Driver details retrieved successfully",
    "data": {
           'driver_details':{
                'id': driver.id,
                'user': driver.user,
                'license_no': driver.licenseNo,
                'license_doc': driver.licenseDoc,
                'pic': driver.pic,
                'approved': driver.approved,
                'available': driver.available,
                'rating': driver.rating,
                'note': driver.note,
                'perma': driver.perma,
            },

            'user': {
                'id': user.id,
                'mobile': user.mobile,
                'fname': user.fname,
                'lname': user.lname,
                'email': user.email,
                'sex': user.sex,
                'password': user.pwd,
                'pwd_salt': user.salt,
                'registration': user.reg,
                'role': user.role,
                'acc_enabled': 1 if user.enabled else 0,
                'credit': user.credit,
                'region': user.region,
                'restore_id': user.restore_id,
                'ref_code': user.ref_code,
                'marked': user.marked,
                'label_bv': user.label_bv,
                'alt_mobile' : user.alt_mobile
            } if user else None,
            'details': {
                'rides': details.ride_count,
                'b2b_ride_count': details.b2b_ride_count,
                'rating_rides': details.rating_count,
                'hours': details.hour_count,
                'rating': details.rating,
                'earning': details.earning,
                'owed': details.owed,
                'wallet': details.wallet,
                'withdrawable': details.withdrawable,
                'approval_ts': details.approval_ts
            } if details else None,
            'skill': {
                'hb_m': skill.hb_m,
                'sed_m': skill.sed_m,
                'suv_m': skill.suv_m,
                'lux_m': skill.lux_m,
                'hb_a': skill.hb_a,
                'sed_a': skill.sed_a,
                'suv_a': skill.suv_a,
                'lux_a': skill.lux_a
            } if skill else None,
            'location': {
                'lat': location.lat,
                'lng': location.lng,
                'timestamp': location.timestamp
            } if location else None,
            'verification': {
                'id_card': verification.id_card,
                'photo': verification.photo,
                'ref': verification.ref,
                'lic': verification.lic,
                'bank': verification.bank
            } if verification else None,
            'info': {
                'license': info.license,
                'license_exp': info.license_exp,
                'dob': info.dob,
                'driver_languages':info.driver_languages,
                'id_verified':info.id_verified,
                'license_verified':info.license_verified,
                'pres_region': info.pres_region,
                'pres_addr': info.pres_addr,
                'pres_addr_lat': info.pres_addr_lat,
                'pres_addr_lng': info.pres_addr_lng,
                'verf_name': info.verf_name,
                'verf_ph': info.verf_ph,
                'verf_rel': info.verf_rel,
                'id_no': info.id_no,
                'behav': info.behav,
                'road': info.road,
                'body': info.body,
                'pic': info.pic,
                'driver_id_doc_f': info.driver_id_doc_f,
                'driver_id_doc_b': info.driver_id_doc_b,
                'driver_lic_doc_f': info.driver_lic_doc_f,
                'driver_lic_doc_b': info.driver_lic_doc_b,
                'driver_remark':info.driver_remark,
                'driver_description':info.driver_description,
                'driver_trip_pref':info.driver_trip_pref,
                'driver_weight': info.extras["weight"],
                'driver_height_feet': driver_height_feet,
                'driver_height_inch':driver_height_inches,
                'driver_reg_source':info.driver_reg_source,
                'driver_bike_status':info.driver_bike_status
            } if info else None,
            'bank': {
                'bank_verified':bank.bank_verified,
                'pan_verified':bank.pan_verified,
                'acc_no': bank.acc_no,
                'ifsc': bank.ifsc,
                'pan_no': bank.pan_no,
                'acc_doc': bank.acc_doc,
                'pan_doc': bank.pan_doc
            } if bank else None,
            'idle': {
                'idle_date': idle.idle_date
            } if idle else None,
            'id_ver_details':{
                'id_card_type':id_ver_details.id_type,
                'id_card_no':id_ver_details.id_card_no,
                'name': id_ver_details.name,
                'gender': id_ver_details.gender,
                'driver_pic': id_ver_details.driver_pic,
                'dob': id_ver_details.dob,
                'house': id_ver_details.house,
                'street': id_ver_details.street,
                'district': id_ver_details.district,
                'sub_district': id_ver_details.sub_district,
                'landmark': id_ver_details.landmark,
                'state': id_ver_details.state,
                'pincode': id_ver_details.pincode,
                'country': id_ver_details.country,
                'vtc_name': id_ver_details.vtc_name,
                'name_status': id_ver_details.name_status,
                'photo_status': id_ver_details.photo_status,
                'photo_score': round(id_ver_details.photo_score * 100, 2) if id_ver_details.photo_score
                is not None else None,
                'dob_status': id_ver_details.dob_status
            } if id_ver_details else None,
            'dl_ver_details':{
                'dl_no': dl_ver_details.dl_no,
                'name': dl_ver_details.name,
                'driver_pic': dl_ver_details.driver_pic,
                'dob': dl_ver_details.dob,
                'addr': dl_ver_details.addr,
                'pincode': dl_ver_details.pincode,
                'license_exp': dl_ver_details.license_exp,
                'state': dl_ver_details.state,
                'name_status': dl_ver_details.name_status,
                'photo_status': dl_ver_details.photo_status,
                'photo_score': round(dl_ver_details.photo_score * 100, 2) if dl_ver_details.photo_score
                is not None else None,
                'dob_status': dl_ver_details.dob_status,
                'lic_exp_status': dl_ver_details.lic_exp_status
            } if dl_ver_details else None,
                'bank_ver_details':{
                'name': bank_ver_details.name,
                'acc_no': bank_ver_details.acc_no,
                'ifsc': bank_ver_details.ifsc,
                'bank_name': bank_ver_details.bank_name,
                'district': bank_ver_details.district,
                'bank_branch': bank_ver_details.bank_branch,
                'name_status': bank_ver_details.name_status
            } if bank_ver_details else None,
                'pan_ver_details':{
                'name': pan_ver_details.name,
                'pan_no': pan_ver_details.pan_no,
                'name_status': pan_ver_details.name_status,
                'dob_status': pan_ver_details.dob_status,
                "dob": pan_ver_details.dob,
                'gender': pan_ver_details.gender,
                'city': pan_ver_details.city,
                'state': pan_ver_details.state,
                'aadhar_linked': pan_ver_details.aadhar_linked
            } if pan_ver_details else None
        }}
    
    
def add_driver_log_func(driver, approval, editedby, remark, changes, change_from, change_to,verf=0):
    """
    Add log entries for driver field changes or verification.

    If `verf == 1`, it logs a single verification-related change. Otherwise, it logs field-wise changes.

    Args:
        driver (int): ID of the driver.
        approval (int): Approval status.
        editedby (str): Name or ID of the person making the changes.
        remark (str): Remark explaining the change.
        changes (list or str): Field names changed (if verf == 0, must be list).
        change_from (list or str): Old values (if verf == 0, must be list).
        change_to (list or str): New values (if verf == 0, must be list).
        verf (int, optional): If set to 1, logs verification-only change. Defaults to 0.

    Returns:
        None
    """
    try:
        if verf==1:
            try:
                new_log = DriverApprovalLog(
                    driver=int(driver),
                    changes=changes,
                    approval=int(approval),
                    editedby=editedby.strip(),
                    change_from=str(change_from).strip(),
                    change_to=str(change_to).strip(),
                    remark=remark.strip()
                    )
                db.session.add(new_log)
                db.session.commit()
                print('Logs added successfully')
            except Exception as e:
                print(f'Error: {str(e)}')
            return
        if not all([driver, approval, editedby, remark, changes, change_from, change_to]):
            print('All fields are required!!!')
            return
        changes_list = changes
        change_from_list = change_from
        change_to_list = change_to

        if len(changes_list) != len(change_from_list) or len(changes_list) != len(change_to_list):
            print('Check lengths of changes, change_from, and change_to are same')
            return

        for change, change_fr, change_to in zip(changes_list, change_from_list, change_to_list):
            new_log = DriverApprovalLog(
                driver=int(driver),
                changes=change.strip(),
                approval=int(approval),
                editedby=editedby.strip(),
                change_from=str(change_fr).strip(),
                change_to=str(change_to).strip(),
                remark=remark.strip()
            )
            db.session.add(new_log)

        db.session.commit()
        print('Logs added successfully')

    except Exception as e:
        print(f'Error: {str(e)}')
        db.session.rollback()
        raise e

def fetch_driver_objects(driver_id):   
    """
    Fetch driver-related objects from the database.

    Args:
        driver_id (int): Driver ID.

    Returns:
        tuple: (Drivers, DriverInfo, DriverBank) if found, else (None, None, None).
    """
    result = (
        db.session.query(Drivers, DriverInfo, DriverBank)
        .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)
        .outerjoin(DriverBank, Drivers.id == DriverBank.driver_id)
        .filter(Drivers.id == driver_id)
        .first()
    )
    return result if result else (None, None, None)


def init_update_data():
    """
    Initialize dictionary to hold updates for different driver-related tables.

    Returns:
        dict: Dictionary with all expected tables as keys and empty dicts as values.
    """
    return {
        'Users': {}, 'Drivers': {}, 'DriverDetails': {}, 'DriverSkill': {}, 'DriverRegion': {},
        'DriverLoc': {}, 'DriverVerify': {}, 'DriverInfo': {}, 'DriverBank': {}, 'DriverIdle': {}
    }

FIELD_MAP = {
    'Users': ['fname', 'lname', 'mobile', 'alt_mobile', 'email', 'sex', 'pwd', 'role', 'enabled', 'credit', 'driver_region', 'ref_code', 'marked', 'label_bv'],
    'Drivers': ['licenseNo', 'approved', 'available', 'rating', 'note', 'perma'],
    'DriverDetails': ['ride_count', 'rating_count', 'hour_count', 'detail_rating', 'earning', 'owed', 'wallet', 'withdrawable', 'approval_ts'],
    'DriverSkill': ['hb_m', 'sed_m', 'suv_m', 'lux_m', 'hb_a', 'sed_a', 'suv_a', 'lux_a'],
    'DriverRegion': ['regions'],
    'DriverLoc': ['lat', 'lng'],
    'DriverVerify': ['id_card', 'photo', 'ref', 'lic', 'bank'],
    'DriverInfo': ['license', 'license_exp', 'dob', 'pres_region', 'pres_addr_lat', 'pres_addr_lng', 'verf_name', 'verf_ph', 'verf_rel', 'id_no', 'behav', 'road', 'body', 'driver_trip_pref', 'extras', 'driver_bike_status', 'driver_reg_source'],
    'DriverBank': ['acc_no', 'ifsc', 'pan_no'],
    'DriverIdle': ['idle_date']
}

def process_field_changes(table, field, value, data, update_data, driver_info, driver_bank,
                          changes, changes_from, changes_to, user_id):
    """
    Process a single field change and update tracking structures.

    Args:
        table (str): Table name from FIELD_MAP.
        field (str): Field being updated.
        value (str): New value or "new,old" string if tracking change.
        data (dict): Full request data.
        update_data (dict): Dictionary of pending updates.
        driver_info (DriverInfo): DriverInfo model instance.
        driver_bank (DriverBank): DriverBank model instance.
        changes (list): Field change names.
        changes_from (list): Old values.
        changes_to (list): New values.
        user_id (int): User ID.

    Returns:
        Union[bool, dict]: True if successful, error dict if validation fails.
    """
    if ',' in value:
        new_value, old_value = value.split(',')
        changes.append(field)
        changes_from.append(old_value)
        changes_to.append(new_value)

        if field in ['role', 'driver_region', 'label_bv', 'approved', 'available', 'ride_count', 'rating_count', 'hour_count', 'detail_rating', 'wallet', 'withdrawable',
                     'hb_m', 'sed_m', 'suv_m', 'lux_m', 'hb_a', 'sed_a', 'suv_a', 'lux_a', 'behav', 'road', 'body', 'driver_trip_pref']:
            if field == 'driver_region':
                update_data[table]['region'] = int(new_value)
            elif field == 'approved':
                update_data[table]['approved'] = int(new_value)
                if int(new_value) > 0 and int(old_value) < 0:
                    if (int(new_value) == 1) and (
                        driver_info.license_verified != DriverInfo.DOC_VERIFIED or
                        driver_info.id_verified != DriverInfo.DOC_VERIFIED or
                        driver_bank.bank_verified != DriverBank.DOC_VERIFIED
                    ):
                        return {"status": -2, "message": "Cannot approve driver without verified documents", "status_code": 400}
                    db.session.query(DriverDetails).filter(DriverDetails.driver_id == int(data['driver_id'])) \
                        .update({DriverDetails.approval_ts: datetime.utcnow()})
            else:
                update_data[table][field] = int(new_value)

        elif field in ['enabled', 'marked', 'id_card', 'photo', 'ref', 'lic', 'bank', 'perma', 'driver_bike_status']:
            update_data[table][field] = new_value.lower() in ['true', '1']

        elif field in ['lat', 'lng', 'pres_addr_lat', 'pres_addr_lng', 'earning', 'owed', 'credit', 'rating']:
            update_data[table][field] = float(new_value)

        elif field in ['license_exp', 'dob', 'idle_date', 'approval_ts']:
            if field in ['dob', 'license_exp']:
                record_to_delete = db.session.query(DriverGLDrivLicDetails).filter_by(driver_id=data['driver_id']).first()
                if record_to_delete:
                    db.session.delete(record_to_delete)
                driver_info.license_verified = DriverInfo.DOC_NOT_VERIFIED
            update_data[table][field] = datetime.strptime(new_value, '%Y-%m-%d').date() if field != 'approval_ts' else datetime.strptime(new_value, '%Y-%m-%d %H:%M:%S')

        else:
            if field == 'mobile':
                new_value, old_value = data.get('mobile').split(',')
                prev_user = db.session.query(Users).filter(Users.mobile == new_value).first()
                curr_user = db.session.query(Users).filter(Users.mobile == old_value).first()
                if prev_user and curr_user:
                    name_matches = fuzz.token_sort_ratio(prev_user.get_name().lower(), curr_user.get_name().lower())
                    if name_matches > 60:
                        # three-way swap logic
                        a, b = int(curr_user.mobile), int(prev_user.mobile)
                        a = a + b
                        curr_user.mobile = str(a)
                        db.session.flush()
                        b = a - b
                        prev_user.mobile = str(b)
                        db.session.flush()
                        a = a - b
                        curr_user.mobile = str(a)
                        db.session.add(MobileChange(int(prev_user.id), b, a))
                        db.session.add(MobileChange(int(curr_user.id), a, b))
                    else:
                        return {"status": -5, "message": "Mobile number already linked to another user", "status_code": 400}
                else:
                    update_data[table][field] = new_value
                    db.session.add(MobileChange(user_id, old_value, new_value))

            elif field == 'license':
                new_value, old_value = data.get('license').split(',')
                update_data['Drivers']['licenseNo'] = new_value
                update_data['DriverInfo']['license'] = new_value
                if db.session.query(Drivers).filter(Drivers.licenseNo == new_value).first():
                    return {"status": -4, "message": "Duplicate license entry detected", "status_code": 400}
                record_to_delete = db.session.query(DriverGLDrivLicDetails).filter_by(driver_id=data['driver_id']).first()
                if record_to_delete:
                    db.session.delete(record_to_delete)
                driver_info.license_verified = DriverInfo.DOC_NOT_VERIFIED

            elif field == 'id_no':
                record_to_delete = db.session.query(DriverGLIDDocDetails).filter_by(driver_id=data['driver_id']).first()
                if record_to_delete:
                    db.session.delete(record_to_delete)
                driver_info.id_verified = DriverInfo.DOC_NOT_VERIFIED
                driver_info.id_no = new_value

            elif field in ['acc_no', 'ifsc']:
                record_to_delete = db.session.query(DriverGLBankDetails).filter_by(driver_id=data['driver_id']).first()
                if record_to_delete:
                    db.session.delete(record_to_delete)
                driver_bank.bank_verified = DriverBank.DOC_NOT_VERIFIED
                update_data[table][field] = new_value

            elif field == 'pan_no':
                record_to_delete = db.session.query(DriverGLPanDetails).filter_by(driver_id=data['driver_id']).first()
                if record_to_delete:
                    db.session.delete(record_to_delete)
                driver_bank.pan_verified = DriverBank.DOC_NOT_VERIFIED
                update_data[table][field] = new_value

            else:
                update_data[table][field] = new_value
    else:
        # no comma in value, treat it as new set value
        if field in ['role', 'driver_region', 'label_bv', 'approved', 'available', 'ride_count', 'rating_count', 'hour_count', 'detail_rating', 'wallet', 'withdrawable',
                     'hb_m', 'sed_m', 'suv_m', 'lux_m', 'hb_a', 'sed_a', 'suv_a', 'lux_a', 'behav', 'road', 'body', 'driver_trip_pref']:
            if field == 'driver_region':
                update_data[table]['region'] = int(value)
            elif field == 'approved':
                update_data[table]['approved'] = int(value)
                if int(value) > 0:
                    db.session.query(DriverDetails).filter(DriverDetails.driver_id == data['driver_id']) \
                        .update({DriverDetails.approval_ts: datetime.utcnow()})
            else:
                update_data[table][field] = int(value)
        elif field in ['enabled', 'marked', 'id_card', 'photo', 'ref', 'lic', 'bank', 'perma', 'driver_bike_status']:
            update_data[table][field] = value.lower() in ['true', '1']
        elif field in ['lat', 'lng', 'pres_addr_lat', 'pres_addr_lng', 'earning', 'owed', 'credit', 'rating']:
            update_data[table][field] = float(value)
        elif field in ['license_exp', 'dob', 'idle_date', 'approval_ts']:
            update_data[table][field] = datetime.strptime(value, '%Y-%m-%d').date() if field != 'approval_ts' else datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        else:
            update_data[table][field] = value
    return True

def process_optional_field(data, field_key, old_val, update_key, table, update_data, changes, changes_from, changes_to):
    new_val = data.get(field_key)
    if new_val is not None:
        changes.append(update_key.replace('_', ' ').title())
        changes_from.append(old_val if old_val else '')
        changes_to.append(new_val)
        update_data[table][update_key] = new_val
        
def process_driver_fields(data, update_data, driver_info, driver_bank, changes, changes_from, changes_to, user_id):
    """
    Process and track all field-level updates for driver across mapped tables.

    Args:
        data (dict): Incoming request data.
        update_data (dict): Dictionary to track update values.
        driver_info (DriverInfo): DriverInfo model instance.
        driver_bank (DriverBank): DriverBank model instance.
        changes (list): Changed fields.
        changes_from (list): Previous values.
        changes_to (list): New values.
        user_id (int): ID of user performing the update.

    Returns:
        Union[None, dict]: Returns error dict if validation fails, otherwise None.
    """
    for table, table_fields in FIELD_MAP.items():
        for field in table_fields:
            value = data.get(field)
            if value:
                result = process_field_changes(table, field, value, data, update_data, driver_info, driver_bank,
                                               changes, changes_from, changes_to, user_id)
                if result is not True:
                    return result

    # Manual fields
    process_optional_field(data, 'driver_languages', driver_info.driver_languages, 'driver_languages', 'DriverInfo',
                           update_data, changes, changes_from, changes_to)
    process_optional_field(data, 'driver_description', driver_info.driver_description, 'driver_description', 'DriverInfo',
                           update_data, changes, changes_from, changes_to)
    process_optional_field(data, 'driver_remark', driver_info.driver_remark, 'driver_remark', 'DriverInfo',
                           update_data, changes, changes_from, changes_to)
    process_optional_field(data, 'pres_addr', driver_info.pres_addr, 'pres_addr', 'DriverInfo',
                           update_data, changes, changes_from, changes_to)
    
def process_driver_info_extras(data, driver_info, update_data, changes, changes_from, changes_to):
    if data.get('height_feet') or data.get('height_inches') or data.get('weight'):
        existing = driver_info.extras
        feet, inches = cm_to_feet_and_inches(existing.get('height', 0))
        height_feet = int(data.get('height_feet', feet))
        height_inches = int(data.get('height_inches', inches))
        weight = float(data.get('weight', existing.get('weight', 0)))

        height_cm = feet_and_inches_to_cm(height_feet, height_inches)

        if weight != existing.get('weight'):
            changes.append('weight')
            changes_from.append(f"{existing['weight']}kg")
            changes_to.append(f"{weight}kg")
        if height_cm != existing.get('height'):
            changes.append('height')
            changes_from.append(f"{feet}'{inches}")
            changes_to.append(f"{height_feet}'{height_inches}")

        update_data['DriverInfo']['extras'] = {
            **existing, 'height': height_cm, 'weight': weight
        }
        
def process_uploaded_docs(files, driver, driver_info, driver_bank, update_data, changes, changes_from, changes_to):
    doc_fields = {
        'pic': ('Drivers', 'pic', driver.pic),
        'licDocFront': ('Drivers', 'licenseDoc', driver_info.driver_lic_doc_f),
        'licDocBack': ('DriverInfo', 'driver_lic_doc_b', driver_info.driver_lic_doc_b),
        'idDocFront': ('DriverInfo', 'driver_id_doc_f', driver_info.driver_id_doc_f),
        'idDocBack': ('DriverInfo', 'driver_id_doc_b', driver_info.driver_id_doc_b),
        'accDoc': ('DriverBank', 'acc_doc', driver_bank.acc_doc),
        'panDoc': ('DriverBank', 'pan_doc', driver_bank.pan_doc),
    }

    for file_key, (table, field, old_val) in doc_fields.items():
        if files.get(file_key):
            pic_url = upload_pic(files[file_key])
            changes.append(file_key)
            changes_from.append(old_val or '')
            changes_to.append(pic_url)
            update_data[table][field] = pic_url
            
def apply_updates(update_data, driver_id, user_id):
    """
    Apply all pending updates to the database.

    Args:
        update_data (dict): Update dictionary for all related tables.
        driver_id (int): Driver ID.
        user_id (int): User ID.

    Raises:
        Exception: If database commit fails.
    """
    if update_data['Users']:
        db.session.query(Users).filter(Users.id == user_id).update(update_data['Users'])
    if update_data['Drivers']:
        db.session.query(Drivers).filter(Drivers.id == driver_id).update(update_data['Drivers'])
    if update_data['DriverDetails']:
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).update(update_data['DriverDetails'])
    if update_data['DriverSkill']:
        db.session.query(DriverSkill).filter(DriverSkill.driver_id == driver_id).update(update_data['DriverSkill'])
    if update_data['DriverRegion']:
        db.session.query(DriverRegion).filter(DriverRegion.driver == driver_id).update(update_data['DriverRegion'])
    if update_data['DriverLoc']:
        db.session.query(DriverLoc).filter(DriverLoc.driver_id == driver_id).update(update_data['DriverLoc'])
    if update_data['DriverVerify']:
        db.session.query(DriverVerify).filter(DriverVerify.driver_id == driver_id).update(update_data['DriverVerify'])
    if update_data['DriverInfo']:
        db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).update(update_data['DriverInfo'])
    if update_data['DriverBank']:
        db.session.query(DriverBank).filter(DriverBank.driver_id == driver_id).update(update_data['DriverBank'])
    if update_data['DriverIdle']:
        db.session.query(DriverIdle).filter(DriverIdle.driver_id == driver_id).update(update_data['DriverIdle'])

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        raise e
    
def trigger_live_update(driver_id):
    driver_det = (
        db.session.query(Users, Drivers)
        .filter(Users.id == Drivers.user, Drivers.id == driver_id)
        .first()
    )
    live_update_to_channel({'driver_id': driver_id}, room_name='drivers', type='drivers',
                           region=driver_det[0].region, channel='update_driver')
        
def update_driver_and_log(data, files):
    """
    Update driver information, documents, and log all changes.

    Args:
        data (dict): Request data including driver fields and metadata.
        files (dict): Dictionary of uploaded files.

    Returns:
        dict: Response containing status, message, and HTTP code.
    """
    driver, driver_info, driver_bank = fetch_driver_objects(data['driver_id'])
    if not driver:
        return {"status": -1, "message": "Driver not found", "status_code": 404}

    user_id = driver.user
    update_data = init_update_data()
    changes, changes_from, changes_to = [], [], []

    process_driver_fields(data, update_data, driver_info, driver_bank, changes, changes_from, changes_to, user_id)

    process_driver_info_extras(data, driver_info, update_data, changes, changes_from, changes_to)
    
    process_uploaded_docs(files, driver, driver_info, driver_bank, update_data, changes, changes_from, changes_to)

    if data.get('approval') and data.get('editedby') and data.get('remark'):
        add_driver_log_func(data['driver_id'], data['approval'], data['editedby'], data['remark'],
                            changes, changes_from, changes_to)

    apply_updates(update_data, data['driver_id'], user_id)

    trigger_live_update(data['driver_id'])

    return {'success': 1, 'message': 'Driver updated successfully', "status_code": 200}
     

def get_locality_service(lat: float, long: float):
    """
    Fetch locality name based on latitude and longitude using MapMyIndia API.

    Args:
        lat (float): Latitude.
        long (float): Longitude.

    Returns:
        dict: API response with locality info or error.
    """
    params = {
        'lat': lat,
        'lng': long
    }

    response = requests.get(mapmyindia_url, params=params)

    if response.status_code == 200:
        data = response.json()
        if 'results' in data and data['results']:
            result = data['results'][0]
            locality = result.get('locality') or result.get('village') or result.get('subDistrict') or result.get('district') or result.get('formatted_address')
            return {'success': 1, "status":"success",'data': locality,"message":"Locality fetched successfully","status_code":200}
        else:
            return {'success': 0, 'data': {'locality': 'No locality found'}, 'message': 'No locality found',"status":"success","status_code":200}
    else:
        return {'success': -3, 'message': 'Failed to fetch locality',"status_code":response.status_code,
                "status":"error","data":response.text}
        
