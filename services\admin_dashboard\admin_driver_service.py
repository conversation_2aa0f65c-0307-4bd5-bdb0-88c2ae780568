import math

from sqlalchemy.sql import func, or_,desc,asc,and_,exists, case
from models.models import db, Drivers, Users, DriverDetails, DriverInfo, Bookings, Trip
from utils.distance_utils import EARTH_RADIUS
from utils.bookings.booking_params import Regions



def _base_driver_query():
    return db.session.query(
        Drivers.id.label('driver_id'),
        Users.mobile.label('mobile'),
        func.concat(Users.fname, ' ', Users.lname).label('name'),
        DriverInfo.pres_region.label('location'),
        DriverInfo.pres_addr_lat.label('lat'),
        DriverInfo.pres_addr_lng.label('lng'),
        Drivers.rating.label('rating'),
        DriverDetails.rating_count.label('rating_count'),
        Drivers.approved.label('driver_approved'),
        Drivers.available.label('driver_available'),
        Users.reg.label('timestamp'),
        Drivers.pic.label('driver_pic'),
        Users.region.label('region'),
        Users.label_bv.label('label')
    ).outerjoin(Users, Drivers.user == Users.id) \
     .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id) \
     .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)


def _apply_global_search(query, payload):
    if payload.is_global and payload.search_query:
        if payload.is_global == 1:
            return query.filter(Drivers.licenseNo == payload.search_query)
        elif payload.is_global == 2:
            return query.filter(Users.mobile == int(payload.search_query))
    return query


def _apply_text_search(query, payload):
    if not payload.search_query:
        return query

    q = payload.search_query.strip()

    if q.startswith('#'):
        return query.filter(DriverInfo.pres_region.ilike(f"%{q[1:]}%"))

    try:
        search_id = int(q)
        return query.filter(or_(Drivers.id == search_id, Users.mobile == search_id))
    except ValueError:
        name_parts = q.split()
        if len(name_parts) >= 2:
            conditions = []
            for i in range(len(name_parts)):
                for j in range(i + 1, len(name_parts)):
                    conditions.append(
                        and_(
                            Users.fname.ilike(f"%{name_parts[i]}%"),
                            Users.lname.ilike(f"%{name_parts[j]}%")
                        )
                    )
                    conditions.append(
                        and_(
                            Users.fname.ilike(f"%{name_parts[j]}%"),
                            Users.lname.ilike(f"%{name_parts[i]}%")
                        )
                    )
            return query.filter(or_(*conditions))
        else:
            return query.filter(
                or_(
                    Users.fname.ilike(f"%{q}%"),
                    Users.lname.ilike(f"%{q}%"),
                    Drivers.licenseNo == q
                )
            )


def _apply_region_filter(query, payload):
    if not payload.is_global and payload.region and payload.region != Regions.ALL_REGIONS_ACCESS:
        try:
            regions = [int(value) for value in payload.region.split(',')]
            return query.filter(Users.region.in_(regions))
        except Exception:
            raise ValueError("Error in search regions string")
    return query


def _apply_status_filters(query, payload):
    if payload.status:
        status_vals = [int(x) for x in payload.status.split(',')]
        query = query.filter(Drivers.available.in_(status_vals))
    if payload.approval:
        approval_vals = [int(x) for x in payload.approval.split(',')]
        query = query.filter(Drivers.approved.in_(approval_vals))
    if payload.rating_gt:
        query = query.filter(Drivers.rating >= float(payload.rating_gt))
    if payload.rating_lt:
        query = query.filter(Drivers.rating <= float(payload.rating_lt))
    return query


def _apply_timestamp_filters(query, payload):
    if payload.timestamp_gt:
        query = query.filter(Users.reg >= payload.timestamp_gt)
    if payload.timestamp_lt:
        query = query.filter(Users.reg <= payload.timestamp_lt)
    return query


def _apply_label_filter(query, payload):
    if payload.label:
        query = query.filter(Users.label_bv == int(payload.label))
    return query


def _get_ongoing_driver_ids():
    ongoing = db.session.query(Bookings.driver) \
        .join(Trip, Trip.book_id == Bookings.id) \
        .filter(Trip.endtime.is_(None)) \
        .distinct().all()
    return set(row.driver for row in ongoing)


def _apply_ongoing_filter(query, payload, ongoing_driver_ids):
    if payload.ongoing:
        try:
            values = [int(x) for x in payload.ongoing.split(',')]
            conditions = []
            if 1 in values:
                conditions.append(Drivers.id.in_(ongoing_driver_ids))
            if 0 in values:
                conditions.append(~Drivers.id.in_(ongoing_driver_ids))
            if conditions:
                query = query.filter(or_(*conditions))
        except ValueError:
            raise ValueError("Invalid ongoing value(s) (must be 0 or 1)")
    return query


def _apply_location_radius_filter(query, payload):
    if payload.lat and payload.long and payload.radius_km:
        try:
            lat1 = float(payload.lat)
            long1 = float(payload.long)
            radius = float(payload.radius_km)
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid lat/long/radius: {e}")

        degrees_to_radians = math.pi / 180.0
        lat2 = func.coalesce(DriverInfo.pres_addr_lat, 0)
        long2 = func.coalesce(DriverInfo.pres_addr_lng, 0)

        phi1 = (90.0 - lat1) * degrees_to_radians
        phi2 = (90.0 - lat2) * degrees_to_radians
        theta1 = long1 * degrees_to_radians
        theta2 = long2 * degrees_to_radians

        cos_value = (
            func.sin(phi1) * func.sin(phi2) * func.cos(theta1 - theta2) +
            func.cos(phi1) * func.cos(phi2)
        )
        arc = func.acos(func.least(func.greatest(cos_value, -1.0), 1.0))
        distance_expr = arc * EARTH_RADIUS

        query = query.filter(distance_expr <= radius)
    return query


def _apply_sorting(query, payload):
    payload.sort_by, payload.sort_order = (
        ('timestamp', 'desc') if payload.sort_by == '1' else
        ('timestamp', 'asc') if payload.sort_by == '2' else
        ('driver_rating', 'desc') if payload.sort_by == '3' else
        ('driver_rating', 'asc') if payload.sort_by == '4' else
        (payload.sort_by, payload.sort_order)
    )

    if payload.sort_by == 'timestamp':
        return query.order_by(desc(Users.reg) if payload.sort_order == 'desc' else asc(Users.reg))
    elif payload.sort_by == 'driver_rating':
        return query.order_by(
            desc(Drivers.rating) if payload.sort_order == 'desc' else asc(Drivers.rating),
            desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count)
        )
    elif payload.sort_by == 'driver_rating_count':
        return query.order_by(desc(DriverDetails.rating_count) if payload.sort_order == 'desc' else asc(DriverDetails.rating_count))
    elif payload.sort_by == 'driver_ride_count':
        return query.order_by(desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count))

    return query


def _build_response(results, starting_from, ongoing_driver_ids):
    response = []
    latest_timestamp = None

    for result in results:
        response.append({
            'driver_id': result.driver_id,
            'mobile': result.mobile,
            'name': result.name,
            'location': result.location,
            'region': result.region,
            'rating': result.rating,
            'rating_count': result.rating_count,
            'approval': result.driver_approved,
            'status': result.driver_available,
            'image': result.driver_pic,
            'timestamp': result.timestamp,
            'label': result.label,
            'ongoing': result.driver_id in ongoing_driver_ids
        })
        if not int(starting_from) and (latest_timestamp is None or result.timestamp > latest_timestamp):
            latest_timestamp = result.timestamp

    last_entry = latest_timestamp.strftime('%Y-%m-%d %H:%M:%S') if latest_timestamp else None
    return response, last_entry

def driver_search_service(payload):
    try:
        starting_from = payload.starting_from
        no_of_logs = payload.no_of_logs

        if payload.is_global and not payload.search_query:
            return {'success': 1, 'count': 1, 'lastentry': None, 'data': []}, 200

        query = _base_driver_query()

        query = _apply_global_search(query, payload)
        query = _apply_text_search(query, payload)
        query = _apply_region_filter(query, payload)
        query = _apply_status_filters(query, payload)
        query = _apply_timestamp_filters(query, payload)
        query = _apply_label_filter(query, payload)
        ongoing_driver_ids = _get_ongoing_driver_ids()
        query = _apply_ongoing_filter(query, payload, ongoing_driver_ids)
        query = _apply_location_radius_filter(query, payload)

        query = _apply_sorting(query, payload)
        query = query.order_by(desc(DriverDetails.driver_id))

        count = db.session.query(func.count()).select_from(query.subquery()).scalar()
        query = query.offset(starting_from).limit(no_of_logs)
        results = query.all()

        response, last_entry = _build_response(results, starting_from, ongoing_driver_ids)

        return {'success': 1, 'count': count, 'lastentry': last_entry, 'data': response}, 200

    except Exception as e:
        return {'success': -5, 'error': f'Error in driver search: {str(e)}'}, 500
    
# def driver_search_service(payload):
#     try:
#         starting_from = payload.starting_from
#         no_of_logs = payload.no_of_logs
#         if payload.is_global and not payload.search_query:
#             return {'success': 1,'count':1,'lastentry': None, 'data': []}, 200
#         # Initial query
#         query = db.session.query(
#             Drivers.id.label('driver_id'),
#             Users.mobile.label('mobile'),
#             func.concat(Users.fname, ' ', Users.lname).label('name'),
#             DriverInfo.pres_region.label('location'),
#             DriverInfo.pres_addr_lat.label('lat'),
#             DriverInfo.pres_addr_lng.label('lng'),
#             Drivers.rating.label('rating'),
#             DriverDetails.rating_count.label('rating_count'),
#             Drivers.approved.label('driver_approved'),
#             Drivers.available.label('driver_available'),
#             Users.reg.label('timestamp'),
#             Drivers.pic.label('driver_pic'),
#             Users.region.label('region'),
#             Users.label_bv.label('label')
#         ).outerjoin(Users, Drivers.user == Users.id) \
#         .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id) \
#         .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)

#         # Global search
#         if payload.is_global and payload.search_query:
#             # print(is_global, search_query, flush = True)
#             if payload.is_global == 1: #global search by licence of driver
#                 query = query.filter(Drivers.licenseNo == payload.search_query)
#             elif payload.is_global == 2: #global search by mobile number of driver
#                 query = query.filter(Users.mobile == int(payload.search_query))
#         elif payload.search_query and payload.search_query.startswith('#'):
#             query = query.filter(DriverInfo.pres_region.ilike(f"%{payload.search_query[1:]}%"))
#         elif payload.search_query:
#             try:
#                 search_id = int(payload.search_query.strip())
#                 query = query.filter(or_(Drivers.id == search_id, Users.mobile == search_id))
#             except ValueError:
#                 name_parts = payload.search_query.split()
#                 if len(name_parts) >= 2:
#                     conditions = []
#                     for i in range(len(name_parts)):
#                         for j in range(i + 1, len(name_parts)):
#                             conditions.append(
#                                 and_(
#                                     Users.fname.ilike(f"%{name_parts[i]}%"),
#                                     Users.lname.ilike(f"%{name_parts[j]}%")
#                                 )
#                             )
#                             conditions.append(
#                                 and_(
#                                     Users.fname.ilike(f"%{name_parts[j]}%"),
#                                     Users.lname.ilike(f"%{name_parts[i]}%")
#                                 )
#                             )
#                     query = query.filter(or_(*conditions))
#                 else:
#                     query = query.filter(
#                         or_(
#                             Users.fname.ilike(f"%{payload.search_query}%"),
#                             Users.lname.ilike(f"%{payload.search_query}%"),
#                             Drivers.licenseNo == payload.search_query.strip()
#                         )
#                     )

#         try:
#             if not payload.is_global and payload.region and payload.region != Regions.ALL_REGIONS_ACCESS:
#                 regions = [int(value) for value in payload.region.split(',')]
#                 query = query.filter(Users.region.in_(regions))
#         except Exception as e:
#             return {'success': -1, 'error': 'Error in search regions string'}, 400
#         if payload.status:
#             available_values = [int(value) for value in payload.status.split(',')]
#             query = query.filter(Drivers.available.in_(available_values))
#         if payload.approval:
#             approval_values = [int(value) for value in payload.approval.split(',')]
#             query = query.filter(Drivers.approved.in_(approval_values))
#         if payload.rating_gt:
#             query = query.filter(Drivers.rating >= float(payload.rating_gt))
#         if payload.rating_lt:
#             query = query.filter(Drivers.rating <= float(payload.rating_lt))
#         if payload.timestamp_gt:
#             query = query.filter(Users.reg >= payload.timestamp_gt)
#         if payload.timestamp_lt:
#             query = query.filter(Users.reg <= payload.timestamp_lt)
#         if payload.label:
#             query = query.filter(Users.label_bv == int(payload.label))
            
#         ongoing_driver_ids = db.session.query(Bookings.driver).join(Trip, Trip.book_id == Bookings.id)\
#             .filter(Trip.endtime.is_(None)).distinct().all()
#         ongoing_driver_ids = set(row.driver for row in ongoing_driver_ids)
        
#         if payload.ongoing:
#             try:
#                 ongoing_values = [int(value) for value in payload.ongoing.split(',')]
#                 conditions = []

#                 if 1 in ongoing_values:
#                     conditions.append(Drivers.id.in_(ongoing_driver_ids))
#                 if 0 in ongoing_values:
#                     conditions.append(~Drivers.id.in_(ongoing_driver_ids))

#                 if conditions:
#                     query = query.filter(or_(*conditions))
#             except ValueError:
#                 return {'success': -1, 'error': 'Invalid ongoing value(s) (must be 0 or 1)'}, 400
            
#         if payload.lat and payload.long and payload.radius_km:
#             try:
#                 lat1 = float(payload.lat)
#                 long1 = float(payload.long)
#                 radius = float(payload.radius_km)
#             except (ValueError, TypeError) as e:
#                 return {
#                     'success': -1,
#                     'error': f'Invalid lat/long/radius: {e}'
#                 }, 400

#             # Convert degrees to radians for SQL expression
#             degrees_to_radians = math.pi / 180.0

#             # lat/long fields in DB
#             lat2 = func.coalesce(DriverInfo.pres_addr_lat, 0)
#             long2 = func.coalesce(DriverInfo.pres_addr_lng, 0)

#             phi1 = (90.0 - lat1) * degrees_to_radians
#             phi2 = (90.0 - lat2) * degrees_to_radians
#             theta1 = long1 * degrees_to_radians
#             theta2 = long2 * degrees_to_radians

#             # Formula for spherical distance (in radians)
#             cos_value = (
#                 func.sin(phi1) * func.sin(phi2) * func.cos(theta1 - theta2) +
#                 func.cos(phi1) * func.cos(phi2)
#             )

#             arc = func.acos(func.least(func.greatest(cos_value, -1.0), 1.0))  # Clamp for safety

#             distance_expr = arc * EARTH_RADIUS

#             query = query.filter(distance_expr <= radius)


#         '''
#         1 : 'timestamp' desc
#         2 : timestamp asc
#         3 : rating desc
#         4 : raitng asc
#         '''
#         payload.sort_by, payload.sort_order = (
#             ('timestamp', 'desc') if payload.sort_by == '1' else
#             ('timestamp', 'asc') if payload.sort_by == '2' else
#             ('driver_rating', 'desc') if payload.sort_by == '3' else
#             ('driver_rating', 'asc') if payload.sort_by == '4' else
#             (payload.sort_by, payload.sort_order)
#         )

#         if payload.sort_by == 'timestamp':
#             query = query.order_by(desc(Users.reg) if payload.sort_order == 'desc' else asc(Users.reg))
#         elif payload.sort_by == 'driver_rating':
#             query = query.order_by(
#                 desc(Drivers.rating) if payload.sort_order == 'desc' else asc(Drivers.rating),
#                 desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count)
#             )
#         elif payload.sort_by == 'driver_rating_count':
#             query = query.order_by(desc(DriverDetails.rating_count) if payload.sort_order == 'desc' else asc(DriverDetails.rating_count))
#         elif payload.sort_by == 'driver_ride_count':
#             query = query.order_by(desc(DriverDetails.ride_count) if payload.sort_order == 'desc' else asc(DriverDetails.ride_count))
#         query = query.order_by(desc(DriverDetails.driver_id))
        
#         count = db.session.query(func.count()).select_from(query.subquery()).scalar()

#         query = query.offset(starting_from).limit(no_of_logs)

#         results = query.all()

#         response = []
#         latest_timestamp = None
#         for result in results:
#             response.append({
#                 'driver_id': result.driver_id,
#                 'mobile': result.mobile,
#                 'name': result.name,
#                 'location': result.location,
#                 'region': result.region,
#                 'rating': result.rating,
#                 'rating_count': result.rating_count,
#                 'approval': result.driver_approved,
#                 'status': result.driver_available,
#                 'image': result.driver_pic,
#                 'timestamp': result.timestamp,
#                 'label':result.label,
#                 'ongoing': result.driver_id in ongoing_driver_ids
#             })
#             if not int(starting_from) and (latest_timestamp is None or result.timestamp > latest_timestamp):
#                 latest_timestamp = result.timestamp
#         last_entry = latest_timestamp.strftime('%Y-%m-%d %H:%M:%S') if latest_timestamp else None

#         return {'success': 1,'count':count,'lastentry': last_entry, 'data': response}, 200
#     except Exception as e:
#         return {'success': -5, 'error': f'Error in driver search: {str(e)}'}, 500


def fetch_single_driver_profile(driver_id: int) -> dict:
    """
    Fetch detailed information of a single driver for admin view.
    """
    query = db.session.query(
        Drivers.id.label('driver_id'),
        Users.mobile.label('mobile'),
        func.concat(Users.fname, ' ', Users.lname).label('name'),
        DriverInfo.pres_region.label('location'),
        Drivers.rating.label('rating'),
        DriverDetails.rating_count.label('rating_count'),
        Drivers.approved.label('driver_approved'),
        Drivers.available.label('driver_available'),
        Users.reg.label('timestamp'),
        Drivers.pic.label('driver_pic'),
        Users.region.label('region'),
        Users.label_bv.label('label')
    ).filter(Drivers.id == driver_id).outerjoin(Users, Drivers.user == Users.id)\
     .outerjoin(DriverDetails, Drivers.id == DriverDetails.driver_id)\
     .outerjoin(DriverInfo, Drivers.id == DriverInfo.driver_id)

    result = query.first()

    if not result:
        return {}

    ongoing_driver_ids = db.session.query(Bookings.driver).join(Trip, Trip.book_id == Bookings.id)\
        .filter(Trip.endtime.is_(None)).distinct().all()
    ongoing_driver_ids = set(row.driver for row in ongoing_driver_ids)

    return {
        'driver_id': result.driver_id,
        'mobile': result.mobile,
        'name': result.name,
        'location': result.location,
        'region': result.region,
        'rating': result.rating,
        'rating_count': result.rating_count,
        'approval': result.driver_approved,
        'status': result.driver_available,
        'image': result.driver_pic,
        'timestamp': result.timestamp,
        'label': result.label,
        'ongoing': result.driver_id in ongoing_driver_ids
    }