#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra# === Standard Library ===

# === Standard Library ===
import http.client
from datetime import timedelta
from flask import current_app as app
import base64
import os
import json
import re, requests

# === Third-Party Libraries ===

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

# === Application (Internal) Imports ===
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from utils.auth.login_utils import create_token
from utils.response_utils import standard_response
from utils.auth.login_params import ExpiryTime,TokenValidity
from urllib.parse import urlencode
from urllib.parse import quote
from config import BaseConfig

MASTER_OTP='3487'
# Configuration
GUPSHUP_BASE_URL = "https://enterpriseapi.smsgupshup.com/GatewayAPI/rest"
GUPSHUP_USER_ID_TRANSACTIONAL = BaseConfig.GUPSHUP_USER_ID_TRANSACTIONAL
GUPSHUP_PASSWORD_TRANSACTIONAL = BaseConfig.GUPSHUP_PASSWORD_TRANSACTIONAL
KEY_BASE64_TRANSACTIONAL = BaseConfig.KEY_BASE64_TRANSACTIONAL
GUPSHUP_USER_ID_OTP = BaseConfig.GUPSHUP_USER_ID_OTP
GUPSHUP_PASSWORD_OTP = BaseConfig.GUPSHUP_PASSWORD_OTP
KEY_BASE64_OTP = BaseConfig.KEY_BASE64_OTP
PRINCIPAL_ENTITY_ID = BaseConfig.PRINCIPAL_ENTITY_ID
MASK="DRVFME"
COUNTRY_CODE_IN = '91'
MSG_SENDER_ID = 'DRVFME'
IST_OFFSET_TIMEDELTA = timedelta(hours=5, minutes=30)
SUPPORT_PH = ['']
TEMPLATE_ID_MAPPING = {
    "trip-allocated": "1207161718498465531",
    "trip-start":"1207161718541285038",
    "trip-stop": "1207161718524019073",
    "user-cancelled": "1207161718517667252",
    "d4m-cancelled": "1207161718470839793",
    "b2b-pickup-drop":"1207171836881518305",
    "d4m-cancelled-user-fine":"1207174774224480567",
    "d4m-cancelled-user-not-fine": "1207174678250741967",
    "d4m-cancelled-driver-fine": "1207174774635566624",
    "d4m-cancelled-driver-not-fine": "1207174774495624919",

}

def send_email(fr, to, template="test"):

    conn = http.client.HTTPConnection("api.msg91.com")
    payload = '{ "authkey" : "' + app.config['OTP_AUTH_KEY'] + '", "template_id" : "test", "to" : "' + to + '", "from" :"' + fr + '", }'
    print(payload)
    headers = { 'content-type': "application/json" }
    conn.request("POST", "/api/v5/email", payload, headers)
    res = conn.getresponse()
    data = res.read()

    print(data.decode("utf-8"))
    if res.status != 200:
        return False
    else:
        return True

def decode_base64_key(key_base64: str) -> bytes:
    padding_needed = len(key_base64) % 4
    if padding_needed:
        key_base64 += '=' * (4 - padding_needed)
    return base64.urlsafe_b64decode(key_base64)

def encrypt_data(query_string: str) -> str:
    AES_KEY = decode_base64_key(KEY_BASE64_OTP)
    if len(AES_KEY) != 32:
        raise ValueError("AES_KEY must be 32 bytes long for AES-256 encryption.")
    
    aesgcm = AESGCM(AES_KEY)
    iv = os.urandom(12)  # 12 bytes for IV as required by Gupshup
    # Encrypt the query string; no additional authenticated data (AAD) is used.
    ciphertext = aesgcm.encrypt(iv, query_string.encode('utf-8'), None)
    
    # Concatenate IV and ciphertext (ciphertext includes the auth tag)
    encrypted_bytes = iv + ciphertext
    # Encode using URL-safe Base64 encoding.
    encrypted_payload = base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
    return encrypted_payload

def generate_otp_gupshup(phone_no: str, otp_length: int = 4, otp_type: str = "NUMERIC"):
    # Prepare the payload according to the API requirements.
    payload = {
        "password": GUPSHUP_PASSWORD_OTP,
        "method": "TWO_FACTOR_AUTH",
        "v": "1.1",
        "phone_no": phone_no,
        "msg": "Your OTP Verification Number for Drivers4Me is %code%",
        "format": "text",
        "otpCodeLength": otp_length,
        "otpCodeType": otp_type,
    }
    
    # Convert payload to a URL-encoded query string.
    query_string = urlencode(payload)
    print("string 2: ",query_string, flush=True)
    
    # Encrypt the query string as required.
    encrypted_payload = encrypt_data(query_string)
    
    # Build the final request URL.
    request_url = f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&encrdata={encrypted_payload}"
    #request_url = f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&{query_string}"
    print("string: ",request_url, flush=True)
    # Send the GET request to the Gupshup API.
    response = requests.get(request_url)
    print("Response: ",response.text,flush=True)
    return response.text

def validate_otp(mobile, otp):
    try:
        if otp == MASTER_OTP:
            return True
        conn = http.client.HTTPConnection("control.msg91.com")
        headers = {'content-type': "application/x-www-form-urlencoded"}
        mob_str = COUNTRY_CODE_IN + str(mobile)
        content = f"/api/verifyRequestOTP.php?authkey={app.config['OTP_AUTH_KEY']}&mobile={mob_str}&otp={otp}"
        conn.request("POST", content, "", headers)
        res = conn.getresponse()
        data = res.read()
        try:
            resp_arr = json.loads(data.decode("utf-8"))
            return resp_arr['type'] == 'success'
        except Exception:
            return False
    except Exception as e:
        return False
    
# Verify OTP
def verify_otp_gupshup(phone_no: str, otp_code: str):
    # Master override
    if otp_code == MASTER_OTP:
        return {"status": "success", "message": "OTP verified successfully"}

    # Prepare Gupshup request
    payload = {
        "password": GUPSHUP_PASSWORD_OTP,
        "method": "TWO_FACTOR_AUTH",
        "v": "1.1",
        "phone_no": phone_no,
        "otp_code": otp_code,
    }
    query_string = urlencode(payload)
    encrypted_payload = encrypt_data(query_string)
    response = requests.get(
        f"{GUPSHUP_BASE_URL}?userid={GUPSHUP_USER_ID_OTP}&encrdata={encrypted_payload}"
    )

    print("Response:", response.text, flush=True)

    # Check Gupshup’s reply
    if "success" in response.text:
        return {"status": "success", "success": 1, "message": "OTP verified successfully"}

    # On Gupshup error, fall back to validate_otp
    is_valid = validate_otp(phone_no, otp_code)
    if is_valid:
        return {
            "status": "success",
            "success": 1,
            "message": "OTP verified successfully "
        }
    else:
        # Determine the error message
        if "OTP expired" in response.text:
            msg = "OTP expired"
        elif "invalid OTP" in response.text:
            msg = "Invalid OTP"
        else:
            msg = "Unknown error"
        return {"status": "error", "success": 0, "message": msg}
  
    
def send_bulk_message_gupshup(
    phone_numbers: list[str],
    message: str,
    msg_type: str = "TEXT",
    auth_scheme: str = "plain",
    v: str = "1.1",
    response_format: str = "text",
    **optional_params
) -> str:
    """
    Send an SMS to one or more recipients via GupShup Enterprise API.
    Args:
        phone_numbers (list[str]): List of phone numbers in international format.
        message (str): Message text.
        msg_type (str): Message type (e.g. TEXT, Unicode_text).
        auth_scheme (str): Authentication scheme (default is 'plain').
        v (str): API version.
        response_format (str): Format of API response.
        **optional_params: Additional optional API parameters.
    Returns:
        str: API response.
    """
    # Join phone numbers with commas for bulk sending
    recipients = ",".join(phone_numbers)
        # Base payload
    payload = {
        "userid": GUPSHUP_USER_ID_TRANSACTIONAL,
        "password": GUPSHUP_PASSWORD_TRANSACTIONAL,
        "send_to": recipients,
        "msg": message,
        "msg_type": msg_type,
        "method": "sendMessage",
        "auth_scheme": auth_scheme,
        "v": v,
        "format": response_format,
    }
    # Merge in any optional parameters
    payload.update(optional_params)
    # Encode the query and make the GET request
    query_string = urlencode(payload)
    request_url = f"{GUPSHUP_BASE_URL}?{query_string}"
    print(f"Request URL: {request_url}")
    response = requests.get(request_url)
    print(f"GupShup response: {response.text}")
    return response.text

def process_otp_request(data):
    mobile = data.mobile
    country_code = data.countrycode

    redis_key = f"generate_otp_attempts_{mobile}"
    redis_data,_ = read_redis_data(redis_key)
    attempts = redis_data.get('count', 0)
    lock_ttl = execute_with_fallback('ttl', redis_key)

    if lock_ttl > 0:
        return standard_response(
            success=-3,
            status=423,
            message=f"Too many tries. Try again in {lock_ttl} seconds.",
            response_status="error"
        )

    attempts += 1
    update_redis_data(redis_key, {"count": attempts})

    if attempts >= ExpiryTime.FAILED_ATTEMPTS_REGISTRATION_ALLOWED:
        update_redis_data(redis_key, {"count": 0})
        execute_with_fallback('expire', redis_key, ExpiryTime.LOCK_EXPIRY_TIME)
        return standard_response(
            success=-3,
            status=423,
            message="Too many OTP requests. Please try again later in 10 mins.",
            response_status="error"
        )

    raw_response = generate_otp_gupshup(country_code + mobile)
    parts = [p.strip() for p in raw_response.split('|')]
    status = parts[0].lower() if parts else ""
    provider_msg = parts[-1] if len(parts) > 1 else raw_response

    if status == 'success':
        write_redis_data(redis_key, {"count": 0})
        return standard_response(
            success=1,
            status=201,
            message="OTP has been successfully sent.",
            response_status="success"
        )

    return standard_response(
        success=-4,
        status=500,
        message="Unable to process OTP request. Please try again later.",
        response_status="error"
    )


def process_otp_validation(mobile: str, otp: str, countrycode: str):
    redis_key = f"failed_otp_attempts_{mobile}"
    lock_ttl = execute_with_fallback('ttl', redis_key)

    try:
        lock_ttl = int(lock_ttl)
    except (TypeError, ValueError):
        lock_ttl = -1

    if lock_ttl > 0:
        return standard_response(
            success=-3,
            status=423,
            message=f"Account locked due to multiple failed attempts. Try again in {lock_ttl} seconds.",
            response_status="error"
        )

    response = verify_otp_gupshup(countrycode + mobile, otp)
    status = response.get("status")
    message = response.get("message", "")

    if status == "error" and "expired" in message.lower():
        return standard_response(
            success=-6,
            status=401,
            message="OTP expired. Please request a new one.",
            response_status="error"
        )

    if status == "error" and "invalid" in message.lower():
        redis_data,_ = read_redis_data(redis_key)
        failed_attempts = redis_data.get("count", 0) + 1

        if failed_attempts >= ExpiryTime.FAILED_ATTEMPTS_ALLOWED:
            update_redis_data(redis_key, {"count": 0})
            execute_with_fallback('expire', redis_key, ExpiryTime.LOCK_EXPIRY_TIME)
            return standard_response(
                success=-7,
                status=423,
                message="Account locked due to multiple failed attempts. Try again in 10 minutes.",
                response_status="error"
            )

        update_redis_data(redis_key, {"count": failed_attempts})
        return standard_response(
            success=-4,
            status=401,
            message=f"The OTP provided is incorrect. Failed attempt: {failed_attempts}.",
            response_status="error"
        )

    if status == "error":
        return standard_response(
            success=-5,
            status=400,
            message=f"OTP verification failed: {message}",
            response_status="error"
        )

    # Success path
    update_redis_data(redis_key, {"count": 0})
    token_data = create_token(phone_number=mobile)

    return standard_response(
        success=1,
        status=200,
        message="OTP validated successfully.",
        data={"token_and_secret": token_data},
        response_status="success"
    )

