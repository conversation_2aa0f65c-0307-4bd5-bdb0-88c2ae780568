tags:
  - Admin Analytics
summary: Get Driver Inventory Count
description: >
  This endpoint retrieves the inventory count for driver transactions.
  It returns the counts of various T-Shirt sizes (S, M, L, XL, XXL, XXXL) and Bags.
  The data is filtered by a specified date range, time range, and optionally by a region filter.
  Only those inventory categories with non-zero counts are returned in the response.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    default: "00:00:00"
    description: "Start time for filtering transactions (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    default: "23:59:59"
    description: "End time for filtering transactions (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A comma-separated list of region IDs to filter transactions.
      Use "-1" to include all regions.
    example: "1,2,3"
    description: >
      Specifies the type of transaction to use in inventory calculations.
      Although this endpoint primarily calculates inventory counts for T-Shirt, Registration, and Bag methods
    example: "Fine"
responses:
  200:
    description: "Successfully retrieved driver inventory counts."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 indicates success)."
          example: 1
        data:
          type: object
          description: >
            An object representing the inventory counts for each category.
            Only categories with non-zero counts are returned.
          properties:
            S:
              type: integer
              description: "Count of S T-Shirts."
              example: 10
            M:
              type: integer
              description: "Count of M T-Shirts."
              example: 15
            L:
              type: integer
              description: "Count of L T-Shirts."
              example: 5
            XL:
              type: integer
              description: "Count of XL T-Shirts."
              example: 8
            XXL:
              type: integer
              description: "Count of XXL T-Shirts."
              example: 2
            XXXL:
              type: integer
              description: "Count of XXXL T-Shirts."
              example: 1
            Bags:
              type: integer
              description: "Count of Bags."
              example: 7
  400:
    description: "Invalid request due to missing or invalid parameters."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (e.g., -2 indicates missing parameters)."
          example: -2
        error:
          type: string
          description: "Error message describing the issue."
          example: "Missing date parameters"
  500:
    description: "Internal server error or database failure."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 indicates a server error)."
          example: 0
        error:
          type: string
          description: "Error message."
          example: "Internal server error"
