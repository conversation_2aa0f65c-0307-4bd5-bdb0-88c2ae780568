from models.models import Users, db, UserFCM, AdminAccess
import random
from sqlalchemy import exc
from flask_jwt_extended import create_access_token
from datetime import timedelta
from unittest.mock import patch
from faker import Faker

fake = Faker()

def unique_admin_data():
    return {
        'mobile': f'{random.randint(7000000000, 9999999999)}',  # Random Indian mobile number
        'email': fake.email(),
        'pwd': fake.text(),
        'fname': fake.name(),
        'lname': "<PERSON>e"
    }

def create_admin_user(data, role=Users.ROLE_SUPERADMIN):
    admin_user = Users(
        fname = data['fname'],
        lname = data['lname'],
        mobile = data['mobile'],
        email = data['email'],
        pwd = data['pwd'],
        role = role
    )

    try:
        db.session.add(admin_user)
        db.session.commit()
        admin_access = AdminAccess(admin_user_id=admin_user.id)
        db.session.add(admin_access)
        db.session.commit()
        print(f"Admin created with ID: {admin_user.id}")
    except exc.IntegrityError:
        db.session.rollback()

    return admin_user

""" Test cases for api: /token/admin/login """

def test_successful_superadmin_login(client):
    data = unique_admin_data()
    user = create_admin_user(data)

    form_data = {
        'mobile': data['mobile'],
        'pwd': data['pwd']
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1  # Expect success
    assert 'notification' in res_data['data']
    assert Users.ROLE_SUPERADMIN == res_data['data']['role']

# Test case for missing credentials
def test_missing_credentials(client):

    response = client.post('/token/admin/login', data={}, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1

# Test case for invalid password
def test_invalid_password(client):
    data = unique_admin_data()
    create_admin_user(data)

    form_data = {
        'mobile': data['mobile'],
        'pwd': 'wrongpassword'
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for forbidden access due to role
def test_forbidden_role(client):
    data = unique_admin_data()
    forbidden_role = 1
    user = create_admin_user(data, role=forbidden_role)

    form_data = {
        'mobile': data['mobile'],
        'pwd': data['pwd']
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 403
    res_data = response.get_json()
    assert res_data['success'] == 0
    assert res_data['message'] == 'Forbidden access for this role'

# Test case for user not found
def test_user_not_found(client):
    data = unique_admin_data()  # Generate data but don't create the user

    form_data = {
        'mobile': data['mobile'],
        'pwd': data['pwd']
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for missing admin access
def test_missing_admin_access(client):
    data = unique_admin_data()
    user = create_admin_user(data)

    # Delete admin access to simulate the case where it is missing
    AdminAccess.query.filter_by(admin_user_id=user.id).delete()
    db.session.commit()

    form_data = {
        'mobile': data['mobile'],
        'pwd': data['pwd']
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 403
    res_data = response.get_json()
    assert res_data['success'] == 0
    assert res_data['message'] == 'Admin access not found for this user'

# Test case for disabled user account
def test_disabled_user(client):
    data = unique_admin_data()
    user = create_admin_user(data)

    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'mobile': data['mobile'],
        'pwd': data['pwd']
    }

    response = client.post('/token/admin/login', data=form_data, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

""" Test cases for api: /token/admin/login """
