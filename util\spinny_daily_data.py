import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail_v2

date_str = (datetime.now() - timedelta(days=0)).date().strftime("%d%m%Y")
csv_paths = [D4M_UTIL_PATH + 'output/spinny-daily.csv',D4M_UTIL_PATH + 'output/spinny-monthly.csv']
subject = "Spinny Daily Update - "+date_str
content = "Please find the attached data."
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
send_mail_v2(from_addr, to_addr_list, subject, content, csv_paths)