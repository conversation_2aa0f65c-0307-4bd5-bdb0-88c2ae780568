tags:
  - Login_admin
summary: Delete User and Invalidate Tokens
description: >
  This endpoint allows a SUPERADMIN to delete a user by their `user_id`, and invalidates all of their associated tokens. Access is restricted to users with the SUPERADMIN role.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - in: formData
    name: user_id
    type: integer
    required: true
    description: ID of the user to be deleted.
    example: 12345
responses:
  200:
    description: User successfully deleted and tokens invalidated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: integer
          example: 200
        message:
          type: string
          example: User deleted and tokens invalidated
        data:
          type: object
        response_status:
          type: string
          example: success

  403:
    description: Access forbidden due to lack of SUPERADMIN role.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: integer
          example: 403
        message:
          type: string
          example: This is not valid api call
        response_status:
          type: string
          example: error

  422:
    description: Validation failed due to missing or invalid user ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 422
        message:
          type: string
          example: User ID required
        data:
          type: object
          example:
            error:
              - user_id: field required
        response_status:
          type: string
          example: error

  404:
    description: User not found in the system.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        status:
          type: integer
          example: 404
        message:
          type: string
          example: User not found
        response_status:
          type: string
          example: error

  500:
    description: Internal server error occurred during the deletion process.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        status:
          type: integer
          example: 500
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
        response_status:
          type: string
          example: error
