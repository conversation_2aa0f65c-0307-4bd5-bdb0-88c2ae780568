from flask import Blueprint, request, jsonify
from flasgger import swag_from
from flask_jwt_extended import (jwt_required, get_jwt_identity, get_jwt)
from booking_params import BookingParams
from affiliate_b2b.affiliate_models import AffiliateRep, Affiliate, AffBookingLogs,AffiliateCollections
from affiliate_b2b.affiliate_models import AffiliateSpoc, AffiliateAddress, AddressSpoc, AffiliateWalletLogs
from affiliate_b2b.affiliate_models import AffiliateDriverSearch
from models import Bookings, Users, Drivers, BookPending, BookDest, BookPricing, Trip, BookingAlloc, TripPricing,AdminAccess
from models import TripLog, DriverInfo, BookingCancelled, DriverTrans, DriverDetails, DriverCancelled, DriverSearch
from db_config import db, fb_db
from book_ride import DriverBook, create_pending_entry, create_pricing_entry
from _rtdb import _update_driver_pending
from price import Price
from _utils import complete, read_redis_data, compute_driver_wallet, combine_and_convert_to_local, convert_to_local_time
from _utils import compute_driver_wallet, to_camel_case, convert_to_utc, convert_datetime_to_utc, split_date_time
from pymongo.errors import PyMongoError
import pytz
import _sms
from datetime import datetime, timedelta, time,  timezone
import uuid
from _utils_booking import get_dist_on_map, get_b2b_book_code, fetch_booking_trip,process_search_query
from payments import PaymentType
from sqlalchemy import exc, exists, and_, union_all,cast, Date, Time
from _utils_acc import account_enabled, get_driver_user_id
from price_b2b import PriceOneWay
from socketio_b2b import send_live_update_to_affiliate, send_notification_to_affiliate
from socketio_app import send_notification_to_channel, live_update_to_channel
from live_update_booking import send_live_update_of_booking, add_track_data_into_s3
from live_update_aff_book import send_live_aff_booking_table
import time
import json
from b2b import get_affiliate_price_mapping, get_cancel_reason, affiliate_account_enabled
from adminnew.booking.booking_admin import process_search_query
from sqlalchemy.orm import aliased
from sqlalchemy.sql import func, or_, desc, asc, and_, exists, case, not_, cast, select, text
import traceback
from flask import current_app as app
from redis_config import redis_client
from _utils import complete, combine_and_convert_to_local, split_date_time, convert_to_utc
from sqlalchemy.dialects import mysql
from affiliate_b2b.affiliate_login import check_affiliate_tab_access
from affiliate_b2b.api_integration_constants import SPINNY_TYPE_MAPPING, ZOOMCAR_TYPE_MAPPING, AFFILIATE_TYPE_MAPPING
from sqlalchemy.orm import sessionmaker
import math

aff_book = Blueprint('affiliate_booking', __name__)

KNOWN_FIELDS = {
    "aff_id", "search_ids", "loc", "dest_lat", "dest_long", "dest_loc", "trip_name", "trip_type", "remark", "vehicle_no",
    "appoint_ids", "vehicle_models", "src_nickname_id", "dest_nickname_id", "return_trip_name", "release", "priority"
}

KNOWN_FIELDS_API = {
    "aff_id", "search_ids", "loc", "dest_lat", "dest_long", "dest_loc", "trip_name", "trip_type", "remark", "vehicle_no",
    "appointment_id", "vehicle_model", "src_nickname_id", "dest_nickname_id", "return_trip_name", "release"
}

SPOC_FIELDS = {
    "source_spoc_name", "source_spoc_contact" , "dest_spoc_name", "dest_spoc_contact"
}

SEARCH_FIELDS = {
    "trans_type", "city", "reflat", "reflong", "dur", "time", "dashboard"
}


def process_affiliate_search(params: dict,tz='Asia/Kolkata') -> dict:
    """
    Process affiliate search based on the provided parameters and JWT claims.
    Expects parameters:
      - aff_id, trans_type, trip_type, city, reflat, reflong, dur, time,
        and optionally dest_lat, dest_long, is_return, return_time, return_dur.
    """

    try:
        affiliate_id = int(params['aff_id'])
    except (KeyError, ValueError):
        return {'success': -1, 'message': "Invalid affiliate id"}
    

    source = 'admin'
    rep_id = None
    try:
        trip_type = BookingParams.get_b2b_trip_type(params.get('trip_type', BookingParams.TYPE_ROUNDTRIP))
    except (KeyError, ValueError):
        return {'success': -1, 'message': "Invalid trip type"}
    is_return_trip = params.get('is_return', '0') == '1'

    # Duration is only applicable if not oneway
    dur = params.get('dur') if trip_type != BookingParams.TYPE_ONEWAY else None

    try:
        city = int(params['city'])
    except (KeyError, ValueError):
        return {'success': -1, 'message': "Invalid city"}
    
 
    trans_type_list = params.get('trans_type_int', "").split(",")
    try:
        reflat = float(params['reflat'])
        reflong = float(params['reflong'])
    except (KeyError, ValueError):
        return {'success': -1, 'message': "Invalid pickup coordinates"}

    # Parse time; try multiple formats
    time_str = params.get('time')
    if not time_str:
        return {'success': -1, 'message': "Time is required"}
    time_obj = convert_datetime_to_utc(time_str, tz=tz)
    
    # Destination coordinates, if provided
    try:
        dest_lat = float(params.get('dest_lat', 0.0))
        dest_long = float(params.get('dest_long', 0.0))
        dest_exists = ('dest_lat' in params and 'dest_long' in params)
    except ValueError:
        dest_lat, dest_long, dest_exists = 0.0, 0.0, False

    if dest_exists:
        dist = get_dist_on_map(reflat, reflong, dest_lat, dest_long)
    else:
        dist = 0

    results = []
    entries_list = []

    # Process estimates for each transaction type for the outbound journey.
    for trans_type in trans_type_list:
        estimate_result, search_entry = get_affiliate_estimate(
            affiliate_id, rep_id, reflat, reflong, dest_lat, dest_long, trip_type, trans_type,
            time_obj, dur, dist, city, f"{source}-0"
        )
        results.append(estimate_result.get_json())
        entries_list.append(search_entry)

    # If it is a return trip, process estimates for the return journey.
    if is_return_trip:
        return_time_str = params.get('return_time')
        if not return_time_str:
            return {'success': -1, 'message': "Return time is required for return trips"}
        return_time_obj = convert_datetime_to_utc(return_time_str, tz=tz)
        # try:
        #     return_time_obj = datetime.strptime(return_time_str, "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
        # except ValueError:
        #     try:
        #         return_time_obj = datetime.strptime(return_time_str, "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
        #     except ValueError:
        #         try:
        #             return_time_obj = datetime.strptime(return_time_str, "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)
        #         except ValueError:
        #             return {'success': -1, 'message': "Invalid return time format"}
        return_dur = params.get('return_dur') if trip_type != BookingParams.TYPE_ONEWAY else None

        # For return trip, use destination coordinates as starting point.
        for trans_type in trans_type_list:
            estimate_result, search_entry = get_affiliate_estimate(
                affiliate_id, rep_id, dest_lat, dest_long, reflat, reflong, trip_type, trans_type,
                return_time_obj, return_dur, dist, city, f"{source}-1"
            )
            results.append(estimate_result.get_json())
            entries_list.append(search_entry)

    try:
        db.session.bulk_save_objects(entries_list)
        db.session.commit()
    except exc.IntegrityError as e:
        db.session.rollback()
        return {'success': -1, 'message': 'DB Error'}

    return {'success': 1, 'estimates': results}



def get_affiliate_estimate(affiliate_id, rep_id, reflat, reflong, dest_lat, dest_long, trip_type, car_type,
                        time, dur, dist, city, source):
    search_id = uuid.uuid4().urn[9:]
    now = datetime.utcnow()
    
    mapped_id, mapped_client = get_affiliate_price_mapping(affiliate_id, city, trip_type)

    if dur is None:
        oneway = PriceOneWay(mapped_client)
        dur = oneway.CUST_DEFAULT_DUR

    time_dur = datetime.strptime(dur, "%H:%M:%S")
    date = time.date()

    if trip_type == BookingParams.TYPE_OUTSTATION:
        t_dur = str(time_dur.minute) + ":00:00"
        days = time_dur.hour
        end_time = (datetime(date.year, date.month, date.day, time.hour, time.minute, time.second)
                    + timedelta(days, time_dur.minute*3600))
    else:
        days = 0
        t_dur = time_dur
        end_time = (datetime(date.year, date.month, date.day, time.hour, time.minute, time.second)
                        + timedelta(0, time_dur.hour * 3600 + time_dur.minute * 60 + time_dur.second))
    
    min_price = Price.get_price(BookingParams.TYPE_B2B, time_dur, time.time(), end_time.time(), dist, car_type,
                                    time.date(), end_time.date(), 0, client_name=mapped_client, client_trip_type=trip_type)

    search_entry = AffiliateDriverSearch(
        search_id, affiliate_id, rep_id, car_type, reflat, reflong, dest_lat, dest_long, time.time(), time.date(),
        t_dur, now, type=BookingParams.TYPE_B2B, trip_type=trip_type, days=days, insurance=min_price[15],
        insurance_ch=min_price[10], region=city, dist=dist, source=source,estimate=min_price[0],
        cust_base=min_price[1], cust_night=min_price[2], driver_base=min_price[13], driver_night=min_price[14],
        price_id=mapped_id)

    return jsonify({
        'success': 1, 'id': search_id, 'cust_total_fare': min_price[0], 'cust_base_fare': min_price[1],
        'cust_night_fare': min_price[2], 'customer_ot_fare': 0, 'insurance_fare': min_price[10],
        'pretax': min_price[6], 'cgst': min_price[7], 'sgst': min_price[8],
        'booking_ch': min_price[4], 'driver_total_fare': min_price[12], 'driver_base_fare': min_price[13],
        'driver_night_fare': min_price[14], 'driver_ot_fare': 0, 'night': min_price[11], 'insurance': min_price[15],
        'distance': math.ceil(dist / 1000)
    }), search_entry


def create_book(affiliate_search, affiliate_id, mapped_wallet_affiliate_id, loc, dest_lat, dest_long, dest_loc, dest_exists, remark, custom_data,
                trip_type, trip_name, client_name, claims, vehicle_no, src_nickname_id, dest_nickname_id,
                vehicle_model="", appoint_id="", release=1, priority=0, tz='Asia/Kolkata'):
    source = claims.get("source", "")
    affiliate_remark = ""
    admin_remark = ""
    if source == 'admin':
        admin_id = int(claims.get("admin_id"))
        admin_remark = remark
    else:
        rep_id = int(claims.get('id'))
        rep_aff_id = int(claims.get('aff_id'))
        affiliate_remark = remark
    if isinstance(affiliate_search, list):
        estimate, cur_search = affiliate_search
    else:
        cur_search = affiliate_search
    end_time = (datetime(cur_search.date.year, cur_search.date.month, cur_search.date.day,
                        cur_search.time.hour, cur_search.time.minute, cur_search.time.second) +
                timedelta(cur_search.days, cur_search.dur.hour * 3600 +
                        cur_search.dur.minute * 60 + cur_search.dur.second))
    booking = Bookings(None, cur_search.id, BookingParams.BOOKING_DUMMY_ID, cur_search.reflat, cur_search.reflong,
                                cur_search.time, cur_search.date, str(cur_search.dur), end_time.time(), end_time.date(),
                                cur_search.estimate, cur_search.estimate, loc, cur_search.car_type, cur_search.type, cur_search.days,
                                comment=admin_remark, payment_type=PaymentType.PAY_D4M_CREDIT, region=cur_search.region,
                                insurance=cur_search.insurance, insurance_cost=cur_search.insurance_ch, insurance_num=cur_search.insurance)
    db.session.add(booking)
    db.session.flush()
    if source == 'admin':
        rep_book = AffBookingLogs(admin_id=admin_id, aff_id=affiliate_id, book_id=booking.id,
                        mapped_by=cur_search.price_id, mapped_wallet=mapped_wallet_affiliate_id)
    else:
        rep_book = AffBookingLogs(rep_id=rep_id, aff_id=affiliate_id, book_id=booking.id,
                        mapped_by=cur_search.price_id, raised_by=rep_aff_id, mapped_wallet=mapped_wallet_affiliate_id,
                        comment=affiliate_remark)
    db.session.add(rep_book)
    booking.code = get_b2b_book_code(booking.id)
    field_form = {}
    affiliate_mongo_details = AffiliateCollections.affiliates_details.find_one({'affiliate_id': affiliate_id})

    if trip_type == BookingParams.TYPE_ONEWAY:
        field_form = affiliate_mongo_details.get('form_field_oneway', {}).get('add_custom_fields', {})
        trip_type_list = [
            {key: trip[key] for key in trip if key not in ['startImages', 'stopImages']}
            for trip in affiliate_mongo_details.get("trip_type", [])
            if trip.get("tripType") == "One Way"
        ]
    elif trip_type in [BookingParams.TYPE_ROUNDTRIP, BookingParams.TYPE_OUTSTATION]:
        field_form = affiliate_mongo_details.get('form_field_round', {}).get('add_custom_fields', {})
        trip_type_list = [
            {key: trip[key] for key in trip if key not in ['startImages', 'stopImages']}
            for trip in affiliate_mongo_details.get("trip_type", [])
            if trip.get("tripType") != "One Way"
        ]

    matched_trip = next(
        (trip for trip in affiliate_mongo_details["trip_type"]
        if trip["trip_type_name"].strip().lower() == trip_name.strip().lower()),
        None
    )
    start_images_to_upload = matched_trip.get("startImages", [])
    stop_images_to_upload = matched_trip.get("stopImages", [])
    custom_field_data = custom_data.copy()
    spoc_data = {k: custom_field_data.pop(k) for k in SPOC_FIELDS if k in custom_field_data}

    if release == BookPending.BROADCAST:
        pending_state = BookPending.BROADCAST
    else:
        pending_state = BookPending.SUPPRESSED

    other_data = {
        "search_id": cur_search.id,
        "book_ref": booking.id,
        "trip_name": trip_name,
        "trip_type": BookingParams.get_b2b_trip_name(trip_type),
        "affiliate_id": affiliate_id,
        "client_name": client_name,
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": vehicle_no.strip().strip('"').strip("'"),
        "priority": priority,
        "pending_state": pending_state,
        'src_nickname_id': src_nickname_id,
        "spoc_data": spoc_data,
        'custom_data': custom_field_data,
        "custom": field_form,
        "trip_type_list": trip_type_list,
        "trip_start_images_structure": start_images_to_upload,
        "trip_stop_images_structure": stop_images_to_upload
    }

    if source == 'admin':
        other_data['admin_name'] = claims.get('name')
        other_data['admin_id'] = claims.get('admin_id')
    else:
        other_data['rep_fullname'] = claims.get('fullname')
        other_data['rep_username'] = claims.get('username')
        other_data['rep_mobile'] = claims.get('mobile')
        other_data['rep_id'] = rep_id

    if appoint_id:
        other_data["appointment_id"] = appoint_id.strip().strip('"').strip("'")

    if vehicle_model:
        other_data["vehicle_model"] = vehicle_model.strip().strip('"').strip("'")

    if dest_exists:
        other_data['dest_nickname_id'] = dest_nickname_id

    try:
        mongo_insert_result = AffiliateCollections.affiliates_book.insert_one(other_data)
        mongo_doc_id = mongo_insert_result.inserted_id
        # print("Mongo data added successfully", mongo_doc_id)
    except PyMongoError as mongo_ex:
        db.session.rollback()
        print("MongoDB Error:", mongo_ex)
        return None, jsonify({"success": 0, "error": "MongoDB error occurred"}), 500

    try:
        if dest_exists:
            dest = BookDest(booking.id, dest_lat, dest_long, dest_loc)
            db.session.add(dest)
        driver_total = cur_search.driver_base_fare + cur_search.driver_night_fare
        cust_total = cur_search.cust_base_fare + cur_search.cust_night_fare
        booking_ch = cust_total - driver_total
        cur_price = [cur_search.insurance, cur_search.cust_base_fare, cur_search.cust_night_fare, 0, booking_ch,
                    0, cust_total, 0, 0, cust_total, cur_search.insurance_ch, 0, 0, cur_search.driver_base_fare,
                    cur_search.driver_night_fare]
        temp_driver = db.session.query(Users, Drivers).filter(
                                        Drivers.user == Users.id).filter(
                                        Drivers.id == BookingParams.BOOKING_DUMMY_ID).first()
        driver_book_entry = DriverBook(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                    -1, -1, temp_driver[1].rating, cur_price, 0)

        create_pending_entry(driver_book_entry, booking, pending_state)
        create_pricing_entry(driver_book_entry, booking)

        db.session.commit()
    except PyMongoError as mongo_ex:
        db.session.rollback()
        return None, jsonify({"success": 0, "error": "MongoDB insert failed"}), 500
    except exc.SQLAlchemyError as excp:
        db.session.rollback()
        try:
            AffiliateCollections.affiliates_book.delete_one({"_id": mongo_doc_id})
        except PyMongoError as rollback_ex:
            print("MongoDB rollback failed:", rollback_ex)
        return None, jsonify({"success": 0, "error": "MySQL transaction error"}), 500
    send_live_update_of_booking(booking.id, booking.region)
    send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affiliate_id, booking_region=booking.region,tz=tz)
    return booking.code, None, 200

@aff_book.route('/api/affiliate/search', methods=['POST'])
@jwt_required()
def affiliate_search():
    try:
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    claims = get_jwt()
    rep_aff_id = int(claims.get('aff_id'))
    rep_id = int(claims.get('id'))

    if not affiliate_account_enabled(rep_aff_id):
        return jsonify({"success": -1, "message": "Affiliate account is not enabled"}), 403

    allowed_affiliates = claims.get("aff_slaves", [])

    if not complete(request.form, ['aff_id', 'trans_type', 'trip_type', 'city', 'reflat', 'reflong', 'dur', 'time']):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

    affiliate_id = int(request.form['aff_id'])

    if rep_aff_id != affiliate_id and affiliate_id not in allowed_affiliates:
        return jsonify({'success': -3, 'message': "Unauthorized access"}), 403

    MappedAffiliate = aliased(Affiliate)
    result = db.session.query(Affiliate, MappedAffiliate) \
                        .outerjoin(MappedAffiliate, Affiliate.mapped_wallet_affiliate == MappedAffiliate.id) \
                        .filter(Affiliate.id == affiliate_id).first()

    affiliate, mapped_affiliate_wallet = result
    
    source = claims['client_name']
    trip_type = int(request.form['trip_type'])
    is_return_trip = request.form.get('is_return', '0') == '1'

    dur = request.form['dur'] if trip_type != BookingParams.TYPE_ONEWAY else None
    city = int(request.form['city'])

    trans_type_list = request.form['trans_type'].split(',')

    reflat = float(request.form['reflat'])
    reflong = float(request.form['reflong'])
    time = convert_datetime_to_utc(request.form['time'], tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
    # try:
    #     time = datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
    # except ValueError:
    #     try:
    #         time = datetime.strptime(request.form['time'], "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
    #     except ValueError:
    #         time = datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    try:
        dest_lat = float(request.form.get('dest_lat', 0.0))
        dest_long = float(request.form.get('dest_long', 0.0))
        dest_exists = 'dest_lat' in request.form and 'dest_long' in request.form
    except ValueError:
        dest_lat, dest_long, dest_exists = 0.0, 0.0, False

    if dest_exists:
        dist = get_dist_on_map(reflat, reflong, dest_lat, dest_long)
    else:
        dist = 0

    results = []
    entries_list = []
    total_fare = 0
    for trans_type in trans_type_list:
        estimate_result, search_entry = get_affiliate_estimate(affiliate_id, rep_id, reflat, reflong, dest_lat, dest_long, trip_type, trans_type,
                        time, dur, dist, city, f"{source}-0")
        estimate_data = estimate_result.get_json()
        results.append(estimate_data)

        entries_list.append(search_entry)
        total_fare += estimate_data.get('cust_total_fare')

    if is_return_trip:
        return_time_str = request.form.get('return_time', '').strip()

        if return_time_str:
            return_time = convert_datetime_to_utc(return_time_str, tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
            # try:
            #     return_time = datetime.strptime(return_time_str, "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
            # except ValueError:
            #     try:
            #         return_time = datetime.strptime(return_time_str, "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
            #     except ValueError:
            #         return_time = datetime.strptime(return_time_str, "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)
        else:
            return_time = None

        if return_time is None:
            return jsonify({'success': -1, 'message': 'Return time is empty'}), 400

        return_dur = request.form['return_dur'] if trip_type != BookingParams.TYPE_ONEWAY else None

        return_reflat = dest_lat
        return_reflong = dest_long
        return_destlat = reflat
        return_destlong = reflong

        for trans_type in trans_type_list:
            estimate_result, search_entry = get_affiliate_estimate(affiliate_id, rep_id, return_reflat, return_reflong,
                        return_destlat, return_destlong, trip_type, trans_type,
                        return_time, return_dur, dist, city, f"{source}-1")
            estimate_data = estimate_result.get_json()
            results.append(estimate_data)

            entries_list.append(search_entry)
            total_fare += estimate_data.get('cust_total_fare')

    rem_limit = mapped_affiliate_wallet.wallet - total_fare

    try:
        db.session.bulk_save_objects(entries_list)
        db.session.commit()
    except exc.IntegrityError as e:
        db.session.rollback()
        print(f"IntegrityError: {e}")
        return jsonify({'success': -1, 'message': 'Integrity constraint violated'}), 409
    except Exception as e:
        db.session.rollback()
        print(f"Unexpected DB Error: {e}")
        return jsonify({'success': -1, 'message': 'Database error occurred'}), 500
    
    return jsonify({'success': 1, 'estimates': results, 'rem_limit': rem_limit})


@aff_book.route('/api/affiliate/book', methods=['POST'])
@swag_from('/app/swagger_docs/affiliate_booking/booking.yml')
@jwt_required()
def affiliate_book():
    claims = get_jwt()
    rep_aff_id = int(claims.get('aff_id'))
    rep_id = int(claims.get('id'))
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    if not rep_aff_id or not rep_id:
         return jsonify({'success': -1, 'message': "Invalid JWT claims"}), 403
    
    if not affiliate_account_enabled(rep_aff_id):
        return jsonify({"success": -1, "message": "Affiliate account is not enabled"}), 403

    allowed_affiliates = claims.get("aff_slaves", [])

    if not complete(request.form, ['aff_id', 'search_ids', 'loc', 'remark', 'trip_name', 'trip_type', 'vehicle_no', 'priority', 'release']):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

    if not complete(request.form, ['source_spoc_name', 'source_spoc_contact']):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

    affiliate_id = int(request.form.get('aff_id', rep_aff_id))

    if rep_aff_id != affiliate_id and affiliate_id not in allowed_affiliates:
        return jsonify({'success': -3, 'message': "Unauthorized access"}), 403
    
    MappedAffiliate = aliased(Affiliate)
    result = db.session.query(Affiliate, MappedAffiliate) \
                        .outerjoin(MappedAffiliate, Affiliate.mapped_wallet_affiliate == MappedAffiliate.id) \
                        .filter(Affiliate.id == affiliate_id).first()

    affiliate, mapped_affiliate_wallet = result

    mapped_wallet_affiliate_id = mapped_affiliate_wallet.id

    search_ids = request.form['search_ids']
    search_ids = search_ids.split(",") if search_ids else []
    vehicle_no_list = request.form['vehicle_no']
    vehicle_no_list = vehicle_no_list.split(",") if vehicle_no_list else []

    # optional
    appoint_ids = request.form.get('appoint_ids', "")
    appoint_ids = appoint_ids.split(',') if appoint_ids else []
    vehicle_model_list = request.form.get('vehicle_models', "")
    vehicle_model_list = vehicle_model_list.split(',') if vehicle_model_list else []
    priority_list = request.form['priority']
    priority_list = priority_list.split(',') if priority_list else []

    src_nickname_id = request.form.get('src_nickname_id', '0')
    dest_nickname_id = request.form.get('dest_nickname_id', '0')
    no_of_booking = len(search_ids)
    if no_of_booking == 0:
        return jsonify({'success': -1, 'message': 'No search id'})
    try:
        loc = request.form['loc']
    except Exception:
        loc = "N/A"
    remark = request.form['remark']
    trip_name = request.form['trip_name']
    trip_type = int(request.form['trip_type'])
    release = int(request.form.get('release', '1'))
    form_data = request.form.to_dict()
    fixed_data = {k: form_data.pop(k, None) for k in KNOWN_FIELDS}
    custom_data = form_data
    try:
        dest_lat = request.form['dest_lat']
        dest_long = request.form['dest_long']
        dest_loc = request.form['dest_loc']
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False

    search_results = []
    total_price = 0

    for search_id in search_ids:
        cur_search = AffiliateDriverSearch.query.filter_by(id=search_id).first()
        if not cur_search:
            return jsonify({'success': 0, 'message': f'Search ID {search_id} not found'}), 400

        source = int(cur_search.source.split("-")[1])
        search_results.append({'search': cur_search, 'source': source})

        total_price += cur_search.estimate

    limit = mapped_affiliate_wallet.wallet + mapped_affiliate_wallet.wallet_threshold
    if total_price > limit:
        return jsonify({'success': -2, 'message': 'Not enough credits'})

    rem_limit = mapped_affiliate_wallet.wallet - total_price

    normal_trip_count = sum(1 for trip in search_results if trip['source'] != BookingParams.RETURN_TRIP)
    for i in range(0, no_of_booking):
        source = search_results[i]['source']
        cur_search = search_results[i]['search']
        appoint_id = ""
        vehicle_model = ""
        index = i
        trip_category = trip_name
        temp_src_loc = loc
        temp_dest_loc = dest_loc
        temp_src_nickname_id = src_nickname_id
        temp_dest_nickname_id = dest_nickname_id
        if source == BookingParams.RETURN_TRIP:
            return_trip_name = request.form.get("return_trip_name", "")
            trip_category = return_trip_name
            index = i if i < normal_trip_count else (i - normal_trip_count)
            dest_lat = cur_search.destlat
            dest_long = cur_search.destlong
            temp_src_loc = dest_loc
            temp_dest_loc = loc
            temp_src_nickname_id = dest_nickname_id
            temp_dest_nickname_id = src_nickname_id
        if len(appoint_ids) != 0:
            appoint_id = appoint_ids[index]
        if len(vehicle_model_list) != 0:
            vehicle_model = vehicle_model_list[index]
        vehicle_no = vehicle_no_list[index]
        priority = int(priority_list[index])
        booking_code, response, status = create_book(cur_search, affiliate_id, mapped_wallet_affiliate_id, temp_src_loc, dest_lat, dest_long, temp_dest_loc, dest_exists, remark, custom_data,
                trip_type, trip_category, affiliate.client_name, claims, vehicle_no, temp_src_nickname_id,
                temp_dest_nickname_id, vehicle_model=vehicle_model, appoint_id=appoint_id, release=release, priority=priority, tz=tz)
        if status != 200:
            print(f"Error in {search_ids[i]}: {response.get('error')}, {response.get('success')}")
            continue
    return jsonify({'success': 1, 'msg': "Booking created successfully", "rem_limit": rem_limit})

@aff_book.route('/api/affiliate/spoc_address_list', methods=['POST'])
@jwt_required()
def get_all_spoc_addresses():
    try:
        rep_id = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['aff_id', 'region']):
        return jsonify({'success': -2, 'message': "Incomplete form"}), 400

    region = int(request.form['region'])
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    if not rep:
        return jsonify({'success': 0, 'message': "Affiliate representative not found"}), 404

    rep_aff_id = rep.affiliate_id
    aff_id = int(request.form['aff_id'])

    try:
        # Fetch local addresses only if rep_aff_id matches aff_id
        local_addresses = []
        local_spocs = []

        local_addresses = db.session.query(AffiliateAddress).filter(
            AffiliateAddress.rep_id == rep_id,
            AffiliateAddress.address_type == AffiliateAddress.LOCAL_ADDRESS,
            AffiliateAddress.region == region
        ).all()

        local_spocs = db.session.query(AffiliateSpoc).filter(
                AffiliateSpoc.aff_id == rep_aff_id,
                AffiliateSpoc.rep_id == rep_id,
                AffiliateSpoc.region == region,
                AffiliateSpoc.spoc_type == AffiliateSpoc.LOCAL_SPOC
            ).all()

        local_nickname = {address.nickname for address in local_addresses} if local_addresses else set()

        # Fetch global addresses excluding local nicknames
        global_addresses = db.session.query(AffiliateAddress).filter(
                                or_(
                                    AffiliateAddress.aff_id == aff_id,
                                    AffiliateAddress.rep_id == rep_id
                                ),
                                AffiliateAddress.address_type == AffiliateAddress.GLOBAL_ADDRESS,
                                AffiliateAddress.region == region,
                                ~AffiliateAddress.nickname.in_(local_nickname)
                            ).all()

        # Combine local and global addresses
        all_addresses = local_addresses + global_addresses

        # Fetch all SPOCs in one query using JOIN
        spoc_query = (
            db.session.query(
                AddressSpoc.address_id,
                AddressSpoc.spoc_id,
                AddressSpoc.spoc_name,
                AffiliateSpoc.spoc_mobile
            )
            .join(AffiliateSpoc, AddressSpoc.spoc_id == AffiliateSpoc.spoc_id)
            .filter(AddressSpoc.address_id.in_([addr.add_id for addr in all_addresses]))
            .all()
        )

        # Organize SPOC data in a dictionary for faster lookup
        spoc_dict = {}
        for address_id, spoc_id, spoc_name, spoc_mobile in spoc_query:
            if address_id not in spoc_dict:
                spoc_dict[address_id] = []
            spoc_dict[address_id].append({
                'spoc_id': spoc_id,
                'spoc_name': spoc_name,
                'spoc_mobile': spoc_mobile
            })

        # Construct the final response
        address_list = [{
            'add_id': address.add_id,
            'address': address.address,
            'nickname': address.nickname,
            'lat': address.latitude,
            'long': address.longitude,
            'add_type': address.address_type,
            'spocs': spoc_dict.get(address.add_id, [])  # Fetch SPOCs if available
        } for address in all_addresses]

        local_mobile_numbers = {spoc.spoc_mobile for spoc in local_spocs} if local_spocs else set()

        # Query global SPOCs not already in the local mobile numbers
        global_spocs = db.session.query(AffiliateSpoc).filter(
                                        AffiliateSpoc.spoc_type == AffiliateSpoc.GLOBAL_SPOC,
                                        or_(
                                            AffiliateSpoc.aff_id == aff_id,
                                            AffiliateSpoc.rep_id == rep_id
                                        ),
                                        AffiliateSpoc.region == region,
                                        ~AffiliateSpoc.spoc_mobile.in_(local_mobile_numbers)
                                    ).all()

        all_spocs = local_spocs + global_spocs

        spoc_list = [
            {
                'spoc_id': spoc.spoc_id,
                'spoc_name': spoc.spoc_name,
                'spoc_mobile': spoc.spoc_mobile,
            }
            for spoc in all_spocs
        ]

        return jsonify({'success': 1, 'addresses': address_list, 'spocs': spoc_list}), 200

    except Exception as e:
        return jsonify({'success': -1, 'error': str(e)}), 500


def get_max_search_time_book(driver_user, limited, search_by, search_query, actual_startdatetime, to_date, to_time, firstRequestOrNot, regions, isglobal, search_query_filters, or_conditions=None,aff_id_list = None, book_refs=None):
    where_conditions = [
        Bookings.type==BookingParams.TYPE_B2B,
        AffBookingLogs.aff_id.in_(aff_id_list),
        Trip.starttime.is_(None),
    ]

    if not firstRequestOrNot:
        where_conditions.append((
                    (Bookings.startdate < to_date) |
                    ((Bookings.startdate == to_date) & (Bookings.starttime < to_time))
                )
            )
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))
    if or_conditions:
        where_conditions.append(or_(*or_conditions))
    if isglobal == 0:
        where_conditions.append((Bookings.startdate > cast(actual_startdatetime, Date)) | ((Bookings.startdate == cast(actual_startdatetime, Date)) & (Bookings.starttime >= cast(actual_startdatetime, Time))))
    if search_query_filters:
        where_conditions.append(and_(*search_query_filters))

    if search_by == 3:
        latest_booking_ts = (
            select(func.timestamp(Bookings.startdate, Bookings.starttime))\
            .outerjoin(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .where(*where_conditions)
            .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [1,5]:
        latest_booking_ts = (
            select(func.timestamp(Bookings.startdate, Bookings.starttime))\
            .join(Users, Bookings.user == Users.id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .outerjoin(Trip, Bookings.id == Trip.book_id)
            .where(*where_conditions)
            .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [2,4]:
        latest_booking_ts = (
            select(func.timestamp(Bookings.startdate, Bookings.starttime))
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .join(driver_user, Drivers.user == driver_user.id)
            .outerjoin(Trip, Bookings.id == Trip.book_id)
            .where(*where_conditions)
            .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [6] and book_refs:
        latest_booking_ts = (
            select(func.timestamp(Bookings.startdate, Bookings.starttime))
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .join(driver_user, Drivers.user == driver_user.id)
            .outerjoin(Trip, Bookings.id == Trip.book_id)
            .where(Bookings.id.in_(book_refs))
            .where(*where_conditions)
            .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    else:
        return None

    query = select(func.coalesce(latest_booking_ts, None))
    # print('max search book',
    #     query.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ), flush=True
    # )
    return db.session.execute(query).scalar()

def get_max_search_time_trip(driver_user, limited, search_by, search_query, actual_startdatetime, to_date, to_time, firstRequestOrNot, regions, isglobal, search_query_filters, or_conditions=None,aff_id_list = None, book_refs=None):
    where_conditions = [
        Bookings.type==BookingParams.TYPE_B2B,
        AffBookingLogs.aff_id.in_(aff_id_list),
        not_(Trip.starttime.is_(None))
    ]
    if not firstRequestOrNot:
        where_conditions.append(
                Trip.starttime < func.timestamp(to_date, to_time)
            )
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))

    if or_conditions:
        where_conditions.append(or_(*or_conditions))

    if search_query_filters:
        where_conditions.append(and_(*search_query_filters))

    if isglobal == 0:
        where_conditions.append(Trip.starttime >= actual_startdatetime)

    if search_by == 3:
        latest_trip_start = (
            select(Trip.starttime)
            .join(Bookings, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .where(*where_conditions)
            .order_by(Trip.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [1,5]:
        latest_trip_start = (
            select(Trip.starttime)
            .join(Bookings, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .join(Users, Bookings.user == Users.id)
            .where(*where_conditions)
            .order_by(Trip.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [2,4]:
        latest_trip_start = (
            select(Trip.starttime)
            .join(Bookings, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(driver_user, Drivers.user == driver_user.id)
            .where(*where_conditions)
            .order_by(Trip.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    elif search_by in [6] and book_refs:
        latest_trip_start = (
            select(Trip.starttime)
            .join(Bookings, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(driver_user, Drivers.user == driver_user.id)
            .where(Bookings.id.in_(book_refs))
            .where(*where_conditions)
            .order_by(Trip.starttime.desc())
            .limit(1)
            .scalar_subquery()
        )
    else:
        return None
    query = select(func.coalesce(latest_trip_start, None))

    # print('max trip search',
    #         query.compile(
    #             db.engine,
    #             dialect=mysql.dialect(),
    #             compile_kwargs={"literal_binds": True}
    #         ), flush=True
    #     )
    return db.session.execute(query).scalar()

def get_max_time_bookings(to_date, to_time, actual_startdatetime, firstRequestOrNot, regions, isglobal, or_conditions=None,aff_id_list = None):
    where_conditions = [
        Bookings.type==BookingParams.TYPE_B2B,
        AffBookingLogs.aff_id.in_(aff_id_list),
        Trip.starttime.is_(None),
        (
            (Bookings.startdate < to_date) |
            ((Bookings.startdate == to_date) & (Bookings.starttime <= to_time))
        ) if firstRequestOrNot else (
            (Bookings.startdate < to_date) |
            ((Bookings.startdate == to_date) & (Bookings.starttime < to_time))
        )
    ]
    if isglobal == 0:
        where_conditions.append((Bookings.startdate > cast(actual_startdatetime, Date)) | ((Bookings.startdate == cast(actual_startdatetime, Date)) & (Bookings.starttime >= cast(actual_startdatetime, Time))))
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))

    if or_conditions:
        where_conditions.append(or_(*or_conditions))

    latest_booking_ts = (
        select(func.timestamp(Bookings.startdate, Bookings.starttime))
        .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
        .outerjoin(Trip, Bookings.id == Trip.book_id)
        .where(*where_conditions)
        .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
        .limit(1)
        .scalar_subquery()
    )

    query = select(func.coalesce(latest_booking_ts, None))
    # print('max book query',
    #     query.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ), flush=True
    # )
    return db.session.execute(query).scalar()

def get_max_time_trips(to_date, to_time, actual_startdatetime, firstRequestOrNot, regions, isglobal, or_conditions = None,aff_id_list = None):
    where_conditions = [
            Bookings.type==BookingParams.TYPE_B2B,
            AffBookingLogs.aff_id.in_(aff_id_list),
            not_(Trip.starttime.is_(None)),
            Trip.starttime <= func.timestamp(to_date, to_time)
            if firstRequestOrNot else
            Trip.starttime < func.timestamp(to_date, to_time)
        ]
    if isglobal == 0:
        where_conditions.append(Trip.starttime >= actual_startdatetime)
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))
    if or_conditions:
        where_conditions.append(or_(*or_conditions))
    latest_trip_start = (
        select(Trip.starttime)
        .join(Bookings, Bookings.id == Trip.book_id)
        .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
        .where(*where_conditions)
        .order_by(Trip.starttime.desc())
        .limit(1)
        .scalar_subquery()
    )
    query = select(func.coalesce(latest_trip_start, None))
    # print('max trip time ',
    #     query.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ), flush=True
    # )

    return db.session.execute(query).scalar()

def get_min_time_bookings(to_date, to_time, actual_enddatetime, limited, regions, isglobal, or_conditions = None,aff_id_list = None):
    where_conditions = [
            Bookings.type==BookingParams.TYPE_B2B,
            AffBookingLogs.aff_id.in_(aff_id_list),
            Trip.starttime.is_(None),
            or_(
                Bookings.startdate > to_date,
                and_(
                    Bookings.startdate == to_date,
                    Bookings.starttime > to_time
                )
            )
        ]
    if isglobal == 0 and limited:
        where_conditions.append(or_(
                    Bookings.startdate < cast(actual_enddatetime, Date),
                    and_(
                        Bookings.startdate == cast(actual_enddatetime, Date),
                        Bookings.starttime < cast(actual_enddatetime, Time)
                    )
                )
            )
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))

    if or_conditions:
        where_conditions.append(or_(*or_conditions))
    latest_booking_ts = (
        select(func.timestamp(Bookings.startdate, Bookings.starttime))
        .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
        .outerjoin(Trip, Bookings.id == Trip.book_id)
        .where(*where_conditions)
        .order_by(Bookings.startdate.asc(), Bookings.starttime.asc())
        .limit(1)
        .scalar_subquery()
    )
    query = select(func.coalesce(latest_booking_ts, None))
    # print('min book time ',
    #     query.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ), flush=True
    # )
    return db.session.execute(query).scalar()

def get_min_time_trips(to_date, to_time, actual_enddatetime, limited, regions, isglobal, or_conditions = None,aff_id_list = None):
    target_timestamp = func.timestamp(to_date, to_time)
    where_conditions = [
            Bookings.type==BookingParams.TYPE_B2B,
            AffBookingLogs.aff_id.in_(aff_id_list),
            not_(Trip.starttime.is_(None)),
            Trip.starttime > target_timestamp
        ]
    if isglobal == 0 and limited:
        where_conditions.append(Trip.starttime <= actual_enddatetime)
    if regions and isglobal < 2:
        where_conditions.append(Bookings.region.in_(regions))

    if or_conditions:
        where_conditions.append(or_(*or_conditions))
    latest_trip_start = (
        select(Trip.starttime)
        .join(Bookings, Bookings.id == Trip.book_id)
        .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
        .where(*where_conditions)
        .order_by(Trip.starttime.asc())
        .limit(1)
        .scalar_subquery()
    )
    query = select(func.coalesce(latest_trip_start, None))
    # print('min trip time ',
    #     query.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ), flush=True
    # )
    return db.session.execute(query).scalar()

def search_total_counts_bookings(request):
    try:
        book_refs = None
        aff_id_list = request.form.get('aff_ids', None)
        if aff_id_list:
            aff_id_list = aff_id_list.split(',')
        driver_user = aliased(Users, name="driver_user")
        # --- Input and time conversion ---
        from_date_str = request.form.get('from_date')
        to_date_str = request.form.get('to_date')
        if not aff_id_list or not complete(request.form, ['from_date', 'to_date']):
            return jsonify({'success': -1, 'message': 'Incomplete Form Details'}), 400

        from_time_str = request.form.get('from_time', "00:00:00")
        to_time_str = request.form.get('to_time', "23:59:59")
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata') 
        from_date_str, from_time_str = convert_to_utc(from_date_str, from_time_str,tz)
        to_date_str, to_time_str = convert_to_utc(to_date_str,to_time_str,tz)
        from_datetime = datetime.strptime(f"{from_date_str} {from_time_str}", "%Y-%m-%d %H:%M:%S")
        to_datetime = datetime.strptime(f"{to_date_str} {to_time_str}", "%Y-%m-%d %H:%M:%S")

        # --- Other form parameters ---
        search_region = request.form.get('search_region')
        search_query = request.form.get('search_text', '')
        search_by=request.form.get('search_by','3')
        filter_by=request.form.get('filter')
        # all_or_filter = request.form.get('all_or_filter', 1, type = int)
        is_b2c = request.form.get('is_b2c',1,type=int)
        userid = request.form.get('userid', 0, type=int)
        limited = request.form.get('limited', 1, type=int) # default 1 means future time bound applied
        isglobal = request.form.get('isglobal', 0, type=int)

        # --- Build base and additional filters ---
        base_filter = [Bookings.type == BookingParams.TYPE_B2B, AffBookingLogs.aff_id.in_(aff_id_list)]  # 50 means only B2C bookings
        trip_filter = []
        booking_filter = []
        filter_values = []
        if userid:
            base_filter.append(Bookings.user == userid)

        if isglobal == 0 and limited == 1:  # limited means time bound applied - no infinite future for fetching data
            booking_filter = [
                Bookings.startdate >= from_date_str,
                Bookings.startdate <= to_date_str,
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            ]
            trip_filter = [Trip.starttime >= from_datetime, Trip.starttime <= to_datetime]
        elif isglobal == 0: # 0 means region + time bound restrictions
            booking_filter = [
                Bookings.startdate >= from_date_str,
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                )
            ]
            trip_filter = [Trip.starttime >= from_datetime]
        elif isglobal == 1: # 1 means all history of data no time bound
            booking_filter = []
            trip_filter = []

        if isglobal != 2 and search_region and search_region != '-1': # -1 means all regions global 2 means no region restrictions
            regions = [int(val.strip()) for val in search_region.split(',') if val.strip().isdigit()]
            if regions:
                base_filter.append(Bookings.region.in_(regions))

        # if filter_by and filter_by!='-1':
        #     filter_values = [int(val) for val in filter_by.split(',')]

        search_by_int = int(search_by)
        if search_query:
            search_query_filters = []
            if search_by_int == 3:  # Search by Booking ID or Code
                try:
                    search_id = int(search_query)
                    search_query_filters.append(Bookings.id == search_id)
                except ValueError:
                    search_query_filters.append(Bookings.code == search_query)

            elif search_by_int in {1, 5}:  # Search by User
                try:
                    search_id = int(search_query)
                    search_query_filters.append(Users.mobile == search_query)
                except ValueError:
                    search_query_filters = process_search_query(search_query, Users)

            elif search_by_int in {2, 4}:  # Search by Driver
                try:
                    search_id = int(search_query)
                    search_query_filters.append(driver_user.mobile == search_query)
                except ValueError:
                    search_query_filters = process_search_query(search_query, driver_user)

            if search_query_filters:
                base_filter.append(and_(*search_query_filters))

        # if search_by_int in [6]:
        #     vehbooks = affiliates_book.find({"vehicle_no": search_query}, {"book_ref": 1, "_id": 0})
        #     book_refs = [doc["book_ref"] for doc in vehbooks]
        #     print(book_refs, 'book ref search_total_counts_bookings', flush=True)
        if search_by_int in [6]:
            vehbooks = AffiliateCollections.affiliates_book.find(
                {"vehicle_no": {"$regex": search_query, "$options": "i"}},  # "i" = case-insensitive
                {"book_ref": 1, "_id": 0}
            )
            book_refs = [doc["book_ref"] for doc in vehbooks]
            print(book_refs, 'book ref search_total_counts_bookings', flush=True)
        if search_by_int in [6] and not book_refs:
            return jsonify({'success': 1, 'data': [0] * 12}), 200
        if book_refs:
                base_filter.append(Bookings.id.in_(book_refs))

        join_map = {
            1: [(Users, Bookings.user == Users.id)],
            2: [(Drivers, Bookings.driver == Drivers.id), (driver_user, Drivers.user == driver_user.id)],
            4: [(Drivers, Bookings.driver == Drivers.id), (driver_user, Drivers.user == driver_user.id)],
            5: [(Users, Bookings.user == Users.id)],
        }
        # --- Build subqueries ---
        # 1. Total unallocated bookings (no join needed)

        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.UNALLOCATED,
            Bookings.driver == Bookings.DEFAULT_DRIVER
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_unallocated_sq = query.scalar_subquery() if (not filter_values or 1 in filter_values) else 0

        # 2. Total allocated bookings (using NOT EXISTS for Trip)
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            not_(exists().where(Trip.book_id == Bookings.id))
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_allocated_sq = query.scalar_subquery() if (not filter_values or 2 in filter_values) else 0

        # For queries that filter on Trip fields we perform an explicit join
        # and count distinct bookings to avoid duplicates.

        # 3. Total on_my_way bookings
        query = db.session.query(func.count(func.distinct(Bookings.id))).join(Trip, Bookings.id == Trip.book_id).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            Trip.status == Trip.TRIP_INIT,
            Trip.starttime.is_(None)
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_on_my_way_sq = query.scalar_subquery() if (not filter_values or 3 in filter_values) else 0

        # 4. Total checked-in bookings
        query = db.session.query(func.count(func.distinct(Bookings.id))).join(Trip, Bookings.id == Trip.book_id).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            or_(Trip.status == Trip.TRIP_REACHED_SRC, Trip.status == Trip.TRIP_START_PIC),
            Trip.starttime.is_(None)
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_checked_in_sq = query.scalar_subquery() if (not filter_values or 4 in filter_values) else 0

        # 5. Total ongoing bookings
        query = db.session.query(func.count(func.distinct(Bookings.id))).join(Trip, Bookings.id == Trip.book_id).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter,
            or_(and_(*trip_filter), and_(*booking_filter, not_(Trip.starttime.is_(None)))),
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            Trip.status == Trip.TRIP_STARTED
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_ongoing_sq = query.scalar_subquery() if (not filter_values or 5 in filter_values) else 0

        # 6. Total reached destination bookings
        query = db.session.query(func.count(func.distinct(Bookings.id))).join(Trip, Bookings.id == Trip.book_id).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter,
            or_(and_(*trip_filter), and_(*booking_filter, not_(Trip.starttime.is_(None)))),
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            or_(Trip.status == Trip.TRIP_REACHED_DEST, Trip.status == Trip.TRIP_STOP_PIC)
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_reached_dest_sq = query.scalar_subquery() if (not filter_values or 6 in filter_values) else 0

        # 7. Total completed bookings
        query = db.session.query(func.count(func.distinct(Bookings.id))).join(Trip, Bookings.id == Trip.book_id).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter,
            or_(and_(*trip_filter), and_(*booking_filter, not_(Trip.starttime.is_(None)))),
            Bookings.valid == Bookings.ALLOCATED,
            Bookings.driver != Bookings.DEFAULT_DRIVER,
            Trip.status == Trip.TRIP_STOPPED
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_completed_sq = query.scalar_subquery() if (not filter_values or 7 in filter_values) else 0

        # 8. Total customer cancellations without allocation
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.CANCELLED_USER,
            Bookings.driver == Bookings.DEFAULT_DRIVER
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_customer_cancel_without_alloc_sq = query.scalar_subquery() if (not filter_values or 11 in filter_values) else 0

        # 9. Total customer cancellations after allocation
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.CANCELLED_USER,
            Bookings.driver != Bookings.DEFAULT_DRIVER
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_customer_cancel_after_alloc_sq = query.scalar_subquery() if (not filter_values or 12 in filter_values) else 0

        # 10. Total admin cancellations
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            or_(Bookings.valid == Bookings.CANCELLED_D4M, Bookings.valid == Bookings.CANCELLED_DRIVER)
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_admin_cancel_sq = query.scalar_subquery() if (not filter_values or 10 in filter_values) else 0

        # 11. Total customer cancellations (all)
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.CANCELLED_USER
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_customer_cancel_sq = query.scalar_subquery() if (not filter_values or 8 in filter_values) else 0

        # 12. Total driver cancellations
        query = db.session.query(func.count(Bookings.id)).join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id).filter(
            *base_filter, *booking_filter,
            Bookings.valid == Bookings.CANCELLED_DRIVER
        )

        for join_model, join_condition in join_map.get(search_by_int, []):
            query = query.join(join_model, join_condition)

        total_driver_cancel_sq = query.scalar_subquery() if (not filter_values or 9 in filter_values) else 0

        # --- Single combined query to get all counts ---
        result = db.session.query(
            total_unallocated_sq,
            total_allocated_sq,
            total_on_my_way_sq,
            total_checked_in_sq,
            total_ongoing_sq,
            total_reached_dest_sq,
            total_completed_sq,
            total_customer_cancel_without_alloc_sq,
            total_customer_cancel_after_alloc_sq,
            total_admin_cancel_sq,
            total_customer_cancel_sq,
            total_driver_cancel_sq
        )
        # print('count search query',
        #     result.statement.compile(
        #         db.engine,
        #         dialect=mysql.dialect(),
        #         compile_kwargs={"literal_binds": True}
        #     ), flush=True
        # )
        result = result.one()

        # Ensure no None values (return 0 instead)
        result_list = [val or 0 for val in result]

        return jsonify({'success': 1, 'data': result_list}), 200

    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e), 'data': []}), 500

@aff_book.route('/api/affiliate/total_count', methods=['POST'])
@jwt_required()
def affiliate_total_counts_bookings():
    try:
        # --- Input and time conversion ---
        aff_id_list = request.form.get('aff_ids', None)
        if aff_id_list:
            aff_id_list = aff_id_list.split(',')
        from_date_str = request.form.get('from_date')
        to_date_str = request.form.get('to_date')
        if not complete(request.form, ['from_date', 'to_date']) or not aff_id_list:
            return jsonify({'success': -1, 'message': 'Incomplete Form Details'}), 400
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata') 
        from_time_str = request.form.get('from_time', "00:00:00")
        to_time_str = request.form.get('to_time', "23:59:59")
        from_date_str, from_time_str = convert_to_utc(from_date_str, from_time_str,tz)
        to_date_str, to_time_str = convert_to_utc(to_date_str, to_time_str,tz)
        from_datetime = datetime.strptime(f"{from_date_str} {from_time_str}", "%Y-%m-%d %H:%M:%S")
        to_datetime = datetime.strptime(f"{to_date_str} {to_time_str}", "%Y-%m-%d %H:%M:%S")

        # --- Other form parameters ---
        search_region = request.form.get('search_region')
        search_query = request.form.get('search_text', '')
        search_by=request.form.get('search_by','3')
        filter_by=request.form.get('filter')
        all_or_filter = request.form.get('all_or_filter', 1, type = int)

        userid = request.form.get('userid', 0, type=int)
        limited = request.form.get('limited', 1, type=int) # default 1 means future time bound applied
        isglobal = request.form.get('isglobal', 0, type=int)


        # all_or_filter = request.form.get('all_or_filter', 1, type = int)
        is_b2c = 0
        # is_b2c = request.form.get('is_b2c',1,type=int)

        if search_query:
            return search_total_counts_bookings(request)

        # --- Build base and additional filters ---
        base_filter = [Bookings.type == BookingParams.TYPE_B2B, AffBookingLogs.aff_id.in_(aff_id_list)]
        trip_filter = []
        booking_filter = []
        filter_values = []
        if userid:
            base_filter.append(Bookings.user == userid)

        if isglobal == 0 and limited == 1:  # limited means time bound applied - no infinite future for fetching data
            booking_filter = [
                Bookings.startdate >= from_date_str,
                Bookings.startdate <= to_date_str,
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            ]
            trip_filter = [Trip.starttime >= from_datetime, Trip.starttime <= to_datetime]
        elif isglobal == 0: # 0 means region + time bound restrictions
            booking_filter = [
                Bookings.startdate >= from_date_str,
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                )
            ]
            trip_filter = [Trip.starttime >= from_datetime]
        elif isglobal == 1: # 1 means all history of data no time bound
            booking_filter = []
            trip_filter = []

        if isglobal != 2 and search_region and search_region != '-1': # -1 means all regions global 2 means no region restrictions
            regions = [int(val.strip()) for val in search_region.split(',') if val.strip().isdigit()]
            if regions:
                base_filter.append(Bookings.region.in_(regions))

        # if filter_by and filter_by!='-1':
        #     filter_values = [int(val) for val in filter_by.split(',')]

        # --- Build subqueries ---
        # 1. Total unallocated bookings (no join needed)
        total_unallocated_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.UNALLOCATED
            )
            .scalar_subquery()
        ) if (not filter_values or 1 in filter_values) else 0

        # 2. Total allocated bookings (using NOT EXISTS for Trip)
        total_allocated_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                not_(exists().where(Trip.book_id == Bookings.id))
            )
            .scalar_subquery()
        ) if (not filter_values or 2 in filter_values) else 0

        # 3. Total on_my_way bookings
        total_on_my_way_sq = (
            db.session.query(func.count(func.distinct(Bookings.id)))
            .join(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                Trip.status == Trip.TRIP_INIT,
                Trip.starttime.is_(None)
            )
            .scalar_subquery()
        ) if (not filter_values or 3 in filter_values) else 0

        # 4. Total checked-in bookings
        total_checked_in_sq = (
            db.session.query(func.count(func.distinct(Bookings.id)))
            .join(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                or_(Trip.status == Trip.TRIP_REACHED_SRC, Trip.status == Trip.TRIP_START_PIC),
                Trip.starttime.is_(None)
            )
            .scalar_subquery()
        ) if (not filter_values or 4 in filter_values) else 0

        # 5. Total ongoing bookings
        total_ongoing_sq = (
            db.session.query(func.count(func.distinct(Bookings.id)))
            .join(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter,
                or_(and_(*trip_filter), and_(*booking_filter, Trip.starttime.is_(None))),
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                Trip.status == Trip.TRIP_STARTED
            )
            .scalar_subquery()
        ) if (not filter_values or 5 in filter_values) else 0

        # 6. Total reached destination bookings
        total_reached_dest_sq = (
            db.session.query(func.count(func.distinct(Bookings.id)))
            .join(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter,
                or_(and_(*trip_filter), and_(*booking_filter, Trip.starttime.is_(None))),
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                or_(Trip.status == Trip.TRIP_REACHED_DEST, Trip.status == Trip.TRIP_STOP_PIC)
            )
            .scalar_subquery()
        ) if (not filter_values or 6 in filter_values) else 0

        # 7. Total completed bookings
        total_completed_sq = (
            db.session.query(func.count(func.distinct(Bookings.id)))
            .join(Trip, Bookings.id == Trip.book_id)
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter,
                or_(and_(*trip_filter), and_(*booking_filter, Trip.starttime.is_(None))),
                Bookings.valid == Bookings.ALLOCATED,
                Bookings.driver != Bookings.DEFAULT_DRIVER,
                Trip.status == Trip.TRIP_STOPPED
            )
            .scalar_subquery()
        ) if (not filter_values or 7 in filter_values) else 0

        # 8. Total customer cancellations without allocation
        total_customer_cancel_without_alloc_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.CANCELLED_USER,
                Bookings.driver == Bookings.DEFAULT_DRIVER
            )
            .scalar_subquery()
        ) if (not filter_values or 11 in filter_values) else 0

        # 9. Total customer cancellations after allocation
        total_customer_cancel_after_alloc_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.CANCELLED_USER,
                Bookings.driver != Bookings.DEFAULT_DRIVER
            )
            .scalar_subquery()
        ) if (not filter_values or 12 in filter_values) else 0

        # 10. Total admin cancellations
        total_admin_cancel_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.CANCELLED_D4M
            )
            .scalar_subquery()
        ) if (not filter_values or 10 in filter_values) else 0

        # 11. Total customer cancellations (all)
        total_customer_cancel_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.CANCELLED_USER
            )
            .scalar_subquery()
        ) if (not filter_values or 8 in filter_values) else 0

        # 12. Total driver cancellations
        total_driver_cancel_sq = (
            db.session.query(func.count(Bookings.id))
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(
                *base_filter, *booking_filter,
                Bookings.valid == Bookings.CANCELLED_DRIVER
            )
            .scalar_subquery()
        ) if (not filter_values or 9 in filter_values) else 0


        # --- Single combined query to get all counts ---
        result = db.session.query(
            total_unallocated_sq,
            total_allocated_sq,
            total_on_my_way_sq,
            total_checked_in_sq,
            total_ongoing_sq,
            total_reached_dest_sq,
            total_completed_sq,
            total_customer_cancel_without_alloc_sq,
            total_customer_cancel_after_alloc_sq,
            total_admin_cancel_sq,
            total_customer_cancel_sq,
            total_driver_cancel_sq
        )
        # print('count query ',
        #     result.statement.compile(
        #         db.engine,
        #         dialect=mysql.dialect(),
        #         compile_kwargs={"literal_binds": True}
        #     ), flush=True
        # )
        result = result.one()

        # Ensure no None values (return 0 instead)
        result_list = [val or 0 for val in result]

        return jsonify({'success': 1, 'data': result_list}), 200

    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e), 'data': []}), 500


def search_result_query(driver_user, limited, search_by, search_query, actual_startdatetime, actual_enddatetime, isglobal, regions, tripsto_date, tripsto_time, to_date, to_time, or_conditions, firstRequestOrNot, search_query_filters, aff_id_list,tz='Asia/Kolkata'):
    try:
        bookings_available_to = None
        trips_available_to = None
        trip_timestamp = None
        book_refs = None
        start = time.time()

        batch_size = BookingParams.SEARCH_BOOKING_DATA_BATCHSIZE
        if search_by:
            search_by = int(search_by)

        # if search_by in [6]:
        #     vehbooks = affiliates_book.find({"vehicle_no": search_query}, {"book_ref": 1, "_id": 0})
        #     book_refs = [doc["book_ref"] for doc in vehbooks]
        #     print(book_refs, 'book ref search_result_query', flush=True)
        if search_by in [6]:
            vehbooks = AffiliateCollections.affiliates_book.find(
                {"vehicle_no": {"$regex": search_query, "$options": "i"}},  # "i" = case-insensitive
                {"book_ref": 1, "_id": 0}
            )
            book_refs = [doc["book_ref"] for doc in vehbooks]
            print(book_refs, 'book ref search_total_counts_bookings', flush=True)

        if to_date and to_time:
            bookings_available_to = get_max_search_time_book(driver_user, limited, search_by, search_query, actual_startdatetime, to_date, to_time, firstRequestOrNot, regions, isglobal, search_query_filters, or_conditions, aff_id_list, book_refs=book_refs)
        bookfindquerytime = time.time() - start
        start = time.time()
        # print('bookings_available_to', bookings_available_to, flush=True)

        if tripsto_date and tripsto_time:
            trips_available_to = get_max_search_time_trip(driver_user, limited, search_by, search_query, actual_startdatetime, tripsto_date, tripsto_time, firstRequestOrNot, regions, isglobal, search_query_filters, or_conditions, aff_id_list, book_refs=book_refs)
        tripfindquerytime = time.time() - start
        # print('trips_available_to', trips_available_to, flush=True)

        if not bookings_available_to and not trips_available_to:
            return jsonify({
                    'success': 1,
                    'bookings': [],
                    'acount': None,
                    'abookfindquerytime': bookfindquerytime,
                    'atripfindquerytime': tripfindquerytime,
                    'aamin_triptime': None,
                    'aamin_booktime': None,
                    'aamax_triptime': None,
                    'aamax_booktime': None,
                    'info': 'No booking found for this search',
                }), 200

        if bookings_available_to is not None:
            if bookings_available_to.tzinfo is None:
                bookings_available_to = bookings_available_to.replace(tzinfo=timezone.utc)

        if bookings_available_to and isglobal == 0 and  bookings_available_to > actual_enddatetime and limited:
            # print('made none bookings_available_to',actual_enddatetime , bookings_available_to, flush=True)
            bookings_available_to = None
        elif bookings_available_to:
            bookings_available_from = (bookings_available_to - timedelta(hours=batch_size)).replace(tzinfo=timezone.utc)
            if isglobal == 0 and  bookings_available_from < actual_startdatetime:
                bookings_available_from = actual_startdatetime
            to_date = bookings_available_to.date()
            to_time = bookings_available_to.time()
            from_date = bookings_available_from.date()
            from_time = bookings_available_from.time()

        if trips_available_to is not None:
            if trips_available_to.tzinfo is None:
                trips_available_to = trips_available_to.replace(tzinfo=timezone.utc)

        if trips_available_to and isglobal == 0 and trips_available_to > actual_enddatetime:
            trips_available_to = None
        elif trips_available_to:
            trips_available_from = (trips_available_to - timedelta(hours=batch_size)).replace(tzinfo=timezone.utc)
            if isglobal == 0 and  trips_available_from < actual_startdatetime:
                trips_available_from = actual_startdatetime

        query1 = None
        query2 = None
        if trips_available_to:
            query1 = (
                db.session.query(
                    AffBookingLogs.aff_id.label('aff_id'),
                    Bookings.id.label('book_id'),
                    Bookings.valid.label('book_valid'),
                    Bookings.code.label("book_code"),
                    Bookings.created_at.label('book_timestamp'),
                    Bookings.startdate.label('book_startdate'),
                    Bookings.starttime.label('book_starttime'),
                    Bookings.dur.label('book_dur'),
                    Bookings.car_type.label("book_car_type"),
                    Bookings.estimate.label('book_estimate'),
                    Bookings.estimate_pre_tax.label('book_estimate_pre_tax'),
                    Bookings.region.label('book_region'),
                    Bookings.days.label("book_days"),
                    Bookings.loc.label('book_starting_name'),
                    Bookings.lat.label('book_start_lat'),
                    Bookings.long.label('book_start_long'),
                    Bookings.payment_type.label('book_payment_type'),
                    AffBookingLogs.comment.label('book_remark'),
                    Bookings.driver.label('driver_id'),
                    Trip.status.label('trip_status'),
                    Trip.starttime.label('trip_starttime'),
                    Trip.endtime.label('trip_endtime'),
                    BookDest.name.label('book_dest_name'),
                    BookDest.lat.label('book_dest_lat'),
                    BookDest.lng.label('book_dest_long'),
                    driver_user.mobile.label('driver_mobile'),
                    driver_user.label_bv.label('driver_label'),
                    driver_user.label_bv.label('driver_user'),
                    func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
                )
                .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
                .join(Trip, Bookings.id == Trip.book_id)
                .outerjoin(BookDest, Bookings.id == BookDest.book_id)
                .join(Drivers, Bookings.driver == Drivers.id)
                .join(driver_user, Drivers.user == driver_user.id)
                .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)
                .filter(
                    Bookings.type == BookingParams.TYPE_B2B,
                    AffBookingLogs.aff_id.in_(aff_id_list),
                    Bookings.driver != Bookings.DEFAULT_DRIVER,
                    not_(Trip.starttime.is_(None)),
                    Bookings.valid == Bookings.ALLOCATED,
                    Trip.starttime >= trips_available_from,
                    Trip.starttime <= trips_available_to,
                )
            )
            if or_conditions:
                query1 = query1.filter(or_(*or_conditions))
            if search_query_filters:
                query1 = query1.filter(and_(*search_query_filters))
            if book_refs:
                query1 = query1.filter(Bookings.id.in_(book_refs))
            if isglobal == 0:
                query1 = query1.filter(Trip.starttime >= actual_startdatetime)

        if bookings_available_to:
            query2 = (
                db.session.query(
                    AffBookingLogs.aff_id.label('aff_id'),
                    Bookings.id.label('book_id'),
                    Bookings.valid.label('book_valid'),
                    Bookings.code.label("book_code"),
                    Bookings.created_at.label('book_timestamp'),
                    Bookings.startdate.label('book_startdate'),
                    Bookings.starttime.label('book_starttime'),
                    Bookings.dur.label('book_dur'),
                    Bookings.car_type.label("book_car_type"),
                    Bookings.estimate.label('book_estimate'),
                    Bookings.estimate_pre_tax.label('book_estimate_pre_tax'),
                    Bookings.region.label('book_region'),
                    Bookings.days.label("book_days"),
                    Bookings.loc.label('book_starting_name'),
                    Bookings.lat.label('book_start_lat'),
                    Bookings.long.label('book_start_long'),
                    Bookings.payment_type.label('book_payment_type'),
                    AffBookingLogs.comment.label('book_remark'),
                    Bookings.driver.label('driver_id'),
                    Trip.status.label('trip_status'),
                    Trip.starttime.label('trip_starttime'),
                    Trip.endtime.label('trip_endtime'),
                    BookDest.name.label('book_dest_name'),
                    BookDest.lat.label('book_dest_lat'),
                    BookDest.lng.label('book_dest_long'),
                    driver_user.mobile.label('driver_mobile'),
                    driver_user.label_bv.label('driver_label'),
                    driver_user.label_bv.label('driver_user'),
                    func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
                )
                .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
                .outerjoin(Trip, Bookings.id == Trip.book_id)
                .outerjoin(BookDest, Bookings.id == BookDest.book_id)
                .join(Drivers, Bookings.driver == Drivers.id)
                .join(driver_user, Drivers.user == driver_user.id)
                .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)
                .filter(
                    Bookings.type == BookingParams.TYPE_B2B,
                    AffBookingLogs.aff_id.in_(aff_id_list),
                    Trip.starttime.is_(None),
                    or_(
                        and_(Bookings.startdate > from_date),
                        and_(
                            Bookings.startdate == from_date,
                            Bookings.starttime >= from_time,
                        ),
                    ),
                    or_(
                        and_(Bookings.startdate < to_date),
                        and_(
                            Bookings.startdate == to_date,
                            Bookings.starttime <= to_time,
                        ),
                    ),
                )
            )
            if or_conditions:
                query2 = query2.filter(or_(*or_conditions))
            if isglobal == 0:
                query2 = query2.filter((Bookings.startdate > cast(actual_startdatetime, Date)) | ((Bookings.startdate == cast(actual_startdatetime, Date)) & (Bookings.starttime >= cast(actual_startdatetime, Time))))
            if search_query_filters:
                query2 = query2.filter(and_(*search_query_filters))
            if book_refs:
                query2 = query2.filter(Bookings.id.in_(book_refs))

        if regions:
            if query1:
                query1 = query1.filter(Bookings.region.in_(regions))
            if query2:
                query2 = query2.filter(Bookings.region.in_(regions))

        if query1 and query2:
            combined_query = query1.union(query2)
        elif query2:
            combined_query = query2
        elif query1:
            combined_query = query1
        else:
            return jsonify({'success': 1, 'bookings': [], 'acount': f'0', 'aatime': start, 'afirstqtime':'', 'aamin_triptime':'', 'aamin_booktime':''}), 200
        sorted_query = combined_query.order_by(desc(func.concat(Bookings.startdate, ' ', Bookings.starttime,Bookings.id)))
        # print('final search query',
        #     sorted_query.statement.compile(
        #         db.engine,
        #         dialect=mysql.dialect(),
        #         compile_kwargs={"literal_binds": True}
        #     ), flush=True
        # )

        response_data = sorted_query.all()
        book_ids = [result.book_id for result in response_data]

        # Fetch all MongoDB data in a single query
        mongo_data_map = {doc["book_ref"]: doc for doc in AffiliateCollections.affiliates_book.find({"book_ref": {"$in": book_ids}})}

        bookings = []
        min_triptime = '9999-12-31 23:59:59'
        min_booktime = '9999-12-31 23:59:59'
        max_triptime = '0000-01-01 00:00:00'
        max_booktime = '0000-01-01 00:00:00'
        for result in response_data:
            dateb, timeb = split_date_time(combine_and_convert_to_local(result.book_startdate, result.book_starttime,tz))
            dateb = dateb.strftime('%Y-%m-%d')
            timeb = timeb.strftime('%H:%M:%S')

            if result.trip_starttime and result.trip_starttime:
                min_triptime = min(min_triptime,convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S'))
                max_triptime = max(max_triptime,convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S'))

            if dateb and timeb and not result.trip_starttime:
                min_booktime = min(min_booktime, f"{dateb} {timeb}")
                max_booktime = max(max_booktime, f"{dateb} {timeb}")

            booking_data = {
                "book_id": result.book_id,
                "aff_id": result.aff_id,
                "sorttimestamp": f'{dateb}T{timeb}',
                "book_valid": result.book_valid,
                "book_code": result.book_code,
                "book_timestamp": result.book_timestamp.strftime('%d/%m/%Y %H:%M:%S'),
                "book_startdate": dateb,
                "book_starttime": timeb,
                "book_dur": result.book_dur.strftime('%H:%M:%S'),
                "book_car_type": result.book_car_type,
                "book_estimate": result.book_estimate,
                "book_estimate_pre_tax": result.book_estimate_pre_tax,
                "book_region": result.book_region,
                "book_days": result.book_days,
                "book_starting_name": result.book_starting_name,
                "book_start_lat": result.book_start_lat,
                "book_start_long": result.book_start_long,
                "book_payment_type": result.book_payment_type,
                "book_remark": result.book_remark,
                "trip_status": result.trip_status,
                "trip_starttime": convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_starttime else '-',
                "trip_endtime": convert_to_local_time(result.trip_endtime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_endtime else '-',
                "book_dest_name": result.book_dest_name,
                "book_dest_lat": result.book_dest_lat,
                "book_dest_long": result.book_dest_long,
                "driver_mobile": result.driver_mobile,
                "driver_user": result.driver_user,
                "driver_name": result.driver_name,
                "driver_label": result.driver_label,
                "driver_id": result.driver_id,
            }

            # Retrieve MongoDB data from the pre-fetched map
            mongo_data = mongo_data_map.get(result.book_id)
            if mongo_data:
                src_nickname_id = int(mongo_data.get("src_nickname_id", "0"))
                dest_nickname_id = int(mongo_data.get("dest_nickname_id", "0"))
                nickname_ids = set()
                if src_nickname_id > 0:
                    nickname_ids.add(src_nickname_id)
                if dest_nickname_id > 0:
                    nickname_ids.add(dest_nickname_id)
                nickname_addresses = db.session.query(AffiliateAddress).filter(
                        AffiliateAddress.add_id.in_(nickname_ids)
                    ).all()
                nickname_map = {address.add_id: address.nickname for address in nickname_addresses}
                src_nickname = nickname_map.get(src_nickname_id, "")
                dest_nickname = nickname_map.get(dest_nickname_id, "")
               
                booking_data.update({
                    "vehicle_no": mongo_data.get("vehicle_no"),
                    "client_name": mongo_data.get("client_name") if mongo_data.get("client_name") else 'Spinny',
                    "trip_name": mongo_data.get("trip_name"),
                    "trip_type": mongo_data.get("trip_type"),
                    "vehicle_model": mongo_data.get("vehicle_model", ""),
                    "priority": mongo_data.get("priority", 0),
                    "book_pending_valid": mongo_data.get("pending_state", BookPending.BROADCAST),
                    "book_starting_nickname": src_nickname,
                    "book_dest_nickname": dest_nickname
                })

            bookings.append(booking_data)

        return jsonify({
                'success': 1,
                "bookings": bookings,
                'acount': f'{len(bookings)}',
                'trips_available_to': convert_to_local_time(trips_available_to,tz) if trips_available_to else None,
                'bookings_available_to': convert_to_local_time(bookings_available_to,tz) if bookings_available_to else None,
                'aamin_triptime': '' if min_triptime == '9999-12-31 23:59:59' else min_triptime,
                'aamin_booktime': '' if min_booktime == '9999-12-31 23:59:59' else min_booktime,
                'aamax_triptime': '' if max_triptime == '0000-01-01 00:00:00' else max_triptime,
                'aamax_booktime': '' if max_booktime == '0000-01-01 00:00:00' else max_booktime,
            }), 200
    except ValueError:
        return jsonify({'success': -1, 'error': 'Some error in types'}), 400
    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e)}), 500

@aff_book.route('/api/affiliate/bookings', methods=['POST'])
@jwt_required()
def fetch_bookings():
    try:
        aff_id_list = request.form['aff_ids'].split(',')
        search_query = request.form.get('search_query','')
        search_by=request.form.get('search_by','3')
        filter_by=request.form.get('filter')
        claims = get_jwt()
        search_region = request.form.get('search_region')
        bottomBookings = request.form.get('fromtop', 1, type=int)
        isglobal = request.form.get('isglobal', type=int)
        limited = request.form.get('limited', 1, type=int)
        driver_user = aliased(Users, name="driver_user")

        tripdata = True
        bookdata = True
        regions = None
        is_b2c = 0

        batch_size = BookingParams.BOOKING_DATA_BATCHSIZE_BOTTOM
        batch_size_top = BookingParams.BOOKING_DATA_BATCHSIZE_TOP

        if search_region and search_region != '-1' and isglobal != 2: # isgobal 2 means no region restrictions
            regions = [int(value) for value in search_region.split(',')]
        rep_id = get_jwt_identity()

        if not complete(request.form, ['aff_ids']):
            return jsonify({'success': -1, 'bookings':[], 'message': "Incomplete form"}), 400

        start = time.time()
        firstRequestOrNot = request.form.get('firstRequestOrNot', 0, type=int)

        #this is for trip and book for bottom scroll
        bookdate, booktime = request.form.get('booktime').split() if request.form.get('booktime') else ['','']
        tripdate, triptime = request.form.get('triptime').split() if request.form.get('triptime') else ['','']

        to_date = request.form.get('to_date') if firstRequestOrNot else bookdate
        to_time = request.form.get('to_time') if firstRequestOrNot else booktime

        tripsto_date = tripdate if tripdate else request.form.get('to_date') if firstRequestOrNot else ''
        tripsto_time = triptime if triptime else request.form.get('to_time') if firstRequestOrNot else ''

        #this is for trip and book for top scroll
        fromtop_bookdate, fromtop_booktime = request.form.get('fromtop_booktime').split() if request.form.get('fromtop_booktime') else ['','']
        fromtop_tripdate, fromtop_triptime = request.form.get('fromtop_triptime').split() if request.form.get('fromtop_triptime') else ['','']

        from_date = request.form.get('from_date') if firstRequestOrNot and not fromtop_bookdate else fromtop_bookdate
        from_time = request.form.get('from_time') if firstRequestOrNot and not fromtop_booktime else fromtop_booktime

        tripsfrom_date = request.form.get('from_date') if firstRequestOrNot and not fromtop_tripdate  else fromtop_tripdate
        tripsfrom_time = request.form.get('from_time') if firstRequestOrNot and not fromtop_triptime  else fromtop_triptime
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata') 
        if to_date and to_time:
            to_date, to_time = convert_to_utc(to_date, to_time,tz)
        if from_date and from_time:
            from_date, from_time = convert_to_utc(from_date, from_time,tz)

        if tripsto_date and tripsto_time:
            tripsto_date, tripsto_time = convert_to_utc(tripsto_date, tripsto_time,tz)

        if tripsfrom_date and tripsfrom_time:
            tripsfrom_date, tripsfrom_time = convert_to_utc(tripsfrom_date, tripsfrom_time,tz)

        actual_startdatetime = request.form.get('actual_startdatetime', '')
        actual_enddatetime = request.form.get('actual_enddatetime', '')

        actual_startdatetime = convert_datetime_to_utc(actual_startdatetime,tz)
        actual_enddatetime = convert_datetime_to_utc(actual_enddatetime,tz)

        start = time.time()

        or_conditions = []

        #dropdown filters
        if filter_by and filter_by!='-1':
            filter_values = [int(val) for val in filter_by.split(',')]
             # these constants is sequence of filter from frontend list of filters in dropdown
            for filter_val in filter_values:
                # Unallocated Trips
                if filter_val == 1:
                    or_conditions.append(Bookings.valid == Bookings.UNALLOCATED)
                # Allocated Trips
                elif filter_val == 2:
                    or_conditions.append(and_(Bookings.valid == Bookings.ALLOCATED, Bookings.driver != 1, Trip.status.is_(None)))
                # On my way
                elif filter_val == 3:
                    or_conditions.append(and_(Bookings.valid == 1, Trip.status == 6))
                # Checked In Trip
                elif filter_val == 4:
                    or_conditions.append(and_(Bookings.valid == Bookings.ALLOCATED,  or_(Trip.status == Trip.TRIP_START_PIC, Trip.status ==Trip.TRIP_REACHED_SRC)))
                # Ongoing trip
                elif filter_val == 5:
                    or_conditions.append(and_(Bookings.valid == Bookings.ALLOCATED, Trip.status == Trip.TRIP_STARTED))
                # Reached trip
                elif filter_val == 6:
                    or_conditions.append(and_(Bookings.valid == Bookings.ALLOCATED, or_(Trip.status == Trip.TRIP_REACHED_DEST, Trip.status == Trip.TRIP_STOP_PIC)))
                # Completed trip
                elif filter_val == 7:
                    or_conditions.append(and_(Bookings.valid == Bookings.ALLOCATED, or_(Trip.status == Trip.TRIP_STOPPED, Trip.endtime != None)))
                # Cancelled customer
                elif filter_val == 8:
                    or_conditions.append(Bookings.valid == Bookings.CANCELLED_USER)
                # Cancelled driver
                # elif filter_val == 9:
                #     or_conditions.append(Bookings.valid == Bookings.CANCELLED_DRIVER)
                # D4M Cancelled Trips
                elif filter_val == 10:
                    or_conditions.append(or_(Bookings.valid == Bookings.CANCELLED_D4M, Bookings.valid == Bookings.CANCELLED_DRIVER))
                elif filter_val == 11:
                # Cancelled Before Allocation
                    or_conditions.append(and_(Bookings.valid == Bookings.CANCELLED_USER, Bookings.driver == Bookings.DEFAULT_DRIVER))
                elif filter_val == 12:
                # Cancelled After Allocation
                    or_conditions.append(and_(Bookings.valid == Bookings.CANCELLED_USER, Bookings.driver != Bookings.DEFAULT_DRIVER))

        search_query_filters = []

        if search_query:
            if int(search_by) == 3:  # Search by Booking ID or Code
                try:
                    search_id = int(search_query)
                    search_query_filters.append(Bookings.id == search_id)
                except ValueError:
                    search_query_filters.append(Bookings.code == search_query)

            elif int(search_by) in {1, 5}:  # Search by User
                try:
                    search_id = int(search_query)
                    search_query_filters.append(Users.mobile == search_query)
                except ValueError:
                    search_query_filters = process_search_query(search_query, Users)

            elif int(search_by) in {2, 4}:  # Search by Driver
                try:
                    search_id = int(search_query)
                    search_query_filters.append(driver_user.mobile == search_query)
                except ValueError:
                    search_query_filters = process_search_query(search_query, driver_user)

        if search_query:
            return search_result_query(driver_user, limited, search_by, search_query, actual_startdatetime, actual_enddatetime, isglobal, regions, tripsto_date, tripsto_time, to_date, to_time, or_conditions, firstRequestOrNot, search_query_filters, aff_id_list,tz)

        trips_available_to = None
        trips_available_from = None
        bookings_available_to = None
        bookings_available_from = None
        #if loading bottom bookings
        if bottomBookings:
            if to_date and to_time:
                bookings_available_to = get_max_time_bookings(to_date, to_time, actual_startdatetime, firstRequestOrNot, regions, isglobal, or_conditions,aff_id_list)
                # print('bookings_available_to', bookings_available_to)
                if bookings_available_to:
                    bookings_available_from = (bookings_available_to - timedelta(hours=batch_size)).replace(tzinfo=timezone.utc)
                    if isglobal == 0 and  bookings_available_from < actual_startdatetime:
                        bookings_available_from = actual_startdatetime

                    to_date = bookings_available_to.date()
                    to_time = bookings_available_to.time()

                    from_date = bookings_available_from.date()
                    from_time = bookings_available_from.time()
                    # print('bookings_available_to', from_date, from_time, to_date, to_time)
                else:
                    bookdata = False
            else:
                bookdata = False

            # find nearest bottom booking
            if tripsto_date and tripsto_time:
                trips_available_to = get_max_time_trips(tripsto_date, tripsto_time, actual_startdatetime, firstRequestOrNot, regions, isglobal, or_conditions,aff_id_list)

                if trips_available_to:
                    trips_available_from = (trips_available_to - timedelta(hours=batch_size)).replace(tzinfo=timezone.utc)
                    if isglobal == 0 and  trips_available_from < actual_startdatetime:
                        trips_available_from = actual_startdatetime

                    trips_available_to_ist = convert_to_local_time(trips_available_to,tz)
                else:
                    tripdata = False
            else:
                tripdata = False
        #if loading top bookings
        else:
            if from_date and from_time:
                bookings_available_from = get_min_time_bookings(from_date, from_time, actual_enddatetime, limited, regions, isglobal, or_conditions,aff_id_list)
                if bookings_available_from:
                    bookings_available_to = (bookings_available_from + timedelta(hours=batch_size_top)).replace(tzinfo=timezone.utc)
                    if isglobal == 0 and bookings_available_to > actual_enddatetime and limited:
                        bookings_available_to = actual_enddatetime

                    to_date = bookings_available_to.date()
                    to_time = bookings_available_to.time()

                    from_date = bookings_available_from.date()
                    from_time = bookings_available_from.time()
                else:
                    bookdata = False
            else:
                bookdata = False

            # find nearest top booking
            if tripsfrom_date and tripsfrom_time:
                trips_available_from = get_min_time_trips(tripsfrom_date, tripsfrom_time, actual_enddatetime, limited, regions, isglobal, or_conditions,aff_id_list)

                if trips_available_from:
                    trips_available_to = (trips_available_from + timedelta(hours=batch_size_top)).replace(tzinfo=timezone.utc)
                    if isglobal == 0 and  trips_available_to > actual_enddatetime and limited:
                        trips_available_to = actual_enddatetime

                    #for debugging purose only
                    trips_available_from_ist = convert_to_local_time(trips_available_from,tz)
                else:
                    tripdata = False
            else:
                tripdata = False

        firstquerytime = time.time() - start
        start = time.time()

        query1 = None
        query2 = None
        if tripdata:
            query1 = (
                db.session.query(
                    AffBookingLogs.aff_id.label('aff_id'),
                    Bookings.id.label('book_id'),
                    Bookings.valid.label('book_valid'),
                    Bookings.code.label("book_code"),
                    Bookings.created_at.label('book_timestamp'),
                    Bookings.startdate.label('book_startdate'),
                    Bookings.starttime.label('book_starttime'),
                    Bookings.dur.label('book_dur'),
                    Bookings.car_type.label("book_car_type"),
                    Bookings.estimate.label('book_estimate'),
                    Bookings.estimate_pre_tax.label('book_estimate_pre_tax'),
                    Bookings.region.label('book_region'),
                    Bookings.days.label("book_days"),
                    Bookings.loc.label('book_starting_name'),
                    Bookings.lat.label('book_start_lat'),
                    Bookings.long.label('book_start_long'),
                    Bookings.payment_type.label('book_payment_type'),
                    AffBookingLogs.comment.label('book_remark'),
                    Bookings.driver.label('driver_id'),
                    Trip.status.label('trip_status'),
                    Trip.starttime.label('trip_starttime'),
                    Trip.endtime.label('trip_endtime'),
                    BookDest.name.label('book_dest_name'),
                    BookDest.lat.label('book_dest_lat'),
                    BookDest.lng.label('book_dest_long'),
                    driver_user.mobile.label('driver_mobile'),
                    driver_user.label_bv.label('driver_label'),
                    driver_user.label_bv.label('driver_user'),
                    func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
                )
                .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
                .join(Trip, Bookings.id == Trip.book_id)
                .outerjoin(BookDest, Bookings.id == BookDest.book_id)
                .join(Drivers, Bookings.driver == Drivers.id)
                .join(driver_user, Drivers.user == driver_user.id)
                .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)
                .filter(
                    AffBookingLogs.aff_id.in_(aff_id_list),
                    Bookings.type == BookingParams.TYPE_B2B,
                    Bookings.driver != Bookings.DEFAULT_DRIVER,
                    not_(Trip.starttime.is_(None)),
                    Bookings.valid == Bookings.ALLOCATED,
                    Trip.starttime >= trips_available_from,
                    Trip.starttime <= trips_available_to,
                )
            )
            if or_conditions:
                query1 = query1.filter(or_(*or_conditions))
        if bookdata:
            query2 = (
                db.session.query(
                    AffBookingLogs.aff_id.label('aff_id'),
                    Bookings.id.label('book_id'),
                    Bookings.valid.label('book_valid'),
                    Bookings.code.label("book_code"),
                    Bookings.created_at.label('book_timestamp'),
                    Bookings.startdate.label('book_startdate'),
                    Bookings.starttime.label('book_starttime'),
                    Bookings.dur.label('book_dur'),
                    Bookings.car_type.label("book_car_type"),
                    Bookings.estimate.label('book_estimate'),
                    Bookings.estimate_pre_tax.label('book_estimate_pre_tax'),
                    Bookings.region.label('book_region'),
                    Bookings.days.label("book_days"),
                    Bookings.loc.label('book_starting_name'),
                    Bookings.lat.label('book_start_lat'),
                    Bookings.long.label('book_start_long'),
                    Bookings.payment_type.label('book_payment_type'),
                    AffBookingLogs.comment.label('book_remark'),
                    Bookings.driver.label('driver_id'),
                    Trip.status.label('trip_status'),
                    Trip.starttime.label('trip_starttime'),
                    Trip.endtime.label('trip_endtime'),
                    BookDest.name.label('book_dest_name'),
                    BookDest.lat.label('book_dest_lat'),
                    BookDest.lng.label('book_dest_long'),
                    driver_user.mobile.label('driver_mobile'),
                    driver_user.label_bv.label('driver_label'),
                    driver_user.label_bv.label('driver_user'),
                    func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
                )
                .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
                .outerjoin(Trip, Bookings.id == Trip.book_id)
                .outerjoin(BookDest, Bookings.id == BookDest.book_id)
                .join(Drivers, Bookings.driver == Drivers.id)
                .join(driver_user, Drivers.user == driver_user.id)
                .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)
                .filter(
                    AffBookingLogs.aff_id.in_(aff_id_list),
                    Bookings.type == BookingParams.TYPE_B2B,
                    Trip.starttime.is_(None),
                    or_(
                        and_(Bookings.startdate > from_date),
                        and_(
                            Bookings.startdate == from_date,
                            Bookings.starttime >= from_time,
                        ),
                    ),
                    or_(
                        and_(Bookings.startdate < to_date),
                        and_(
                            Bookings.startdate == to_date,
                            Bookings.starttime <= to_time,
                        ),
                    ),
                )
            )
            if or_conditions:
                query2 = query2.filter(or_(*or_conditions))

        if regions:
            if query1:
                query1 = query1.filter(Bookings.region.in_(regions))
            if query2:
                query2 = query2.filter(Bookings.region.in_(regions))

        if query1 and query2:
            combined_query = query1.union(query2)
        elif query2:
            combined_query = query2
        elif query1:
            combined_query = query1
        else:
            return jsonify({'success': 1, 'bookings': [], 'acount': f'0', 'aatime': start, 'afirstqtime':firstquerytime, 'aamin_triptime':'', 'aamin_booktime':''}), 200

        sorted_query = combined_query.order_by(desc(func.concat(Bookings.startdate, ' ', Bookings.starttime,Bookings.id)))
        # print('final query',
        #     sorted_query.statement.compile(
        #         db.engine,
        #         dialect=mysql.dialect(),
        #         compile_kwargs={"literal_binds": True}
        #     ), flush=True
        # )

        results = sorted_query.all()

        # Extract all booking IDs first
        book_ids = [result.book_id for result in results]

        # Fetch all MongoDB data in a single query
        mongo_data_map = {doc["book_ref"]: doc for doc in AffiliateCollections.affiliates_book.find({"book_ref": {"$in": book_ids}})}

        # Format the results
        bookings = []
        min_triptime = '9999-12-31 23:59:59'
        min_booktime = '9999-12-31 23:59:59'
        max_triptime = '0000-01-01 00:00:00'
        max_booktime = '0000-01-01 00:00:00'
        for result in results:
            dateb, timeb = split_date_time(combine_and_convert_to_local(result.book_startdate, result.book_starttime,tz))
            dateb = dateb.strftime('%Y-%m-%d')
            timeb = timeb.strftime('%H:%M:%S')

            if result.trip_starttime and result.trip_starttime:
                min_triptime = min(min_triptime,convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S'))
                max_triptime = max(max_triptime,convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S'))

            if dateb and timeb and not result.trip_starttime:
                min_booktime = min(min_booktime, f"{dateb} {timeb}")
                max_booktime = max(max_booktime, f"{dateb} {timeb}")

            booking_data = {
                "book_id": result.book_id,
                "aff_id": result.aff_id,
                "sorttimestamp": f'{dateb}T{timeb}',
                "book_valid": result.book_valid,
                "book_code": result.book_code,
                "book_timestamp": result.book_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                "book_startdate": dateb,
                "book_starttime": timeb,
                "book_dur": result.book_dur.strftime('%H:%M:%S'),
                "book_car_type": result.book_car_type,
                "book_estimate": result.book_estimate,
                "book_estimate_pre_tax": result.book_estimate_pre_tax,
                "book_region": result.book_region,
                "book_days": result.book_days,
                "book_starting_name": result.book_starting_name,
                "book_start_lat": result.book_start_lat,
                "book_start_long": result.book_start_long,
                "book_payment_type": result.book_payment_type,
                "book_remark": result.book_remark,
                "trip_status": result.trip_status,
                "trip_starttime": convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_starttime else '-',
                "trip_endtime": convert_to_local_time(result.trip_endtime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_endtime else '-',
                "book_dest_name": result.book_dest_name,
                "book_dest_lat": result.book_dest_lat,
                "book_dest_long": result.book_dest_long,
                "driver_mobile": result.driver_mobile,
                "driver_user": result.driver_user,
                "driver_name": result.driver_name,
                "driver_label": result.driver_label,
                "driver_id": result.driver_id,
            }

            # Retrieve MongoDB data from the pre-fetched map
            mongo_data = mongo_data_map.get(result.book_id)
            if mongo_data:
                src_nickname_id = int(mongo_data.get("src_nickname_id", "0"))
                dest_nickname_id = int(mongo_data.get("dest_nickname_id", "0"))
                nickname_ids = set()
                if src_nickname_id > 0:
                    nickname_ids.add(src_nickname_id)
                if dest_nickname_id > 0:
                    nickname_ids.add(dest_nickname_id)
                nickname_addresses = db.session.query(AffiliateAddress).filter(
                        AffiliateAddress.add_id.in_(nickname_ids)
                    ).all()
                nickname_map = {address.add_id: address.nickname for address in nickname_addresses}
                src_nickname = nickname_map.get(src_nickname_id, "")
                dest_nickname = nickname_map.get(dest_nickname_id, "")
                booking_data.update({
                    "vehicle_no": mongo_data.get("vehicle_no"),
                    "client_name": mongo_data.get("client_name") if mongo_data.get("client_name") else 'Spinny',
                    "trip_name": mongo_data.get("trip_name"),
                    "trip_type": mongo_data.get("trip_type"),
                    "vehicle_model": mongo_data.get("vehicle_model", ""),
                    "book_pending_valid": mongo_data.get("pending_state", BookPending.BROADCAST),
                    "priority": mongo_data.get("priority", 0),
                    "book_starting_nickname": src_nickname,
                    "book_dest_nickname": dest_nickname
                })

            bookings.append(booking_data)

        return jsonify({
                'success': 1,
                "bookings": bookings,
                'acount': f'{len(bookings)}',
                'aatime': start,
                'trips_available_from': convert_to_local_time(trips_available_from,tz) if trips_available_from else None,
                'trips_available_to': convert_to_local_time(trips_available_to,tz) if trips_available_to else None,
                'bookings_available_from': convert_to_local_time(bookings_available_from,tz) if bookings_available_from else None,
                'bookings_available_to': convert_to_local_time(bookings_available_to,tz) if bookings_available_to else None,
                'afirstqtime': firstquerytime,
                'aamin_triptime': '' if min_triptime == '9999-12-31 23:59:59' else min_triptime,
                'aamin_booktime': '' if min_booktime == '9999-12-31 23:59:59' else min_booktime,
                'aamax_triptime': '' if max_triptime == '0000-01-01 00:00:00' else max_triptime,
                'aamax_booktime': '' if max_booktime == '0000-01-01 00:00:00' else max_booktime,
            }), 200
    except ValueError:
        return jsonify({'success': -1, 'error': 'Some error in types'}), 400
    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e)}), 500


@aff_book.route('/api/affiliate/booking/details', methods=['POST'])
@jwt_required()
def fetch_booking_details():
    try:
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    try:
        if not complete(request.form, ['booking_id']):
            return jsonify({'success': -1, 'message': "Incomplete form"}), 400

        book_id = int(request.form['booking_id'])

        book = db.session.query(Bookings, Trip, BookDest, BookPricing, TripPricing, AffBookingLogs) \
                        .outerjoin(Trip, Bookings.id == Trip.book_id) \
                        .outerjoin(BookDest, Bookings.id == BookDest.book_id) \
                        .outerjoin(BookPricing, Bookings.id == BookPricing.book_id) \
                        .outerjoin(TripPricing, Bookings.id == TripPricing.book_id) \
                        .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
                        .filter(Bookings.id == book_id).first()

        if not book:
            return jsonify({'success': -2, "message": "Booking does not exist"}), 400

        booking, trip, book_dest, book_pricing, trip_pricing, aff_booking_logs = book

        # Fetch Booking Metadata from MongoDB
        booking_md = AffiliateCollections.affiliates_book.find_one({"book_ref": book_id}) or {}
        # Handle Trip Type Details (Avoid Repeating Redis and MongoDB Calls)
        trip_type = booking_md.get("trip_type", "NA")
        src_nickname_id = int(booking_md.get("src_nickname_id", "0"))
        dest_nickname_id = int(booking_md.get("dest_nickname_id", "0"))
        affiliate_id = aff_booking_logs.aff_id

        trip_type_label = "N/A"
        trip_type_placeholder = "N/A"
        form_field = {}

        affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()

        nickname_ids = set()
        if src_nickname_id > 0:
            nickname_ids.add(src_nickname_id)
        if dest_nickname_id > 0:
            nickname_ids.add(dest_nickname_id)
        nickname_addresses = db.session.query(AffiliateAddress).filter(
                AffiliateAddress.add_id.in_(nickname_ids)
            ).all()
        nickname_map = {address.add_id: address.nickname for address in nickname_addresses}
        src_nickname = nickname_map.get(src_nickname_id, "")
        dest_nickname = nickname_map.get(dest_nickname_id, "")

        client_name = affiliate.client_name

        affiliate_data = AffiliateCollections.affiliates_details.find_one({'affiliate_id': affiliate_id}) or {}
        trip_type_label = affiliate_data.get('tripTypeLabel', "N/A")
        trip_type_placeholder = affiliate_data.get('tripTypePlaceholder', "N/A")
        form_field_key = 'form_field_oneway' if trip_type == "One Way" else 'form_field_round'
        form_field = affiliate_data.get(form_field_key, {})

        # Exclude 'add_custom_fields'
        filtered_form_field = {key: value for key, value in form_field.items() if key != 'add_custom_fields'}

        # Fetch Driver Details (Safer Query)
        driver_user = db.session.query(Users, Drivers, DriverInfo) \
                        .join(Drivers, Drivers.user == Users.id) \
                        .join(DriverInfo, DriverInfo.driver_id == Drivers.id) \
                        .filter(Drivers.id == booking.driver).first() if booking else None

        # Trip Logs (Check-in Time)
        checked_in_time = "NA"
        trip_logs = db.session.query(TripLog).filter(TripLog.booking_id == book_id).all()
        for log in trip_logs:
            if log.action == TripLog.ACTION_REACHED_SRC:
                checked_in_time = log.timestamp.strftime("%d/%m/%Y %H:%M:%S")
                break  # Stop after first match

        # Build Response
        data = {
            "schema_details": {
                'trip_type_list': booking_md.get("trip_type_list", []),
                'trip_start_image_structure': booking_md.get("trip_start_images_structure", []),
                'trip_stop_image_structure': booking_md.get("trip_stop_images_structure", []),
                'custom_data_structure': booking_md.get("custom", []),
                'trip_type_label': trip_type_label,
                'trip_type_placeholder': trip_type_placeholder,
                'form_field': filtered_form_field
            },
            "basic_details": {
                'book_id': booking.id,
                'book_code': booking.code,
                'book_otp': booking.otp,
                'book_valid': booking.valid,
                'trip_status': trip.status if trip else -1,
                'book_car_type': booking.car_type,
                'book_driver_rating': booking.driver_rating,
                'book_date_time': booking.created_at.strftime("%d/%m/%Y %H:%M:%S"),
                'book_account': client_name,
                'affiliate_wallet': affiliate.wallet,
                'book_account_id': affiliate_id,
                'vehicle_no': booking_md.get("vehicle_no", "NA"),
                'trip_name': booking_md.get("trip_name", "NA"),
                'trip_type': trip_type,
                'vehicle_model': booking_md.get("vehicle_model", ""),
                'appoint_id': booking_md.get("appointment_id", ""),
                'custom_data': booking_md.get("custom_data"),
                'book_location': booking.loc if booking and booking.loc else "NA",
                'book_region': booking.region if booking else "NA",
            },
            "trip_schedule": {
                'start_date': booking.startdate.strftime("%d/%m/%Y"),
                'start_time': booking.starttime.strftime("%H:%M:%S"),
                'end_date': booking.enddate.strftime("%d/%m/%Y"),
                'end_time': booking.endtime.strftime("%H:%M:%S"),
                'duration': booking.dur.strftime("%H:%M:%S"),
                'days': booking.days
            },
            "trip_time": {
                'start_date_time': trip.starttime.strftime("%d/%m/%Y %H:%M:%S") if trip and trip.starttime else "NA",
                'end_date_time': trip.endtime.strftime("%d/%m/%Y %H:%M:%S") if trip and trip.endtime else "NA",
                'check_in_time': checked_in_time,
            },
            "location_details": {
                'src_loc': booking.loc,
                'src_lat': booking.lat,
                'src_long': booking.long,
                'src_nickname_id': src_nickname_id,
                'src_nickname': src_nickname,
                'dest_nickname_id': dest_nickname_id,
                'dest_nickname': dest_nickname,
                'dest_loc': book_dest.name if book_dest else "NA",
                'dest_lat': book_dest.lat if book_dest else 0,
                'dest_long': book_dest.lng if book_dest else 0
            },
            "spoc_data": booking_md.get("spoc_data"),
            "trip_start_image": booking_md.get("trip_start_images"),
            "trip_stop_image": booking_md.get("trip_stop_images"),
            "trip_remarks": aff_booking_logs.comment if aff_booking_logs and aff_booking_logs.comment else "",
            "trip_price": {
                'estimate': booking.estimate,
                'base_fare': getattr(trip_pricing, 'base_ch', getattr(book_pricing, 'base_ch', 0)),
                'night_fare': getattr(trip_pricing, 'night_ch', getattr(book_pricing, 'night_ch', 0)),
                'ot_fare': getattr(trip_pricing, 'ot_ch', 0),
                'ins_fare': book_pricing.insurance_ch if book_pricing else 0,
                'ins_avl': booking.insurance
            },
            "driver_details": {
                'driver_id': booking.driver,
                'driver_pic': driver_user[1].pic,
                'driver_name': driver_user[0].get_name(),
                'driver_lic': driver_user[1].licenseNo,
                'driver_address': driver_user[2].pres_addr if driver_user[2] else "NA",
                'driver_region': driver_user[2].pres_region if driver_user[2] else "NA",
                'driver_mobile': driver_user[0].mobile

            }
        }

        return jsonify({'success': 1, 'booking_id': book_id, 'data': data})
    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e)}), 500

@aff_book.route('/api/affiliate/booking/add_remark', methods=['POST'])
@jwt_required()
def add_booking_remark():
    try:
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    try:
        if not complete(request.form, ['booking_id', 'remark']):
            return jsonify({'success': -1, 'message': "Incomplete form"}), 400
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        book_id = int(request.form['booking_id'])
        remark = request.form['remark'].strip()

        book = db.session.query(Bookings, AffBookingLogs) \
                         .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
                         .filter(Bookings.id == book_id).first()

        if not book:
            return jsonify({'success': -2, "message": "Booking does not exist"}), 400

        booking, aff_book_logs = book

        if aff_book_logs:
            aff_book_logs.comment = remark
        db.session.commit()
        send_live_aff_booking_table(book_id, channel='table', dest_aff_id=aff_book_logs.aff_id, booking_region=booking.region,tz=tz)
        return jsonify({'success': 1, 'message': "Remark saved successfully"})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -3, 'message': f"Error: {str(e)}"}), 500


@aff_book.route('/api/affiliate/booking/details_update', methods=['POST'])
@jwt_required()
def update_booking_details():
    try:
        claims = get_jwt()
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1, 'message': "Incomplete form"}), 400

    try:
        book_id = int(request.form['booking_id'])
    except ValueError:
        return jsonify({'success': -1, 'message': "Invalid booking ID"}), 400

    result = db.session.query(Bookings, AffBookingLogs) \
                                .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
                                .filter(Bookings.id == book_id).first()

    if not result:
        return jsonify({'success': -1, 'message': "No booking found"}), 400

    booking, aff_booking_logs = result

    vehicle_no = request.form.get("vehicle_no")
    appoint_id = request.form.get("appoint_id", "")
    trip_type = request.form.get("trip_type")
    trip_name = request.form.get("trip_name")
    vehicle_model = request.form.get("vehicle_model", "")
    custom_data_str = request.form.get("custom_data")
    remark = request.form.get("remark", "")
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')

    custom_data = {}
    if custom_data_str:
        try:
            custom_data = json.loads(custom_data_str)
        except json.JSONDecodeError:
            return jsonify({'success': -1, 'message': "Invalid JSON format for custom_data"}), 400

    search_id = request.form.get("search_id", "")
    src_nickname_id = "0"
    dest_nickname_id = "0"
    if search_id:
        search = db.session.query(AffiliateDriverSearch).filter(AffiliateDriverSearch.id == search_id).first()
        db.session.query(BookPricing).filter(BookPricing.book_id == book_id).update({
            BookPricing.estimate: search.estimate,
            BookPricing.base_ch: search.cust_base_fare,
            BookPricing.night_ch: search.cust_night_fare,
            BookPricing.driver_base_ch: search.driver_base_fare,
            BookPricing.driver_night_ch: search.driver_night_fare,
            BookPricing.est_pre_tax: search.estimate,
            BookPricing.insurance_ch: search.insurance_ch,
            BookPricing.booking_ch: search.estimate - (search.driver_base_fare + search.driver_night_fare)
        })

        start_date_time = request.form.get('start_date_time')
        end_date_time = request.form.get('end_date_time')

        loc = request.form.get("src_loc")
        src_nickname_id = request.form.get("src_nickname_id","0")

        try:
            dest_lat = request.form['dest_lat']
            dest_long = request.form['dest_long']
            dest_loc = request.form['dest_loc']
            dest_nickname_id = request.form.get("dest_nickname_id", "0")
            dest_exists = True
        except Exception:
            dest_lat = 0.0
            dest_long = 0.0
            dest_loc = "N/A"
            dest_exists = False

        if dest_exists:
            book_dest = db.session.query(BookDest).filter(BookDest.book_id == book_id).first()
            if book_dest:
                book_dest.lat = dest_lat
                book_dest.lng = dest_long
                book_dest.name = dest_loc
            else:
                book_dest = BookDest(book_id, dest_lat, dest_long, dest_loc)
                db.session.add(book_dest)

        start_dt = None
        end_dt = None

        if start_date_time:
            start_dt = convert_datetime_to_utc(start_date_time, tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
            # try:
            #     start_dt = datetime.strptime(start_date_time, "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
            # except ValueError:
            #     try:
            #         start_dt = datetime.strptime(start_date_time, "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
            #     except ValueError:
            #         start_dt = datetime.strptime(start_date_time, "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

        if end_date_time:
            end_dt = convert_datetime_to_utc(end_date_time, tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
            # try:
            #     end_dt = datetime.strptime(end_date_time, "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
            # except ValueError:
            #     try:
            #         end_dt = datetime.strptime(end_date_time, "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
            #     except ValueError:
            #         end_dt = datetime.strptime(end_date_time, "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

        db.session.query(Bookings).filter(Bookings.id == book_id).update({
            Bookings.starttime: start_dt.time() if start_dt else None,
            Bookings.endtime: end_dt.time() if end_dt else None,
            Bookings.dur: search.dur,
            Bookings.days: search.days,
            Bookings.startdate: start_dt.date() if start_dt else None,
            Bookings.enddate: end_dt.date() if end_dt else None,
            Bookings.car_type: search.car_type,
            Bookings.lat: search.reflat,
            Bookings.long: search.reflong,
            Bookings.loc: loc,
            Bookings.search_key: search_id,
            Bookings.estimate: search.estimate,
            Bookings.estimate_pre_tax: search.estimate,
            Bookings.price: search.estimate,
            Bookings.insurance: search.insurance,
            Bookings.insurance_cost: search.insurance_ch,
            Bookings.insurance_num: 1 if search.insurance_ch > 0 else 0
        })


    source_spoc_name = request.form.get('src_spoc_name')
    source_spoc_mobile = request.form.get('src_spoc_mobile')
    dest_spoc_name = request.form.get('dest_spoc_name')
    dest_spoc_mobile = request.form.get('dest_spoc_mobile')

    if aff_booking_logs:
        aff_booking_logs.comment = remark

    update_spoc_data = {}
    if source_spoc_name:
        update_spoc_data["source_spoc_name"] = source_spoc_name
    if source_spoc_mobile:
        update_spoc_data["source_spoc_contact"] = source_spoc_mobile
    if dest_spoc_name:
        update_spoc_data["dest_spoc_name"] = dest_spoc_name
    if dest_spoc_mobile:
        update_spoc_data["dest_spoc_contact"] = dest_spoc_mobile

    update_data = {
        "spoc_data": update_spoc_data,
        "custom_data": custom_data,
        "trip_name": trip_name,
        "trip_type": trip_type,
        "vehicle_no": vehicle_no,
    }


    if search_id:
        update_data["src_nickname_id"] = src_nickname_id
        update_data["dest_nickname_id"] = dest_nickname_id

    if appoint_id:
        update_data["appointment_id"] = appoint_id

    if vehicle_model:
        update_data["vehicle_model"] = vehicle_model
    try:
        db.session.commit()
        result = AffiliateCollections.affiliates_book.update_one(
                    {"book_ref": book_id},
                    {"$set": update_data}
                )
        if result.matched_count == 0:
            return jsonify({'success': -1, 'message': "Booking not found in MongoDB"}), 404
        send_live_update_of_booking(book_id, booking.region)
        send_live_aff_booking_table(book_id, channel='table', dest_aff_id=aff_booking_logs.aff_id, booking_region=booking.region,tz=tz)
    
        return jsonify({'success': 1, 'message': "Booking details updated successfully"})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'message': "Database update failed", 'error': str(e)}), 500


@aff_book.route('/api/affiliate/driver_list', methods=['POST'])
@jwt_required()
def affiliate_driver_list():
    try:
        claims = get_jwt()
    except Exception as e:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1, 'message': "Incomplete form"}), 400

    book_id = int(request.form['booking_id'])
    search_query = request.form.get("search_query", "").strip()
    sort_by = int(request.form.get("sort_by", 0))
    page = int(request.form.get("page", 1))
    limit = int(request.form.get("limit", 25))

    client_name = claims.get("client_name")
    affiliate_data = AffiliateCollections.affiliates_details.find_one({'client_name': client_name}) or {}
    driver_available = int(affiliate_data.get("driverAvailVisEnabled", 0))

    try:
        booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
        if not booking:
            return jsonify({'success': -1, 'message': "Booking not found"}), 404

        region = booking.region
        has_booking = exists().where(
            (Bookings.driver == Drivers.id) &
            (Bookings.region == region) &
            (Bookings.valid == 1) &
            (Bookings.type >= BookingParams.TYPE_C24)
        ).correlate(Drivers)

        # Select only necessary columns
        drivers_query = (
            db.session.query(
                Drivers.id,
                Users.fname,
                Users.lname,
                Users.mobile,
                Drivers.rating,
                Drivers.pic,
                Drivers.licenseNo,
                Drivers.available,
                DriverDetails.ride_count,
                DriverDetails.rating_count
            )
            .join(Users, Drivers.user == Users.id)
            .join(DriverDetails, Drivers.id == DriverDetails.driver_id)
            .filter(
                Drivers.approved == 1,
                has_booking
            )
        )

        if driver_available != 1:
            drivers_query = drivers_query.filter(Drivers.available == 1)

        # Apply search filter
        if search_query:
            if search_query.isdigit():
                drivers_query = drivers_query.filter(Users.mobile.contains(search_query))
            else:
                drivers_query = drivers_query.filter(
                    (Users.fname.ilike(f"%{search_query}%")) |
                    (Users.lname.ilike(f"%{search_query}%"))
                )

        if sort_by == 0:
            drivers_query = drivers_query.order_by(Drivers.rating.desc())
        else:
            drivers_query = drivers_query.order_by(Drivers.rating.asc())

        # Pagination with has_more check
        start = (page - 1) * limit
        adjusted_limit = limit + 1  # Fetch one extra to determine has_more
        drivers_query = drivers_query.offset(start).limit(adjusted_limit)

        driver_results = drivers_query.all()

        has_more = len(driver_results) > limit
        if has_more:
            driver_results = driver_results[:limit]

        # Format response
        b2b_driver_info = [
            {
                "id": driver_id,
                "fname": fname,
                "lname": lname,
                "name": f"{fname} {lname}",
                "mobile": mobile,
                "rating": round(rating, 2) if rating else 0.0,
                "pic": pic,
                "lic_no": license_no,
                "ride_count": ride_count,
                "rating_count": rating_count,
                "driver_avl": avl
            }
            for (driver_id, fname, lname, mobile, rating, pic, license_no, avl, ride_count, rating_count) in driver_results
        ]

        return jsonify({"success": 1, "data": b2b_driver_info, "has_more": has_more})

    except Exception as e:
        print(f"Error in affiliate_driver_list: {e}")
        return jsonify({'success': -1, 'message': 'Internal Server Error'}), 500

@aff_book.route('/api/affiliate/booking/cancel_charge', methods=['POST'])
@jwt_required()
def booking_cancel_charge():
    try:
        rep_id = get_jwt_identity()
        claims = get_jwt()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['book_code', 'reason']):
        return jsonify({'success': -1, 'message': 'Incomplete Form Details'}), 400

    booking_code = request.form['book_code']
    try:
        reason = int(request.form['reason'])
    except ValueError:
        return jsonify({'success': -1, 'message': 'Invalid Data Format'}), 400

    # Precompute current time
    now = datetime.utcnow()

    # Subquery for last_alloc_time
    last_alloc_subq = db.session.query(
                BookingAlloc.timestamp
            ).filter(
                BookingAlloc.booking_id == Bookings.id,
                BookingAlloc.driver_id == Bookings.driver
            ).order_by(
                BookingAlloc.timestamp.desc()
            ).limit(1).scalar_subquery()

    result = db.session.query(Bookings, Trip, last_alloc_subq.label('last_alloc_time'),
            ).outerjoin(
                Trip, Trip.book_id == Bookings.id
            ).filter(Bookings.code == booking_code).first()

    if result is None:
        return jsonify({'success': -1, 'message': 'Booking not found'}), 404

    booking, trip, last_alloc_time = result

    if not booking:
        return jsonify({'success': -1, 'message': 'Booking not found'}), 404

    logs_result = (
                    db.session.query(AffBookingLogs, Affiliate)
                    .join(Affiliate, Affiliate.id == AffBookingLogs.mapped_by)
                    .filter(AffBookingLogs.book_id == booking.id)
                    .first()
                )

    if not logs_result:
        return jsonify({'success': -1, 'message': 'Affiliate mapping not found'}), 404

    book_logs, mapped_affiliate = logs_result
    mapped_client = mapped_affiliate.client_name
    # Check trip status
    has_trip = trip is not None
    if trip and trip.status < Trip.TRIP_REACHED_SRC:
        return jsonify({'success': -2, 'message': 'Can not cancel after checkIn'}), 200

    if booking.driver != 1:
        # Check waiver conditions
        if last_alloc_time and reason in BookingCancelled.WAIVER and (now - last_alloc_time) < BookingCancelled.FORGIVE_DELTA:
            return jsonify({'success': 1, 'charge': [0, 0]})

        penalty, level = Price.get_cancel_ch( now, datetime.combine(booking.startdate, booking.starttime),
                            cancel_cat=reason, has_trip=has_trip, type=booking.type, client_name=mapped_client)
        return jsonify({'success': 1, 'charge': penalty})
    else:
        return jsonify({'success': 1, 'charge': [0, 0]})


def process_booking_cancel_charge(booking_code: str, reason: int, claims: dict,is_admin_call:int=0) -> dict:
    """
    Process a booking cancellation charge based on booking_code and reason.
    Returns a dict with keys 'success' and either 'charge' or 'message'.
    """
    now = datetime.utcnow()

    # Subquery to get the last allocation time
    last_alloc_subq = db.session.query(
                BookingAlloc.timestamp
            ).filter(
                BookingAlloc.booking_id == Bookings.id,
                BookingAlloc.driver_id == Bookings.driver
            ).order_by(
                BookingAlloc.timestamp.desc()
            ).limit(1).scalar_subquery()

    result = db.session.query(Bookings, Trip, last_alloc_subq.label('last_alloc_time'),
            ).outerjoin(
                Trip, Trip.book_id == Bookings.id
            ).filter(Bookings.code == booking_code).first()

    if not result:
        return {"success": -1, "message": "Booking not found"}

    booking, trip, last_alloc_time = result

    if not booking:
        return {"success": -1, "message": "Booking not found"}

    book_logs, mapped_affiliate = (
                    db.session.query(AffBookingLogs, Affiliate)
                    .join(Affiliate, Affiliate.id == AffBookingLogs.mapped_by)
                    .filter(AffBookingLogs.book_id == booking.id)
                    .first()
                )

    mapped_client = mapped_affiliate.client_name

    # Check trip status – if trip exists and status is less than TRIP_INIT then cancellation is not allowed
    has_trip = trip is not None
    if is_admin_call and has_trip and trip.status < Trip.TRIP_STOP_PIC:
        return {"success": -2, "message": "Can not cancel after completed"}
    if has_trip and trip.status < Trip.TRIP_REACHED_SRC and not is_admin_call:
        return {"success": -2, "message": "Can not cancel after start pic"}

    # Process cancellation charge
    if booking.driver != 1:
        # If there's a waiver applicable (based on last allocation time, reason, and forgive delta), return zero charge
        if last_alloc_time and reason in BookingCancelled.WAIVER and (now - last_alloc_time) < BookingCancelled.FORGIVE_DELTA:
            return {"success": 1, "charge": [0, 0]}
        # Otherwise, calculate the penalty
        penalty, level = Price.get_cancel_ch(
            now,
            datetime.combine(booking.startdate, booking.starttime),
            cancel_cat=reason,
            has_trip=has_trip,
            type=booking.type,
            client_name=mapped_client
        )
        return {"success": 1, "charge": penalty}
    else:
        return {"success": 1, "charge": [0, 0]}
    
    
@aff_book.route('/api/affiliate/booking/cancel', methods=['POST'])
@jwt_required()
def booking_cancel():
    try:
        rep_id = get_jwt_identity()
        claims = get_jwt()
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    if not complete(request.form, ['book_code', 'reason']):
        return jsonify({'success': -1, 'message': 'Incomplete Form Details'}), 400

    booking_code = request.form['book_code']
    try:
        reason = int(request.form['reason'])
    except (KeyError, ValueError):
        return jsonify({"success": 0, "message": "Invalid reason code."}), 400
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    reason_detail = request.form.get('reason_details', "")
    if reason_detail == "":
        reason_detail = get_cancel_reason(reason)

    mapped_by_alias = aliased(Affiliate)
    mapped_wallet_alias = aliased(Affiliate)

    engine = db.engine.execution_options(timeout=30)
    Session = sessionmaker(bind=engine)
    session = Session()
    try:
        with session.begin():
            result = session.query(Bookings, AffBookingLogs, Affiliate, Trip, mapped_by_alias, mapped_wallet_alias) \
                .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
                .outerjoin(Affiliate, AffBookingLogs.aff_id == Affiliate.id) \
                .outerjoin(Trip, Trip.book_id == Bookings.id) \
                .outerjoin(mapped_by_alias, mapped_by_alias.id == AffBookingLogs.mapped_by) \
                .outerjoin(mapped_wallet_alias, mapped_wallet_alias.id == AffBookingLogs.mapped_wallet) \
                .filter(Bookings.code == booking_code).with_for_update().first()

            if result is None:
                return jsonify({'success': -1, 'message': 'Booking not found'}), 404

            booking, aff_book_logs, affiliate, trip, mapped_affiliate, mapped_affiliate_wallet = result

            if affiliate is None:
                return jsonify({'success': -1, 'message': 'Affiliate not found'}), 404

            if booking.valid < 0:
                return jsonify({'success': 0, 'message': 'Booking already cancelled'}), 200

            has_trip = trip is not None

            if has_trip and trip.status < Trip.TRIP_REACHED_SRC:
                return jsonify({'success': -2, 'message': 'Can not cancel after checkIn'}), 200

            if trip:
                session.delete(trip)
            mapped_client = mapped_affiliate.client_name

            source = BookingCancelled.SRC_USER
            source_id = affiliate.id
            rep_aff_id = int(claims.get('aff_id'))
            action_name = claims.get('client_name')

            booking.valid = Bookings.CANCELLED_USER
            booking.cancelled_dt = datetime.utcnow()
            _update_driver_pending(booking.driver)

            penalty, level = [0, 0], 0
            user_fine, driver_fine = 0, 0
            at_id = dt_id = None
            if booking.driver != 1:
                fb_db.collection(u'book_cancelled_driver').document(str(booking.driver)).set({
                    str(booking.id): {
                        u'book_time': booking.starttime.strftime("%H:%M:%S"),
                        u'book_date': booking.startdate.strftime("%Y-%m-%d")
                    }}, merge=True
                )
                starttime = datetime(booking.startdate.year, booking.startdate.month,
                                    booking.startdate.day, booking.starttime.hour,
                                    booking.starttime.minute, booking.starttime.second)
                penalty, level = Price.get_cancel_ch(datetime.utcnow(), starttime, cancel_cat=reason, has_trip=has_trip,
                                                    type=booking.type, client_name=mapped_client)
                user_fine, driver_fine = penalty
                last_alloc = session.query(BookingAlloc.timestamp).filter(BookingAlloc.booking_id == booking.id,
                    BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
                if last_alloc and (reason in BookingCancelled.WAIVER and datetime.utcnow() - last_alloc.timestamp < BookingCancelled.FORGIVE_DELTA):
                    user_fine = driver_fine = 0
                if user_fine > 0:
                    cancel_wallet_logs = AffiliateWalletLogs(-user_fine*100, method=f"Cancellation charges #{booking.code}",
                        aff_id=rep_aff_id, rep_id=rep_id, from_account=mapped_affiliate_wallet.id, wallet_before=mapped_affiliate_wallet.wallet,
                        wallet_after = mapped_affiliate_wallet.wallet - user_fine, source=source)
                    mapped_affiliate_wallet.wallet -= user_fine
                    session.add(cancel_wallet_logs)
                    at_id = cancel_wallet_logs.id
                if driver_fine > 0:
                    driver_details = session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).first()
                    if driver_details:
                        wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)
                        driver_trans = DriverTrans(booking.driver, -driver_fine * 100, wall_a=wallet,
                            wall_b=driver_details.wallet, with_a=withdrawable, with_b=driver_details.withdrawable,
                            method=f"Cancellation: Booking {booking.code}", status=DriverTrans.COMPLETED, stop=True)
                        session.add(driver_trans)
                        dt_id = driver_trans.id
                        driver_details.owed += driver_fine
                        driver_details.wallet = wallet
                        driver_details.withdrawable = withdrawable
            dc = DriverCancelled(booking.driver, booking.id, driver_fine)
            bc = BookingCancelled(user=source_id, cancel_source=source, booking=booking.id,
                        uid=None, did=booking.driver, penalty_user=user_fine, penalty_driver=driver_fine,
                        rsn=reason, reason_detail=reason_detail, atransid=at_id, dtransid=dt_id)
            session.add(bc)
            session.flush()
            if rep_id is None:
                rep_book = AffBookingLogs(admin_id=source_id, aff_id=affiliate.id, cancel_id=bc.id)
            else:
                rep_book = AffBookingLogs(rep_id=rep_id, aff_id=rep_aff_id, cancel_id=bc.id)

            session.bulk_save_objects([rep_book, dc])
            book_id, book_region, book_code = booking.id, booking.region, booking.code
    
        affiliate_notification = {
            'book_code': book_code,
            'type': 'Cancelled by Affiiliate',
            'dest_client_name': affiliate.client_name,
            'rep_name': claims.get('fullname'),
            'client_name': action_name
        }
        send_notification_to_affiliate(affiliate_notification, 'Cancelled by Affiliate', affiliate.id,
                                src_rep=rep_id, src_aff_id=rep_aff_id)
        admin_notification = {
                'id': book_id,
                'type': claims.get('client_name', 'unknown'),
                'username': action_name,
                'content': f'Booking canceled by affiliate: {action_name}. Code:{book_code}',
                'imageUrl': '/assets/icons/action/admin-cross.svg',
                'timestamp': int(time.time() * 1000)
        }
        send_notification_to_channel(admin_notification, 'Cancelled by Affiliate:' + str(book_region))

        send_live_update_of_booking(book_id, book_region)
        send_live_aff_booking_table(book_id, channel='table', dest_aff_id=aff_book_logs.aff_id, booking_region=book_region,tz=tz)
        if trip:
            driver_det = session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.id == booking.driver).first()
            driver_data = {'driver_id': driver_det[0].id}
            live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel='update_driver')

        penalty_json = {"customer_penalty": user_fine, "driver_penalty": driver_fine}
        return jsonify({'success': 1, 'message': 'Booking cancelled successfully', 'penalty': penalty_json})
    except exc.IntegrityError as e:
        session.rollback()
        error_message = str(e.__cause__)
        return jsonify({'success': -1, 'message': f'DB Integrity Error: {error_message}'}), 500
    except Exception as e:
        session.rollback()
        print(f"Error cancelling booking: {e}")
        return jsonify({'success': -1, 'message': 'Internal Server Error'}), 500
    finally:
        session.close()


def _process_cancel_logic(session, booking_code, reason, source, reason_detail, rep_id, claims, is_admin_call, now,tz):
    try:
        mapped_by_alias = aliased(Affiliate)
        mapped_wallet_alias = aliased(Affiliate)

        result = session.query(Bookings, AffBookingLogs, Affiliate, Trip, mapped_by_alias, mapped_wallet_alias) \
                            .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
                            .outerjoin(Affiliate, AffBookingLogs.aff_id == Affiliate.id) \
                            .outerjoin(Trip, Trip.book_id == Bookings.id) \
                            .outerjoin(mapped_by_alias, mapped_by_alias.id == AffBookingLogs.mapped_by) \
                            .outerjoin(mapped_wallet_alias, mapped_wallet_alias.id == AffBookingLogs.mapped_wallet) \
                            .filter(Bookings.code == booking_code).first()
        if not result:
            return jsonify({'success': -1, 'message': 'Booking not found'}), 404

        booking, aff_book_logs, affiliate, trip, mapped_affiliate, mapped_affiliate_wallet = result


        if not affiliate:
            return {'success': -1, 'message': 'Affiliate not found'}

        if booking.valid < 0:
            return {'success': 0, 'message': 'Booking already cancelled'}

        has_trip = trip is not None
        if is_admin_call and has_trip and trip.status < Trip.TRIP_STOP_PIC:
            return {"success": -2, "message": "Can not cancel after completed"}
        if has_trip and trip.status < Trip.TRIP_REACHED_SRC and not is_admin_call:
            return {"success": -2, "message": "Can not cancel after start pic"}
        if has_trip:
            add_track_data_into_s3(booking.id) 
        # If trip exists, delete it.
                # --- Begin transactional block with locking ---

        # Acquire a pessimistic lock on the booking record.
        locked_booking = session.query(Bookings).filter(Bookings.id == booking.id) \
                                .with_for_update().one_or_none()
        if not locked_booking:
            return {'success': -1, 'message': 'Booking not found'}

        try:
            if trip:
                session.delete(trip)
        except Exception as ex:
            return {'success': -3, 'message': f'Error deleting trip: {ex}'}

        # Fetch affiliated booking logs and mapped affiliate details.
        book_logs, mapped_affiliate = (
            session.query(AffBookingLogs, Affiliate)
            .join(Affiliate, Affiliate.id == AffBookingLogs.mapped_by)
            .filter(AffBookingLogs.book_id == locked_booking.id)
            .first()
        )
        mapped_client = mapped_affiliate.client_name if mapped_affiliate else None

        # Determine cancellation source and assign proper ids and action name.
        if source == AffiliateWalletLogs.SOURCE_ADMIN:
            cancel_source = BookingCancelled.SRC_ADMIN
            source_id = rep_id
            action_name = claims.get('name')
            rep_id = None
            rep_aff_id = None
        else:
            cancel_source = BookingCancelled.SRC_USER
            source_id = affiliate.id
            rep_aff_id = int(claims.get('aff_id'))
            action_name = claims.get('client_name')

        # Update booking cancellation status.
        if source == AffiliateWalletLogs.SOURCE_AFFILIATE:
            locked_booking.valid = Bookings.CANCELLED_USER
        else:
            locked_booking.valid = Bookings.CANCELLED_D4M
        locked_booking.cancelled_dt = now

        # Update pending statuses.
        _update_driver_pending(locked_booking.driver)
        # Process penalties and wallet adjustments only if a driver is allocated.
        penalty, level = [0, 0], 0
        user_fine, driver_fine = 0, 0
        at_id = dt_id = None
        if locked_booking.driver != 1:
            # Record cancellation in Firestore for driver, if needed.
            fb_db.collection(u'book_cancelled_driver').document(str(locked_booking.driver)).set({
                str(locked_booking.id): {
                    u'book_time': locked_booking.starttime.strftime("%H:%M:%S"),
                    u'book_date': locked_booking.startdate.strftime("%Y-%m-%d")
                }
            }, merge=True)
            starttime = datetime(
                locked_booking.startdate.year, locked_booking.startdate.month,
                locked_booking.startdate.day, locked_booking.starttime.hour,
                locked_booking.starttime.minute, locked_booking.starttime.second
            )
            penalty, level = Price.get_cancel_ch(
                now, starttime, cancel_cat=reason, has_trip=has_trip,
                type=locked_booking.type, client_name=mapped_client
            )
            user_fine, driver_fine = penalty
            last_alloc = session.query(BookingAlloc.timestamp).filter(
                BookingAlloc.booking_id == locked_booking.id,
                BookingAlloc.driver_id == locked_booking.driver
            ).order_by(BookingAlloc.timestamp.desc()).first()
            if last_alloc and (reason in BookingCancelled.WAIVER and
                            now - last_alloc.timestamp < BookingCancelled.FORGIVE_DELTA):
                user_fine = driver_fine = 0

            if user_fine > 0:
                mapped_affiliate_wallet = session.query(Affiliate).filter(
                    Affiliate.id == affiliate.mapped_wallet_affiliate
                ).first()
                if not mapped_affiliate_wallet:
                    return {'success': -1, 'message': 'Mapped affiliate wallet not found'}
                cancel_wallet_logs = AffiliateWalletLogs(
                    -user_fine * 100,
                    method=f"Cancellation charges #{locked_booking.code}",
                    aff_id=rep_aff_id,
                    rep_id=rep_id,
                    from_account=mapped_affiliate_wallet.id,
                    wallet_before=mapped_affiliate_wallet.wallet,
                    wallet_after=mapped_affiliate_wallet.wallet - user_fine,
                    source=source
                )
                mapped_affiliate_wallet.wallet -= user_fine
                session.add(cancel_wallet_logs)
                at_id = cancel_wallet_logs.id

            if driver_fine > 0:
                driver_details = session.query(DriverDetails).filter(
                    DriverDetails.driver_id == locked_booking.driver
                ).first()
                if driver_details:
                    wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)
                    driver_trans = DriverTrans(
                        locked_booking.driver,
                        -driver_fine * 100,
                        wall_a=wallet,
                        wall_b=driver_details.wallet,
                        with_a=withdrawable,
                        with_b=driver_details.withdrawable,
                        method=f"Cancellation: Booking {locked_booking.code}",
                        status=DriverTrans.COMPLETED,
                        stop=True
                    )
                    session.add(driver_trans)
                    dt_id = driver_trans.id
                    driver_details.owed += driver_fine
                    driver_details.wallet = wallet
                    driver_details.withdrawable = withdrawable
                    session.flush()

        # Create BookingCancelled log and related records.
        dc = DriverCancelled(locked_booking.driver, locked_booking.id, driver_fine)
        bc = BookingCancelled(
            user=source_id,
            cancel_source=cancel_source,
            booking=locked_booking.id,
            uid=None,
            did=locked_booking.driver,
            penalty_user=user_fine,
            penalty_driver=driver_fine,
            rsn=reason,
            reason_detail=reason_detail,
            atransid=at_id,
            dtransid=dt_id
        )
        session.add(bc)
        session.flush()  # flush to generate bc.id

        if rep_id is None:
            rep_book = AffBookingLogs(admin_id=source_id, aff_id=affiliate.id, cancel_id=bc.id)
        else:
            rep_book = AffBookingLogs(rep_id=rep_id, aff_id=rep_aff_id, cancel_id=bc.id)
        session.bulk_save_objects([rep_book, dc])

        if source == AffiliateWalletLogs.SOURCE_ADMIN:
            notification = {
                'book_code': locked_booking.code,
                'type': 'Cancelled by Admin'
            }
            send_notification_to_affiliate(notification, 'Cancelled by Admin', affiliate.id)
        else:
            affiliate_notification = {
                'book_code': locked_booking.code,
                'type': 'Cancelled by Affiliate',
                'dest_client_name': affiliate.client_name,
                'rep_name': claims.get('fullname'),
                'client_name': action_name
            }
            send_notification_to_affiliate(affiliate_notification, 'Cancelled by Affiliate', affiliate.id,
                                            src_rep=rep_id, src_aff_id=rep_aff_id)
            admin_notification = {
                'id': locked_booking.id,
                'type': claims.get('client_name', 'unknown'),
                'username': action_name,
                'content': f'Booking canceled by affiliate: {action_name}. Code: {locked_booking.code}',
                'imageUrl': '/assets/icons/action/admin-cross.svg',
                'timestamp': int(time.time() * 1000)
            }
            send_notification_to_channel(admin_notification, 'Cancelled by Affiliate:' + str(locked_booking.region))

        send_live_update_of_booking(locked_booking.id, locked_booking.region)
        send_live_aff_booking_table(locked_booking.id, channel='table', dest_aff_id=aff_book_logs.aff_id,
                                    booking_region=locked_booking.region,tz=tz)
        if trip:
            driver_det = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.id == locked_booking.driver).first()
            driver_data = {
                'driver_id': driver_det[0].id,
            }   
            live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel= 'update_driver')
        return {
            'success': 1,
            'message': 'Booking cancelled successfully',
            'penalty': penalty
        }

    except Exception as e:
        # catch anything—log it if you have a logger
        print(f"[ERROR][_process_cancel_logic] {e}", flush=True)
        # We roll back in the outer function; just return failure here
        return {'success': -2, 'message': f'Internal error: {e}'}
    
def process_booking_cancel(booking_code: str, reason: int, source: int, reason_detail: str, rep_id: int,
                           claims: dict, is_admin_call: int = 0,testing=0,tz='Asia/Kolkata') -> dict:
    """
    Process a booking cancellation based on the given booking code, reason, source, and other details.
    Returns a dictionary with keys: 'success', 'message', and optionally 'penalty'.
    """
    print(booking_code,flush=True)
    with app.app_context():
        if testing:
            print("testing")
            session = db.session
            print(session)
        else:
            print("not testing")
            engine = db.engine.execution_options(timeout=30)
            Session = sessionmaker(bind=engine)
            session = Session()
        now = datetime.utcnow()
        try:
            if testing:
                result = _process_cancel_logic(session, booking_code, reason, source, reason_detail, rep_id, claims, is_admin_call, now,tz)
            else:
                with session.begin():
                    result =_process_cancel_logic(session, booking_code, reason, source, reason_detail, rep_id, claims, is_admin_call, now,tz)
            if result.get('success') != 1:
                if not testing:
                    session.rollback()
                return result
            return result
        except Exception as ex:
            # Unexpected error: rollback, close session, return error dict
            if not testing:
                session.rollback()
            print(f"[ERROR][process_booking_cancel] {ex}", flush=True)
            return {'success': -2, 'message': f'Internal Server Error: {ex}'}


@aff_book.route('/api/affiliate/driver/allocate', methods=['POST'])
@jwt_required()
def driver_allocate():
    try:
        rep_id = get_jwt_identity()
        claims = get_jwt()
    except Exception as e:
        return jsonify({'success': -1, 'message':'Failed to get indentity'}), 401

    if not complete(request.form, ['book_code', 'driver_id']):
        return jsonify({'success': -1,'message':'Incomplete Form Details'}), 400
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    rep_aff_id = int(claims.get('aff_id'))
    booking_code = request.form['book_code']
    try:
        driver_id = int(request.form['driver_id'])
    except ValueError:
        return jsonify({'success': -1, 'message': 'Invalid driver ID'}), 400

    with app.app_context():
        engine = db.engine.execution_options(timeout=30)
        Session = sessionmaker(bind=engine)
        session = Session()
        try:
            with session.begin():
               
                result = (session.query(Bookings, AffBookingLogs, Affiliate, Trip)
                    .outerjoin(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
                    .outerjoin(Affiliate, AffBookingLogs.aff_id == Affiliate.id)
                    .outerjoin(Trip, Trip.book_id == Bookings.id)
                    .filter(Bookings.code == booking_code)
                    .with_for_update()
                    .first())

                if not result:
                    return jsonify({'success': -1, 'message': 'Booking not found'}), 404

                booking, aff_book_logs, affiliate, trip = result

                original_driver = booking.driver
                if original_driver == driver_id:
                    return jsonify({'success': -3, 'message': 'Allocating same driver'}), 400

                driver = session.query(Users, Drivers).filter(
                    Users.id == Drivers.user,
                    Drivers.approved == 1,
                    Drivers.id == driver_id
                ).first()
                  
                if not driver:
                    return jsonify({'success': -2, 'message': 'Driver not found'}), 404

                driver_change = int(request.form.get('change', 0))

                if driver_change:
                    try:
                        reason = int(request.form.get('reason', BookingCancelled.RSN_OTHER))
                    except (KeyError, ValueError):
                        return jsonify({"success": 0, "message": "Invalid reason code."}), 400

                    reason_details = request.form.get('reason_details', "") or get_cancel_reason(reason)

                    mapped_by_alias = aliased(Affiliate)
                    mapped_wallet_alias = aliased(Affiliate)

                    book_logs, mapped_affiliate, mapped_affiliate_wallet = (
                        session.query(
                            AffBookingLogs,
                            mapped_by_alias,
                            mapped_wallet_alias
                        )
                        .join(mapped_by_alias, mapped_by_alias.id == AffBookingLogs.mapped_by)
                        .join(mapped_wallet_alias, mapped_wallet_alias.id == AffBookingLogs.mapped_wallet)
                        .filter(AffBookingLogs.book_id == booking.id)
                        .first()
                    )
                    mapped_client = mapped_affiliate.client_name

                    has_trip = trip is not None
                    if trip and trip.status < Trip.TRIP_INIT:
                        return jsonify({'success': -2, 'message': 'Cannot change driver after check-in'}), 400

                    penalty, _ = Price.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate, booking.starttime),
                                                cancel_cat=reason, has_trip=has_trip, type=booking.type, client_name=mapped_client)
                    _, driver_fine = penalty
                    dt_id = None
                    if driver_fine > 0:
                        details = session.query(DriverDetails).filter(DriverDetails.driver_id == original_driver).first()
                        wallet, withdrawable = compute_driver_wallet(details, driver_fine)
                        dt = DriverTrans(original_driver, -driver_fine * 100, wall_a=wallet, wall_b=details.wallet,
                                        with_a=withdrawable, with_b=details.withdrawable,
                                        method=f"Cancellation: Booking {booking.code}",
                                        status=DriverTrans.COMPLETED, stop=True)
                        session.add(dt)
                        session.flush()
                        dt_id = dt.id
                        details.owed += driver_fine
                        details.wallet = wallet
                        details.withdrawable = withdrawable
                    if original_driver != 1:
                        _update_driver_pending(original_driver)
                    bc = BookingCancelled(None, cancel_source=BookingCancelled.SRC_USER, booking=booking.id,
                                    uid=None, did=original_driver, penalty_user=0,
                                    penalty_driver=driver_fine, rsn=reason, reason_detail=reason_details,
                                    dtransid=dt_id, cancel_type=BookingCancelled.TYPE_UNALLOCATION)
                    session.add(bc)
                    session.flush()
                    rep_book_cancel = AffBookingLogs(rep_id=rep_id, aff_id=rep_aff_id, cancel_id=bc.id)
                    session.add(rep_book_cancel)
                pending_entry = session.query(BookPending). \
                    filter(BookPending.book_id == booking.id, BookPending.driver == driver[1].id).first()
                if pending_entry:
                    ba = BookingAlloc(booking.id, driver[1].id, None, phase=pending_entry.phase, score=pending_entry.score)
                    session.delete(pending_entry)
                else:
                    ba = BookingAlloc(booking.id, driver[1].id, None)

                try:
                    old_doc = AffiliateCollections.affiliates_book.find_one({"book_ref": booking.id})
                    mongo_result = AffiliateCollections.affiliates_book.update_one(
                        {"book_ref": booking.id},
                        {"$unset": {"pending_state": ""}}
                    )
                except PyMongoError as e:
                    session.rollback()
                    return jsonify({'success': 0, 'message': "Mongo update failed", 'error': str(e)}), 500
                # Final DB operations
                booking.driver = driver_id
                booking.valid = 1
                session.query(BookPending).filter(BookPending.book_id == booking.id).delete(synchronize_session=False)
                session.add(ba)
                session.flush()

                rep_alloc_log = AffBookingLogs(rep_id=rep_id, aff_id=rep_aff_id, alloc_id=ba.id)
                session.add(rep_alloc_log)
                notification_data = {
                    'book_code': booking.code,
                    'type': 'Allocate',
                    'rep_name': claims.get('fullname'),
                    'client_name': claims.get('client_name')
                }
                _update_driver_pending(booking.driver)
                send_notification_to_affiliate(notification_data, "Allocate", rep_id, rep_aff_id, affiliate.id)
                live_update = {
                    'book_id': booking.id,
                    'book_valid': booking.valid
                }
                send_live_update_to_affiliate(live_update, "Allocation", affiliate.id, booking.region)
                send_live_update_of_booking(booking.id, booking.region)
                send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=aff_book_logs.aff_id, booking_region=booking.region)
        except ValueError as ve:
            session.rollback()
            return jsonify({'success': 0, 'message': str(ve)}), 400
        except exc.SQLAlchemyError as e:
            session.rollback()
            if old_doc:
                AffiliateCollections.affiliates_book.replace_one({"book_ref": booking.id}, old_doc)
            return jsonify({'success': 0, 'message': 'Database error', 'error': str(e)}), 500
        finally:
            session.close()
    
    return jsonify({'success': 1, 'message': 'Driver allocated successfully'}), 200


def resolve_trip_name_value(affiliate_name: str, trip_name: int):
    """
    Given an affiliate_name and a numeric trip_name key, returns
    (trip_name_value, error_response, status_code). If lookup fails,
    trip_name_value will be None and error_response/status_code will be set.
    """
    mapping = AFFILIATE_TYPE_MAPPING.get(affiliate_name)
    if mapping is None:
        return None, jsonify({"error": "Affiliate name not found"}), 400

    trip_name_value = mapping.get(trip_name)
    if trip_name_value is None:
        return None, jsonify({"error": "Not a valid trip_name"}), 400

    return trip_name_value, None, None


@aff_book.route('/api/affiliate/book_trip', methods=['POST'])
@jwt_required()
def affiliate_search_and_book():
    # -- Authentication and JWT claims --
    try:
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    claims = get_jwt()
    rep_aff_id = int(claims.get('aff_id'))
    rep_id = int(claims.get('id'))
    if not rep_aff_id or not rep_id:
        return jsonify({'success': -1, 'message': "Invalid JWT claims"}), 401

    allowed_affiliates = claims.get("aff_slaves", [])
    dashboard = int(request.form.get('dashboard', '0'))
    # -- Validate that all necessary fields for search and booking exist --
    required_fields = ['trans_type', 'trip_type', 'city', 'reflat', 'reflong', 'time',
        'loc', 'remark', 'vehicle_no', 'source_spoc_name', 'source_spoc_contact',
        'dest_spoc_name', 'dest_spoc_contact'
    ]
    if not complete(request.form, required_fields):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

    # -- Affiliate authorization check --
    affiliate_id = int(rep_aff_id)
    if rep_aff_id != affiliate_id and affiliate_id not in allowed_affiliates:
        return jsonify({'success': -3, 'message': "Unauthorized access"}), 403

    # -- Search / Estimate parameters --
    source = claims['client_name']
    affiliate_name = request.form.get('affiliate_name', '').lower()
    trip_type = int(request.form['trip_type'])

    if trip_type != BookingParams.TYPE_ONEWAY:
        return jsonify({'success': -1, 'message': 'Invalid trip type. Only one-way trips are allowed.'}), 400

    # For one-way trips, duration may be omitted
    dur = request.form['dur'] if trip_type != BookingParams.TYPE_ONEWAY else None
    city = int(request.form['city'])
    trans_type = request.form['trans_type']
    reflat = float(request.form['reflat'])
    reflong = float(request.form['reflong'])
    time = convert_datetime_to_utc(request.form['time'], tz=request.headers.get('X-Timezone', 'Asia/Kolkata'))
    # try:
    #     time = datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
    # except ValueError:
    #     try:
    #         time = datetime.strptime(request.form['time'], "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
    #     except ValueError:
    #         time = datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    # Optional destination coordinates (used for calculating distance)
    try:
        dest_lat = float(request.form.get('dest_lat', 0.0))
        dest_long = float(request.form.get('dest_long', 0.0))
        dest_exists = 'dest_lat' in request.form and 'dest_long' in request.form
    except ValueError:
        dest_lat, dest_long, dest_exists = 0.0, 0.0, False
    dist = get_dist_on_map(reflat, reflong, dest_lat, dest_long) if dest_exists else 0

    if dist == 0 and trip_type != BookingParams.TYPE_ROUNDTRIP:
        return jsonify({'success': -1, 'message': "Distance cannot be zero"})
    # -- Perform search estimates and collect search entries --
    results = []
    # only one booking is allowed
    estimate_result, search_entry = get_affiliate_estimate(
        affiliate_id, rep_id, reflat, reflong, dest_lat, dest_long,
        trip_type, trans_type, time, dur, dist, city, source
    )

    result_json = estimate_result.get_json()

    if not dashboard:
        modified_estimates = {"customer_total_fare": result_json.get('cust_total_fare')}
        results.append(modified_estimates)
    else:
        results.append(result_json)

    try:
        db.session.add(search_entry)
        db.session.commit()
    except exc.IntegrityError as e:
        print(e)
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error'})

    # -- Booking parameters --
    # Use the saved search entry IDs for booking creation.
    search_id = search_entry.id
    vehicle_no = request.form['vehicle_no']
    appointment_id = request.form.get('appointment_id', "")
    vehicle_model = request.form.get('vehicle_model', "")
    src_nickname_id = request.form.get('src_nickname_id', '0')
    dest_nickname_id = request.form.get('dest_nickname_id', '0')
    loc = request.form.get('loc', "N/A")
    remark = request.form['remark']
    trip_name = int(request.form.get('trip_name', '0'))
    # Separate out any known fields from custom data
    form_data = request.form.to_dict()
    fixed_data = {k: form_data.pop(k, None) for k in KNOWN_FIELDS_API}
    trip_name_value=""
    # Assume form_data is the dictionary from the request
    search_data = {k: form_data.pop(k, None) for k in SEARCH_FIELDS}
    custom_data = form_data
    custom_data.pop('affiliate_name', None) #only in this api
    custom_data_without_spoc = custom_data.copy()
    for key in SPOC_FIELDS:
        custom_data_without_spoc.pop(key, None)

    trip_name_value, err_resp, err_status = resolve_trip_name_value(affiliate_name, trip_name)
    if err_resp:
        return err_resp, err_status

    try:
        dest_loc = request.form['dest_loc']
    except Exception:
        dest_loc = "N/A"

    total_price = result_json.get('cust_total_fare')

    # -- Check affiliate wallet balance --
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
    mapped_wallet_affiliate_id = affiliate.mapped_wallet_affiliate
    mapped_affiliate_wallet = db.session.query(Affiliate).filter(Affiliate.id == mapped_wallet_affiliate_id).first()

    limit = mapped_affiliate_wallet.wallet + mapped_affiliate_wallet.wallet_threshold
    if total_price > limit:
        return jsonify({'success': -2, 'message': 'Not enough credits'})

    affiliate_mongo_details = AffiliateCollections.affiliates_details.find_one({'affiliate_id': affiliate_id})

    field_form = {}
    if trip_type == BookingParams.TYPE_ONEWAY:
        field_form = affiliate_mongo_details.get('form_field_oneway', {}).get('add_custom_fields', {})
    elif trip_type in [BookingParams.TYPE_ROUNDTRIP, BookingParams.TYPE_OUTSTATION]:
        field_form = affiliate_mongo_details.get('form_field_round', {}).get('add_custom_fields', {})

    allowed_keys = {to_camel_case(field["label"]) for field in field_form if "label" in field}
    converted_custom = {}
    invalid_raw    = []

    # convert each raw key to camelCase, validate
    for key, value in custom_data_without_spoc.items():
        if key in allowed_keys:
            converted_custom[key] = value
        else:
            invalid_raw.append(key)

    if invalid_raw:
        return jsonify({"error": "Invalid custom field(s)","invalid_fields": invalid_raw}),400

    # Create booking for search entry.
    booking_code, response, status = create_book(
        search_entry, affiliate_id, mapped_wallet_affiliate_id, loc, dest_lat, dest_long, dest_loc,dest_exists,
        remark, custom_data, trip_type, trip_name_value, claims.get('client_name'),
        claims, vehicle_no, src_nickname_id, dest_nickname_id,
        vehicle_model=vehicle_model, appoint_id=appointment_id
    )
    if status != 200:
        print("Error in {}: {}, {}".format(search_id, response[0], response[1]))
        return response

    return jsonify({'success': 1,
                    'msg': "Booking created successfully",
                    "estimates": results,
                    "book_code": booking_code,
                    "tracking_link":f"https://www.drivers4me.com/track/booking/{booking_code}"})

def storage_url(filename: str):
    """
    Returns the full storage URL if filename is valid; otherwise returns None.
    """
    if filename:
        return f"https://storage.drivers4me.com/static/uploads/{filename}"
    return None

def construct_image_links(image_dict: dict, booking_id: str) -> dict:
    """
    Constructs full URLs for image filenames stored in trip_start_images or trip_stop_images.

    :param image_dict: Dictionary of image keys and filenames
    :param booking_md: Mongo document for the booking (should ideally contain numeric ID)
    :param booking_id: Original booking reference ID (e.g., 'BK731720')
    :return: Dictionary with same keys, but values replaced with full URLs
    """
    # Prefer numeric ID from DB, else extract digits from booking_id
    
    return {
        key: f"https://storage.drivers4me.com/static/uploads/book-{booking_id}/{filename}"
        for key, filename in image_dict.items()
        if filename  # Skip if filename is None or empty
    }

def get_affiliate_book_state(booking_id,booking_code,tz):
    try:
        action_mapping = {
            TripLog.ACTION_INIT: 2,
            TripLog.ACTION_REACHED_SRC: 3,
            TripLog.ACTION_START: 4,
            TripLog.ACTION_START_PIC: 4,
            TripLog.ACTION_REACHED_DEST: 5,
            TripLog.ACTION_STOP: 6,
            TripLog.ACTION_STOP_PIC: 6
        }
        book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
        trip_obj = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
        booking = book_obj.first()
        if booking is None or booking.type != BookingParams.TYPE_B2B:
            return jsonify({
                    "status": 404,
                    "result": "FAILURE",
                    "message": "Booking Not Found"
                }), 404
        trip_logs = (
            db.session.query(
                TripLog.timestamp.label('timestamp'),
                func.concat(Users.fname, " ", Users.lname).label('driver_name'),
                Users.mobile.label('driver_mobile'),
                case(
                    *[(TripLog.action == key, value) for key, value in action_mapping.items()],
                    else_=None  # Default value if no match is found
                ).label('booking_state'),
            )
            .join(Users, TripLog.driver_user == Users.id)
            .filter(TripLog.booking_id == booking_id)
        )
        booking_cancels = (
        db.session.query(
            BookingCancelled.timestamp.label('timestamp'),
            case(
                (BookingCancelled.did == 1, "-"),  # Pass individual conditions as positional arguments
                else_=func.concat(Users.fname, " ", Users.lname)
            ).label("driver_name"),
            case(
                (BookingCancelled.did == 1, "-"),  # Pass individual conditions as positional arguments
                else_=Users.mobile
            ).label("driver_mobile"),
            case(
                (BookingCancelled.cancel_source == BookingCancelled.SRC_USER, -1),  # Pass individual conditions
                (BookingCancelled.cancel_source == BookingCancelled.SRC_DRIVER, 8),
                (and_(BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,BookingCancelled.cancel_type == BookingCancelled.TYPE_UNALLOCATION),8),
                else_=-2
            ).label("booking_state"),

        )
        .join(Drivers, BookingCancelled.did == Drivers.id)
        .join(Users, Users.id == Drivers.user)
        .filter(BookingCancelled.booking == booking_id)
        )

        booking_allocs = (
            db.session.query(
                BookingAlloc.timestamp.label('timestamp'),
                func.concat(Users.fname, " ", Users.lname).label('driver_name'),
                Users.mobile.label('driver_mobile'),
                db.literal(1).label('booking_state'),
            )
        .join(Drivers, BookingAlloc.driver_id == Drivers.id)  # Join BookingCancelled to Drivers
            .join(Users, Users.id == Drivers.user)  # Join Drivers to Users
            .filter(BookingAlloc.booking_id == booking_id)
        )
        combined_logs = union_all(trip_logs, booking_allocs, booking_cancels).alias('combined_logs')  # Add alias here

        # Fetch and sort combined logs
        booking_history = (
            db.session.query(
                combined_logs.c.timestamp.label("timestamp"),
                combined_logs.c.booking_state.label("booking_state"),
                combined_logs.c.driver_name.label("driver_name"),
                combined_logs.c.driver_mobile.label("driver_mobile"),
            )
            .order_by(desc(combined_logs.c.timestamp))
            .all()
            )
        filtered_logs = []
        last_state = None

        for book in booking_history:
            if book.booking_state in [4, 6]:
                # Only add the log if the state has changed from the previous log
                if book.booking_state != last_state:
                    filtered_logs.append(book)
                    last_state = book.booking_state
            else:
                filtered_logs.append(book)
                last_state = book.booking_state

        print("all good",flush=True)
        booking_history_list = [
        {
        "timestamp": convert_to_local_time(book.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S"),
        "booking_state": book.booking_state,
        "driver_name": book.driver_name,
        "driver_mobile": book.driver_mobile,
        }
        for book in filtered_logs
        ]

        driver_search = (
        db.session.query(AffiliateDriverSearch.timestamp.label("timestamp"))
        .filter(AffiliateDriverSearch.id == booking.search_key)
        .first()
            )

        if driver_search :
            initial_entry = {
            "timestamp": convert_to_local_time(driver_search.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S"),
            "booking_state": 0,
            "driver_name": '-',
            "driver_mobile": '-',
            }
            booking_history_list.append(initial_entry)

        driver_user=get_driver_user_id(booking.driver)
        driver_details = db.session.query(Users).filter(Users.id == driver_user).first()
        driver = db.session.query(Drivers).filter(Drivers.id == booking.driver).first()

        driver_info = {
            "driver_name": driver_details.get_name() if driver_details else "-",
            "driver_mobile": driver_details.mobile if driver_details else "-",
            "license_no": driver.licenseNo if driver else "-",
            "license_doc_url": storage_url(driver.licenseDoc) if driver and driver.licenseDoc else None,
            "driver_pic_url": storage_url(driver.pic) if driver and driver.pic else None
        }


        trip = fetch_booking_trip(booking_id).first()
        is_ontrip = trip is not None
        book_cancel = db.session.query(BookingCancelled) \
                .filter(BookingCancelled.booking == booking_id) \
                .order_by(BookingCancelled.id.desc()) \
                .first()
        reason_detail_mapping = BookingCancelled.BOOKING_CANCELLATION_REASON_MAPPING
        reason_cancel=""
        if book_cancel:
            if book_cancel.reason in reason_detail_mapping: # means if b2b reason then show them as it is but for old reasons of D4M cancel show 'BOOKING_CANCELLED_BY_D4M' reason
                    reason_cancel = reason_detail_mapping.get(book_cancel.reason, 'OTHER_REASON')
            else:
                    reason_cancel = 'BOOKING_CANCELLED_BY_D4M'

        affiliate_booking_logs = db.session.query(AffBookingLogs).filter_by(book_id=booking_id).first()

        logs = None
        if affiliate_booking_logs:
            logs = {key: value for key, value in affiliate_booking_logs.__dict__.items() if key != '_sa_instance_state'}
        else:
            logs = None


        booking_md = AffiliateCollections.affiliates_book.find_one({"book_ref": booking_id}) or {}
        trip_start_images = booking_md.get("trip_start_images") or {}
        trip_stop_images = booking_md.get("trip_stop_images") or {}
        

        trip_start_image_links = construct_image_links(trip_start_images,booking_id) or None
        trip_stop_image_links = construct_image_links(trip_stop_images, booking_id) or None

        if booking.valid== Bookings.UNALLOCATED and book_cancel:
            if book_cancel.cancel_source ==  BookingCancelled.SRC_DRIVER:
                 return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":8,
                        "cancel_reason": reason_cancel,
                        "driver_details": driver_info,
                    "booking_history": booking_history_list,
                        "booking_status":'Booking Unallocated',
                        'timestamp':convert_to_local_time(book_cancel.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
            elif book_cancel.cancel_source ==  BookingCancelled.SRC_ADMIN:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state": 8,
                        "driver_details": driver_info,
                    "booking_history": booking_history_list,
                        "booking_status":'Booking Unallocated by D4M',
                        'timestamp':convert_to_local_time(book_cancel.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200

        if booking.valid== Bookings.UNALLOCATED :
            return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":0,
                    "booking_status":'Booking Created, not allocated to driver',
                    "booking_history": booking_history_list,
                    'timestamp':convert_to_local_time(driver_search.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S") if driver_search else None
                    }), 200
        elif booking.valid== Bookings.ALLOCATED:
            booking_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking_id)
            if not is_ontrip:
                return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":1,
                    "driver_details":driver_info,
                    "booking_status":'Booking allocated to Driver',
                     "booking_history": booking_history_list,
                    'timestamp':convert_to_local_time(booking_alloc_time.first().timestamp,tz).strftime("%Y-%m-%d %H:%M:%S") if booking_alloc_time else None
                    }), 200
            else:
                trip_log = db.session.query(TripLog).filter(TripLog.booking_id == booking_id).order_by(desc(TripLog.timestamp)).first()
                timestamp_last_state = convert_to_local_time(trip_log.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S") if trip_log else None
                if trip.status==Trip.TRIP_INIT:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":2,
                    "booking_status":'Driver On the Way',
                   "booking_history": booking_history_list,
                   "tracking_link":f"https://www.drivers4me.com/track/booking/{booking_code}",
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status==Trip.TRIP_REACHED_SRC:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":3,
                    "driver_details": driver_info,
                    "tracking_link":f"https://www.drivers4me.com/track/booking/{booking_code}",
                    "trip_images" : {
                        "trip_start_images": trip_start_image_links if trip_start_image_links else "No images available",
                        "trip_stop_images": trip_stop_image_links if trip_stop_image_links else "No images available"
                    },
                    "booking_status":'Driver Reached Source Location - Check In',
                    "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status in [Trip.TRIP_START_PIC, Trip.TRIP_STARTED]:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":4,
                    "booking_status":'OnGoing Trip',
                    "trip_images" : {
                        "trip_start_images": trip_start_image_links if trip_start_image_links else "No images available",
                        "trip_stop_images": trip_stop_image_links if trip_stop_image_links else "No images available"
                    },
                    "driver_details": driver_info,
                    "tracking_link":f"https://www.drivers4me.com/track/booking/{booking_code}",
                    "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status==Trip.TRIP_REACHED_DEST:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "driver_details": driver_info,
                    "trip_images" : {
                        "trip_start_images": trip_start_image_links if trip_start_image_links else "No images available",
                        "trip_stop_images": trip_stop_image_links if trip_stop_image_links else "No images available"
                    },
                    "booking_state":5,
                     "booking_history": booking_history_list,
                    "booking_status":'Driver Reached Destination Location',
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status in [Trip.TRIP_STOPPED, Trip.TRIP_STOP_PIC]:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":6,
                    "driver_details": driver_info,
                    "trip_images" : {
                        "trip_start_images": trip_start_image_links if trip_start_image_links else "No images available",
                        "trip_stop_images": trip_stop_image_links if trip_stop_image_links else "No images available"
                    },
                    "customer_fare": trip_obj.price,
                    "booking_status":'Trip Completed',
                     "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
        elif booking.valid <  Bookings.UNALLOCATED:
            print("all good 7",flush=True)
            if booking.valid ==  Bookings.CANCELLED_USER:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":-1,
                        "cancel_reason": reason_cancel,
                        "driver_details": driver_info,
                         "booking_history": booking_history_list,
                        "booking_status":'Cancelled by Affiliate',
                        'timestamp':convert_to_local_time(book_cancel.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
            else:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":-2,
                        "cancel_reason": reason_cancel,
                        "driver_details": driver_info,
                        "booking_status":'Cancelled by D4M',
                        "booking_history": booking_history_list,
                        'timestamp': convert_to_local_time(book_cancel.timestamp,tz).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
    except Exception as e:
        return jsonify({
                "error": str(e),
                "status": 500,
                "message": "Internal Server Error"
            }), 500


@aff_book.route('/api/affiliate/book_state', methods=['POST'])
@jwt_required()
def affiliate_booking_state():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({
                "status": 401,
                "result": "FAILURE",
                "message": "Unauthorized Access."
            }), 401
    # claims = get_jwt()
    # if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
    #     return jsonify({
    #             "status": 403,
    #             "result": "FAILURE",
    #             "message": "User not allowed to check state."
    #         }), 403
    if not complete(request.form, ['booking_code']):
        return jsonify({
                "status": 400,
                "result": "FAILURE",
                "message": "Incomplete form details."
            }), 400
    booking_code = request.form['booking_code']
    booking = db.session.query(Bookings).filter(Bookings.code== booking_code).first()
    if not booking:
        return jsonify({
                "status": 404,
                "result": "FAILURE",
                "message": "Booking Not Found"
            }), 404
    booking_id=booking.id
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    return get_affiliate_book_state(booking_id,booking_code,tz)

@aff_book.route('/api/affiliate/booking/partial/details', methods=['POST'])
@jwt_required()
def fetch_popup_booking_details():
    try:
        rep_id = get_jwt_identity()
        if not rep_id:
            return jsonify({'success': -1, 'msg': "Unauthorized"}), 401
    except Exception:
        return jsonify({'success': -1, 'msg': "Unauthorized"}), 401

    try:
        if not complete(request.form, ['booking_id']):
            return jsonify({'success': -1, 'message': "Incomplete form"}), 400

        book_id = int(request.form['booking_id'])

        book = db.session.query(
                Bookings, Trip, BookDest, BookPricing, AffiliateDriverSearch
            ).outerjoin(Trip, Bookings.id == Trip.book_id) \
            .outerjoin(BookDest, Bookings.id == BookDest.book_id) \
            .outerjoin(BookPricing, Bookings.id == BookPricing.book_id) \
            .outerjoin(AffiliateDriverSearch, Bookings.search_key == AffiliateDriverSearch.id) \
            .filter(Bookings.id == book_id).first()

        if not book:
            return jsonify({'success': -2, "message": "Booking does not exist"}), 400

        booking, trip, book_dest, book_pricing, affsearch = book

        trip_pricing = db.session.query(TripPricing).filter(TripPricing.book_id == book_id).first()

        # Fetch Booking Metadata from MongoDB
        booking_md = AffiliateCollections.affiliates_book.find_one({"book_ref": book_id}) or {}

        # Handle Trip Type Details (Avoid Repeating Redis and MongoDB Calls)
        trip_type = booking_md.get("trip_type", "NA")
        src_nickname_id = int(booking_md.get("src_nickname_id", "0"))
        dest_nickname_id = int(booking_md.get("dest_nickname_id", "0"))
        affiliate_id = int(booking_md.get('affiliate_id'))
        trip_type_label = "N/A"
        trip_type_placeholder = "N/A"
        form_field = {}

        affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()

        src_nickname = ""
        dest_nickname = ""
        src_address = db.session.query(AffiliateAddress).filter(AffiliateAddress.add_id == src_nickname_id).first()
        if src_address:
            src_nickname = src_address.nickname

        dest_address = db.session.query(AffiliateAddress).filter(AffiliateAddress.add_id == dest_nickname_id).first()
        if dest_address:
            dest_nickname = dest_address.nickname

        client_name = affiliate.client_name

        affiliate_data = AffiliateCollections.affiliates_details.find_one({'affiliate_id': affiliate_id}) or {}
        trip_type_label = affiliate_data.get('tripTypeLabel', "N/A")
        trip_type_placeholder = affiliate_data.get('tripTypePlaceholder', "N/A")
        form_field_key = 'form_field_oneway' if trip_type == "One Way" else 'form_field_round'
        form_field = affiliate_data.get(form_field_key, {})

        # Exclude 'add_custom_fields'
        filtered_form_field = {key: value for key, value in form_field.items() if key != 'add_custom_fields'}

        # Fetch Driver Details (Safer Query)
        driver_user = db.session.query(Users, Drivers, DriverInfo) \
                        .join(Drivers, Drivers.user == Users.id) \
                        .join(DriverInfo, DriverInfo.driver_id == Drivers.id) \
                        .filter(Drivers.id == booking.driver).first() if booking else None

        # Build Response
        data = {
            "basic_details": {
                'book_id': booking.id,
                'book_code': booking.code,
                'book_valid': booking.valid,
                'trip_status': trip.status if trip else -1,
                'book_car_type': booking.car_type,
                'book_driver_rating': booking.driver_rating,
                'book_date_time': booking.created_at.strftime("%d/%m/%Y %H:%M:%S"),
                'book_account': client_name,
                'affiliate_wallet': affiliate.wallet,
                'book_account_id': affiliate_id,
                'vehicle_no': booking_md.get("vehicle_no", "NA"),
                'trip_name': booking_md.get("trip_name", "NA"),
                'trip_type': trip_type,
                'vehicle_model': booking_md.get("vehicle_model", ""),
                'appoint_id': booking_md.get("appointment_id", ""),
                'custom_data': booking_md.get("custom_data"),
                'book_location': booking.loc if booking and booking.loc else "NA",
                'book_region': booking.region if booking else "NA",
            },
            "trip_schedule": {
                'start_date': booking.startdate.strftime("%d/%m/%Y"),
                'start_time': booking.starttime.strftime("%H:%M:%S"),
                'end_date': booking.enddate.strftime("%d/%m/%Y"),
                'end_time': booking.endtime.strftime("%H:%M:%S"),
                'duration': booking.dur.strftime("%H:%M:%S"),
                'days': booking.days
            },
            "trip_time": {
                'start_date_time': trip.starttime.strftime("%d/%m/%Y %H:%M:%S") if trip and trip.starttime else "NA",
                'end_date_time': trip.endtime.strftime("%d/%m/%Y %H:%M:%S") if trip and trip.endtime else "NA",
            },
            "location_details": {
                'src_loc': booking.loc,
                'distance': math.ceil(affsearch.dist/1000),
                'src_lat': booking.lat,
                'src_long': booking.long,
                'src_nickname_id': src_nickname_id,
                'src_nickname': src_nickname,
                'dest_nickname_id': dest_nickname_id,
                'dest_nickname': dest_nickname,
                'dest_loc': book_dest.name if book_dest else "NA",
                'dest_lat': book_dest.lat if book_dest else 0,
                'dest_long': book_dest.lng if book_dest else 0
            },
            "spoc_data": booking_md.get("spoc_data"),
            "driver_details": {
                'driver_id': booking.driver,
                'driver_pic': driver_user[1].pic,
                'driver_name': driver_user[0].get_name(),
                'driver_lic': driver_user[1].licenseNo,
                'driver_address': driver_user[2].pres_addr if driver_user[2] else "NA",
                'driver_region': driver_user[2].pres_region if driver_user[2] else "NA",
                'driver_mobile': driver_user[0].mobile

            }
        }
        if booking.valid < 0:
            booking_cancel = (
                    db.session.query(BookingCancelled)
                    .filter(BookingCancelled.booking == booking.id)
                    .order_by(BookingCancelled.timestamp.desc())
                    .first()
                )
            data['cancellation_reason'] = booking_cancel.reason_detail

        return jsonify({'success': 1, 'booking_id': book_id, 'data': data})
    except Exception as e:
        error_details = traceback.format_exc()
        app.logger.error(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e)}), 500


def handle_booking_alloc(entry,booking_details,is_b2c,client_name,user_names,driver_names,tz):
    driver_name = driver_names.get(entry.driver_id, "Unknown Driver")
    user_name = user_names.get(entry.alloc_id, "Unknown User")
    source=2
    reallocate=0
    alloc_by_driver=db.session.query(Drivers).filter(Drivers.user == entry.alloc_id).first()
    action_by_affiliate=db.session.query(Affiliate).filter(Affiliate.id == entry.alloc_id).first()
    action_by_admin=db.session.query(AdminAccess).filter(AdminAccess.admin_user_id == entry.alloc_id).first()
    reallocate_check=db.session.query(BookingAlloc).filter(BookingAlloc.booking_id==booking_details.booking_id).all()
    if alloc_by_driver and not action_by_affiliate:
        source = 1
    elif action_by_affiliate and is_b2c==0 and not action_by_admin:
        source = 0  # Explicitly set for clarity
    if is_b2c==0:
        rep_info=db.session.query(AffBookingLogs).filter(AffBookingLogs.alloc_id==entry.id).first()
        if rep_info:
            affiliate_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id ==rep_info.rep_id).first()
            if affiliate_rep:
                rep_name = affiliate_rep.fullname  # Assuming AffiliateRep has a 'name' attribute
            else:
                rep_name = "Unknown Rep"
        else:
            rep_name = "No Rep Assigned"
    return {
            'date_and_time': combine_and_convert_to_local(entry.timestamp.date(), entry.timestamp.time(),tz).strftime("%d %b %y,%H:%M:%S"),
            'driver_name': driver_name,
            'user_name': client_name if is_b2c == 0 else booking_details.customer_name,
            'action_type': 0,
            'action_source':source,
            'action_by':rep_name if source == 0 and  is_b2c == 0 else user_name,
            'driver_fine':"",
            'user_fine':"",
            'location_lat':"",
            'location_long':"",
            'reason':"",
            'cancel_id':"",
            'cancel_type':"",
            'cancel_reverse':""
    }

def handle_booking_cancel(entry,booking_details,is_b2c,client_name,user_names,driver_names,tz):
    driver_name = driver_names.get(entry.did, "Unknown Driver")
    cancel_src_name = user_names.get(entry.user, "Unknown Cancel Source")
    if is_b2c==0:
        rep_info=db.session.query(AffBookingLogs).filter(AffBookingLogs.cancel_id==entry.id).first()
        if rep_info:
            affiliate_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id ==rep_info.rep_id).first()
            if affiliate_rep:
                rep_name = affiliate_rep.fullname  # Assuming AffiliateRep has a 'name' attribute
            else:
                rep_name = "Unknown Rep"
        else:
            rep_name = "No Rep Assigned"
    return {
       'date_and_time': combine_and_convert_to_local(entry.timestamp.date(), entry.timestamp.time(),tz).strftime("%d %b %y,%H:%M:%S"),
            'driver_name': driver_name,
            'user_name': client_name if is_b2c == 0 else booking_details.customer_name,
            'action_type':1,
            'action_source':entry.cancel_source,
            'action_by':rep_name if entry.cancel_source == 0 and  is_b2c == 0 else cancel_src_name,
            'user_fine':entry.penalty_user,
            'location_lat':"",
            'location_long':"",
            'reason': entry.reason,
            'cancel_type':entry.cancel_type,
            'cancel_id':entry.id,
            'cancel_type':entry.cancel_type,
            'cancel_reversed': entry.cancel_reversed
    }

def handle_trip_log(entry,booking_details,is_b2c,client_name,user_names,tz):
    driver_name = user_names.get(entry.driver_user, "Unknown Driver")
    action_type = 2 + entry.action
    action_src_name = user_names.get(entry.action_user, "Unknown Action Source")
    source=2
    action_by_driver=db.session.query(Drivers).filter(Drivers.user == entry.action_user).first()
    if action_by_driver:
        source = 1
    return {
        'date_and_time': combine_and_convert_to_local(entry.timestamp.date(), entry.timestamp.time(),tz).strftime("%d %b %y,%H:%M:%S"),
            'driver_name':driver_name,
            'user_name': client_name if is_b2c == 0 else booking_details.customer_name,
            'action_type':action_type,
            'action_source':source,
            'action_by':action_src_name,
            'user_fine':"",
            'location_lat':entry.lat,
            'location_long':entry.lng,
            'reason':"",
            'cancel_id':"",
            'cancel_type':"",
            'cancel_reversed': ""
    }

def handle_booking_entry(entry,booking_details,is_b2c,client_name,claims):
    source=0
    if is_b2c==0:
        rep_info=db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id==entry.id).first()
        if rep_info:
            affiliate_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id ==rep_info.rep_id).first()
            if affiliate_rep:
                rep_name = affiliate_rep.fullname  # Assuming AffiliateRep has a 'name' attribute
            else:
                rep_name = "Unknown Rep"
        else:
            rep_name = "No Rep Assigned"
    return {
            'date_and_time':  combine_and_convert_to_local(entry.created_at.date(), entry.created_at.time()).strftime("%d %b %y,%H:%M:%S"),
            'driver_name':"Not Allocated",
            'user_name': client_name if is_b2c == 0 else booking_details.customer_name,
            'action_type':2,
            'action_source':source,
            'action_by':rep_name if is_b2c == 0 else booking_details.customer_name,
            'user_fine':"",
            'location_lat':"",
            'location_long':"",
            'reason':"",
            'cancel_id':"",
            'cancel_type':"",
            'cancel_reversed': "",
    }

@aff_book.route('/api/affiliate/booking/trip_log', methods=['POST'])
@jwt_required()
def trip_log_affiliate():
    try:
        claims = get_jwt()
        rep_aff_id = claims.get('aff_id')
        rep_id = claims.get('id')
        print(rep_aff_id,rep_id,flush=True)
        if not rep_aff_id:
            return jsonify({'error': 'Affiliate ID missing in token'}), 401
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        booking_id = request.form.get('booking_id')
        if not booking_id:
            return jsonify({'error': 'booking_id parameter is required'}), 400

        booking_id = int(booking_id)

        # Ensure booking is linked to affiliate
        aff_booking = db.session.query(AffBookingLogs).filter_by(book_id=booking_id).first()
        if not aff_booking:
            return jsonify({'result': -1, 'message': 'Booking not found or not linked to your affiliate'}), 404

        booking = db.session.query(Bookings).filter_by(id=booking_id).first()
        print(aff_booking,booking_id,booking,flush=True)

        if not booking:
            return jsonify({'result': -1, 'message': 'Booking not found'}), 404

        all_allocs = db.session.query(BookingAlloc).filter_by(booking_id=booking_id).all()
        all_trip_log = db.session.query(TripLog).filter_by(booking_id=booking_id).all()
        all_cancel = db.session.query(BookingCancelled).filter_by(booking=booking_id).all()

        user_ids = {entry.alloc_id for entry in all_allocs}.union(
            {entry.driver_user for entry in all_trip_log},
            {entry.action_user for entry in all_trip_log},
            {entry.user for entry in all_cancel}
        )
        driver_ids = {entry.driver_id for entry in all_allocs}.union({entry.did for entry in all_cancel})

        users = db.session.query(Users).filter(Users.id.in_(user_ids)).all()
        user_names = {user.id: user.get_name() for user in users}

        drivers = db.session.query(Drivers, Users).filter(Users.id == Drivers.user).filter(Drivers.id.in_(driver_ids)).all()
        driver_names = {driver[0].id: driver[1].get_name() for driver in drivers}

        booking_details = db.session.query(
            Bookings.code.label("booking_code"),
            Bookings.driver.label('booking_driver_id'),
            Bookings.created_at.label('booking_timestamp'),
            Bookings.region.label('booking_region'),
            Bookings.valid.label("book_valid"),
            Bookings.id.label("booking_id"),
            Bookings.user.label("booking_user"),
            Trip.status.label('trip_status'),
            func.concat(Users.fname, ' ', Users.lname).label('customer_name'),
            ).outerjoin(Users, Bookings.user == Users.id
            ).outerjoin(Trip, Bookings.id == Trip.book_id
            ).filter(Bookings.id == booking_id
            ).first()
        if not booking_details:
            return jsonify({'success': -1, 'message': 'Booking details not found'}), 404

        all_cancel_sorted = sorted(all_cancel, key=lambda x: x.id, reverse=True)
        entries = sorted(all_allocs + all_trip_log + all_cancel_sorted, key=lambda x: x.timestamp, reverse=True)
        entries.append(booking)

        affiliate = db.session.query(Affiliate).filter(Affiliate.id == rep_aff_id).first()
        client_name = affiliate.client_name if affiliate else ""

        result_json = []
        for entry in entries:
            if isinstance(entry, BookingAlloc):
                result_json.append(handle_booking_alloc(entry, booking_details, is_b2c=0, client_name=client_name, user_names=user_names, driver_names=driver_names,tz=tz))
            elif isinstance(entry, BookingCancelled):
                result_json.append(handle_booking_cancel(entry, booking_details, is_b2c=0, client_name=client_name, user_names=user_names, driver_names=driver_names,tz=tz))
            elif isinstance(entry, TripLog):
                result_json.append(handle_trip_log(entry, booking_details, is_b2c=0, client_name=client_name, user_names=user_names,tz=tz))
            elif isinstance(entry, Bookings):
                result_json.append(handle_booking_entry(entry, booking_details, is_b2c=0, client_name=client_name, claims=claims))

        ist_time = combine_and_convert_to_local(booking_details.booking_timestamp.date(), booking_details.booking_timestamp.time(),tz)
        formatted_date = ist_time.strftime("%d %B %Y")
        formatted_time = ist_time.strftime("%I:%M %p IST")

        driver_id = booking_details.booking_driver_id
        driver_name = ''
        if driver_id:
            driver_user = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).filter(Drivers.id == driver_id).first()
            if driver_user:
                driver_name = driver_user[0].get_name()

        response = {
            'success': 1,
            'customer': booking_details.customer_name,
            'driver': driver_name,
            'booking_date': formatted_date,
            'booking_time': formatted_time,
            'booking_id': booking_details.booking_id,
            'booking_code': booking_details.booking_code,
            'booking_location': booking_details.booking_region,
            'book_status': booking_details.book_valid,
            'trip_status': booking_details.trip_status,
            'log_details': result_json
        }
        return jsonify(response), 200

    except Exception as e:
        return jsonify({'success': -1, 'error': str(e)}), 500

@aff_book.route('/api/affiliate/release_pending_booking', methods=['POST'])
@jwt_required()
def release_pending_booking():
    if not complete(request.form, ['booking_id', 'release']):
        return jsonify({'success': -1, 'msg': "Incomplete form"}), 400

    try:
        book_id = int(request.form['booking_id'])
        release = int(request.form['release'])
        tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
        book = db.session.query(Bookings, AffBookingLogs, BookPending) \
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id) \
            .join(BookPending, Bookings.id == BookPending.book_id) \
            .filter(Bookings.id == book_id).first()

        if not book:
            return jsonify({'success': -2, "message": "Booking does not exist"}), 400

        booking, aff_book_logs, book_pending = book

        if release:
            book_pending.valid = BookPending.BROADCAST
            pending_state = BookPending.BROADCAST
            status_msg = "Driver will be auto-allocated"
        else:
            book_pending.valid = BookPending.SUPPRESSED
            pending_state = BookPending.SUPPRESSED
            status_msg = "Driver will be manually allocated"

        old_doc = AffiliateCollections.affiliates_book.find_one({"book_ref": book_id})

        result = AffiliateCollections.affiliates_book.update_one(
            {"book_ref": book_id},
            {"$set": {"pending_state": pending_state}}
        )

        if result.matched_count == 0:
            db.session.rollback()
            return jsonify({'success': 0, 'message': "Booking not found in MongoDB"}), 404

        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            if old_doc:
                AffiliateCollections.affiliates_book.replace_one(
                    {"book_ref": book_id},
                    old_doc
                )
            return jsonify({'success': 0, 'message': 'Database error', 'error': str(e)}), 500
        send_live_update_of_booking( book_id, booking.region)
        send_live_aff_booking_table(book_id, channel='table', dest_aff_id=aff_book_logs.aff_id, booking_region=booking.region,tz=tz)

        return jsonify({'success': 1, 'msg': status_msg}), 200

    except Exception as e:
        db.session.rollback()
        print(f"Error releasing pending booking: {e}", flush=True)
        return jsonify({'success': -2, 'msg': 'Internal Server Error'}), 500
