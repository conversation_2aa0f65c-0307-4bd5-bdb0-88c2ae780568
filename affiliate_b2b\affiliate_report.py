from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt_identity,get_jwt
)
from affiliate_b2b.affiliate_models import AffiliateCollections, AffBookingLogs,AffiliateDriverSearch
from models import Bookings, Trip,BookDest,BookingAlloc,BookPricing,TripPricing
from db_config import db
from booking_params import BookingParams,Regions
from datetime import datetime,timedelta
from sqlalchemy import or_
from sqlalchemy.orm import aliased
from sqlalchemy.sql import func
from _email import send_mail
import os
import pandas as pd
from _utils import ist_to_gmt
import math

aff_report = Blueprint('affiliate_report', __name__)

def build_affiliate_report_query(report_type, affiliates, book_valid, start_date=None, end_date=None, city_filter=None):
    is_report_type_booking = report_type == "Booking"

    start_date = ist_to_gmt(str(start_date)) if start_date else None
    end_date = ist_to_gmt(str(end_date)) if end_date else None
    # Base columns
    columns = [
        Bookings.id,
        Bookings.code,
        Bookings.startdate,
        Bookings.starttime,
        Bookings.created_at,
        Bookings.region,
        Bookings.loc,
        (
            func.coalesce(BookPricing.estimate,0) +
            func.coalesce(BookPricing.insurance_ch.label, 0)

        ).label('estimated_cost'),
        BookPricing.estimate,
        BookPricing.insurance_ch.label('estimated_ins_ch'),
        Bookings.price,
        BookDest.name.label('destination'),
        AffiliateDriverSearch.dist
    ]

    # Add trip-related fields if it's a Booking report
    if is_report_type_booking:
        columns.extend([
            Trip.starttime.label('trip_start'),
            Trip.endtime.label('trip_end'),
            (
                func.coalesce(TripPricing.base_ch, 0) +
                func.coalesce(TripPricing.cartype_ch, 0) +
                func.coalesce(TripPricing.night_ch, 0) +
                func.coalesce(TripPricing.ot_ch, 0) +
                func.coalesce(TripPricing.dist_ch, 0) +
                func.coalesce(TripPricing.cgst, 0) +
                func.coalesce(TripPricing.sgst, 0) +
                func.coalesce(TripPricing.insurance_ch, 0)

            ).label('cost')
        ])
    # Determine the date field based on report type
    date_field = Trip.starttime if is_report_type_booking else Bookings.startdate

    # Build the base query
    query = db.session.query(*columns)\
        .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)\
        .join(AffiliateDriverSearch, Bookings.search_key == AffiliateDriverSearch.id)\
        .join(BookPricing, BookPricing.book_id == Bookings.id)\
        .outerjoin(BookDest, BookDest.book_id == Bookings.id)\
        .filter(
            AffBookingLogs.aff_id.in_(affiliates),
            Bookings.valid.in_(book_valid)
        )

    if is_report_type_booking:
        query = query.join(Trip, Bookings.id == Trip.book_id)\
                     .filter(Trip.status == Trip.TRIP_STOPPED)\
                     .join(TripPricing, Bookings.id == TripPricing.book_id)

    if city_filter:
        query = query.filter(Bookings.region.in_(city_filter))

    # Apply date filters
    if start_date and end_date:
        query = query.filter(date_field.between(start_date, end_date))
    elif start_date:
        query = query.filter(date_field >= start_date)
    elif end_date:
        query = query.filter(date_field <= end_date)

    return query.order_by(date_field.desc())


def build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map):
    data = []
    for booking in results:
        destination, distance = booking.destination, booking.dist
        trip_start_diff,trip_start, trip_end = None, None,None  # Initialize defaults

        if report_type == "Booking":
            trip_start, trip_end = booking.trip_start, booking.trip_end
            book_start_datetime = datetime.combine(booking.startdate, booking.starttime)
            trip_start_datetime = booking.trip_start  

            # Calculate time difference in minutes
            if trip_start_datetime and book_start_datetime:
                trip_start_diff = int((trip_start_datetime - book_start_datetime).total_seconds() // 60)


        latest_alloc_time = latest_alloc_times.get(booking.id)

        # Calculate allocation time difference
        alloc_time_diff = None
        if latest_alloc_time and booking.created_at:
            alloc_time_diff = int((latest_alloc_time - booking.created_at).total_seconds() // 60)

        # Calculate trip duration
        duration = None
        if trip_start and trip_end:
            duration = int((trip_end - trip_start).total_seconds() // 60)


        # Build booking data
        booking_data = {
            'book_code': booking.code,
            'book_start_date': (datetime.combine(booking.startdate, booking.starttime) + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y'),
            'book_start_time': (datetime.combine(booking.startdate, booking.starttime) + timedelta(hours=5, minutes=30)).strftime('%I:%M %p'),
            'book_raised_time': (booking.created_at + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y %I:%M %p'),
            'city_name': Regions.REGN_NAME[booking.region],
            'book_estimate': booking.estimated_cost,
            'pickup_location': booking.loc,
            'drop_location': destination,
            'distance': math.ceil(distance/1000), 
        }
        
        if report_type == "Booking":
            booking_data.update({
            'pick_up_date': (trip_start + timedelta(hours=5, minutes=30)).strftime('%d/%m/%Y') if trip_start else None,
            'trip_start_time': (trip_start + timedelta(hours=5, minutes=30)).strftime('%I:%M %p') if trip_start else None,
            'trip_end_time': (trip_end + timedelta(hours=5, minutes=30)).strftime('%I:%M %p') if trip_end else None,
            'duration': duration,
            'trip_delay':trip_start_diff if trip_start_diff>0 else 0,
            'allocation_time': alloc_time_diff,
            'cost': booking.cost

            })
           
        mongo_data = mongo_data_map.get(booking.id)
        if mongo_data:        
            booking_data.update({
                "trip_type": mongo_data.get("trip_type", ""),
                "affiliate_trip_type": mongo_data.get("trip_name", ""),
                "appointment_id": mongo_data.get("appointment_id", ""),
                "car_reg_no": mongo_data.get("vehicle_no", ""),
                "booked By":mongo_data.get("rep_fullname",""),
                "vehicle_model": mongo_data.get("vehicle_model", "")
            })
            spoc_data = mongo_data.get("spoc_data", {})
            if isinstance(spoc_data, dict):
                booking_data.update({k: v for k, v in spoc_data.items() if k not in {'dest_spoc_contact', 'source_spoc_contact'}})
    
            custom_data = mongo_data.get("custom_data", {})
            if isinstance(custom_data, dict):
                booking_data.update(custom_data)
        data.append(booking_data)
    return data

@aff_report.route('/api/affiliate/report', methods=['GET'])
@jwt_required()
def affiliate_report():
    cities = request.args.get('cities')
    affiliates = request.args.get("affiliates")

    city_filter = [int(a.strip()) for a in cities.split(',') if a.strip().isdigit()] if cities else []
    affiliate_filter = [int(aff.strip()) for aff in affiliates.split(',') if aff.strip().isdigit()] if affiliates else []

    # Get filters from request
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    report_type = request.args.get('trans_type')

    # Convert start_date & end_date to datetime
    start_date = end_date = None
    try:
        if start_date_str:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d %H:%M:%S")
        if end_date_str:
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return jsonify({'success': 0, 'message': 'Invalid date format, use YYYY-MM-DD HH:mm:ss'}), 400

    
    book_valid = [1] if report_type == "Booking" else [-1, -2, -3]
    
    query = build_affiliate_report_query(report_type=report_type, affiliates=affiliate_filter, book_valid=book_valid,
                            start_date=start_date, end_date=end_date, city_filter=city_filter)

    results = query.all()


    # Fetch latest allocation times efficiently
    book_ids_in_results = [booking.id for booking in results]
    latest_alloc_times = {}
    if book_ids_in_results:
        latest_alloc_times = dict(
            db.session.query(
                BookingAlloc.booking_id,
                func.max(BookingAlloc.timestamp)
            )
            .filter(BookingAlloc.booking_id.in_(book_ids_in_results))
            .group_by(BookingAlloc.booking_id)
            .all()
        )

    # Fetch MongoDB data in one query
    book_ids = [booking.id for booking in results]
    mongo_data_map = {
        doc['book_ref']: doc 
        for doc in AffiliateCollections.affiliates_book.find(
            {"book_ref": {"$in": book_ids}},
            {
                "book_ref": 1,
                "trip_type": 1,
                "trip_name":1,
                "client_name": 1,
                "custom_data": 1,  # Fetch the ENTIRE custom_data object
                "vehicle_no": 1,
                "spoc_data": 1,
                "rep_fullname": 1,
                "appointment_id": 1,
                "vehicle_model" : 1               
            }
        )
    }
    data = build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map)
    return jsonify({'success': 1, 'data': data})
   
   
def create_excel_file(data, filename, sheet_name='Sheet1'):
    # Build full path
    D4M_UTIL_PATH = "/app/util/"

    filename = os.path.join(D4M_UTIL_PATH, 'output', filename)
    
    df = pd.DataFrame(data)
    writer = pd.ExcelWriter(filename, engine='xlsxwriter')
    df.to_excel(writer, sheet_name=sheet_name, index=False)
    writer._save()
    return filename


@aff_report.route('/api/affiliate/send_report', methods=['POST'])
@jwt_required()
def affiliate_report_excel():
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')
    report_type = request.form.get('trans_type')
    affiliates = request.form.get('affiliates')
    cities = request.form.get('cities')
    email_list = request.form.get('email_list')
    message = request.form.get('message')
    subject = request.form.get('subject')
    # Parse filters
   
    city_filter = [int(a.strip()) for a in cities.split(',') if a.strip().isdigit()] if cities else []
    affiliate_filter = [int(aff.strip()) for aff in affiliates.split(',') if aff.strip().isdigit()] if affiliates else []
        

    try:
        start_date = datetime.strptime(start_date_str, "%Y-%m-%d %H:%M:%S") if start_date_str else None
        end_date = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S") if end_date_str else None
    except ValueError:
        return jsonify({'success': 0, 'message': 'Invalid date format, use YYYY-MM-DD HH:mm:ss'}), 400

    book_valid = [1] if report_type == "Booking" else [-1, -2, -3]

    # Get SQL results
    query = build_affiliate_report_query(report_type=report_type, affiliates=affiliate_filter, book_valid=book_valid,
                    start_date=start_date, end_date=end_date, city_filter=city_filter)
    results = query.all()

    # Get allocation times
    book_ids = [booking.id for booking in results]
    latest_alloc_times = {}
    if book_ids:
        latest_alloc_times = dict(
            db.session.query(
                BookingAlloc.booking_id,
                func.max(BookingAlloc.timestamp)
            ).filter(BookingAlloc.booking_id.in_(book_ids))
            .group_by(BookingAlloc.booking_id)
            .all()
        )

    # Mongo data
    mongo_data_map = {
        doc['book_ref']: doc
        for doc in AffiliateCollections.affiliates_book.find(
            {"book_ref": {"$in": book_ids}},
            {
                "book_ref": 1,
                "trip_type": 1,
                "trip_name":1,
                "client_name": 1,
                "custom_data": 1,
                "vehicle_no": 1,
                "spoc_data": 1,
                "rep_fullname": 1,
                "appointment_id": 1,
                "vehicle_model" : 1
            }
        )
    }

    # Build final report data
    data = build_affiliate_report_data(results, report_type, latest_alloc_times, mongo_data_map)

    # Create Excel file
    filename = f"affiliate_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    file_path = create_excel_file(data,filename)

    # Email recipients
    if not email_list:
        return jsonify({'success': 0, 'message': 'No email list provided'}), 400
    to_list = [email.strip() for email in email_list.split(',') if email.strip()]

    # Email content
    subject = subject if subject else f"{report_type} Report"
    content = message if message is not None else f"Hi,\n\nPlease find attached the affiliate report.\n\nRegards,\nTeam"
    content = content.replace('\n', '<br>') 
    from_address="<EMAIL>"

    # Send mail
    try:
        send_mail(from_address, to_list, subject, content,file_path)
        os.remove(file_path)
        return jsonify({'success': 1, 'message': 'Report emailed successfully'})

    except Exception as e:
        if os.path.exists(file_path):
            os.remove(file_path)
        return jsonify({'success': 0, 'message': f"Email sending failed: {str(e)}"}), 500
