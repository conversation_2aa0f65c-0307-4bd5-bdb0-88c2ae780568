from datetime import datetime,timedelta   
from models.models import Users,Drivers,Bookings,Trip,db,BookPricing,BookingAlloc,BookingCancelled,DriverInfo,DriverSearch,BookDest, \
    DriverLoc,BookingDetailsUpdate
import pytest
from conftest import unique_user_data, driver_bookings,driver_trip, create_master_affiliate, create_user_and_driver, driver_details, create_user, create_b2b_booking, create_b2b_booking_test
from unittest.mock import patch
from models.affiliate_models import AffiliateDriverSearch,AffBookingLogs,AffiliateCollections
from sqlalchemy import exc
from io import BytesIO
from test_analytics import create_booking_and_trip
from zoneinfo import ZoneInfo 
from models.affiliate_models import Affiliate,AffiliateCollections, AffiliateRep,AffiliateDriverSearch,AffiliateRepLogs, AffiliateCustomLogs, AffiliateAddress, AffiliateWalletLogs, AffiliatePricingLogs, AffiliateSpoc, DraftAffiliate,AffBookingLogs, AddressSpoc


#  API - /api/admin/total_count

def test_booking_overview_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    from_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d')
    today = datetime.utcnow()
    driver_booking=driver_bookings('1','2')  
    driver_booking2=driver_bookings('1','2') 
    booking2 = db.session.query(Bookings).filter(Bookings.id == driver_booking2)
    booking2.valid=1
    driver_booking3=driver_bookings('1','2')  
    driver_booking4=driver_bookings('1','2')  
    trip=driver_trip(driver_booking,0)
    trip2=driver_trip(driver_booking2,6)
    trip3=driver_trip(driver_booking3,3)
    booking4 = db.session.query(Bookings).filter(Bookings.id == driver_booking4).first()
    booking4.valid=-1
    driver_booking5=driver_bookings('1','5')  
    booking5 = db.session.query(Bookings).filter(Bookings.id == driver_booking5).first()
    booking5.valid=-3
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {
        'search_region': '0',
        'from_date': from_date,
        'to_date': to_date,
        'all_or_filter':1,
        'search_by':3
    }
    response = client.post('/api/admin/total_count',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success']==1
    data = response_json['data']
    assert data == [0, 0, 0, 0, 1, 0, 1, 0, 1, 1, 1, 0]
    
def test_booking_overview_isglobal_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    today = datetime.utcnow()
    driver_booking=driver_bookings('1','2')  
    driver_booking2=driver_bookings('1','2') 
    booking2 = db.session.query(Bookings).filter(Bookings.id == driver_booking2)
    booking2.valid=1
    driver_booking3=driver_bookings('1','2')  
    driver_booking4=driver_bookings('1','2')  
    trip=driver_trip(driver_booking,0)
    trip2=driver_trip(driver_booking2,6)
    trip3=driver_trip(driver_booking3,3)
    booking4 = db.session.query(Bookings).filter(Bookings.id == driver_booking4).first()
    booking4.valid=-1
    driver_booking5=driver_bookings('1','5')  
    booking5 = db.session.query(Bookings).filter(Bookings.id == driver_booking5).first()
    booking5.valid=-3
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()

    form_data = {
        'search_region': '-1',
        'from_date': from_date,
        'to_date': to_date,
        'isglobal':'1',
        'all_or_filter':1,
        'search_by':3
    }
    response = client.post('/api/admin/total_count',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success']==1
    data = response_json['data']
    assert data == [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0]
    
# ---------------------------------     

#  API - /api/admin/trip_log

def test_booking_trip_log_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    today = datetime.utcnow()
    driver_booking=driver_bookings(user.id,driver.id)
    ba = BookingAlloc(driver_booking, driver.id, '1')
    db.session.add(ba)
    bc = BookingCancelled(
                user='1', 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid=user.id,
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    bc.timestamp=(today - timedelta(minutes=60))
    db.session.add(bc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {
        'regions': '0',
        'booking_id':driver_booking
    }
    response = client.post('/api/admin/trip_log',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success'] == 1

# ---------------------------------     

#  API - /api/admin/book/details
    
def test_booking_details_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    ds=DriverSearch('tezt',user.id,0,22,77,(current_time-timedelta(hours=2)).strftime("%H:%M:%S"),datetime.utcnow().strftime("%Y-%m-%d"),
                        1,current_time)
    db.session.add(ds)
    booking = Bookings(
            user=user.id,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    db.session.add(booking)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {   
        'regions': '-1',
        'booking_id':booking.id
    }
    response = client.post('/api/admin/book/details',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success'] == 1
    booking_details = response_json['data']['booking_details']
    assert booking_details['book_location'] == 'Test Location'
    assert booking_details['book_status'] == 1
    assert booking_details['booking_region'] == 0
    customer_details = response_json['data']['customer_details']
    assert customer_details['label'] == 0
    assert customer_details['location'] == 'Test Location'
    driver_details = response_json['data']['driver_details']
    
    assert driver_details['label'] == 0
    assert driver_details['location'] == 'Delhi'
    trip_details = response_json['data']['trip_details']
    assert trip_details['car_type'] == '0'
    assert trip_details['trip_status'] == 'NA'
    trip_price = response_json['data']['trip_price']
    assert trip_price['driver_rating'] == 0
    assert trip_price['estimate'] == 100.0
    assert trip_price['estimate_pre_tax'] == 90
    
    
    
def test_booking_details_success_b2b(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp

        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
        print("New affiliate book added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
    form_data = {   
        'regions': '-1',
        'booking_id':booking.id
    }
    response = client.post('/api/admin/book/details',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success'] == 1
    booking_details = response_json['data']['booking_details']
    assert booking_details['book_location'] == 'Test Location'
    assert booking_details['book_status'] == 1
    assert booking_details['booking_region'] == 0
    assert booking_details['booking_account'] == 'Master_Client'

    customer_details = response_json['data']['customer_details']
    assert customer_details['location'] == 'Test Location'

    driver_details = response_json['data']['driver_details']
    assert driver_details['label'] == 0
    assert driver_details['location'] == 'Delhi'

    trip_details = response_json['data']['trip_details']
    assert trip_details['car_type'] == '0'
    assert trip_details['trip_status'] == 'NA'

    trip_price = response_json['data']['trip_price']
    assert trip_price['driver_rating'] == 0
    assert trip_price['estimate'] == 100.0
    assert trip_price['estimate_pre_tax'] == 90
    
# ---------------------------------     

#  API - /api/admin/driver_loc
    
def test_driver_loc_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    drivloc=DriverLoc(driver.id,12.34,56.78)
    db.session.add(drivloc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {   
        'regions': '-1',
        'driver_id':driver.id
    }
    response = client.post('/api/admin/driver_loc',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success'] == 1
    data=response_json['data']

def test_driver_loc_exception(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    drivloc=DriverLoc(driver.id,12.34,56.78)
    db.session.add(drivloc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {   
        'regions': '-1',
        'driver_id':driver.id
    }   
  
    with patch('models.db.session.query') as mock_commit:
        mock_commit.side_effect = exc.IntegrityError(None, None, None)
        response = client.post('/api/admin/driver_loc',data=form_data,headers=auth_headers)
        response_json = response.json
        assert response_json['success'] == -1
        data=response_json['data']
        assert data['cur_lat'] == 0
        assert data['cur_lng'] == 0
    
#---------------------------------     

#API - /api/admin/booking/update_all

def test_booking_details_update_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    ds=DriverSearch('tezt',user.id,0,22,77,(current_time-timedelta(hours=2)).strftime("%H:%M:%S"),datetime.utcnow().strftime("%Y-%m-%d"),
                        1,current_time)
    db.session.add(ds)
    booking = Bookings(
            user=user.id,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    db.session.add(booking)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    booking_id = booking.id
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    trip=driver_trip(booking.id,6)
    ds1=DriverSearch('tezt1',user.id,0,22,77,(current_time-timedelta(hours=2)).strftime("%H:%M:%S"),datetime.utcnow().strftime("%Y-%m-%d"),
                        1,current_time)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {  
        'booking_id':booking.id,
        'search_id':ds1.id,
        'regions': '-1',
        'car_type':'2',
        'trip_type':'3',
        'price':'400',
        'driver_rating':'4',
        'start_loc':'22.7',
        'duration':'03:00:00',
        }
    response = client.post('/api/admin/booking/update_all',data=form_data,headers=auth_headers)
    response_json = response.json
    
    booking_update=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    trip_update=db.session.query(Trip).filter(Trip.id==trip).first()
    assert response_json['success'] == 1
    assert response_json['message'] == 'Booking updated successfully'
    assert booking_update.driver_rating==4
    assert booking_update.car_type==2
    assert booking_update.type==3
    
    
def test_booking_details_update_success_b2b(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp

        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    booking_id = booking.id
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    
    search_entry2 = AffiliateDriverSearch(
        "tezt2",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp

        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    trip=driver_trip(booking.id,6)
    db.session.add(search_entry2)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
        print("New affiliate book added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
    form_data = {  
        'booking_id':booking.id,
        'regions': '-1',
        'price':'400',
        'car_type':'2',
        'driver_rating':'4',
        'start_loc':'22.7',
        'duration':'03:00:00',
        'vehicle_number':'test_number',
        'trip_name':'test_trip'
        }
    response = client.post('/api/admin/booking/update_all',data=form_data,headers=auth_headers)
    response_json = response.json
    booking = db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking_update=db.session.query(Bookings).filter(Bookings.id==booking.id).first()
    trip_update=db.session.query(Trip).filter(Trip.id==trip).first()
    assert response_json['success'] == 1
    assert response_json['message'] == 'Booking updated successfully'
    assert booking_update.driver_rating==4
    assert booking_update.car_type==2
    affiliate_book_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': booking.id})
    assert affiliate_book_mongo['trip_name'] == 'test_trip'
    assert affiliate_book_mongo['vehicle_no'] == 'test_number'
    
#---------------------------------     

# API - /api/trip/status_change


def test_booking_trip_state_change_invalid_params(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {  
        'booking_id':'-1', 
        'regions': '-1',
        'state':'-1'
        }
    response = client.post('/api/trip/status_change',data=form_data,headers=auth_headers)
    response_json = response.json
    assert response_json['success'] == -1
    assert response_json['error']['message'] == 'Invalid params'
    
trip_states = [
    (Trip.TRIP_INIT, "TRIP_INIT"),
    (Trip.TRIP_REACHED_SRC, "TRIP_REACHED_SRC"),
    (Trip.TRIP_STARTED, "TRIP_STARTED"),
    (Trip.TRIP_REACHED_DEST, "TRIP_REACHED_DEST"),
    (Trip.TRIP_STOPPED, "TRIP_STOPPED")
]

@pytest.mark.timeout(300)  # ⏰ timeout in seconds
@pytest.mark.parametrize("trip_state, state_name", trip_states)   
def test_trip_state_change_success(client,admin_login,trip_state, state_name, mock_upload_pic):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    current_time = datetime.utcnow()
    if trip_state!=Trip.TRIP_INIT:
        trip_id=driver_trip(booking_id,trip_state+1)
        trip = db.session.query(Trip).filter(Trip.id == trip_id).first()
        if trip.status in [Trip.TRIP_START_PIC, Trip.TRIP_STOP_PIC]:
            db.session.query(Trip).filter(Trip.id == trip_id).update({Trip.status: Trip.status + 1})
            db.session.commit()
    mock_upload_pic.side_effect = ['url_cleft', 'url_cright', 'url_cback', 'url_cfront', 'url_selfie']
    if trip_state == Trip.TRIP_STARTED or trip_state == Trip.TRIP_STOPPED:  # TRIP_START_PIC or TRIP_STOP_PIC
        
        data = {
            'state': trip_state,
            'regions':'-1',
            'booking_id':booking_id ,  
            'lat': '22.5726',   
            'lng': '88.3639',   
            'driver_user':driver_user.id,
            'cleft': (BytesIO(b'mock car left pic'), 'car_left.jpg'),
            'cright': (BytesIO(b'mock car right pic'), 'car_right.jpg'),
            'cback': (BytesIO(b'mock car back pic'), 'car_back.jpg'),
            'cfront': (BytesIO(b'mock car front pic'), 'car_front.jpg'),
            'selfie': (BytesIO(b'mock selfie pic'), 'selfie.jpg'),
            'start_time':current_time,
            'stop_time':(current_time + timedelta(hours=4)).strftime("%Y-%m-%d %H:%M:%S.%f"),
        }
    else:
        data = {
            'regions':'-1',
            'state': trip_state,
            'booking_id': booking_id,  
            'driver_user':driver_user.id,
            'lat': '22.5726',   
            'lng': '88.3639',
            'start_time':current_time,
            'stop_time':(current_time + timedelta(hours=4)).strftime("%Y-%m-%d %H:%M:%S.%f"),
        }
    response = client.post('/api/trip/status_change', data=data, headers=auth_headers)
    response_data = response.get_json()
    
    assert response.status_code == 200

    # Assert the API response contains the expected trip state
    assert response_data["status"] == 200
    assert "data" in response_data
    if trip_state==Trip.TRIP_INIT:
        assert response_data["data"] == True
    else:
        assert response_data["data"]['success'] == True
        
# ---------------------------------     

#  API - /api/admin/booking/int<booking_id>/logs

def test_booking_logs_no_logs(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {  
        'regions': '-1',
        }
    response = client.get('/api/admin/booking/'+'1'+'/logs', data=form_data, headers=auth_headers)
    response_data = response.get_json()
    response.status_code==404
    
def test_booking_logs_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.driver_rating='5'
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    form_data = {  
        'regions': '-1',
    }

    bd=BookingDetailsUpdate(book_id=booking_id,changes='driver_rating',edited_by=user.id,editedby_name='test',change_from='5',change_to='3',remark='rating changed')
    db.session.add(bd)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    response = client.get('/api/admin/booking/'+str(booking_id)+'/logs', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert response_data['success'] == 1
    logs=response_data['logs'][0]
    logs['changed_field']='driver_rating'
    logs['editedby_name']='test'
    
# ---------------------------------   
    
    
def test_booking_list_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': '2025-04-01 00:00:00',
        'actual_enddatetime': '2025-04-30 23:59:59',
        'from_date': '2025-04-01',
        'from_time': '00:00:00',
        'to_date': '2025-04-30',
        'to_time': '23:59:59',
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert response_data['success'] == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert response_data['success'] == 1
    
# ---------------------------------   
    
    
def test_booking_list_access_forbidden(client, customer_login):
    auth_headers, user = customer_login()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': '2025-04-01 00:00:00',
        'actual_enddatetime': '2025-04-30 23:59:59',
        'from_date': '2025-04-01',
        'from_time': '00:00:00',
        'to_date': '2025-04-30',
        'to_time': '23:59:59',
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert response_data['error'] == 'Forbidden access for this role'

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert response_data['error'] == 'Forbidden access for this role'
    
# ---------------------------------   
    
    
def test_booking_list_is_b2c_default_olddate(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': '2025-04-01 00:00:00',
        'actual_enddatetime': '2025-04-30 23:59:59',
        'from_date': '2025-04-01',
        'from_time': '00:00:00',
        'to_date': '2025-04-30',
        'to_time': '23:59:59',
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6, 7, 8, 9, 10, 11',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    
    assert response_data['data'] == []

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 0
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 0
    
# ---------------------------------   
    
    
def test_booking_list_region(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    form_data = {
        'regions': '1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': '2025-04-01 00:00:00',
        'actual_enddatetime': '2025-04-30 23:59:59',
        'from_date': '2025-04-01',
        'from_time': '00:00:00',
        'to_date': '2025-04-30',
        'to_time': '23:59:59',
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    
    assert response_data['data'] == []

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 0
    
#---------------------------------   
    
    
def test_booking_list_region_same(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    driver_details(driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '2',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()   
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   
    
    
def test_booking_list_nofilter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    driver_details(driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   
    
    
def test_booking_list_wrong_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 0

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [0]:
        total += response_data['data'][i]

    assert total == 0
    
# # ---------------------------------   
    
    
def test_booking_list_unallocated(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    db.session.commit()
    data2 = unique_user_data()
    detail_id = driver_details(1)
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)    
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 0
    booking.driver = 1
    db.session.commit()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [0]:
        total += response_data['data'][i]

    assert total == 1
    
# # # ---------------------------------   
    
def test_booking_list_mix_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    db.session.commit()
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    driver_details(1)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 0
    booking.driver = 1
    db.session.commit()
    
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(driver.id)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 2

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert (response_data['data'][0] + response_data['data'][1]) == 2
    
# ---------------------------------   
    
    
def test_booking_list_pre_cancel_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    db.session.commit()
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(1)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = -1
    booking.driver = 1
    db.session.commit()
    
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(driver.id)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=6000)
    after_10_min = now_utc + timedelta(minutes=6000)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '2,11',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }
    
    
    bookingall=db.session.query(Bookings).filter(Bookings.valid==-1).first()

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 2

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert (response_data['data'][1] + response_data['data'][7]) == 2
    
# # ---------------------------------   
    
    
def test_booking_list_post_cancel_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    driver_details(driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = -1
    db.session.commit()
    
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '12',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 3
    total = 0
    for i in [8]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   
    
    
def test_booking_list_admin_cancel_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(driver.id)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = -3
    db.session.commit()
    
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_details(driver.id)
    data2 = unique_user_data()
    user=create_user(data2)
    
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = -2
    db.session.commit()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=60)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '10',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 2

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 3
    total = 0
    for i in [9]:
        total += response_data['data'][i]

    assert total == 2
    
# ---------------------------------   
    
    
def test_booking_list_trip_filter(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_details(driver.id)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    # booking.valid = 1
    # db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '2,7',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 2

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 2
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 2
    
# ---------------------------------   
    
    
def test_booking_list_future(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_details(driver.id)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    # booking.valid = 1
    # db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,10,11,12',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 2

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 2
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   
    
##searchstart

    
def test_booking_list_code_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    details_id=driver_details(driver.id)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    details_id=driver_details(driver.id)
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': booking.code,
        'search_by': '3',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   

def test_booking_list_user_name_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    details_id=driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': user.fname,
        'search_by': '1',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   

def test_booking_list_user_mobile_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    details_id=driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': user.mobile,
        'search_by': '1',
        'isglobal': '5',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------    

def test_booking_list_driver_name_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    details_id=driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': driver_user.fname,
        'search_by': '2',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 1
    
# # ---------------------------------    

def test_booking_list_driver_mobile_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    details_id=driver_details(driver.id)
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '0',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': driver_user.mobile,
        'search_by': '4',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 1
    
# # ---------------------------------   

def test_booking_list_time_boundary_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    details_id=driver_details(driver.id)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': driver_user.mobile,
        'search_by': '4',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 2
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 2
    
# ---------------------------------   

def test_booking_list_time_boundary_with_filter_search(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id=driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    
    data = unique_user_data()
    driver_user,driver = create_user_and_driver(data)
    
    data2 = unique_user_data()
    user=create_user(data2)
    booking_id = driver_bookings(user.id,driver.id)
    booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
    booking.valid = 1
    booking.code='CVALID'
    db.session.commit()
    
    trip_id = driver_trip(booking_id, 0)
    tripentry = db.session.query(Trip).filter(Trip.id==trip_id).first()
    
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,8,9,10,11,12',
        'search_query': driver_user.mobile,
        'search_by': '4',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': '1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23',
        'is_b2c': '1',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 0

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 2
    total = 0
    for i in [1,6]:
        total += response_data['data'][i]

    assert total == 2
    
# ---------------------------------   

def test_booking_list_b2b_booking(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    details_id=driver_details(driver.id)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp
        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.code = 'BROCOD'
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
    except Exception as e:
        print(f"MongoDB error: {e}")
        
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=1800)
    after_10_min = now_utc + timedelta(minutes=1800)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': booking.code,
        'search_by': '3',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': f'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, {master_affiliate.id}',
        'is_b2c': '0',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   

def test_booking_list_b2b_unallocate(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    details_id=driver_details(driver.id)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp
        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 0
    booking.code = 'BROCOD'
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
        print("New affiliate book added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
        
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=1800)
    after_10_min = now_utc + timedelta(minutes=1800)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': booking.code,
        'search_by': '3',
        'isglobal': '1',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': f'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, {master_affiliate.id}',
        'is_b2c': '0',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [0]:
        total += response_data['data'][i]

    assert total == 1
    
# # ---------------------------------   

def test_booking_list_b2b_booking_filter_check(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp
        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.code = 'BROCOD'
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
        print("New affiliate book added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
        
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,3,4,5,6,7,8,9,10,11,12',
        'search_query': '',
        'search_by': '3',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': f'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, {master_affiliate.id}',
        'is_b2c': '0',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 0

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   

def test_booking_list_b2b_search_by_vehNo(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    details_id=driver_details(driver.id)
    today = datetime.utcnow()
    current_time = datetime.utcnow()
    master_affiliate, redis_key = create_master_affiliate()
    search_entry = AffiliateDriverSearch(
        "tezt",                       # id
        master_affiliate.id,          # affiliate
        None,                         # rep_id
        0,                            # car_type
        22.0,                         # reflat
        88.0,                         # reflong
        22.0,                         # destlat
        89.0,                         # destlong
        datetime.utcnow().time(),     # time
        datetime.utcnow().date(),     # date
        (datetime.min + timedelta(minutes=60)).time(),  # dur as a time, not timedelta
        datetime.utcnow(),            # timestamp
        # keyword‐only args
        type=100,
        trip_type=1,
        days=0,                       # use 0 instead of None
        insurance=True,
        insurance_ch=100.0,
        region=0,
        dist=10,
        source="Admin",
        estimate=100.0,
        cust_base=50.0,
        cust_night=20.0,
        driver_base=10.0,
        driver_night=20.0,
        price_id=master_affiliate.id
    )
    db.session.add(search_entry)
    db.session.flush()
    booking = Bookings(
            user=None,
            skey='tezt',
            driver=driver.id,
            lat=0.0,
            long=0.0,
            starttime=today.strftime("%H:%M:%S"),
            startdate=today.strftime("%Y-%m-%d"),
            dur=timedelta(minutes=60).total_seconds(),
            endtime=(today + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=(today + timedelta(minutes=60)).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
    )
    booking.valid = 1
    booking.code = 'BROCOD'
    booking.type=100
    db.session.add(booking)
    db.session.flush()
    aff_log = AffBookingLogs(
        book_id=booking.id,
        admin_id=admin.id,
        aff_id=master_affiliate.id,
        mapped_by=  master_affiliate.id ,
        mapped_wallet=master_affiliate.id
    )
    db.session.add(aff_log)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    ba = BookingAlloc(booking.id, driver.id, '1')
    db.session.add(ba)
    dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
    db.session.add(dest)
    book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
    db.session.add(book_pricing)
    # driver_loc = DriverBaseLoc(driver.id, '22', '88', 'test')
    # db.session.add(driver_loc)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    other_data = {
        "search_id": search_entry.id,
        "book_ref": booking.id,
        "trip_name": "Test Trip",
        "trip_type": "One Way",  # or derived from BookingParams
        "affiliate_id": master_affiliate.id,
        "client_name": "Test Client",
        "booking_created_at": datetime.utcnow(),
        "vehicle_no": "XYZ1234",
        'src_nickname_id': 1,
        "spoc_data": {"name": "John Doe", "phone": "1234567890"},
        'custom_data': {"field": "value"},
        "custom": {"form": "data"},
        "trip_type_list": ["One Way"],
        "trip_start_images_structure": [],
        "trip_stop_images_structure": [],
        "admin_name": admin.fname + " " + admin.lname,
        "admin_id": admin.id,
    }
    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_book.insert_one(other_data)
        print("New affiliate book added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
        
    now_utc = datetime.utcnow()

    # Compute before and after times
    before_10_min = now_utc - timedelta(minutes=60)
    after_10_min = now_utc + timedelta(minutes=180)

    # Convert to IST
    ist_zone = ZoneInfo("Asia/Kolkata")
    now_ist = now_utc.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    before_ist = before_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)
    after_ist = after_10_min.replace(tzinfo=ZoneInfo("UTC")).astimezone(ist_zone)

    # Format IST times
    now_str = now_ist.strftime("%Y-%m-%d %H:%M:%S")
    before_str = before_ist.strftime("%Y-%m-%d %H:%M:%S")
    after_str = after_ist.strftime("%Y-%m-%d %H:%M:%S")

    from_date = before_ist.strftime("%Y-%m-%d")
    from_time = before_ist.strftime("%H:%M:%S")
    to_date = after_ist.strftime("%Y-%m-%d")
    to_time = after_ist.strftime("%H:%M:%S")

    form_data = {
        'regions': '-1',
        'starting_from': '0',
        'no_of_logs': '25',
        'limited': '1',
        'actual_startdatetime': before_str,
        'actual_enddatetime': after_str,
        'from_date': from_date,
        'from_time': from_time,
        'to_date': to_date,
        'to_time': to_time,
        'search_region': '-1',
        'fromtop': '1',
        'filter': '1,2,3,4,5,6,7,8,9,10,11,12',
        'search_query': 'XYZ1234',
        'search_by': '6',
        'isglobal': '0',
        'fromtop_booktime': '',
        'fromtop_triptime': '',
        'firstRequestOrNot': '1',
        'affiliatefilter': f'1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 23, {master_affiliate.id}',
        'is_b2c': '0',
    }

    response = client.post('/api/admin/booking_list', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    assert len(response_data['data']) == 1

    response = client.post('/api/admin/total_count', data=form_data, headers=auth_headers)
    response_data = response.get_json()    
    total = 0
    for i in range(12):
        total += response_data['data'][i]

    assert total == 1
    total = 0
    for i in [1]:
        total += response_data['data'][i]

    assert total == 1
    
# ---------------------------------   