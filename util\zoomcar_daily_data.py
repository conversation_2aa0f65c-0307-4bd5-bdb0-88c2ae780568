import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str = (datetime.now() - timedelta(days=0)).date().strftime("%d%m%Y")
date_str_delhi = (datetime.now() - timedelta(days=0)).date().strftime("%d%m%Y")
filepathD = D4M_UTIL_PATH + 'output/delhi-zoomcar.csv'
filepathK = D4M_UTIL_PATH + 'output/kolkata-zoomcar.csv'
filepathH = D4M_UTIL_PATH + 'output/hyderabad-zoomcar.csv'
subjectD = "Zoomcar Daily Update - Delhi - "+date_str_delhi
subjectK = "Zoomcar Daily Update - Kolkata - "+date_str
subjectH = "Zoomcar Daily Update - Hyderabad - "+date_str
content = "Please find the attached data."
from_addr = "<EMAIL>"
to_addr_list_delhi = ["<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
to_addr_list_kolkata = ["<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>"]
to_addr_list_hyderabad = ["<EMAIL>", "<EMAIL>","<EMAIL>"]
send_mail(from_addr, to_addr_list_hyderabad, subjectH, content, filepathH)
send_mail(from_addr, to_addr_list_delhi, subjectD, content, filepathD)
send_mail(from_addr, to_addr_list_kolkata, subjectK, content, filepathK)