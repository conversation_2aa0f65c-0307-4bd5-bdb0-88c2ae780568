from utils.redis_utils import write_redis_data

""" Test case for api: /api/admin/pricing/add_city """
def test_city_trip_type_incomplete(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "Kolkata"
    }
 
    response = client.post('/api/admin/pricing/add_city', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -2

""" Test case for api: /api/admin/pricing/addOccasion """
def test_addOccasion_form_incomplete(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "Kolkata"
    }
 
    response = client.post('/api/admin/pricing/add_occasion', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -2

def test_addOccasion_city_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "RandomCity",
        'trip_type': 'inCity',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/add_occasion', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -3

def test_addOccasion_trip_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "kolkata",
        'trip_type': 'random_trip',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/add_occasion', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -3


""" Test case for api: /api/admin/pricing/allCity """
def test_fetch_city_success(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0'
    }
 
    response = client.post('/api/admin/pricing/all_city', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert 'cities' in res_data

""" Test case for api: /api/admin/pricing/details """
def test_pricing_details_form_incomplete(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "Kolkata"
    }
 
    response = client.post('/api/admin/pricing/details', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_details_city_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "RandomCity",
        'trip_type': 'inCity',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/details', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_details_trip_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "kolkata",
        'trip_type': 'random_trip',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/details', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1

# """ Test case for api: /api/admin/pricing/update """
def test_pricing_update_form_incomplete(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "Kolkata"
    }
 
    response = client.post('/api/admin/pricing/update', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_update_city_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "RandomCity",
        'trip_type': 'inCity',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/update', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_update_trip_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "kolkata",
        'trip_type': 'random_trip',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/update', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1

# """ Test case for api: /api/admin/pricing/delete """
def test_pricing_delete_form_incomplete(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "Kolkata"
    }
 
    response = client.post('/api/admin/pricing/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_delete_city_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "RandomCity",
        'trip_type': 'inCity',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1

def test_pricing_delete_trip_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'city': "kolkata",
        'trip_type': 'random_trip',
        'occasion': 'Diwali',
        'occasion_dates': '20/12/2024'
    }
    response = client.post('/api/admin/pricing/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1