#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime

from sqlalchemy import exc
from flask_jwt_extended import get_jwt, create_access_token


from services.customer_app.otp_service import generate_otp_gupshup, verify_otp_gupshup
from utils.redis_utils import execute_with_fallback
from models.models import Users,AdminAccess,UserToken,db
from utils.validation_utils import account_enabled
from utils.admin_utils import BIT_0_TO_14, BIT_1_TO_15, BIT_1_TO_6, BIT_0_TO_19
from utils.auth.admin_login_utils import create_token_admin
from utils.auth.login_utils import validate_pass
from utils.slack_utils import send_slack_msg
from utils.auth.admin_login_utils import check_if_token_revoked
from utils.security_utils import get_pwd, get_salt,add_token_to_blacklist


def generate_otp_service(payload):
    mobile = payload.mobile
    now = datetime.datetime.utcnow()

    last_request = execute_with_fallback('get', f"otp_request:{mobile}")
    if last_request:
        last_time = datetime.datetime.strptime(last_request, '%Y-%m-%d %H:%M:%S.%f')
        if (now - last_time).total_seconds() < 60:
            return -3, "OTP request too frequent"
    cur_user = Users.query.filter_by(mobile=mobile).first()
    # if not cur_user or not account_enabled(cur_user.id):
    #     return -2, "Invalid mobile number or account disabled"


    generate_otp_gupshup(mobile)
    execute_with_fallback('set', f"otp_request:{mobile}", now.strftime('%Y-%m-%d %H:%M:%S.%f'), ex=60)

    return 1, None  # success


def validate_admin_otp_service(payload, user_agent: str):
    cur_user = Users.query.filter_by(mobile=payload.mobile).first()

    if cur_user and cur_user.role in Users.NON_ADMIN_ROLES:
        return 0, 403, {'error': 'Forbidden access for this role'}

    otp_valid = verify_otp_gupshup(payload.mobile, payload.otp)
    if not (cur_user and cur_user.enabled and otp_valid.get("status") == "success"):
        try:
            send_slack_msg(10, f"{payload.mobile} failed to log in to the app")
        except Exception:
            pass
        return 0, 401, {}

    admin_access = AdminAccess.query.filter_by(admin_user_id=cur_user.id).first()
    if not admin_access:
        return 0, 403, {'message': 'Admin access not found for this user'}

    if cur_user.role == Users.ROLE_SUPERADMIN:
        admin_access.admin_tab_access = BIT_1_TO_15
        admin_access.admin_notification_access = BIT_1_TO_6
        admin_access.admin_regions_access = BIT_0_TO_19
        db.session.commit()

    access_token, refresh_token, refresh_expiry = create_token_admin(cur_user, admin_access)

    user_data = {
        'success': 1,
        'user_fname': cur_user.fname,
        'user_mobile': cur_user.mobile,
        'user_email': cur_user.email,
        'user_lname': cur_user.lname,
        'user_region': cur_user.region,
        'tabs': ','.join(str(i) for i in range(128) if admin_access.admin_tab_access & (1 << i)),
        'regions': ','.join(str(i) for i in range(128) if admin_access.admin_regions_access & (1 << i)),
        'notification': ','.join(str(i) for i in range(128) if admin_access.admin_notification_access & (1 << i)),
        'id': cur_user.id,
        'code': refresh_token,
        'role': cur_user.role
    }

    return access_token, refresh_token, refresh_expiry, user_data



def login_admin_service(payload, user_agent: str):
    cur_user = Users.query.filter_by(mobile=payload.mobile).first()

    if cur_user and cur_user.role in Users.NON_ADMIN_ROLES:
        return 0, 403, {'error': 'Forbidden access for this role'}

    if not cur_user:
        return 0, 401, {}

    mpwd = cur_user.role != Users.ROLE_SUPERADMIN
    if not (cur_user.enabled and validate_pass(user=cur_user, pwd=payload.pwd, mpwd=mpwd)):
        return 0, 401, {}

    admin_access = AdminAccess.query.filter_by(admin_user_id=cur_user.id).first()
    if not admin_access:
        return 0, 403, {'message': 'Admin access not found for this user'}

    if cur_user.role == Users.ROLE_SUPERADMIN:
        admin_access.admin_tab_access = BIT_1_TO_15
        admin_access.admin_notification_access = BIT_1_TO_6
        admin_access.admin_regions_access = BIT_0_TO_19
        db.session.commit()

    access_token, refresh_token, refresh_expiry = create_token_admin(cur_user, admin_access)

    user_data = {
        'success': 1,
        'user_fname': cur_user.fname,
        'user_mobile': cur_user.mobile,
        'user_email': cur_user.email,
        'user_lname': cur_user.lname,
        'user_region': cur_user.region,
        'tabs': ','.join(str(i) for i in range(128) if admin_access.admin_tab_access & (1 << i)),
        'regions': ','.join(str(i) for i in range(128) if admin_access.admin_regions_access & (1 << i)),
        'notification': ','.join(str(i) for i in range(128) if admin_access.admin_notification_access & (1 << i)),
        'id': cur_user.id,
        'code': access_token,
        'role': cur_user.role
    }

    return access_token, refresh_token, refresh_expiry, user_data



def admin_refresh_service(refresh_token: str):
    if check_if_token_revoked(refresh_token):
        return 0, 401, {'refresh': False, "msg": "Token has been revoked"}

    decoded_token = get_jwt()
    if not isinstance(decoded_token, dict):
        return -3, 500, {'error': 'Invalid token data decode', 'refresh': False}

    additional_claims = {
        'id': decoded_token['id'],
        'roles': decoded_token['roles'],
        'region': decoded_token['region'],
        'tab_access': decoded_token['tab_access'],
        'regions_access': decoded_token['regions_access'],
        'notification_access': decoded_token['notification_access'],
        'name': decoded_token['name']
    }

    expires_access = datetime.timedelta(
        minutes=60 if int(decoded_token['roles']) != Users.ROLE_SUPERADMIN else 720
    )

    access_token = create_access_token(
        identity=decoded_token['id'],
        additional_claims=additional_claims,
        expires_delta=expires_access
    )

    return access_token


def validate_password_change_otp_service(payload):
    cur_user = Users.query.filter_by(mobile=payload.mobile).first()
    if not cur_user:
        return 0, 404, {'error': 'User not found'}

    if cur_user.role in Users.NON_ADMIN_ROLES:
        return 0, 403, {'error': 'Forbidden access for this role'}

    otp_valid = verify_otp_gupshup(payload.mobile, payload.otp)
    if not (cur_user and cur_user.enabled and otp_valid.get("status") == "success"):
        try:
            send_slack_msg(10, f"{payload.mobile} failed to log in to the app")
        except Exception:
            pass
        return 0, 401, {}

    return 1

def password_change_service(payload):
    cur_user = Users.query.filter_by(mobile=payload.mobile).first()
    if not cur_user:
        return 0, 404, {'error': 'User not found'}

    if cur_user.role in Users.NON_ADMIN_ROLES:
        return 0, 403, {'error': 'Forbidden access for this role'}

    try:
        cur_user.salt = get_salt()
        cur_user.pwd = get_pwd(payload.new_pwd, cur_user.salt)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()

    return 1, 200, {'message': 'Password changed successfully'}


def delete_user_service(payload):
    user_to_delete = Users.query.filter_by(id=payload.user_id).first()
    if not user_to_delete:
        return 404, {"result": "FAILURE", "message": {"message": "User not found"}}

    user_to_delete.enabled = False
    db.session.commit()

    user_tokens = UserToken.query.filter_by(user_id=payload.user_id).all()
    for token in user_tokens:
        add_token_to_blacklist(token.refresh, token.expiry)

    return 200, {"result": "SUCCESS", "message": "User deleted and tokens invalidated"}