import math
import random
from copy import deepcopy

def auction_phase(cost, price, assign_person, assign_object, eps, max_iter=10000):
    M, N = len(cost), len(cost[0])
    for _ in range(max_iter):
        try:
            i = assign_object.index(-1)
        except ValueError:
            return True
        
        vals = [cost[i][j] - price[j] if cost[i][j] > -1e8 else -math.inf for j in range(N)]

        # ✅ If this customer has no valid assignments, skip them
        if all(v == -math.inf for v in vals):
            continue  # or optionally: raise RuntimeError(f"No valid drivers for person {i}")
        
        # if any(j == -1 for j in assign_object):
        #     raise RuntimeError("One or more persons could not be assigned a valid driver.")

        j1 = max(range(N), key=lambda j: vals[j])

        v1 = vals[j1]
        v2 = max((v for idx, v in enumerate(vals) if idx != j1), default=-math.inf)
        bid = price[j1] + (v1 - v2) + eps
        prev = assign_person[j1]
        price[j1], assign_person[j1], assign_object[i] = bid, i, j1
        if prev != -1:
            assign_object[prev] = -1
    return False

def auto_params(M, N, max_cost):
    eps_start = max_cost / (100 if M >= 10 else 1000)
    eps_min   = 1.0 / M
    alpha     = 2.0 if M < 10 else 4.0
    max_iter  = int(20 * M * N)
    return eps_start, eps_min, alpha, max_iter


def auction_eps_scaling_rect(cost):
    M, N = len(cost), len(cost[0])
    # initialize ε
    max_cost = max(max(row) for row in cost)

    eps, eps_min, alpha, max_iter = auto_params(M, N, max_cost)

    price = [0.0]*N
    assign_person = [-1]*N
    assign_object = [-1]*M

    # scaling phases
    while eps > eps_min:
        if not auction_phase(cost, price, assign_person, assign_object, eps, max_iter):
            raise RuntimeError(f"Phase at ε={eps} failed")
        eps /= alpha

    # final fine-tune
    auction_phase(cost, price, assign_person, assign_object, eps_min)
    return assign_object

def multi_round_auction(cost_matrix, k):
    M, N = len(cost_matrix), len(cost_matrix[0])
    remaining_cost = deepcopy(cost_matrix)
    remaining_driver_ids = list(range(N))  # column → original driver ID
    result = [[-1] * k for _ in range(M)]  # initialize all to -1

    for round_idx in range(k):
        if not remaining_cost or not remaining_cost[0]:
            break  # no drivers left at all

        # Run a single k‑assignment auction
        assignment = auction_eps_scaling_rect(remaining_cost)

        # Collect which driver‑columns got used this round
        used_driver_indices = set()

        for person_idx, col_idx in enumerate(assignment):
            # If auction returned -1, skip (no valid driver)
            if col_idx < 0:
                continue

            if col_idx >= len(remaining_driver_ids):
                raise ValueError(
                    f"Invalid assignment: driver index {col_idx} out of bounds"
                )

            # Map back to the original driver ID
            result[person_idx][round_idx] = remaining_driver_ids[col_idx]
            used_driver_indices.add(col_idx)

        # Remove every used driver‑column (from right to left)
        for col in sorted(used_driver_indices, reverse=True):
            # drop that column from each row of the cost‑submatrix
            for row in remaining_cost:
                row.pop(col)
            # drop it from the driver‑ID list too
            remaining_driver_ids.pop(col)

    return result

# Example usage
if __name__ == "__main__":
    M, N, k = 5,20, 3
    # build some example benefit matrix
    #cost_matrix = [[random.randint(10,100) for _ in range(N)] for _ in range(M)]
    
    # cost_matrix = [
    #     # C0 → max for driver 7
    #     [13, 24, 31, 12, 10, 15, 16, 99,  4,  3,  2, 12, 19, 11,  9, 17, 18, 22,  7,  6],
        
    #     # C1 → also max for driver 7
    #     [11, 14, 13, 19, 21, 12, 17, 99,  5,  4,  8, 15, 20, 10, 13, 12, 14,  8,  3,  2],

    #     # C2 → multiple second-best options
    #     [25, 25, 18, 20, 15, 25, 24, 21, 14, 13, 10, 25,  9,  8,  7, 26, 10, 11,  6,  5],

    #     # C3 → all low scores
    #     [ 1,  2,  3,  1,  2,  3,  1,  2,  3,  2,  1,  3,  2,  1,  3,  2,  1,  3,  2,  1],

    #     # C4 → two max scores (driver 4 and 11)
    #     [11,  9,  5,  8, 99, 12, 10,  9,  7,  6,  5, 99,  8,  7,  6,  5,  4,  3,  2,  1],
    # ]

    cost_matrix = [
    [50, 60, 70, 55, 65, 75],       # Customer 0
    [80, 85, 90, 70, 60, 55],       # Customer 1
    [40, 45, 50, 35, -1e8, 30],     # Customer 2 → ❌ Driver 4 forbidden
    [20, 25, 30, 35, 40, 45],       # Customer 3
    [10, 15, 25, 20, 30, 35],       # Customer 4
]

    for row in cost_matrix:
        print(row)
        
    assignments = multi_round_auction(cost_matrix, k)
    # assignments[i] is a list of k driver‑indexes (in the shrinking matrix!)
    for i in range(M):
        print(f"Customer {i} assignments over {k} rounds:", assignments[i])