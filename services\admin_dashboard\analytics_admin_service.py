#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

import time
from datetime import datetime, timedelta

from flask import request
from sqlalchemy.sql import func, case
from sqlalchemy.orm import aliased
from sqlalchemy import and_, or_, not_
from typing import Tuple, List, Dict

from models.models import db, Users, UserRegistrationDetails, Bookings, Trip, Drivers, \
    BookingCancelled, UserTrans, DriverTrans, DriverInfo, \
    BookingAlloc, AdminAccess, TripLog, TripPricing , DriverSetLoc, UserLoc, DriverSearch
from models.affiliate_models import AffBookingLogs, AffiliateCollections  
from utils.time_utils import convert_to_utc
from utils.bookings.booking_params import BookingParams, Regions
from utils.user_utils import get_name_by_id
from schemas.admin_dashboard.analytics_schemas import TransactionSummaryPayload, TransactionSummaryCustomerAdminValidator, \
    TransactionSummaryDriverAdminValidator, DriverInventoryCountValidator, BookingSummaryAdminPayload


def get_customer_transaction_summary(from_date, to_date, regions):
    """
    Computes aggregated customer transaction values including referral, gift, fine, cancellation, 
    and reversal amounts for the given time frame and regions.

    Args:
        from_date (datetime): Start datetime for filtering transactions.
        to_date (datetime): End datetime for filtering transactions.
        regions (List[int]): List of region IDs to filter data. Empty list means all regions.

    Returns:
        Tuple[List[Tuple[str, float]], int]: A sorted list of transaction type and amount pairs, or error response.
    """
    try:
        referral_query = (
                db.session.query(
                    Bookings.region.label('region'),
                    (-func.sum(case((Bookings.user.is_(None), 0), else_=UserTrans.amount / 100))).label('total_referral')
                )
                .outerjoin(Bookings, Bookings.user == UserTrans.user_id)
                .join(Users, UserTrans.user_id == Users.id)
                .filter(
                UserTrans.method.like("Referral use%"),
                    ~Users.mobile.between('7000000000', '7000000050'),
                    Bookings.type < 50,
                    UserTrans.timestamp.between(from_date, to_date)
                )
            )
        if regions:
            referral_query = referral_query.filter(Bookings.region.in_(regions))
        referral_results = referral_query.group_by(Bookings.region).all()


        referral_redem_query = (
            db.session.query(
                Bookings.region.label('region'),
                (-func.sum(case((Bookings.user.is_(None), 0), else_=UserTrans.amount / 100))).label('total_referral')
            )
            .outerjoin(Bookings, Bookings.user == UserTrans.user_id)
            .join(Users, UserTrans.user_id == Users.id)
            .filter(
                UserTrans.method.like("Referral redemption%"),
                ~Users.mobile.between('7000000000', '7000000050'),
                Bookings.type < 50,
                UserTrans.timestamp.between(from_date, to_date)
            )
        )
        if regions:
            referral_redem_query = referral_redem_query.filter(Bookings.region.in_(regions))
        referral_redem_results = referral_redem_query.group_by(Bookings.region).all()
        total_amount_gift = db.session.query(
            (-func.sum(UserTrans.amount / 100)).label('total_amount')
        ).select_from(Users).join(
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.method == 'Gift',
            UserTrans.timestamp >= from_date,
            UserTrans.timestamp <= to_date,
            Users.id.notin_(UserTrans.excluded_user_ids),
            UserTrans.amount > 0,
            # Filter by the latest region of the user
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0  # Default to 0 if no booking exists
            ).in_(regions) if regions else True
        ).scalar()  # Use scalar() to get a single total value


        total_amount_fine = db.session.query(
            (-func.sum(UserTrans.amount / 100)).label('total_amount')
        ).select_from(Users).join(
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.method == 'Fine',
            UserTrans.timestamp >= from_date,
            UserTrans.timestamp <= to_date,
            Users.id.notin_(UserTrans.excluded_user_ids),
            UserTrans.amount < 0,
            # Filter by the latest region of the user
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0  # Default to 0 if no booking exists
            ).in_(regions) if regions else True
        ).scalar()  # Use scalar() to get a single total value



        total_amount_cancel = db.session.query(
            -func.sum(UserTrans.amount / 100).label('total_amount')
        ).select_from(Users).join(
            UserTrans, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.method.like('Cancellation charges for%'),
            UserTrans.timestamp >= from_date,
            UserTrans.timestamp <= to_date,
            Users.id.notin_(UserTrans.excluded_user_ids),
            # Filter by the latest region of the user
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0  # Default to 0 if no booking exists
            ).in_(regions) if regions else True
        ).scalar()  # Use scalar() to get a single total value

        total_amount_reversal = db.session.query(
            (-func.sum(UserTrans.amount / 100)).label('total_amount')
        ).select_from(Users).join(
            UserTrans, UserTrans.user_id == Users.id
        ).join(
            BookingCancelled,UserTrans.id == BookingCancelled.utransid
        ).filter(
            UserTrans.method.like('Cancellation reversal for%'),
            UserTrans.timestamp >= from_date,
            UserTrans.timestamp <= to_date,
            BookingCancelled.cancel_source==BookingCancelled.SRC_ADMIN,
            Users.id.notin_(UserTrans.excluded_user_ids),
            # Filter by the latest region of the user
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0  # Default to 0 if no booking exists
            ).in_(regions) if regions else True
        ).scalar()  # Use scalar() to get a single total value



        all_amounts = {
        'referral_given_amount':sum(r.total_referral for r in referral_results if r.total_referral),
        'referral_redem_amount':sum(r.total_referral for r in referral_redem_results if r.total_referral),
        'gift_amount': total_amount_gift if total_amount_gift else 0,
        'fine_amount': total_amount_fine if total_amount_fine else 0,
        'cancel_amount': total_amount_cancel if total_amount_cancel else 0,
        'reverse_amount': total_amount_reversal if total_amount_reversal else 0
            }
        response_data = {k: v for k, v in all_amounts.items() if v != 0}
        sorted_items = sorted(response_data.items(), key=lambda item: abs(item[1]), reverse=True)
        return sorted_items, 200
    except Exception as e:
        return {'success': 0, 'error': str(e)}, 500


def get_driver_transaction_summary(from_date, to_date, regions):
    """
    Computes aggregated driver transaction values such as gifts, fines, inventory deductions, 
    incentives, withdrawals, and cancellation charges across regions within a given date range.

    Args:
        from_date (datetime): Start datetime for the transaction summary.
        to_date (datetime): End datetime for the transaction summary.
        regions (List[int]): List of region IDs to filter transactions. Empty list means all regions.

    Returns:
        Tuple[List[Tuple[str, float]], int]: A sorted list of transaction categories and their absolute amounts,
                                             or an error response with status code.
    """
    try:
        total_amount_gift = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Gift',
                DriverTrans.amount > 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_automatic_inc = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                or_(
                    DriverTrans.method == 'Daily bonus',
                    DriverTrans.method == 'Weekly bonus'
                ),
                DriverTrans.amount > 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_fine = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Admin Fine',
                DriverTrans.amount < 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_tshirt = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'T-Shirt',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_bag = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Bag',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_registration = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Registration',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_due_deduct = db.session.query(
                func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Due Deduction',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions)  if regions else True
            ).scalar()

        total_amount_withdraw = db.session.query(
                func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                 or_(DriverTrans.method == 'Withdraw',
                DriverTrans.method == 'Withdraw Amount'),
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_reactivation = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Reactivation',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_cancel = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method.like('Cancellation: Booking%'),
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_reverse = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).join(
                BookingCancelled,DriverTrans.id == BookingCancelled.dtransid
            ).filter(
                DriverTrans.method.like('Reversal: Booking%'),
                BookingCancelled.cancel_source==BookingCancelled.SRC_ADMIN,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        all_amounts  = {
                'gift_amount': total_amount_gift if total_amount_gift else 0 ,
                'tshirt_amount': total_amount_tshirt if total_amount_tshirt else 0,
                'bag_amount':total_amount_bag if total_amount_bag else 0,
                'fine_amount': total_amount_fine if total_amount_fine else 0,
                'due_deduct_amout':total_amount_due_deduct if total_amount_due_deduct else 0,
                'withdraw_amount':total_amount_withdraw if total_amount_withdraw else 0,
                'autom_incent_amount': total_amount_automatic_inc if total_amount_automatic_inc else 0,
                'registration_amount': total_amount_registration if total_amount_registration else 0,
                'reactivation_amount': total_amount_reactivation if total_amount_reactivation else 0,
                'cancel_amount': total_amount_cancel if total_amount_cancel else 0,
                'cancel_reverse_amount': total_amount_reverse if total_amount_reverse else 0,
            }
        response_data = {k: v for k, v in all_amounts.items() if v != 0}
        sorted_items = sorted(response_data.items(), key=lambda item: abs(item[1]), reverse=True)
        return sorted_items, 200
    except Exception as e:
        return {'success': 0, 'error': str(e)}, 500



def get_transaction_summary(data: TransactionSummaryPayload):
    """
    Retrieves a summary of financial transactions for either drivers or customers 
    based on the specified date range and regions.

    Args:
        data (TransactionSummaryPayload): Request payload containing date range, time range,
                                          data type ("Drivers" or "Customers"), and region info.

    Returns:
        Tuple[List[Tuple[str, float]], int]: A sorted list of (transaction_type, amount) tuples with HTTP 200 status,
                                             or an error response with appropriate HTTP status code.
    """
    from_date_utc = convert_to_utc(data.from_date, data.from_time)
    to_date_utc = convert_to_utc(data.to_date, data.to_time)

    from_dt = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_dt = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_dt > to_dt:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    regions = []
    if data.search_region and data.search_region != Regions.ALL_REGIONS_ACCESS:
        regions = [int(r) for r in data.search_region.split(",")]

    if data.data_type.lower() == 'customers':
        return get_customer_transaction_summary(from_dt, to_dt, regions)
    else:
        return get_driver_transaction_summary(from_dt, to_dt, regions)
    
    
def get_customer_transaction_summary_admin(payload: TransactionSummaryCustomerAdminValidator):
    """
    Retrieves top 10 admins responsible for specific types of customer transactions 
    (Gift, Fine, Deduct, Add, Cancel) during the given date range and region.

    Args:
        payload (TransactionSummaryCustomerAdminValidator): Contains date range, time range, transaction type,
                                                            and region filters.

    Returns:
        List[Dict[str, Union[str, float]]]: A list of dicts with admin names and transaction totals.
                                            For "Cancel" type, includes both charge and reversal counts.
    """
    from_date_utc = convert_to_utc(payload.from_date, payload.from_time)
    to_date_utc = convert_to_utc(payload.to_date, payload.to_time)
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    # Parse regions
    regions = []
    if payload.search_region and payload.search_region != "-1":
        regions = [int(r) for r in payload.search_region.split(",")]

    # Cancel handling block
    if payload.data_type == "Cancel":
        cancel_query = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.sum(
                case(
                    (UserTrans.method.like('Cancellation charges for%'), UserTrans.amount / 100),
                    else_=0
                )
            ).label("cancellation_charges_count"),
            func.sum(
                case(
                    (UserTrans.method.like('Cancellation reversal for%'), UserTrans.amount / 100),
                    else_=0
                )
            ).label("cancellation_reversals_count")
        ).join(
            BookingCancelled, BookingCancelled.utransid == UserTrans.id
        ).join(
            Users, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.timestamp.between(from_date, to_date),
            Users.id.notin_(UserTrans.excluded_user_ids),
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            or_(
                UserTrans.method.like('Cancellation charges for%'),
                UserTrans.method.like('Cancellation reversal for%')
            )
        )

        if regions:
            cancel_query = cancel_query.join(Bookings, Bookings.id == BookingCancelled.booking)\
                                       .filter(Bookings.region.in_(regions))

        results = cancel_query.group_by(BookingCancelled.user)\
                              .order_by(func.sum(func.abs(UserTrans.amount) / 100).desc())\
                              .limit(10).all()

        return [
            {
                "admin_name": get_name_by_id(row.admin_id),
                "cancellation_charges_count": abs(row.cancellation_charges_count),
                "cancellation_reversals_count": abs(row.cancellation_reversals_count)
            }
            for row in results
            if row.cancellation_charges_count is not None and row.cancellation_reversals_count is not None
        ]

    # Default flow for Fine, Gift, Add, Deduct
    query = db.session.query(
        UserTrans.admin_id,
        func.sum(func.abs(UserTrans.amount) / 100).label("total_amount")
    ).join(
        Users, UserTrans.user_id == Users.id
    ).filter(
        UserTrans.timestamp.between(from_date, to_date),
        Users.id.notin_(UserTrans.excluded_user_ids),
    )

    if payload.data_type == "Fine":
        query = query.filter(
            or_(
                UserTrans.method == 'Fine',
                UserTrans.method == 'Admin panel'
            ),
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount < 0
        )
    elif payload.data_type == "Gift":
        query = query.filter(
            or_(
                UserTrans.method == 'Gift',
                UserTrans.method == 'Admin panel'
            ),
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount > 0
        )
    elif payload.data_type == "Deduct":
        query = query.filter(UserTrans.method == 'Deduct', UserTrans.amount < 0)
    elif payload.data_type == "Add":
        query = query.filter(UserTrans.method == 'Add', UserTrans.amount > 0)

    if regions:
        query = query.filter(
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0
            ).in_(regions)
        )

    results = query.group_by(UserTrans.admin_id)\
                   .order_by(func.sum(func.abs(UserTrans.amount) / 100).desc())\
                   .limit(10).all()

    return [
        {
            "admin_name": get_name_by_id(row[0]),
            "total_amount": float(row[1])
        }
        for row in results
    ]
    
    
def get_driver_transaction_summary_admin(payload: TransactionSummaryDriverAdminValidator):
    """
    Retrieves the top 10 admins responsible for specified driver transaction types (e.g., Fine, Gift, 
    Due Deduct, Withdraw, Cancel) based on the total absolute value of transactions in the selected 
    time frame and region.

    Args:
        payload (TransactionSummaryDriverAdminValidator): Request model including date range, transaction type,
                                                          and optional region filters.

    Returns:
        List[Dict[str, Union[str, float]]]: A list of transaction summaries by admin. For "Cancel" type, 
                                            includes charges and reversals separately.
    """
    from_date_utc = convert_to_utc(payload.from_date, payload.from_time)
    to_date_utc = convert_to_utc(payload.to_date, payload.to_time)
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    regions = []
    if payload.search_region and payload.search_region != "-1":
        regions = [int(r) for r in payload.search_region.split(",")]

    if payload.data_type == "Cancel":
        cancel_query = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.sum(case(
                (DriverTrans.method.like('Cancellation: Booking%'), DriverTrans.amount / 100),
                else_=0
            )).label("cancellation_charges_count"),
            func.sum(case(
                (DriverTrans.method.like('Reversal: Booking%'), DriverTrans.amount / 100),
                else_=0
            )).label("cancellation_reversals_count")
        ).join(
            Drivers, BookingCancelled.driver == Drivers.id
        ).join(
            DriverTrans, DriverTrans.id == BookingCancelled.dtransid
        ).join(
            Users, Users.id == Drivers.user
        ).filter(
            DriverTrans.timestamp.between(from_date, to_date),
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            Drivers.id.notin_(DriverTrans.excluded_driver_ids),
            or_(
                DriverTrans.method.like('Cancellation: Booking%'),
                DriverTrans.method.like('Reversal: Booking%')
            )
        )

        if regions:
            cancel_query = cancel_query.filter(Users.region.in_(regions))

        results = cancel_query.group_by(BookingCancelled.user)\
                              .order_by(func.sum(func.abs(DriverTrans.amount) / 100).desc())\
                              .limit(10).all()

        return [
            {
                "admin_name": get_name_by_id(row.admin_id),
                "cancellation_charges_count": abs(row.cancellation_charges_count),
                "cancellation_reversals_count": abs(row.cancellation_reversals_count)
            }
            for row in results
            if row.cancellation_charges_count is not None and row.cancellation_reversals_count is not None
        ]

    # Non-cancel transaction types
    query = db.session.query(
        DriverTrans.admin_name,
        func.sum(func.abs(DriverTrans.amount) / 100).label('total_amount')
    ).select_from(Drivers).join(
        Users, Users.id == Drivers.user
    ).join(
        DriverTrans, DriverTrans.driver_id == Drivers.id
    ).filter(
        DriverTrans.timestamp.between(from_date, to_date),
        Drivers.id.notin_(DriverTrans.excluded_driver_ids)
    )

    if regions:
        query = query.filter(Users.region.in_(regions))

    method_map = {
        "Fine": ("Admin Fine", "<"),
        "Gift": ("Gift", ">"),
        "Due Deduct": ("Due Deduction", ">"),
        "Withdraw": ("Withdraw", "<"),
        "T-Shirt": ("T-Shirt", None),
        "Registration": ("Registration", None),
        "Bag": ("Bag", None),
    }

    if payload.data_type in method_map:
        method, sign = method_map[payload.data_type]
        query = query.filter(DriverTrans.method == method)
        if sign == "<":
            query = query.filter(DriverTrans.amount < 0)
        elif sign == ">":
            query = query.filter(DriverTrans.amount > 0)

    results = query.group_by(DriverTrans.admin_name)\
                   .order_by(func.sum(func.abs(DriverTrans.amount) / 100).desc())\
                   .limit(10).all()

    return [
        {
            "admin_name": row[0],
            "total_amount": float(row[1])
        }
        for row in results if row[0]
    ]
    
    
def get_driver_inventory_count(payload: DriverInventoryCountValidator):
    """
    Computes the count of inventory items (T-shirts and bags) assigned to drivers 
    within a given time frame and optional region filter.

    Args:
        payload (DriverInventoryCountValidator): Contains date range, time range, and region(s).

    Returns:
        dict: A dictionary with inventory item sizes as keys (e.g., 'S', 'M', 'Bags') and 
              their respective counts as values. Only items with count > 0 are returned.

    Raises:
        ValueError: If the from_date is after the to_date.
    """
    from_date_str = payload.from_date
    to_date_str = payload.to_date
    from_time_str = payload.from_time
    to_time_str = payload.to_time

    from_date_utc = convert_to_utc(from_date_str, from_time_str)
    to_date_utc = convert_to_utc(to_date_str, to_time_str)

    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    regions = []
    if payload.search_region and payload.search_region != '-1':
        regions = [int(r) for r in payload.search_region.split(',')]

    inventory = {
        'S': 0, 'M': 0, 'L': 0, 'XL': 0, 'XXL': 0, 'XXXL': 0, 'Bags': 0,
    }

    transactions = (
        db.session.query(DriverTrans)
        .select_from(Drivers)
        .join(Users, Users.id == Drivers.user)
        .join(DriverTrans, DriverTrans.driver_id == Drivers.id)
        .filter(
            DriverTrans.method.in_(['T-Shirt', 'Registration', 'Bag']),
            DriverTrans.timestamp >= from_date,
            DriverTrans.timestamp <= to_date,
            Drivers.id.notin_(DriverTrans.excluded_driver_ids),
        )
    )
    if regions:
        transactions = transactions.filter(Users.region.in_(regions))

    transactions = transactions.all()

    for trans in transactions:
        desc = trans.description.strip() if trans.description else ""
        method = trans.method

        if method == 'T-Shirt' and '-' in desc:
            size, count_str = desc.split('-', 1)
            try:
                count = int(count_str)
            except ValueError:
                count = 0
            if size.upper() in inventory:
                inventory[size.upper()] += count

        elif method == 'Registration':
            tokens = [t.strip() for t in desc.split(',') if t.strip()]
            for token in tokens:
                if '-' in token:
                    code, count_str = token.split('-', 1)
                    try:
                        count = int(count_str)
                    except ValueError:
                        count = 0
                    if code.upper() == 'B':
                        inventory['Bags'] += count
                    elif code.upper() in inventory:
                        inventory[code.upper()] += count

        elif method == 'Bag':
            try:
                count = int(desc)
            except ValueError:
                count = 0
            inventory['Bags'] += count

    return {k: v for k, v in inventory.items() if v > 0}


def booking_summary_admin_service(payload: BookingSummaryAdminPayload):
    """
    Generates a summary of booking allocations or cancellations made by admins,
    based on provided filters such as date range, B2B flag, affiliate IDs, region,
    and sorting preference.

    Args:
        payload (BookingSummaryAdminPayload): Includes date/time range, data type ('Allocation' or 'Cancellation'),
                                              B2B flag, affiliate IDs, regions, and sort preference.

    Returns:
        Tuple[Union[List[Dict[str, Union[str, int]]], Dict[str, Union[int, str]]], int]: 
            - On success: A list of dictionaries with admin names and booking/cancellation counts, and status 200.
            - On error: A dictionary with error message and status code (400).
    """
    from_date_str, from_time_str = convert_to_utc(payload.from_date, payload.from_time)
    to_date_str, to_time_str = convert_to_utc(payload.to_date, payload.to_time)

    if from_date_str > to_date_str:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    is_b2b = payload.is_b2b
    aff_ids = payload.aff_ids
    regions = payload.regions
    sort_asc = (payload.sort == 0)

    if payload.data_type == "Allocation":
        q = db.session.query(
            BookingAlloc.alloc_id.label("admin_id"),
            func.count(BookingAlloc.id).label("alloc_count")
        ).join(
            Bookings, BookingAlloc.booking_id == Bookings.id
        ).join(
            AdminAccess, AdminAccess.admin_user_id == BookingAlloc.alloc_id
        )

        if is_b2b == 0:
            q = q.filter(Bookings.type < BookingParams.TYPE_C24)
        else:
            q = q.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(Bookings.type == Bookings.TYPE_B2B)
            if aff_ids:
                q = q.filter(AffBookingLogs.aff_id.in_(aff_ids))

        q = q.filter(
            and_(
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            )
        )

        if regions:
            q = q.filter(Bookings.region.in_(regions))

        order_clause = func.count(BookingAlloc.id).asc() if sort_asc else func.count(BookingAlloc.id).desc()

        rows = q.group_by(BookingAlloc.alloc_id).order_by(order_clause).limit(10).all()
        data = [{"admin_name": get_name_by_id(r.admin_id), "counts": int(r.alloc_count)} for r in rows]
        return data, 200

    elif payload.data_type == "Cancellation":
        q = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.count(BookingCancelled.id).label("cancel_count")
        ).join(Bookings, BookingCancelled.booking == Bookings.id)

        if is_b2b == 0:
            q = q.filter(Bookings.type < BookingParams.TYPE_C24)
        else:
            q = q.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(Bookings.type == Bookings.TYPE_B2B)
            if aff_ids:
                q = q.filter(AffBookingLogs.aff_id.in_(aff_ids))

        q = q.filter(
            Bookings.valid < 0,
            BookingCancelled.reason != BookingCancelled.RSN_REVERSE_CANCELLATION,
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            and_(
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            )
        )

        if regions:
            q = q.filter(Bookings.region.in_(regions))

        order_clause = func.count(BookingCancelled.id).asc() if sort_asc else func.count(BookingCancelled.id).desc()

        rows = q.group_by(BookingCancelled.user).order_by(order_clause).limit(10).all()
        data = [{"admin_name": get_name_by_id(r.admin_id), "counts": int(r.cancel_count)} for r in rows]
        return  data, 200

    return {'success': -4, 'error': f'Unknown data_type={payload.data_type}'}, 400


def parse_regions(region_str):
    """
    Parses a comma-separated string of region IDs into a list of integers.

    Args:
        region_str (str): Comma-separated region IDs, or None/"-1" for all regions.

    Returns:
        List[int]: List of region IDs as integers. Empty list means all regions.
    """
    if region_str and region_str != Regions.ALL_REGIONS_ACCESS:
        return [int(r) for r in region_str.split(',')]
    return []


def fetch_locations(data_type, from_date, to_date, regions):
    """
    Fetches geographical coordinates for various data types (e.g., bookings, drivers, customers)
    within a date range and filtered by regions.

    Args:
        data_type (str): Type of data to fetch (e.g., 'Completed Bookings', 'Active Drivers').
        from_date (datetime): Start date and time.
        to_date (datetime): End date and time.
        regions (List[int]): List of region IDs to filter results.

    Returns:
        List[dict]: A list of coordinate dictionaries in the format {'coordinates': [lng, lat]}.
    """
    # Original logic preserved, only extracted
    query = None

    if data_type == "Completed Bookings":
        query = db.session.query(Bookings.lat, Bookings.long).filter(
            Bookings.startdate >= from_date.date(),
            Bookings.startdate <= to_date.date(),
            Bookings.type < BookingParams.TYPE_C24,
            Bookings.valid.notin_([Bookings.CANCELLED_USER, Bookings.CANCELLED_DRIVER, Bookings.CANCELLED_D4M]),
            Bookings.region.in_(regions) if regions else True
        )

    elif data_type == "Cancelled Bookings":
        query = db.session.query(Bookings.lat, Bookings.long).filter(
            Bookings.startdate >= from_date.date(),
            Bookings.startdate <= to_date.date(),
            Bookings.type < BookingParams.TYPE_C24,
            Bookings.valid.in_([Bookings.CANCELLED_USER, Bookings.CANCELLED_DRIVER, Bookings.CANCELLED_D4M]),
            Bookings.region.in_(regions) if regions else True
        )

    elif data_type == "Maximum Searches":
        query = db.session.query(DriverSearch.reflat, DriverSearch.reflong).filter(
            DriverSearch.date >= from_date.date(),
            DriverSearch.date <= to_date.date(),
            DriverSearch.type < BookingParams.TYPE_C24,
            DriverSearch.region.in_(regions) if regions else True
        )

    elif data_type == "Active Drivers":
        subquery = db.session.query(
            DriverSetLoc.driver_id,
            func.date(DriverSetLoc.timestamp).label('date'),
            func.max(DriverSetLoc.timestamp).label('max_timestamp')
        ).filter(
            DriverSetLoc.timestamp >= from_date,
            DriverSetLoc.timestamp <= to_date
        ).group_by(
            DriverSetLoc.driver_id,
            func.date(DriverSetLoc.timestamp)
        ).subquery()

        query = db.session.query(
            DriverSetLoc.lat, DriverSetLoc.lng
        ).join(
            subquery,
            and_(
                DriverSetLoc.driver_id == subquery.c.driver_id,
                func.date(DriverSetLoc.timestamp) == subquery.c.date,
                DriverSetLoc.timestamp == subquery.c.max_timestamp
            )
        )

    elif data_type == "Home Drivers":
        query = db.session.query(
            DriverInfo.pres_addr_lat, DriverInfo.pres_addr_lng
        ).join(Drivers, DriverInfo.driver_id == Drivers.id) \
        .join(Users, Drivers.user == Users.id) \
        .filter(
            DriverInfo.timestamp >= from_date,
            DriverInfo.timestamp <= to_date,
            Users.region.in_(regions) if regions else True
        )

    elif data_type == "Reg Customers":
        query = db.session.query(
            UserRegistrationDetails.lat, UserRegistrationDetails.lng
        ).filter(
            UserRegistrationDetails.timestamp >= from_date,
            UserRegistrationDetails.timestamp <= to_date,
            UserRegistrationDetails.region.in_(regions) if regions else True
        )

    elif data_type == "Reg Drivers":
        query = db.session.query(
            DriverInfo.reg_lat, DriverInfo.reg_lng
        ).join(Drivers, DriverInfo.driver_id == Drivers.id) \
        .join(Users, Drivers.user == Users.id) \
        .filter(
            DriverInfo.timestamp >= from_date,
            DriverInfo.timestamp <= to_date,
            Users.region.in_(regions) if regions else True
        )

    elif data_type == "Active Customers":
        subquery = db.session.query(
            UserLoc.user_id,
            func.date(UserLoc.timestamp).label('date'),
            func.max(UserLoc.timestamp).label('max_timestamp')
        ).filter(
            UserLoc.timestamp >= from_date,
            UserLoc.timestamp <= to_date
        ).group_by(
            UserLoc.user_id,
            func.date(UserLoc.timestamp)
        ).subquery()

        query = db.session.query(
            UserLoc.lat, UserLoc.lng
        ).join(
            subquery,
            and_(
                UserLoc.user_id == subquery.c.user_id,
                func.date(UserLoc.timestamp) == subquery.c.date,
                UserLoc.timestamp == subquery.c.max_timestamp
            )
        )

    if not query:
        return None

    locations = query.all()
    return [
        {'coordinates': [lng, lat]}
        for lat, lng in locations
        if lat is not None and lng is not None and
        -90 <= lat <= 90 and -180 <= lng <= 180 and
        (not regions or Regions.find_region(lat, lng) in regions if data_type in ["Active Customers", "Active Drivers"] else True)
    ]
    
    
def get_live_routes_data(status_min: int) -> Tuple[List[dict], Dict[str, int]]:
    """
    Retrieves real-time route data for ongoing trips from both SQL and MongoDB,
    filtered by a minimum trip status.

    Args:
        status_min (int): Minimum status value to filter active trips.

    Returns:
        dict: A dictionary containing:
            - "data": A list of dictionaries with booking and trip route details.
            - "summary": A summary of counts by trip status and total documents.
    
    Raises:
        Exception: If any part of the data retrieval or transformation fails.
    """
    try:
        sql_records = db.session.query(Bookings.id, Bookings.driver, Trip.status)\
            .join(Trip, Bookings.id == Trip.book_id)\
            .filter(Bookings.valid == 1)\
            .filter(Trip.status > status_min)\
            .all()

        if not sql_records:
            return [], {}

        book_id_list = []
        book_meta = {}
        status_counts = {4: 0, 5: 0, 6: 0}

        for book_id, driver_id, status in sql_records:
            book_id_list.append(book_id)
            book_meta[book_id] = {
                "driver_id": driver_id,
                "trip_status": status
            }
            if status in status_counts:
                status_counts[status] += 1

        mongo_docs = list(AffiliateCollections.ongoing_booking_routes.find({
            "book_id": {"$in": book_id_list}
        }))

        final_data = []
        for doc in mongo_docs:
            book_id = doc.get("book_id")
            driver_id = doc.get("driver_id")
            trip_started = doc.get("trip_started", 0)
            history = doc.get("history", [])
            deviations = doc.get("deviations", [])
            last_known = doc.get("last_known", [])
            last_known_ts = doc.get("last_known_timestamp")

            final_data.append({
                "book_id": book_id,
                "driver_id": driver_id,
                "trip_status": book_meta.get(book_id, {}).get("trip_status", "Unknown"),
                "trip_started": trip_started,
                "history_count": len(history),
                "deviations_count": len(deviations),
                "last_known": last_known,
                "last_known_timestamp": last_known_ts.isoformat() if last_known_ts else None,
            })

        summary = {
            "status_4": status_counts[4],
            "status_5": status_counts[5],
            "status_6": status_counts[6],
            "total_documents": len(final_data)
        }

        return {"data":final_data,
                "summary": summary
                }
    except Exception:
        raise
    
def delete_ongoing_route_doc(book_id: int, driver_id: int) -> dict:
    """
    Deletes an ongoing route document for the specified booking and driver from MongoDB.

    Args:
        book_id (int): Booking ID.
        driver_id (int): Driver ID.

    Returns:
        Tuple[dict, int]: 
            - On success: Dictionary with success message and HTTP 200.
            - On failure: Dictionary with error message and HTTP 404 if document is not found.
    """
    result = AffiliateCollections.ongoing_booking_routes.delete_one({
        "book_id": book_id,
        "driver_id": driver_id
    })

    if result.deleted_count == 0:
        return {"success": 0, "message": "No document found to delete"}, 404

    return {"success": 1, "message": "Document deleted successfully"}, 200