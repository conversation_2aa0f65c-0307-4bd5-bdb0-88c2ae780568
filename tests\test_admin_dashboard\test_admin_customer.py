from models.models import Users,db,CustomerDetailsLog, AdminLog, UserTrans, Bookings
from conftest import create_user,unique_user_data
from datetime import datetime,timedelta

def test_customer_view_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    user = create_user(data, Users.ROLE_USER)
    user_id = user.id
    form_data = {
        'mobile': user.mobile,
        'region': '0'
    }
    # Call the API
    response = client.post('/api/admin/customer_details', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:",res_data)
    user = db.session.query(Users).filter_by(id=user_id).first()
    assert res_data['success'] == 1
    assert res_data['user_id'] == user.id
    assert res_data['user_fname'] == user.fname
    assert res_data['user_lname'] == user.lname
    assert res_data['user_mobile'] == user.mobile
    assert res_data['user_email'] == user.email
    assert res_data['credit'] == user.credit
    assert res_data['user_enabled'] == 1
    assert res_data['customer_remark'] == user.customer_remark
    assert res_data['user_mul_book_access'] == 0

def test_update_customer_details_success(client, admin_login):
    auth_headers, admin = admin_login
    data = unique_user_data()
    user = create_user(data, Users.ROLE_USER)
    user_id = user.id
    # Prepare form data to update user details
    form_data = {
        'user_id': user.id,
        'user_fname': 'UpdatedFirstName',
        'user_lname': 'UpdatedLastName',
        'user_mobile': '9876543210',
        'user_email': '<EMAIL>',
        'user_label': 7,
        'user_enabled': '1',  # Set user enabled
        'customer_remark': 'Updated remark',
        'region': '0'
    }
    # Call the API
    response = client.post('/api/admin/update_customer_details', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:", res_data)
    assert res_data['success'] == 1
    user = db.session.query(Users).filter_by(id=user_id).first()
    #assert res_data['message'] == 'User updated successfully (not user credits)'
    # Verify that the user's details have been updated in the database
    updated_user = Users.query.get(user.id)
    assert updated_user.fname == 'UpdatedFirstName'
    assert updated_user.lname == 'UpdatedLastName'
    assert updated_user.mobile == '9876543210'
    assert updated_user.email == '<EMAIL>'
    assert updated_user.label_bv == 7
    assert updated_user.enabled is True
    assert updated_user.customer_remark == 'Updated remark'

def test_customer_logs_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    user = create_user(data, Users.ROLE_USER)
    admin_id = admin.id
    # Create some customer logs for this user
    log1 = CustomerDetailsLog(
        user=user.id,
        editedby=admin.id,
        changes='Updated name',
        change_from='John',
        change_to='Johnny'
    )
    log2 = CustomerDetailsLog(
        user=user.id,
        editedby=admin.id,
        changes='Updated email',
        change_from='<EMAIL>',
        change_to='<EMAIL>'
    )
    db.session.add_all([log1, log2])
    db.session.commit()
    # Prepare form data with user ID
    form_data = {'user_id': user.id, 'region':'0'}
    # Call the API
    response = client.post('/api/admin/customer_log', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:", res_data)
    admin = db.session.query(Users).filter_by(id=admin_id).first()
    assert res_data['success'] == 1
    assert len(res_data['data']) == 2
    # Verify the returned log details
    first_log = res_data['data'][0]
    assert first_log['changedby'] == admin.get_name()  # Assuming `get_name_by_id` is fetching admin's name
    assert first_log['changes'] == 'Updated name'
    assert first_log['change_from'] == 'John'
    assert first_log['change_to'] == 'Johnny'
    second_log = res_data['data'][1]
    assert second_log['changedby'] == admin.get_name()
    assert second_log['changes'] == 'Updated email'
    assert second_log['change_from'] == '<EMAIL>'
    assert second_log['change_to'] == '<EMAIL>'

def test_add_customer_credit_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    admin_id = admin.id
    user_data = unique_user_data()
    user = create_user(user_data, Users.ROLE_USER)
    user_id = user.id
    # Initial credit amount for the user
    initial_credit = user.credit
    # Prepare form data for adding credit
    form_data = {
        'user_id': user.id,
        'amount': '100',
        'remark': 'Test credit addition',
        'credit_type': 'Add',
        'payment_mode': 'Credit Card',
        'trans_id': '12345',
        'region':'0'
    }
    # Call the API
    response = client.post('/api/admin/customer_credit', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:", res_data)
    assert res_data['success'] == 1
    admin = db.session.query(Users).filter_by(id=admin_id).first()
    user = db.session.query(Users).filter_by(id=user_id).first()
    # Verify that the user's credit has been updated
    updated_user = Users.query.get(user.id)
    assert updated_user.credit == initial_credit + 100
    admin_log = AdminLog.query.filter_by(user=admin.id).first()
    assert admin_log is not None

def test_customer_credit_log_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    admin_id = admin.id
    user_data = unique_user_data()
    user = create_user(user_data, Users.ROLE_USER)
    user_id = user.id
    # Create a transaction for the user
    user_trans = UserTrans(
        uid=user.id,
        amt=10000,  # 100.00 units
        status=UserTrans.COMPLETED,
        stop=True,
        admin_name=admin.get_name(),
        admin_id=admin.id,
        trans_payment_id='12345',
        description="Admin panel",
        method="Admin panel",
    )
    db.session.add(user_trans)
    user_trans_id = user_trans.id
    db.session.commit()
    # Prepare form data for the credit log
    form_data = {
        'mobile': user.mobile,
        'region':'0'
    }
    # Call the API to fetch the credit log
    response = client.post('/api/admin/fetch_customer_credit_log', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:", res_data)
    assert res_data['success'] == 1
    user = db.session.query(Users).filter_by(id=user_id).first()
    user_trans = db.session.query(UserTrans).filter_by(id=user_trans_id).first()
    admin = db.session.query(Users).filter_by(id=admin_id).first()
    # Verify the response contains the correct user and transaction details
    assert res_data['balance'] == round(user.credit, 2)  # User's balance should be rounded
    assert res_data['data'][0]['changed_for'] == f"{user.fname} {user.lname}"
    assert res_data['data'][0]['user_id'] == user.id
    # Check transaction details in the log
    trans_log = res_data['data'][1]  # Assuming this is the transaction entry
    assert trans_log['amt'] == round(user_trans.amount / 100, 2)  # Check the transaction amount
    assert trans_log['method'] == "Admin panel"
    assert trans_log['remark'] == f"{user_trans.remark}"
    assert trans_log['status'] == UserTrans.COMPLETED
    assert trans_log['changed_by'] == admin.get_name()
    assert trans_log['trans_id'] == '12345'
    
def test_search_by_mobile_user_success(client, admin_login):
    auth_headers, admin = admin_login
    user_data = unique_user_data()
    user = create_user(user_data, Users.ROLE_USER)
    user_id = user.id
    # Prepare form data for searching the user by mobile
    form_data = {
        'mobile': user.mobile,
        'region': '0'
    }
    # Call the API to search the user by mobile
    response = client.post('/api/admin/search_user_by_mobile', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print("Response:", res_data)
    assert res_data['success'] == 1
    user = db.session.query(Users).filter_by(id=user_id).first()
    # Verify the response contains the correct user details
    user_data = res_data['result']
    assert user_data['id'] == user.id
    assert user_data['name'] == user.get_name()
    assert user_data['mobile'] == user.mobile
    assert user_data['wallet'] == round(user.credit, 2)



# def test_search_customers_for_register_list_success(client, admin_login):
#     auth_headers, admin = admin_login
#     # Prepare sample data for the test
#     region = '1,2'  # Example regions (you can adjust based on your regions)
#     timestamp_gt = '2024-01-01'
#     timestamp_lt = '2024-12-31'
    
#     # Create test users
#     user_data_1 = unique_user_data()
#     user_1 = create_user(user_data_1, Users.ROLE_USER, region=1)  # User in region 1
#     # user_data_2 = unique_user_data()
#     # user_2 = create_user(user_data_2, Users.ROLE_USER, region=2)  # User in region 2
#     # Create bookings and trips for the users
#     # booking=Bookings(
#     #     user=user_1.id,
#     #     skey='some_secret_key',
#     #     driver= driver.id,
#     #     lat=0.0,
#     #     long=0.0,
#     #     starttime=datetime.utcnow().strftime("%H:%M:%S"),
#     #     startdate=datetime.utcnow().strftime("%Y-%m-%d"),
#     #     dur=datetime.utcnow().strftime("%H:%M:%S"),
#     #     endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
#     #     enddate=datetime.utcnow().strftime("%Y-%m-%d"),
#     #     estimate=100,
#     #     pre_tax=90,
#     #     loc='Test Location',
#     #     car_type=0,
#     #     )
#     # booking.valid=1
#     # db.session.add(booking)
#     # db.session.commit()
#     #create_booking(user_2.id, past_start_date=True)    # Completed booking for user 2
#     # Prepare form data for the search
#     form_data = {
#         'region': region,
#         'timestamp_gt': timestamp_gt,
#         'timestamp_lt': timestamp_lt,
#         'sort_by': '1',  # Example sort by option
#         'sort_order': 'desc'  # Example sort order
#     }
#     # Call the API
#     response = client.post('/api/admin/register_customer_list', data=form_data, headers=auth_headers)
#     # Validate response
#     assert response.status_code == 200
#     res_data = response.get_json()
#     print("Response:", res_data)
#     assert res_data['success'] == 1
#     assert isinstance(res_data['data'], list)
#     # Verify the response data contains the expected user information
#     customer_ids = {customer['customer_id'] for customer in res_data['data']}
#     assert user_1.id in customer_ids
#     # Check the details of user_1
#     user_1_details = next((customer for customer in res_data['data'] if customer['customer_id'] == user_1.id), None)
#     assert user_1_details is not None
#     assert user_1_details['mobile'] == user_1.mobile
#     assert user_1_details['name'] == f"{user_1.fname} {user_1.lname}"
#     assert user_1_details['region'] == 1
#     assert user_1_details['completed'] == 0  # Assuming completed bookings count for user 1 is 0
#     assert user_1_details['upcoming'] == 1  # Assuming 1 upcoming booking for user 1
