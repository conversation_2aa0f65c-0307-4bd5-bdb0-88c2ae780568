["tests/test_admin_dashboard/test_admin_affiliate_report.py::test_affiliate_report_success", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_as_draft", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_invalid_email_list_format", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_invalid_report_type", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_missing_required_fields", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_create_scheduled_report_success", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_edit_scheduled_report_invalid_report_type", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_edit_scheduled_report_report_not_found", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_edit_scheduled_report_success", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_edit_scheduled_report_to_draft", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_send_report_email_failure", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_send_report_invalid_date_format", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_send_report_missing_email_list", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_send_report_success", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_draft_report_status", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_already_set", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_draft_report", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_invalid_action_format", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_invalid_action_value", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_missing_action", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_report_not_found", "tests/test_admin_dashboard/test_admin_affiliate_report.py::test_update_scheduled_report_status_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_details_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_details_success_b2b", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_details_update_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_details_update_success_b2b", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_access_forbidden", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_admin_cancel_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_b2b_booking", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_b2b_booking_filter_check", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_b2b_search_by_vehNo", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_b2b_unallocate", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_code_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_driver_mobile_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_driver_name_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_future", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_is_b2c_default_olddate", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_mix_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_nofilter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_post_cancel_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_pre_cancel_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_region", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_region_same", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_time_boundary_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_time_boundary_with_filter_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_trip_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_unallocated", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_user_mobile_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_user_name_search", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_list_wrong_filter", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_logs_no_logs", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_logs_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_overview_isglobal_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_overview_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_trip_log_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_booking_trip_state_change_invalid_params", "tests/test_admin_dashboard/test_admin_bookings.py::test_driver_loc_exception", "tests/test_admin_dashboard/test_admin_bookings.py::test_driver_loc_success", "tests/test_admin_dashboard/test_admin_bookings.py::test_trip_state_change_success[0-TRIP_STOPPED]", "tests/test_admin_dashboard/test_admin_bookings.py::test_trip_state_change_success[2-TRIP_REACHED_DEST]", "tests/test_admin_dashboard/test_admin_bookings.py::test_trip_state_change_success[3-TRIP_STARTED]", "tests/test_admin_dashboard/test_admin_bookings.py::test_trip_state_change_success[5-TRIP_REACHED_SRC]", "tests/test_admin_dashboard/test_admin_bookings.py::test_trip_state_change_success[6-TRIP_INIT]", "tests/test_admin_dashboard/test_admin_coupon.py::test_active_coupon_missing_jwt_token", "tests/test_admin_dashboard/test_admin_coupon.py::test_active_coupon_no_active_coupons", "tests/test_admin_dashboard/test_admin_coupon.py::test_active_coupon_unauthorized_admin", "tests/test_admin_dashboard/test_admin_coupon.py::test_active_coupon_valid_active_coupons", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_duplicate_coupon_code", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_incomplete_details", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_invalid_cities_format", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_missing_jwt_token", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_success_general", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_success_specific", "tests/test_admin_dashboard/test_admin_coupon.py::test_add_coupon_unauthorized_admin", "tests/test_admin_dashboard/test_admin_coupon.py::test_admin_search_coupon_by_code", "tests/test_admin_dashboard/test_admin_coupon.py::test_admin_search_coupon_by_mobile", "tests/test_admin_dashboard/test_admin_coupon.py::test_admin_search_coupon_incomplete_search_term", "tests/test_admin_dashboard/test_admin_coupon.py::test_admin_search_coupon_no_coupons_found", "tests/test_admin_dashboard/test_admin_coupon.py::test_change_coupon_state_active_to_inactive", "tests/test_admin_dashboard/test_admin_coupon.py::test_change_coupon_state_coupon_not_found", "tests/test_admin_dashboard/test_admin_coupon.py::test_change_coupon_state_inactive_to_active", "tests/test_admin_dashboard/test_admin_coupon.py::test_change_expire_coupon_state_inactive_to_active", "tests/test_admin_dashboard/test_admin_coupon.py::test_inactive_coupon_inactive_coupons", "tests/test_admin_dashboard/test_admin_coupon.py::test_inactive_coupon_missing_jwt_token", "tests/test_admin_dashboard/test_admin_coupon.py::test_inactive_coupon_no_inactive_coupons", "tests/test_admin_dashboard/test_admin_coupon.py::test_inactive_coupon_unauthorized_admin", "tests/test_admin_dashboard/test_admin_create_booking.py::test_cb_incomplete_mobile_number", "tests/test_admin_dashboard/test_admin_create_booking.py::test_cb_misssing_jwt_token", "tests/test_admin_dashboard/test_admin_create_booking.py::test_cb_successful_user_registration", "tests/test_admin_dashboard/test_admin_create_booking.py::test_cb_unauthorized_admin_role", "tests/test_admin_dashboard/test_admin_create_booking.py::test_cb_user_already_exists", "tests/test_admin_dashboard/test_admin_customer.py::test_add_customer_credit_success", "tests/test_admin_dashboard/test_admin_customer.py::test_customer_credit_log_success", "tests/test_admin_dashboard/test_admin_customer.py::test_customer_logs_success", "tests/test_admin_dashboard/test_admin_customer.py::test_customer_view_success", "tests/test_admin_dashboard/test_admin_customer.py::test_search_by_mobile_user_success", "tests/test_admin_dashboard/test_admin_customer.py::test_update_customer_details_success", "tests/test_admin_dashboard/test_admin_driver.py::test_aadhaar_otp_attempts_exceeded", "tests/test_admin_dashboard/test_admin_driver.py::test_a<PERSON><PERSON><PERSON>_session_expired", "tests/test_admin_dashboard/test_admin_driver.py::test_a<PERSON>har_details_disputed", "tests/test_admin_dashboard/test_admin_driver.py::test_a<PERSON>har_details_verification_failure", "tests/test_admin_dashboard/test_admin_driver.py::test_add_driver_due_missing_fields", "tests/test_admin_dashboard/test_admin_driver.py::test_add_driver_due_success_fine", "tests/test_admin_dashboard/test_admin_driver.py::test_add_driver_due_success_withdraw", "tests/test_admin_dashboard/test_admin_driver.py::test_add_driver_due_unauthorized_access", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_bank_info_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_db_commit_failure", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_details_disputed", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_force_verify", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_missing_driver_id", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_successful_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_bank_reverify_verification_failed", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_account_blocked", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_account_closed", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_bank_info_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_driver_already_fetched", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_invalid_account_number", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_invalid_ifsc", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_missing_parameters", "tests/test_admin_dashboard/test_admin_driver.py::test_bankdoc_verify_successful_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_database_commit_failure", "tests/test_admin_dashboard/test_admin_driver.py::test_disputed_voter_details", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_approval_logs_missing_id", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_approval_logs_successful_data", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_details_missing_id", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_details_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_details_successful_data", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_dispute", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_db_commit_failure", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_details_disputed", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_driver_info_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_force_verify", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_missing_driver_id", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_successful_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_reverify_verification_failed", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_driver_info_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_failed_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_gridlines_api_error", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_gridlines_api_unexpected_error", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_license_already_fetched", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_license_not_exists", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_missing_parameters", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_dl_verify_success", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_due_log_driver_exists_old_format", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_due_log_missing_fields", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_due_log_user_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_trips_missing_id", "tests/test_admin_dashboard/test_admin_driver.py::test_driver_trips_successful_data", "tests/test_admin_dashboard/test_admin_driver.py::test_driving_id_already_fetched", "tests/test_admin_dashboard/test_admin_driver.py::test_external_api_request_exception", "tests/test_admin_dashboard/test_admin_driver.py::test_filter_by_region", "tests/test_admin_dashboard/test_admin_driver.py::test_force_verify_a<PERSON>har_details", "tests/test_admin_dashboard/test_admin_driver.py::test_force_verify_success", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_aadha<PERSON>_does_not_exist", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_aadha<PERSON>_no_mobile", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_already_sent", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_driver_info_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_exceed_limit", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_invalid_a<PERSON>haar", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_missing_driver_id", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_missing_id_no", "tests/test_admin_dashboard/test_admin_driver.py::test_generate_iddoc_otp_success", "tests/test_admin_dashboard/test_admin_driver.py::test_get_locality_missing_lat_long", "tests/test_admin_dashboard/test_admin_driver.py::test_get_locality_no_results", "tests/test_admin_dashboard/test_admin_driver.py::test_get_locality_success", "tests/test_admin_dashboard/test_admin_driver.py::test_iddoc_a_verify_driver_details_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_iddoc_a_verify_driver_not_found", "tests/test_admin_dashboard/test_admin_driver.py::test_iddoc_a_verify_driving_id_already_fetched", "tests/test_admin_dashboard/test_admin_driver.py::test_invalid_otp_or_a<PERSON><PERSON>ar", "tests/test_admin_dashboard/test_admin_driver.py::test_invalid_starting_and_no_of_logs", "tests/test_admin_dashboard/test_admin_driver.py::test_invalid_voter_id", "tests/test_admin_dashboard/test_admin_driver.py::test_match_voter_details_success", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_driver_id_a_verify", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_driver_id_parameter", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_id_no_parameter", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_iddoc_a_verify_driver_id", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_iddoc_a_verify_id_no", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_iddoc_a_verify_trans_id", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_required_parameters", "tests/test_admin_dashboard/test_admin_driver.py::test_missing_v_reverify", "tests/test_admin_dashboard/test_admin_driver.py::test_search_drivers_invalid_timestamp", "tests/test_admin_dashboard/test_admin_driver.py::test_successful_a<PERSON>har_details_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_successful_search", "tests/test_admin_dashboard/test_admin_driver.py::test_successful_voter_details_verification", "tests/test_admin_dashboard/test_admin_driver.py::test_unexpected_api_response_code", "tests/test_admin_dashboard/test_admin_driver.py::test_unexpected_internal_server_error", "tests/test_admin_dashboard/test_admin_driver.py::test_update_and_log_missing_driver_id", "tests/test_admin_dashboard/test_admin_driver.py::test_update_and_log_non_existing_driver", "tests/test_admin_dashboard/test_admin_driver.py::test_update_and_log_success", "tests/test_admin_dashboard/test_admin_driver.py::test_verification_failure", "tests/test_admin_dashboard/test_admin_driver.py::test_voter_details_fetched_but_failed_to_verify", "tests/test_admin_dashboard/test_admin_driver.py::test_voter_details_verified_but_disputed", "tests/test_admin_dashboard/test_admin_driver.py::test_voter_id_does_not_exist", "tests/test_admin_dashboard/test_admin_estimate.py::test_add_remark_success", "tests/test_admin_dashboard/test_admin_estimate.py::test_get_estimate_details_user_success", "tests/test_admin_dashboard/test_admin_estimate.py::test_get_user_search_booking_data_success", "tests/test_admin_dashboard/test_admin_login.py::test_disabled_user", "tests/test_admin_dashboard/test_admin_login.py::test_forbidden_role", "tests/test_admin_dashboard/test_admin_login.py::test_invalid_password", "tests/test_admin_dashboard/test_admin_login.py::test_missing_admin_access", "tests/test_admin_dashboard/test_admin_login.py::test_missing_credentials", "tests/test_admin_dashboard/test_admin_login.py::test_successful_superadmin_login", "tests/test_admin_dashboard/test_admin_login.py::test_user_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_addOccasion_city_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_addOccasion_form_incomplete", "tests/test_admin_dashboard/test_admin_pricing.py::test_addOccasion_trip_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_city_trip_type_incomplete", "tests/test_admin_dashboard/test_admin_pricing.py::test_fetch_city_success", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_delete_city_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_delete_form_incomplete", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_delete_trip_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_details_city_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_details_form_incomplete", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_details_trip_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_update_city_not_found", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_update_form_incomplete", "tests/test_admin_dashboard/test_admin_pricing.py::test_pricing_update_trip_not_found", "tests/test_admin_dashboard/test_admin_utilities.py::test_add_admin_access_success", "tests/test_admin_dashboard/test_admin_utilities.py::test_edit_admin_access_success", "tests/test_admin_dashboard/test_admin_utilities.py::test_get_admin_details_success", "tests/test_admin_dashboard/test_admin_utilities.py::test_list_admins_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_b2b_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_b2b_success_aff_filter", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_exception", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_missing_dates", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_missing_dates_b2b", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_count_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_daily_revenue_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_daily_revenue_success_b2b", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_daily_sales_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_daily_trips_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_daily_trips_success_b2b", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_rating_count_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_revenue_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_revenue_success_b2b", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_sales_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_sales_success_b2b", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_trips_success", "tests/test_admin_dashboard/test_analytics.py::test_admin_analytics_trips_success_b2b", "tests/test_admin_dashboard/test_analytics.py::test_analytics_affiliate_transactions", "tests/test_admin_dashboard/test_analytics.py::test_analytics_driver_earning", "tests/test_admin_dashboard/test_analytics.py::test_analytics_graph_trip_metrics_success", "tests/test_admin_dashboard/test_analytics.py::test_booking_summary_admin_allocation_success", "tests/test_admin_dashboard/test_analytics.py::test_booking_summary_admin_cancellation_success", "tests/test_admin_dashboard/test_analytics.py::test_customer_register_reg_count", "tests/test_admin_dashboard/test_analytics.py::test_customer_register_source_count", "tests/test_admin_dashboard/test_analytics.py::test_driver_inventory_count_success", "tests/test_admin_dashboard/test_analytics.py::test_transaction_summary_admin_customer", "tests/test_admin_dashboard/test_analytics.py::test_transaction_summary_admin_driver", "tests/test_admin_dashboard/test_analytics.py::test_transaction_summary_customer", "tests/test_admin_dashboard/test_analytics.py::test_transaction_summary_driver", "tests/test_admin_dashboard/test_booking_cancel.py::test_cancel_booking_success", "tests/test_admin_dashboard/test_booking_cancel.py::test_cancel_charge_success", "tests/test_admin_dashboard/test_booking_cancel.py::test_cancel_charge_waiver_success", "tests/test_admin_dashboard/test_booking_cancel.py::test_cancel_update_charge_success", "tests/test_admin_dashboard/test_booking_cancel.py::test_change_cancel_reason_already_reversed", "tests/test_admin_dashboard/test_booking_cancel.py::test_change_cancel_reason_incomplete_booking", "tests/test_admin_dashboard/test_booking_cancel.py::test_change_cancel_reason_success", "tests/test_admin_dashboard/test_booking_cancel.py::test_change_unalloc_reason_not_unalloc_booking", "tests/test_admin_dashboard/test_booking_cancel.py::test_change_unalloc_reason_success", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_booking_already_allocated", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_booking_cancelled", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_list_exception", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_list_filter_success", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_list_success", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_no_driver_found", "tests/test_admin_dashboard/test_driver_allocate.py::test_allocate_driver_success", "tests/test_admin_dashboard/test_driver_allocate.py::test_reallocate_driver_same_driver", "tests/test_admin_dashboard/test_driver_allocate.py::test_reallocate_driver_success", "tests/test_admin_dashboard/test_driver_allocate.py::test_reallocate_driver_trip_going", "tests/test_admin_dashboard/test_driver_allocate.py::test_unallocate_driver_success", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_ban_driver", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_driver_already_banned", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_driver_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_invalid_reason", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_missing_driver_or_reason", "tests/test_admin_dashboard/test_driver_strike.py::test_create_driver_strike_success", "tests/test_admin_dashboard/test_driver_strike.py::test_create_strike_reason_success", "tests/test_admin_dashboard/test_driver_strike.py::test_delete_strike_reason_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_get_driver_strike_details_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_get_driver_strike_details_success", "tests/test_admin_dashboard/test_driver_strike.py::test_get_driver_strike_history_driver_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_get_driver_strike_history_success", "tests/test_admin_dashboard/test_driver_strike.py::test_get_strike_reasons", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_invalid_reason", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_missing_fields", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_no_unresolved", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_not_found_in_set", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_reflows_cooldown_and_ban", "tests/test_admin_dashboard/test_driver_strike.py::test_modify_strike_success", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_driver_strike_success", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_reflow_cooldown_preserved", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_strike_driver_unbanned", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_strike_no_unresolved_strikes", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_strike_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_resolve_strike_target_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_update_strike_reason_log_created", "tests/test_admin_dashboard/test_driver_strike.py::test_update_strike_reason_no_changes", "tests/test_admin_dashboard/test_driver_strike.py::test_update_strike_reason_not_found", "tests/test_admin_dashboard/test_driver_strike.py::test_update_strike_reason_success", "tests/test_admin_dashboard/test_driver_strike.py::test_update_strike_reason_validation", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_incomplete_form", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_invalid_color_class", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_invalid_main_class", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_new_strike_valid_to_violation", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_new_strike_waiver_removed", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_reversal_violation_to_valid", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_reversal_waiver_applied", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_selfie_not_found", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_server_error", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_strike_id_handling", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_valid_to_valid_color_change", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_valid_uniform_waiver_ignored", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_edit_label_violation_to_violation_no_waiver_change", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_get_label_uniform_b2c_tagged_success", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_invalid_b2c", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_invalid_type", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_label_uniform_driver_not_found", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_label_uniform_invalid_main_class", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_label_uniform_success_not_company_tshirt_fine_waived", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_label_uniform_success_not_company_tshirt_not_fine_waived", "tests/test_admin_dashboard/test_driver_uniform_label.py::test_missing_parameters", "tests/test_affiliate/create_affiliate.py::test_add_slave_affiliate", "tests/test_affiliate/create_affiliate.py::test_delete_draft_affiliate", "tests/test_affiliate/create_affiliate.py::test_get_affiliate_data", "tests/test_affiliate/create_affiliate.py::test_get_affiliate_list", "tests/test_affiliate/create_affiliate.py::test_mark_favourite", "tests/test_affiliate/test_affiliate.py::test_affiliate_book_state_failure_incomplete_form", "tests/test_affiliate/test_affiliate.py::test_affiliate_book_state_failure_no_booking", "tests/test_affiliate/test_affiliate.py::test_affiliate_book_state_failure_unauthorized", "tests/test_affiliate/test_affiliate.py::test_affiliate_book_state_success", "tests/test_affiliate/test_affiliate.py::test_affiliate_trip_log_invalid_booking_id", "tests/test_affiliate/test_affiliate.py::test_affiliate_trip_log_missing_booking_id", "tests/test_affiliate/test_affiliate.py::test_affiliate_trip_log_unauthorized", "tests/test_affiliate/test_affiliate.py::test_fetch_partial_booking_details_invalid_booking_id", "tests/test_affiliate/test_affiliate.py::test_fetch_partial_booking_details_missing_booking_id", "tests/test_affiliate/test_affiliate.py::test_fetch_partial_booking_details_unauthorized", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_empty_date", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_invalid_date_format", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_start_date_after_end_date", "tests/test_affiliate/test_affiliate_analytics.py::test_allocation_counts_trips_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_empty_date", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_invalid_date_format", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_start_date_after_end_date", "tests/test_affiliate/test_affiliate_analytics.py::test_analytics_count_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_empty_date", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_invalid_date_format", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_start_date_after_end_date", "tests/test_affiliate/test_affiliate_analytics.py::test_cancellation_reason_counts_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_invalid_type", "tests/test_affiliate/test_affiliate_analytics.py::test_daily_expenses_trips_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_monthly_expenses_trips_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_monthly_expenses_trips_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_monthly_expenses_trips_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_monthly_expenses_trips_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_monthly_expenses_trips_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_pct_trip_delayed_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_pct_trip_delayed_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_pct_trip_delayed_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_pct_trip_delayed_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_pct_trip_delayed_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_trip_metrics_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_trip_metrics_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_trip_metrics_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_trip_metrics_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_trip_metrics_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_weekly_expenses_trips_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_weekly_expenses_trips_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_weekly_expenses_trips_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_weekly_expenses_trips_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_weekly_expenses_trips_successful_response", "tests/test_affiliate/test_affiliate_analytics.py::test_yearly_expenses_trips_empty_region", "tests/test_affiliate/test_affiliate_analytics.py::test_yearly_expenses_trips_incomplete_form_data", "tests/test_affiliate/test_affiliate_analytics.py::test_yearly_expenses_trips_invalid_affiliate_id", "tests/test_affiliate/test_affiliate_analytics.py::test_yearly_expenses_trips_invalid_region_format", "tests/test_affiliate/test_affiliate_analytics.py::test_yearly_expenses_trips_successful_response", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_incomplete_form", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_invalid_reason_format", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_success", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_charge_success_redis_off", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_incomplete_form", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_invalid_reason_format", "tests/test_affiliate/test_affiliate_book_admin.py::test_booking_cancel_success", "tests/test_affiliate/test_affiliate_book_admin.py::test_restart_affiliate_trip", "tests/test_affiliate/test_affiliate_booking.py::test_add_remark_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_add_remark_db_exception", "tests/test_affiliate/test_affiliate_booking.py::test_add_remark_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_add_remark_success", "tests/test_affiliate/test_affiliate_booking.py::test_add_remark_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_account_disabled", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_insufficient_balance", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_invalid_search_id", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_no_search_id", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_success", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_book_unauthorized_affiliate", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_account_disabled", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_db_error_on_bulk_save", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_missing_fields", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_success", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_unauthorized_access", "tests/test_affiliate/test_affiliate_booking.py::test_affiliate_search_with_return_trip", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_affiliate_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_after_checkin", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_already_cancelled_trip", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_invalid_reason", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_success_with_penalty", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_success_with_waiver_reason", "tests/test_affiliate/test_affiliate_booking.py::test_booking_cancel_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_exist", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_update_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_update_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_update_invalid_format_booking_id", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_update_success", "tests/test_affiliate/test_affiliate_booking.py::test_booking_details_update_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_driver_is_default", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_invalid_reason_format", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_success_with_penalty", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_trip_checked_in", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_cancel_charge_waiver_applied", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_change_after_checkin", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_db_error", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_driver_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_invalid_driver_id_format", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_same_driver", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_success_with_change", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_success_without_change", "tests/test_affiliate/test_affiliate_booking.py::test_driver_allocate_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_availability_off", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_booking_not_found", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_pagination_has_more", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_search_by_first_name", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_search_by_mobile", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_sort_ascending", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_success_default", "tests/test_affiliate/test_affiliate_booking.py::test_driver_list_unauthorized", "tests/test_affiliate/test_affiliate_booking.py::test_get_spoc_address_list_success", "tests/test_affiliate/test_affiliate_booking.py::test_spoc_address_list_incomplete_form", "tests/test_affiliate/test_affiliate_booking.py::test_spoc_address_list_unauthorized", "tests/test_affiliate/test_affiliate_login.py::test_affiliate_not_found", "tests/test_affiliate/test_affiliate_login.py::test_affiliate_rep_not_found", "tests/test_affiliate/test_affiliate_login.py::test_change_pass_unauthorized_access", "tests/test_affiliate/test_affiliate_login.py::test_first_login_user_type_2", "tests/test_affiliate/test_affiliate_login.py::test_incorrect_current_password", "tests/test_affiliate/test_affiliate_login.py::test_invalid_username", "tests/test_affiliate/test_affiliate_login.py::test_login_account_blocked", "tests/test_affiliate/test_affiliate_login.py::test_login_account_locked", "tests/test_affiliate/test_affiliate_login.py::test_login_dashboard_response", "tests/test_affiliate/test_affiliate_login.py::test_login_first_time_user", "tests/test_affiliate/test_affiliate_login.py::test_login_invalid_password", "tests/test_affiliate/test_affiliate_login.py::test_login_invalid_username", "tests/test_affiliate/test_affiliate_login.py::test_login_missing_fields", "tests/test_affiliate/test_affiliate_login.py::test_login_missing_password", "tests/test_affiliate/test_affiliate_login.py::test_login_success_otp_mode", "tests/test_affiliate/test_affiliate_login.py::test_login_success_password_mode", "tests/test_affiliate/test_affiliate_login.py::test_missing_fields", "tests/test_affiliate/test_affiliate_login.py::test_missing_password", "tests/test_affiliate/test_affiliate_login.py::test_missing_username_field", "tests/test_affiliate/test_affiliate_login.py::test_otp_gen_missing_username_field", "tests/test_affiliate/test_affiliate_login.py::test_successful_password_change", "tests/test_affiliate/test_affiliate_login.py::test_successful_password_reset", "tests/test_affiliate/test_affiliate_login.py::test_successful_username_change", "tests/test_affiliate/test_affiliate_login.py::test_type_1_password_already_set", "tests/test_affiliate/test_affiliate_login.py::test_unauthorized_access", "tests/test_affiliate/test_affiliate_login.py::test_username_already_exists", "tests/test_affiliate/test_affiliate_login.py::test_valid_request_send_otp", "tests/test_affiliate/test_affiliate_profile.py::test_get_all_addresses_internal_error", "tests/test_affiliate/test_affiliate_profile.py::test_get_all_addresses_unauthorized", "tests/test_affiliate/test_affiliate_recharge.py::test_add_credit_success", "tests/test_affiliate/test_affiliate_recharge.py::test_admin_affiliate_wallet_logs_success", "tests/test_affiliate/test_affiliate_recharge.py::test_affiliate_not_found", "tests/test_affiliate/test_affiliate_recharge.py::test_deduct_credit_success", "tests/test_affiliate/test_affiliate_recharge.py::test_get_affiliate_details_success", "tests/test_affiliate/test_affiliate_recharge.py::test_insufficient_balance_on_transfer", "tests/test_affiliate/test_affiliate_recharge.py::test_invalid_gst_number", "tests/test_affiliate/test_affiliate_recharge.py::test_missing_required_fields", "tests/test_affiliate/test_affiliate_recharge.py::test_transfer_credit_success", "tests/test_affiliate/test_affiliate_rep.py::test_add_address_missing_rep", "tests/test_affiliate/test_affiliate_rep.py::test_add_address_success", "tests/test_affiliate/test_affiliate_rep.py::test_add_new_spoc_success", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_all_reps_success", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_register_rep_incomplete_form", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_register_rep_invalid_affiliate", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_register_rep_invalid_regions", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_register_rep_success", "tests/test_affiliate/test_affiliate_rep.py::test_affiliate_register_rep_username_exists", "tests/test_affiliate/test_affiliate_rep.py::test_delete_address_success", "tests/test_affiliate/test_affiliate_rep.py::test_delete_affiliate_rep", "tests/test_affiliate/test_affiliate_rep.py::test_get_address_rep_not_found", "tests/test_affiliate/test_affiliate_rep.py::test_get_address_success", "tests/test_affiliate/test_affiliate_rep.py::test_get_affiliate_rep_details", "tests/test_affiliate/test_affiliate_rep.py::test_get_affiliate_rep_logs", "tests/test_affiliate/test_affiliate_rep.py::test_spoc_list_rep_missing_id", "tests/test_affiliate/test_affiliate_rep.py::test_spoc_list_rep_not_found", "tests/test_affiliate/test_affiliate_rep.py::test_spoc_list_rep_success", "tests/test_affiliate/test_affiliate_rep.py::test_update_address_success", "tests/test_affiliate/test_affiliate_rep.py::test_update_spoc_invalid_rep", "tests/test_affiliate/test_affiliate_rep.py::test_update_spoc_missing_rep", "tests/test_affiliate/test_affiliate_rep.py::test_update_spoc_success", "tests/test_affiliate/test_affiliate_wallet.py::test_credit_start_affiliate_account_disabled", "tests/test_affiliate/test_affiliate_wallet.py::test_credit_start_invalid_jwt_claims", "tests/test_affiliate/test_affiliate_wallet.py::test_credit_start_missing_fields", "tests/test_affiliate/test_affiliate_wallet.py::test_gateway_not_supported", "tests/test_affiliate/test_affiliate_wallet.py::test_gateway_order_creation_failure", "tests/test_affiliate/test_affiliate_wallet.py::test_successful_initiate_credit_order", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_affiliate_account_disabled", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_affiliate_not_found", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_invalid_amount", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_invalid_field_types", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_invalid_jwt_claims", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_missing_fields", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_success", "tests/test_affiliate/test_affiliate_wallet.py::test_transfer_money_unauthorized_affiliate", "tests/test_affiliate/test_affiliate_wallet.py::test_unauthorized_aff_id", "tests/test_customer_app/test_bookings.py::test_confirm_booking_expired_search", "tests/test_customer_app/test_bookings.py::test_confirm_booking_invalid_search_id", "tests/test_customer_app/test_bookings.py::test_confirm_booking_invalid_search_id_negative_one", "tests/test_customer_app/test_bookings.py::test_confirm_booking_search_not_found", "tests/test_customer_app/test_bookings.py::test_confirm_booking_success", "tests/test_customer_app/test_bookings.py::test_confirm_booking_user_negative_credit", "tests/test_customer_app/test_bookings.py::test_confirm_booking_validation_error", "tests/test_customer_app/test_bookings.py::test_invalid_coupon_code", "tests/test_customer_app/test_bookings.py::test_invalid_duration_format", "tests/test_customer_app/test_bookings.py::test_register_search_account_disabled", "tests/test_customer_app/test_bookings.py::test_register_search_invalid_jwt", "tests/test_customer_app/test_bookings.py::test_register_search_missing_fields", "tests/test_customer_app/test_bookings.py::test_register_search_past_pickup_time", "tests/test_customer_app/test_bookings.py::test_register_search_success", "tests/test_customer_app/test_bookings.py::test_register_search_success_coupon", "tests/test_customer_app/test_bookings.py::test_register_search_success_immediate", "tests/test_customer_app/test_bookings.py::test_register_search_success_night", "tests/test_customer_app/test_bookings.py::test_register_search_success_oneway", "tests/test_customer_app/test_login/test_driver_login.py::test_check_exists_driver_user_exists", "tests/test_customer_app/test_login/test_driver_login.py::test_check_exists_driver_user_not_a_driver", "tests/test_customer_app/test_login/test_driver_login.py::test_login_disabled_user", "tests/test_customer_app/test_login/test_driver_login.py::test_login_invalid_password", "tests/test_customer_app/test_login/test_driver_login.py::test_login_otp_success", "tests/test_customer_app/test_login/test_driver_login.py::test_login_success", "tests/test_customer_app/test_login/test_driver_login.py::test_refresh_disabled_user", "tests/test_customer_app/test_login/test_driver_login.py::test_refresh_success", "tests/test_customer_app/test_login/test_driver_login.py::test_refresh_with_expired_token", "tests/test_customer_app/test_login/test_driver_login.py::test_verify_user_disabled", "tests/test_customer_app/test_login/test_driver_login.py::test_verify_user_enabled", "tests/test_customer_app/test_login/test_user_login.py::test_analytics_token_insert", "tests/test_customer_app/test_login/test_user_login.py::test_analytics_token_update", "tests/test_customer_app/test_login/test_user_login.py::test_disabled_user", "tests/test_customer_app/test_login/test_user_login.py::test_fcm_multiple_devices", "tests/test_customer_app/test_login/test_user_login.py::test_fcm_token_empty", "tests/test_customer_app/test_login/test_user_login.py::test_fcm_token_insert", "tests/test_customer_app/test_login/test_user_login.py::test_fcm_token_missing_fields", "tests/test_customer_app/test_login/test_user_login.py::test_fcm_token_update", "tests/test_customer_app/test_login/test_user_login.py::test_invalid_otp", "tests/test_customer_app/test_login/test_user_login.py::test_invalid_password", "tests/test_customer_app/test_login/test_user_login.py::test_logout_with_expired_jwt_token", "tests/test_customer_app/test_login/test_user_login.py::test_logout_without_jwt_token", "tests/test_customer_app/test_login/test_user_login.py::test_missing_fields", "tests/test_customer_app/test_login/test_user_login.py::test_mobile_missing", "tests/test_customer_app/test_login/test_user_login.py::test_no_otp_generation", "tests/test_customer_app/test_login/test_user_login.py::test_otp_generation", "tests/test_customer_app/test_login/test_user_login.py::test_successful_logout", "tests/test_customer_app/test_login/test_user_login.py::test_successful_otp_login", "tests/test_customer_app/test_login/test_user_login.py::test_successful_password_login", "tests/test_customer_app/test_login/test_user_login.py::test_token_creation", "tests/test_customer_app/test_login/test_user_login.py::test_user_account_disabled", "tests/test_customer_app/test_login/test_user_login.py::test_user_does_not_exist", "tests/test_customer_app/test_login/test_user_login_new.py::test_disabled_user", "tests/test_customer_app/test_login/test_user_login_new.py::test_invalid_auth_type", "tests/test_customer_app/test_login/test_user_login_new.py::test_invalid_mobile_format", "tests/test_customer_app/test_login/test_user_login_new.py::test_invalid_password", "tests/test_customer_app/test_login/test_user_login_new.py::test_missing_fields", "tests/test_customer_app/test_login/test_user_login_new.py::test_successful_otp_login", "tests/test_customer_app/test_login/test_user_login_new.py::test_successful_password_login", "tests/test_customer_app/test_login/test_user_login_new.py::test_token_creation_cookies", "tests/test_customer_app/test_login/test_user_login_new.py::test_unsupported_country_code", "tests/test_customer_app/test_login/test_user_login_new.py::test_user_not_found", "tests/test_driver/test_driver_booking.py::test_book_missing_booking_id", "tests/test_driver/test_driver_booking.py::test_confirm_book_driver_not_enabled", "tests/test_driver/test_driver_booking.py::test_confirm_book_due_above_threshold", "tests/test_driver/test_driver_booking.py::test_driver_trans_log", "tests/test_driver/test_driver_booking.py::test_reject_booking_account_disabled", "tests/test_driver/test_driver_booking.py::test_reject_booking_invalid_booking_id", "tests/test_driver/test_driver_booking.py::test_reject_booking_invalid_role", "tests/test_driver/test_driver_booking.py::test_reject_booking_success", "tests/test_driver/test_driver_booking.py::test_set_analytics_token_success", "tests/test_driver/test_driver_booking.py::test_set_fcm_token_success", "tests/test_driver/test_driver_booking.py::test_start_credits", "tests/test_driver/test_driver_booking.py::test_view_credits", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_already_cancel", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_account_disabled", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_booking_notfound", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_incomplete_booking", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_not_driver", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_success", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_charge_trip_started", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_integrity_error", "tests/test_driver/test_driver_cancel.py::test_cancel_driver_success", "tests/test_driver/test_driver_important.py::test_list_confirm_account_disabled", "tests/test_driver/test_driver_important.py::test_list_confirm_invalid_role", "tests/test_driver/test_driver_important.py::test_list_confirm_no_bookings", "tests/test_driver/test_driver_important.py::test_list_confirm_with_bookings", "tests/test_driver/test_driver_important.py::test_list_confirm_with_bookings_b2b", "tests/test_driver/test_driver_important.py::test_ongoing_driver_invalid_role", "tests/test_driver/test_driver_important.py::test_ongoing_driver_not_trip", "tests/test_driver/test_driver_important.py::test_ongoing_driver_trip_b2b_success", "tests/test_driver/test_driver_important.py::test_ongoing_driver_trip_success", "tests/test_driver/test_driver_important.py::test_pending_cust_account_disabled", "tests/test_driver/test_driver_important.py::test_pending_cust_driver_with_no_bookings", "tests/test_driver/test_driver_important.py::test_pending_cust_success", "tests/test_driver/test_driver_profile.py::test_change_driver_available_success[0-1]", "tests/test_driver/test_driver_profile.py::test_change_driver_available_success[1-1]", "tests/test_driver/test_driver_profile.py::test_check_driver_available_success[0]", "tests/test_driver/test_driver_profile.py::test_check_driver_available_success[1]", "tests/test_driver/test_drivers_basics.py::test_exp_set_account_disabled", "tests/test_driver/test_drivers_basics.py::test_exp_set_failure", "tests/test_driver/test_drivers_basics.py::test_exp_set_not_driver", "tests/test_driver/test_drivers_basics.py::test_exp_set_success", "tests/test_driver/test_drivers_basics.py::test_get_earning_monthly", "tests/test_driver/test_drivers_basics.py::test_past_cust_success", "tests/test_driver/test_drivers_basics.py::test_reverify_disabled_account", "tests/test_driver/test_drivers_basics.py::test_reverify_not_verified", "tests/test_driver/test_drivers_basics.py::test_reverify_success", "tests/test_driver/test_drivers_basics.py::test_reverify_unapproved_driver", "tests/test_driver/test_drivers_basics.py::test_set_driver_loc_success", "tests/test_driver/test_drivers_basics.py::test_total_earning", "tests/test_driver/test_register_driver.py::test_register_driver_database_error", "tests/test_driver/test_register_driver.py::test_register_driver_incomplete_form", "tests/test_driver/test_register_driver.py::test_register_driver_success", "tests/test_driver/test_trips.py::test_driver_rate[0-200-1]", "tests/test_driver/test_trips.py::test_driver_rate[4-200-1]", "tests/test_driver/test_trips.py::test_driver_rate[7-200-1]", "tests/test_driver/test_trips.py::test_driver_rate[invalid-201--2]", "tests/test_driver/test_trips.py::test_trip_otp_validate[000000-403-OTP did not match]", "tests/test_driver/test_trips.py::test_trip_otp_validate[123456-200-None]", "tests/test_driver/test_trips.py::test_trip_state_change_disabled", "tests/test_driver/test_trips.py::test_trip_state_change_incorrect_change", "tests/test_driver/test_trips.py::test_trip_state_change_invalid_booking", "tests/test_driver/test_trips.py::test_trip_state_change_invalid_params", "tests/test_driver/test_trips.py::test_trip_state_change_invalid_role", "tests/test_driver/test_trips.py::test_trip_state_change_success[0-TRIP_STOPPED]", "tests/test_driver/test_trips.py::test_trip_state_change_success[1-TRIP_STOP_PIC]", "tests/test_driver/test_trips.py::test_trip_state_change_success[2-TRIP_REACHED_DEST]", "tests/test_driver/test_trips.py::test_trip_state_change_success[3-TRIP_STARTED]", "tests/test_driver/test_trips.py::test_trip_state_change_success[4-TRIP_START_PIC]", "tests/test_driver/test_trips.py::test_trip_state_change_success[5-TRIP_REACHED_SRC]", "tests/test_driver/test_trips.py::test_trip_state_change_success[6-TRIP_INIT]", "tests/test_login/test_admin_login.py::test_disabled_user", "tests/test_login/test_admin_login.py::test_forbidden_role", "tests/test_login/test_admin_login.py::test_invalid_password", "tests/test_login/test_admin_login.py::test_missing_admin_access", "tests/test_login/test_admin_login.py::test_missing_credentials", "tests/test_login/test_admin_login.py::test_successful_superadmin_login", "tests/test_login/test_admin_login.py::test_user_not_found", "tests/test_login/test_user_login_new.py::test_disabled_user", "tests/test_login/test_user_login_new.py::test_invalid_auth_type", "tests/test_login/test_user_login_new.py::test_invalid_mobile_format", "tests/test_login/test_user_login_new.py::test_invalid_password", "tests/test_login/test_user_login_new.py::test_missing_fields", "tests/test_login/test_user_login_new.py::test_successful_otp_login", "tests/test_login/test_user_login_new.py::test_successful_password_login", "tests/test_login/test_user_login_new.py::test_token_creation_cookies", "tests/test_login/test_user_login_new.py::test_unsupported_country_code", "tests/test_login/test_user_login_new.py::test_user_not_found", "tests/test_logout/test_driver_logout.py::test_logout", "tests/test_new_admin/test_admin_driver.py::test_aadhaar_otp_attempts_exceeded", "tests/test_new_admin/test_admin_driver.py::test_a<PERSON><PERSON><PERSON>_session_expired", "tests/test_new_admin/test_admin_driver.py::test_a<PERSON>har_details_disputed", "tests/test_new_admin/test_admin_driver.py::test_a<PERSON>har_details_verification_failure", "tests/test_new_admin/test_admin_driver.py::test_add_driver_due_missing_fields", "tests/test_new_admin/test_admin_driver.py::test_add_driver_due_success_fine", "tests/test_new_admin/test_admin_driver.py::test_add_driver_due_success_withdraw", "tests/test_new_admin/test_admin_driver.py::test_add_driver_due_unauthorized_access", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_bank_info_not_found", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_db_commit_failure", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_details_disputed", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_force_verify", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_missing_driver_id", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_successful_verification", "tests/test_new_admin/test_admin_driver.py::test_bank_reverify_verification_failed", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_account_blocked", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_account_closed", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_bank_info_not_found", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_driver_already_fetched", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_invalid_account_number", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_invalid_ifsc", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_missing_parameters", "tests/test_new_admin/test_admin_driver.py::test_bankdoc_verify_successful_verification", "tests/test_new_admin/test_admin_driver.py::test_database_commit_failure", "tests/test_new_admin/test_admin_driver.py::test_disputed_voter_details", "tests/test_new_admin/test_admin_driver.py::test_driver_approval_logs_missing_id", "tests/test_new_admin/test_admin_driver.py::test_driver_approval_logs_successful_data", "tests/test_new_admin/test_admin_driver.py::test_driver_details_missing_id", "tests/test_new_admin/test_admin_driver.py::test_driver_details_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_details_successful_data", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_dispute", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_db_commit_failure", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_details_disputed", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_driver_info_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_force_verify", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_missing_driver_id", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_successful_verification", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_reverify_verification_failed", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_driver_info_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_failed_verification", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_gridlines_api_error", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_gridlines_api_unexpected_error", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_license_already_fetched", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_license_not_exists", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_missing_parameters", "tests/test_new_admin/test_admin_driver.py::test_driver_dl_verify_success", "tests/test_new_admin/test_admin_driver.py::test_driver_due_log_driver_exists_old_format", "tests/test_new_admin/test_admin_driver.py::test_driver_due_log_missing_fields", "tests/test_new_admin/test_admin_driver.py::test_driver_due_log_user_not_found", "tests/test_new_admin/test_admin_driver.py::test_driver_trips_missing_id", "tests/test_new_admin/test_admin_driver.py::test_driver_trips_successful_data", "tests/test_new_admin/test_admin_driver.py::test_driving_id_already_fetched", "tests/test_new_admin/test_admin_driver.py::test_external_api_request_exception", "tests/test_new_admin/test_admin_driver.py::test_filter_by_region", "tests/test_new_admin/test_admin_driver.py::test_force_verify_a<PERSON>har_details", "tests/test_new_admin/test_admin_driver.py::test_force_verify_success", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_aadhaar_does_not_exist", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_aadha<PERSON>_no_mobile", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_already_sent", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_driver_info_not_found", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_exceed_limit", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_invalid_a<PERSON>haar", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_missing_driver_id", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_missing_id_no", "tests/test_new_admin/test_admin_driver.py::test_generate_iddoc_otp_success", "tests/test_new_admin/test_admin_driver.py::test_get_locality_missing_lat_long", "tests/test_new_admin/test_admin_driver.py::test_get_locality_no_results", "tests/test_new_admin/test_admin_driver.py::test_get_locality_success", "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driver_details_not_found", "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driver_not_found", "tests/test_new_admin/test_admin_driver.py::test_iddoc_a_verify_driving_id_already_fetched", "tests/test_new_admin/test_admin_driver.py::test_invalid_otp_or_a<PERSON><PERSON>ar", "tests/test_new_admin/test_admin_driver.py::test_invalid_starting_and_no_of_logs", "tests/test_new_admin/test_admin_driver.py::test_invalid_voter_id", "tests/test_new_admin/test_admin_driver.py::test_match_voter_details_success", "tests/test_new_admin/test_admin_driver.py::test_missing_driver_id_a_verify", "tests/test_new_admin/test_admin_driver.py::test_missing_driver_id_parameter", "tests/test_new_admin/test_admin_driver.py::test_missing_id_no_parameter", "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_driver_id", "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_id_no", "tests/test_new_admin/test_admin_driver.py::test_missing_iddoc_a_verify_trans_id", "tests/test_new_admin/test_admin_driver.py::test_missing_last_timestamp", "tests/test_new_admin/test_admin_driver.py::test_missing_required_parameters", "tests/test_new_admin/test_admin_driver.py::test_missing_v_reverify", "tests/test_new_admin/test_admin_driver.py::test_no_new_data", "tests/test_new_admin/test_admin_driver.py::test_search_drivers_invalid_timestamp", "tests/test_new_admin/test_admin_driver.py::test_successful_a<PERSON>har_details_verification", "tests/test_new_admin/test_admin_driver.py::test_successful_data_retrieval", "tests/test_new_admin/test_admin_driver.py::test_successful_search", "tests/test_new_admin/test_admin_driver.py::test_successful_voter_details_verification", "tests/test_new_admin/test_admin_driver.py::test_unexpected_api_response_code", "tests/test_new_admin/test_admin_driver.py::test_unexpected_internal_server_error", "tests/test_new_admin/test_admin_driver.py::test_update_and_log_missing_driver_id", "tests/test_new_admin/test_admin_driver.py::test_update_and_log_non_existing_driver", "tests/test_new_admin/test_admin_driver.py::test_update_and_log_success", "tests/test_new_admin/test_admin_driver.py::test_verification_failure", "tests/test_new_admin/test_admin_driver.py::test_voter_details_fetched_but_failed_to_verify", "tests/test_new_admin/test_admin_driver.py::test_voter_details_verified_but_disputed", "tests/test_new_admin/test_admin_driver.py::test_voter_id_does_not_exist", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_b2b_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_b2b_success_aff_filter", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_exception", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_missing_dates", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_missing_dates_b2b", "tests/test_new_admin/test_analytics.py::test_admin_analytics_count_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_revenue_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_revenue_success_b2b", "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_sales_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_trips_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_daily_trips_success_b2b", "tests/test_new_admin/test_analytics.py::test_admin_analytics_rating_count_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_revenue_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_revenue_success_b2b", "tests/test_new_admin/test_analytics.py::test_admin_analytics_sales_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_sales_success_b2b", "tests/test_new_admin/test_analytics.py::test_admin_analytics_trips_success", "tests/test_new_admin/test_analytics.py::test_admin_analytics_trips_success_b2b", "tests/test_new_admin/test_analytics.py::test_analytics_affiliate_transactions", "tests/test_new_admin/test_analytics.py::test_analytics_driver_earning", "tests/test_new_admin/test_analytics.py::test_analytics_graph_trip_metrics_success", "tests/test_new_admin/test_analytics.py::test_booking_summary_admin_allocation_success", "tests/test_new_admin/test_analytics.py::test_booking_summary_admin_cancellation_success", "tests/test_new_admin/test_analytics.py::test_customer_register_reg_count", "tests/test_new_admin/test_analytics.py::test_customer_register_source_count", "tests/test_new_admin/test_analytics.py::test_driver_inventory_count_success", "tests/test_new_admin/test_analytics.py::test_transaction_summary_admin_customer", "tests/test_new_admin/test_analytics.py::test_transaction_summary_admin_driver", "tests/test_new_admin/test_analytics.py::test_transaction_summary_customer", "tests/test_new_admin/test_analytics.py::test_transaction_summary_driver", "tests/test_pricing/test_price.py::test_price_minios_oneway", "tests/test_pricing/test_price.py::test_price_minos", "tests/test_pricing/test_price.py::test_price_oneway", "tests/test_pricing/test_price.py::test_price_os", "tests/test_pricing/test_price.py::test_price_os_oneway", "tests/test_pricing/test_price.py::test_price_redis_fail", "tests/test_pricing/test_price.py::test_price_round_trip"]