from models.models import Users, db, UserFCM,AdminUserLog
import random
from sqlalchemy import exc
from unittest.mock import patch
from faker import Faker

fake = Faker()
def unique_user_data():
    return {
        'mobile': f'{random.randint(7000000000, 9999999999)}',  # Random Indian mobile number
        'email': fake.email(), 
        'pwd': fake.text(),  
        'fname': fake.name(), 
        'lname': "<PERSON><PERSON>" 
    }
def create_user(data):
    user = Users(
        fname = data['fname'],
        lname = data['lname'],
        mobile = data['mobile'],
        email = data['email'],
        pwd = data['pwd'],  
        role = Users.ROLE_USER
    )
    try:
        db.session.add(user)
        db.session.commit()
        print(f"User created with ID: {user.id}")
    except exc.IntegrityError:
        db.session.rollback()

def test_successful_password_login(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': data['pwd'],
        'auth_type': 'password'
    }, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 200
    res_data = response.get_json()

    assert res_data['success'] == 1
    assert 'Login successful.' in res_data['message']
    assert 'user_id' in res_data['data']
    assert 'access_token' in res_data['data']
    assert 'refresh_token' in res_data['data']

#Successful OTP login
def test_successful_otp_login(client):
    data = unique_user_data()
    create_user(data)
    # Mock OTP verification to return True
    #monkeypatch.setattr(login_module._sms, 'verify_otp_gupshup', lambda phone, otp: True)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': '3487',
        'auth_type': 'otp'
    }, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 200
    res_data = response.get_json()

    assert res_data['success'] == 1
    assert res_data['data']['user_mobile'] == data['mobile']

# Missing required fields
def test_missing_fields(client):
    data = unique_user_data()
    create_user(data)
    # Missing auth_type
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile']
    })
    assert response.status_code == 400
    res_data = response.get_json()

    assert res_data['success'] == -7

# Invalid auth_type value
def test_invalid_auth_type(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': data['pwd'],
        'auth_type': 'invalid'
    })
    assert response.status_code == 400
    res_data = response.get_json()

    assert res_data['success'] == -6

# Invalid mobile format
def test_invalid_mobile_format(client):
    response = client.post('/auth/token/login', data={
        'mobile': '12345',
        'password': 'any',
        'auth_type': 'password'
    })
    assert response.status_code == 400
    res_data = response.get_json()

    assert res_data['success'] == -1

# Unsupported country code
def test_unsupported_country_code(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': data['pwd'],
        'auth_type': 'password',
        'countrycode': '+999'
    })
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -11

#Mobile not found
def test_user_not_found(client):
    data = unique_user_data()
    # Do not create the user
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': 'any',
        'auth_type': 'password'
    })
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -2

# Disabled (banned) user
def test_disabled_user(client):
    data = unique_user_data()
    create_user(data)
    # Disable user in DB
    user = Users.query.filter_by(mobile=data['mobile']).first()
    user.enabled = False
    db.session.commit()
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': data['pwd'],
        'auth_type': 'password'
    })
    assert response.status_code == 423
    res_data = response.get_json()
    assert res_data['success'] == -12

# Invalid password attempt
def test_invalid_password(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': 'wrongpwd',
        'auth_type': 'password'
    })
    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == -5
    assert 'Failed attempt: 1' in res_data['message']

# Token creation sets cookies
def test_token_creation_cookies(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/auth/token/login', data={
        'mobile': data['mobile'],
        'password': data['pwd'],
        'auth_type': 'password'
    })
    assert response.status_code == 200
    cookies = response.headers.getlist('Set-Cookie')
    assert any('access_token_cookie' in c for c in cookies)
    assert any('refresh_token_cookie' in c for c in cookies)