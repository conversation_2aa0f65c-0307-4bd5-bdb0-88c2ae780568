from db_config import db
from utils.security_utils import get_salt, get_pwd
from datetime import datetime
from sqlalchemy import PrimaryKeyConstraint
import hashlib
import html


# mongo collection
class AffiliateCollections:
    affiliates_details = None
    affiliates_book = None
    affiliates_notifications = None
    rep_access = None
    draft_affiliates = None
    booking_live_events= None
    booking_price_data = None
    ongoing_booking_routes = None
    @classmethod
    def init(cls, app):
        cls.affiliates_details = app.mdb["affiliates"]
        cls.affiliates_book = app.mdb["affiliate_book"]
        cls.affiliates_notifications = app.mdb['affiliate_notifications']
        cls.rep_access = app.mdb['rep_access']
        cls.draft_affiliates = app.mdb["draftaffiliates"]
        cls.booking_live_events = app.mdb['booking_live_events']
        cls.booking_price_data = app.mdb['booking_price_data']
        cls.ongoing_booking_routes = app.mdb['ongoing_booking_routes']
        
def init_aff_loc(app):
    AffiliateCollections.init(app)
    AffiliateCollections.booking_live_events.create_index(
        [("timestamp", 1)],
        expireAfterSeconds=2 * 24 * 60 * 60  # 2 days
    ) 
               
def get_affiliate_book():
    return AffiliateCollections.affiliates_book

class Affiliate(db.Model):

    APPROVED = 1
    UNAPPROVED = -1

    # Unique id if each affilaite will be generated
    __tablename__ = "affiliate"
    id = db.Column('aff_id', db.Integer, primary_key=True)
    client_name = db.Column('aff_name', db.Text, unique=True)
    display_name = db.Column('aff_dname', db.Text)
    logo = db.Column('aff_logo', db.Text)
    client_region = db.Column('aff_region', db.Text)
    slave = db.Column('aff_slave', db.JSON)
    master = db.Column('aff_master', db.Integer)
    admin = db.Column('aff_admin', db.Integer)
    notification_access = db.Column('aff_notification', db.BigInteger)
    reg = db.Column('aff_reg', db.DateTime)
    wallet = db.Column('aff_wallet', db.Float)
    wallet_threshold = db.Column('aff_wallet_th', db.Float)
    mapped_wallet_affiliate = db.Column('mapped_wallet_affiliate', db.Integer)
    enabled = db.Column('aff_enabled', db.Boolean)

    def __init__(self, client_name, display, client_region, master, admin, mapped_wallet_affiliate,notification=None, enabled = True, wallet_threshold = 0,logo=""):
        self.client_name = client_name
        self.display_name = display
        self.client_region = client_region
        self.slave = {"slaves": []}
        self.master = master
        self.admin = admin
        self.mapped_wallet_affiliate = mapped_wallet_affiliate
        self.notification_access = notification
        self.logo = logo
        self.wallet = 0
        self.enabled = enabled
        self.wallet_threshold = wallet_threshold
        self.reg = datetime.utcnow()

class DraftAffiliate(db.Model):

    APPROVED = 1
    UNAPPROVED = -1
    UNAPPROVED_MEMBER = -2

    # Unique id if each draft affiliate will be generated
    __tablename__ = "draftaffiliate"
    id = db.Column('draft_id', db.Integer, primary_key=True)
    client_name = db.Column('draft_name', db.Text, unique=True)
    display_name = db.Column('draft_dname', db.Text)
    logo = db.Column('draft_logo', db.Text)
    approved = db.Column('draft_approved', db.Integer)
    client_region = db.Column('draft_region', db.Text, default=0)
    slave = db.Column('draft_slave', db.JSON)
    master = db.Column('draft_master', db.Integer)
    admin = db.Column('draft_admin', db.Integer)
    notification_access = db.Column('draft_notification', db.BigInteger, nullable=False, default=0)
    reg = db.Column('draft_reg', db.DateTime)
    wallet = db.Column('draft_wallet', db.Float)
    wallet_threshold = db.Column('draft_wallet_th', db.Float)
    mapped_wallet_affiliate = db.Column('draft_mapped_wallet_affiliate', db.Integer)
    is_favourite = db.Column('is_favourite', db.Boolean, default=False)
    draft_creator_id = db.Column('draft_creator', db.String(40))
    draft_title = db.Column('draft_title', db.String(40))

    def __init__(self, client_name, display, client_region, master, admin,draft_creator_id,draft_title,is_favourite, mapped_wallet_affiliate,wallet_threshold=0, notification=None,logo=""):
        self.client_name = client_name
        self.display_name = display
        self.client_region = client_region
        self.slave = {"slaves": []}
        self.master = master
        self.admin = admin
        self.mapped_wallet_affiliate = mapped_wallet_affiliate
        self.notification_access = notification
        self.logo = logo
        self.approved = 0  # Draft status
        self.wallet = 0
        self.wallet_threshold = wallet_threshold
        self.reg = datetime.utcnow()
        self.is_favourite = is_favourite
        self.draft_creator_id = draft_creator_id
        self.draft_title = draft_title

class AffiliateRep(db.Model):
    APPROVED = 1
    UNAPPROVED = -1
    UNAPPROVED_MEMBER = -2

    __tablename__ = "affiliate_rep"

    id = db.Column('rep_id', db.Integer, primary_key=True)
    # primary key of affiliate model
    affiliate_id = db.Column('rep_aff_id', db.Integer)
    fullname = db.Column('rep_fullname', db.Text)
    user_name = db.Column('rep_username', db.Text, unique=True)
    mobile = db.Column('rep_mobile', db.String(15))
    email = db.Column('rep_email', db.String(100))
    pwd = db.Column('rep_password', db.Text)
    salt = db.Column('rep_pwd_salt', db.Text)
    notification_access = db.Column('rep_notification', db.BigInteger)
    tab_access = db.Column('rep_tab', db.BigInteger)
    region_access = db.Column('rep_region', db.Text)
    create_booking_access = db.Column('rep_cr_book_aff',db.Text)
    admin = db.Column('rep_admin', db.Integer) #affiliate_member
    is_first_login = db.Column('rep_first_login', db.Boolean)
    enabled = db.Column('rep_enabled', db.Boolean)
    reg_timestamp = db.Column('rep_registration', db.DateTime)

    def __init__(self, aff_id, fullname, user_name, mobile, pwd, admin, notification_access,tab_access,region_access,create_booking_access, email="", enabled=True):
        self.affiliate_id = aff_id
        self.fullname = fullname
        self.salt = get_salt()
        self.email = email
        self.user_name = user_name
        self.mobile = mobile
        self.pwd = get_pwd(pwd, self.salt)
        self.admin = admin
        self.notification_access = notification_access
        self.tab_access = tab_access
        self.region_access = region_access
        self.create_booking_access = create_booking_access
        self.is_first_login = True
        self.enabled = enabled
        self.reg_timestamp = datetime.utcnow()

class AffiliateRepLogs(db.Model):
    AFFILIATE_REP_CREATED=0
    AFFILIATE_REP_EDITED=1
    __tablename__ = 'affiliate_rep_logs'

    log_id = db.Column('aff_rep_log_id',db.Integer, primary_key=True, autoincrement=True)
    rep_id = db.Column('aff_rep_log_rid',db.Integer, nullable=False)
    action = db.Column('aff_rep_log_action',db.Integer, nullable=False)  # 0 for created, 1 for edited
    changed_by = db.Column('aff_rep_log_changed_by',db.Integer, nullable=False)
    changed_by_name = db.Column('aff_rep_log_changed_by_name',db.String(100), nullable=False)
    changes_made = db.Column('aff_rep_log_changes_made',db.Text, nullable=False)
    oldvalue = db.Column('aff_rep_log_old_value',db.Text, nullable=True)
    newvalue = db.Column('aff_rep_log_new_value',db.Text, nullable=True)
    created_at = db.Column('aff_rep_log_timestamp',db.DateTime, default=datetime.utcnow)

    def __init__(self, rep_id, action, changed_by, changed_by_name, changes_made, oldvalue=None, newvalue=None):
        self.rep_id = rep_id
        self.action = action
        self.changed_by = changed_by
        self.changed_by_name = changed_by_name
        self.changes_made = changes_made
        self.oldvalue = oldvalue
        self.newvalue = newvalue

class AffiliateToken(db.Model):
    __tablename__ = 'affiliate_token'
    __table_args__ = (PrimaryKeyConstraint('token_rep_id', 'token_refresh'),)
    rep_id = db.Column('token_rep_id', db.Integer)
    refresh = db.Column('token_refresh', db.String(200))
    expiry = db.Column('token_expiry', db.DateTime)
    agent = db.Column('token_agent', db.String(150))
    timestamp = db.Column('token_timestamp', db.DateTime)

    def __init__(self, rep_id, refresh, agent, expiry):
        self.rep_id = rep_id
        self.refresh = hashlib.sha512(refresh.encode('utf-8')).hexdigest()
        self.agent = agent
        self.expiry = expiry
        self.timestamp = datetime.utcnow()


class AffiliatePricingLogs(db.Model):
    __tablename__ = 'affiliate_pricing_logs'
    log_id = db.Column('id', db.Integer, primary_key=True)
    client_name = db.Column('aff_name', db.Text)
    changed_from = db.Column('changed_from', db.JSON)
    changed_to = db.Column('changed_to', db.JSON)

    def __init__(self, client_name, changed_from, changed_to):
        self.client_name = client_name
        self.changed_from = changed_from
        self.changed_to = changed_to

class AffiliateCustomLogs(db.Model):

    CATEGORY_TRIP_DATA = 0
    CATEGORY_TRIP_IMAGES = 1
    CATEGORY_CUSTOM_FORM_FIELD_ONEWAY = 2
    CATEGORY_CUSTOM_FORM_FIELD_ROUND = 3

    __tablename__ = 'affiliate_custom_logs'
    log_id = db.Column('id', db.Integer, primary_key=True)
    client_name = db.Column('aff_name', db.String(255))
    category = db.Column('category', db.String(60))
    changed_from = db.Column('changed_from', db.JSON)
    changed_to = db.Column('changed_to', db.JSON)


    def __init__(self, client_name, category, changed_from, changed_to):
        self.client_name = client_name
        self.category = category
        self.changed_from = changed_from
        self.changed_to = changed_to


class AffiliateLogs(db.Model):
    __tablename__ = 'affiliate_logs'
    log_id = db.Column('log_id', db.Integer, primary_key=True)
    log_date_time = db.Column('log_date_time', db.DateTime, nullable=False)
    changed_by = db.Column('changed_by', db.String(100), nullable=False)
    section = db.Column('section', db.String(100), nullable=False)
    changes_made = db.Column('changes_made', db.Text, nullable=False)
    changed_from = db.Column('changed_from', db.Text)
    changed_to = db.Column('changed_to', db.Text)
    affiliate_foreign_log_id = db.Column('affiliate_foreign_log_id', db.Integer, nullable=True)
    client_name = db.Column('aff_name', db.String(255))

    def __init__(self, log_date_time, changed_by, section, changes_made, changed_from=None, changed_to=None,affiliate_foreign_log_id=-1,client_name=""):
        self.log_date_time = log_date_time
        self.changed_by = changed_by
        self.section = section
        self.changes_made = changes_made
        self.changed_from = changed_from
        self.changed_to = changed_to
        self.affiliate_foreign_log_id = affiliate_foreign_log_id
        self.client_name = client_name

class AffiliateSpoc(db.Model):

    GLOBAL_SPOC = 1
    LOCAL_SPOC = 2

    __tablename__ = 'affiliate_spocs'

    spoc_id = db.Column('spoc_id', db.Integer, primary_key=True, autoincrement=True)
    spoc_name = db.Column('spoc_name', db.Text)
    spoc_mobile = db.Column('spoc_mobile', db.String(15), unique=True)
    aff_id = db.Column('spoc_aff_id', db.Integer)
    rep_id = db.Column('spoc_rep_id', db.Integer)
    spoc_type = db.Column('spoc_type', db.Integer)
    region = db.Column('spoc_region', db.Integer)
    reg = db.Column('spoc_reg', db.DateTime)

    def __init__(self, name, mobile, type, rep_id, aff_id, region=0):
        self.spoc_name = name
        self.spoc_mobile = mobile
        self.spoc_type = type
        self.rep_id = rep_id
        self.aff_id = aff_id
        self.region = region
        self.reg = datetime.utcnow()

class AffiliateAddress(db.Model):

    GLOBAL_ADDRESS = 1
    LOCAL_ADDRESS = 2

    __tablename__ = 'affiliate_addresses'

    add_id = db.Column('aa_add_id', db.Integer, primary_key=True, autoincrement=True)
    address = db.Column('aa_address', db.Text)
    nickname = db.Column('aa_nickname', db.String(50))
    region = db.Column('aa_region', db.Integer)
    latitude = db.Column('aa_latitude', db.Float)
    longitude = db.Column('aa_longitude', db.Float)
    address_type = db.Column('aa_add_type', db.Integer)
    aff_id = db.Column('aa_aff_id', db.Integer)
    rep_id = db.Column('aa_rep_id', db.Integer)

    def __init__(self, address, nickname, latitude, longitude, type, rep_id, aff_id, region=0):
        self.address = address
        self.nickname = nickname
        self.region = region
        self.latitude = latitude
        self.longitude = longitude
        self.address_type = type
        self.aff_id = aff_id
        self.rep_id = rep_id

class AddressSpoc(db.Model):
    __tablename__  = 'address_spocs'

    id = db.Column('as_id', db.Integer, primary_key=True, autoincrement=True)
    spoc_id = db.Column('as_spoc_id', db.Integer)
    spoc_name = db.Column('as_spoc_name', db.Text)
    address_id = db.Column('as_address_id', db.Integer)

    def __init__(self, spoc_id, name, address_id):
        self.spoc_id = spoc_id
        self.spoc_name = name
        self.address_id = address_id

class AffiliateWalletLogs(db.Model):
    # status
    INITIATED = 0
    COMPLETED = 1
    FAILED = 2

    #source
    SOURCE_ADMIN = 0
    SOURCE_AFFILIATE = 1
    SOURCE_DRIVER = 2

    __tablename__ = "affiliate_wallet_logs"

    id = db.Column('awl_id', db.String, primary_key=True)
    affiliate_id = db.Column('awl_aff_id', db.Integer)
    rep_id = db.Column('awl_rep_id', db.Integer)
    amount = db.Column('awl_amount', db.Float)
    method = db.Column('awl_method', db.String)
    status = db.Column('awl_status', db.Integer)
    from_account = db.Column('awl_from_account', db.Integer)
    to_account = db.Column('awl_to_account', db.Integer)
    wallet_before = db.Column('awl_wallet_before', db.Float)
    wallet_after = db.Column('awl_wallet_after', db.Float)
    start_timestamp = db.Column('awl_starttime', db.DateTime)
    stop_timestamp = db.Column('awl_stoptime', db.DateTime)
    order_id = db.Column('awl_order_id', db.String)
    payment_id = db.Column('awl_payment_id', db.String)
    timestamp = db.Column('awl_timestamp', db.DateTime)
    source = db.Column('awl_source', db.Integer)
    admin = db.Column('awl_admin', db.Integer)
    reason = db.Column('awl_reason', db.String)
    remark = db.Column('awl_remark', db.String)

    def __init__(self, amt, method="", aff_id=None, rep_id=None, from_account=None, to_account=None, status=1, wallet_before=0,
                 wallet_after=0, stop_ts=False, pay_id=None, oid=None, source=1, admin=None, reason="", remark=""):
        self.id = get_salt()
        self.affiliate_id = aff_id
        self.rep_id = rep_id
        self.amount = amt
        self.method = method
        self.status = status
        self.from_account = from_account
        self.to_account = to_account
        self.wallet_before = wallet_before
        self.wallet_after = wallet_after
        self.start_timestamp = datetime.utcnow()
        self.stop_timestamp = datetime.utcnow() if stop_ts else None
        self.payment_id = pay_id
        self.order_id = oid
        self.timestamp = datetime.utcnow()
        self.source = source
        self.admin = admin
        self.reason = reason
        self.remark = remark

class AffBookingLogs(db.Model):
    __tablename__ = 'affiliate_book_logs'
    id = db.Column('a_b_log_id', db.Integer, primary_key=True, autoincrement=True)
    rep_id = db.Column('a_b_rep_id', db.Integer)
    admin_id = db.Column('a_b_admin_id', db.Integer)
    aff_id = db.Column('a_b_aff_id', db.Integer)
    book_id = db.Column('a_b_book_id', db.Integer, unique=True)
    alloc_id = db.Column('a_b_alloc_id', db.Integer, unique=True)
    cancel_id = db.Column('a_b_cancel_id', db.Integer, unique=True)
    mapped_by = db.Column('a_b_mapped_by', db.Integer)
    raised_by = db.Column('a_b_raised_by', db.Integer)
    mapped_wallet = db.Column('a_b_mapped_wallet', db.Integer)
    timestamp = db.Column('a_b_log_timestamp', db.DateTime)
    comment = db.Column('a_b_comment', db.String(1000))

    def __init__(self, aff_id,mapped_by=None,raised_by=None,rep_id=None,admin_id=None,book_id=None,
                    alloc_id=None, cancel_id=None, mapped_wallet=None, comment=""):
        self.rep_id = rep_id
        self.aff_id = aff_id
        self.admin_id = admin_id
        self.book_id = book_id
        self.alloc_id = alloc_id
        self.cancel_id = cancel_id
        self.mapped_by = mapped_by
        self.raised_by = raised_by
        self.timestamp = datetime.utcnow()
        self.comment = comment
        self.mapped_wallet = mapped_wallet

class AffiliateDriverSearch(db.Model):
    __tablename__ = 'affiliate_driver_search'
    id = db.Column('search_id', db.String(40), primary_key=True)
    affiliate = db.Column('search_aff_id', db.Integer)
    rep_id = db.Column('search_rep_id', db.Integer)
    reflat = db.Column('search_reflat', db.Float)
    reflong = db.Column('search_reflong', db.Float)
    destlat =  db.Column('search_destlat', db.Float)
    destlong = db.Column('search_destlong', db.Float)
    car_type = db.Column('search_cartype', db.Integer)
    type = db.Column('search_type', db.Integer)
    trip_type = db.Column('search_trip_type', db.Integer)
    time = db.Column('search_time', db.Time)
    date = db.Column('search_date', db.Date)
    dur = db.Column('search_dur', db.Time)
    days = db.Column('search_dur_days', db.Integer)
    dist = db.Column('search_dist', db.Integer)
    insurance = db.Column('search_insurance', db.Boolean)
    insurance_ch = db.Column('search_insurance_ch', db.Float)
    estimate = db.Column('search_estimate', db.Float)
    cust_base_fare = db.Column('search_cust_base_fare', db.Float)
    cust_night_fare = db.Column('search_cust_night_fare', db.Float)
    driver_base_fare = db.Column('search_driver_base_fare', db.Float)
    driver_night_fare = db.Column('search_driver_night_fare', db.Float)
    region = db.Column('search_region', db.Integer)
    source = db.Column('search_source', db.String)
    price_id = db.Column('search_price_id', db.Integer)
    timestamp = db.Column('search_timestamp', db.DateTime)

    def __init__(self, id, affiliate, rep_id, car_type, reflat, reflong, dest_lat, dest_long, time, date, dur, timestamp,
                    type=1, trip_type=1, days=0, insurance=0, insurance_ch=0, region=0, dist=0, source='unknown',
                    estimate=0, cust_base=0, cust_night=0, driver_base=0, driver_night=0, price_id=0):
        self.id = id
        self.affiliate = affiliate
        self.rep_id = rep_id
        self.car_type = int(car_type)
        self.reflat = reflat
        self.reflong = reflong
        self.destlat = dest_lat
        self.destlong = dest_long
        self.time = time
        self.date = date
        self.dur = dur
        self.type = type
        self.trip_type = trip_type
        self.days = days
        self.dist = dist
        self.insurance = insurance
        self.insurance_ch = insurance_ch
        self.estimate = estimate
        self.cust_base_fare = cust_base
        self.cust_night_fare = cust_night
        self.driver_base_fare = driver_base
        self.driver_night_fare = driver_night
        self.region = region
        self.timestamp = timestamp
        self.price_id = price_id
        self.source = html.escape(source.lower())

class AffiliateTaxTrans(db.Model):

    DEFAULT_CGST_PERCENT = 9
    DEFAULT_SGST_PERCENT = 9
    DEFAULT_GST_PERCENT = 18
    DEFAULT_IGST_PERCENT = 18
    DEFAULT_TDS_PERCENT = 2

    __tablename__ = 'aff_tax_trans'
    trans_id = db.Column('tax_tid', db.String, primary_key=True)
    payer_gst_no = db.Column('tax_payer_gst', db.String)
    receiver_gst_no = db.Column('tax_receiver_gst', db.String)
    cgst_amt = db.Column('tax_cgst_amt', db.Float)
    sgst_amt = db.Column('tax_sgst_amt', db.Float)
    igst_amt = db.Column('tax_igst_amt', db.Float)
    total_gst_amt = db.Column('tax_total_gst_amt', db.Float)
    total_tds_amt = db.Column('tax_total_tds_amt', db.Float)

    def __init__(self, trans_id, payer_gst_no, receiver_gst_no, cgst_amt=0, sgst_amt=0, igst_amt=0, total_gst_amt=0,total_tds_amt=0):
        self.trans_id = trans_id
        self.payer_gst_no = payer_gst_no
        self.receiver_gst_no = receiver_gst_no
        self.cgst_amt = cgst_amt
        self.sgst_amt = sgst_amt
        self.igst_amt = igst_amt
        self.total_gst_amt = total_gst_amt
        self.total_tds_amt = total_tds_amt

class ClientGstDetails(db.Model):
    __tablename__ = 'client_gst_details'
    id = db.Column('cgd_id', db.Integer, primary_key=True)
    client_id = db.Column('cgd_client_id', db.Integer)
    client_gst_no = db.Column('cgd_gst_no', db.String)
    state_code = db.Column('cgd_state_code', db.Integer)
    address = db.Column('cgd_address', db.String)
    legal_name = db.Column('cgd_legal_name', db.String)
    trade_name = db.Column('cgd_trade_name', db.String)
    email = db.Column('cgd_email', db.String)
    phone_no = db.Column('cgd_phone', db.String)

    def __init__(self, client_id,client_gst_no, state_code, address=None, legal_name=None, trade_name=None, email=None, phone_no=None):
        self.client_id = client_id
        self.client_gst_no = client_gst_no
        self.state_code = state_code
        self.address = address
        self.legal_name = legal_name
        self.trade_name = trade_name
        self.email = email
        self.phone_no = phone_no


class AffiliatePriceMapping(db.Model):
    __tablename__ = 'aff_price_mapping'
    aff_price_map_id = db.Column('aff_price_map_id', db.Integer, primary_key=True, autoincrement=True)
    aff_id = db.Column('aff_id', db.Integer)  # Typically a primary key
    mapped_region = db.Column('aff_mapped_region', db.Integer)
    mapped_trip_type = db.Column('aff_mapped_trip_type', db.Integer)
    mapped_price_affiliate = db.Column('aff_map_price_aff', db.Integer)
    mapped_price_affiliate_name = db.Column('aff_map_price_aff_name', db.Text)

    def __init__(self, aff_id, mapped_region, mapped_trip_type, mapped_price_affiliate,mapped_price_affiliate_name):
        self.aff_id = aff_id
        self.mapped_region = mapped_region
        self.mapped_trip_type = mapped_trip_type
        self.mapped_price_affiliate = mapped_price_affiliate
        self.mapped_price_affiliate_name = mapped_price_affiliate_name


class SpocLogs(db.Model):
    __tablename__ = 'spoc_logs'

    # Constant definitions for state
    SPOC_ADD = 0
    SPOC_UPDATE = 1
    SPOC_DELETE = 2

    # Constant definitions for add_source
    ADDED_BY_ADMIN = 0
    ADDED_BY_REPRESENTATIVE = 1

    spoc_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    rep_id = db.Column(db.Integer,nullable=True)
    admin_id = db.Column(db.Integer,nullable=True)
    add_source = db.Column(db.Integer)  # 0: added by admin, 1: added by representative
    spoc_state = db.Column(db.Integer)  # 0: add, 1: update, 2: delete
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)


    def __init__(self, add_source, spoc_state, rep_id=None, admin_id=None):
        self.rep_id = rep_id
        self.admin_id = admin_id
        self.add_source = add_source
        self.spoc_state = spoc_state
        self.timestamp = datetime.utcnow()


class AddressLogs(db.Model):
    __tablename__ = 'address_logs'

    # Constant definitions for state
    ADDRESS_ADD = 0
    ADDRESS_UPDATE = 1
    ADDRESS_DELETE = 2

    # Constant definitions for add_source
    ADDED_BY_ADMIN = 0
    ADDED_BY_REPRESENTATIVE = 1

    address_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    rep_id = db.Column(db.Integer,nullable=True)
    admin_id = db.Column(db.Integer,nullable=True)
    add_source = db.Column(db.Integer)  # 0: added by admin, 1: added by representative
    address_state = db.Column(db.Integer)  # 0: add, 1: update, 2: delete
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, add_source, address_state,rep_id=None, admin_id=None):
        self.rep_id = rep_id
        self.admin_id = admin_id
        self.add_source = add_source
        self.address_state = address_state
        self.timestamp = datetime.utcnow()