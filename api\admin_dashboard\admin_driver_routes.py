#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import datetime
from datetime import date

from pydantic import BaseModel, Field, field_validator,ValidationError
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from typing import Optional
from flasgger import swag_from
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from services.admin_dashboard.admin_driver_service import driver_search_service,fetch_single_driver_profile\
    ,fetch_driver_trip_logs, get_all_driver_approval_logs_service, get_driver_details_service, update_driver_and_log, \
        get_locality_service
from services.admin_dashboard.driver_verify_service import driver_dl_verify_service, driver_dl_reverify_service, \
    verify_driver_bank_doc
from flask import current_app as app
from utils.response_utils import standard_response
from schemas.admin_dashboard.driver_schemas import DriverApprovalLogParams, DriverDetailsParams,UpdateDriverRequest, \
    GetLocalityPayload, DriverDLVerifyPayload,DriverDLReverifySchema, BankDocVerifyPayload, DashDriverSearchParams, \
        SingleDriverRequestSchema,DriverTripsLogPayload
from models.models import db

admin_driver = Blueprint('admin_driver', __name__)
    
    
@admin_driver.route('/api/admin/dash/search', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/dash_search.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def search_users_drivers():
    """
    Search for drivers in the admin dashboard using filters like region, status, approval, rating, date, and location.

    Args (form-data):
        starting_from (int): Offset for pagination, must be ≥ 0.
        no_of_logs (int): Number of records to fetch, must be ≥ 1.
        search_query (str, optional): Search term (e.g. driver name or ID).
        region (str, optional): Region filter.
        status (str, optional): Driver status filter.
        approval (str, optional): Approval status.
        rating_gt (float, optional): Minimum rating.
        rating_lt (float, optional): Maximum rating.
        timestamp_gt (str, optional): Start datetime (format: 'YYYY-MM-DD HH:MM:SS').
        timestamp_lt (str, optional): End datetime (format: 'YYYY-MM-DD HH:MM:SS').
        is_global (int, optional): Global search scope flag (0, 1, or 2).
        label (str, optional): Driver label.
        ongoing (str, optional): Ongoing status filter.
        sort_by (str, optional): Sorting field (default: "1").
        sort_order (str, optional): Sorting order ("asc" or "desc").
        lat (float, optional): Latitude for location-based search.
        long (float, optional): Longitude.
        radius_km (float, optional): Radius in kilometers.

    Returns:
        JSON response with:
            - success (int)
            - message (str)
            - response_status (str)
            - data (list of drivers)
            - status (int HTTP code)
    """
    try:
        form_data = request.form.to_dict()
        validated = DashDriverSearchParams(**form_data)
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        app.logger.warning(f"Validation error in search_users_drivers: {error_details}")
        return jsonify(standard_response(
            success=-1,
            status=422,
            response_status="error",
            message="Invalid Input",
            data = {"error":error_details}
        )), 422
    try:
        service_data, status_code = driver_search_service(validated)
        if status_code != 200:
                    return jsonify(standard_response(
                        success=-1,
                        status=status_code,
                        response_status="error",
                        message=service_data.get("error", "Failed to fetch driver search results")
                    )), status_code
        response_payload = {
            "count": service_data.get("count", 0),
            "lastentry": service_data.get("lastentry"),
            "results": service_data.get("data", [])
        }
        return jsonify(standard_response(
            success=1,
            status=200,
            response_status="success",
            message="Driver search successful",
            data=response_payload
        )), 200

    except Exception as e:
        app.logger.error(f"[search_users_drivers] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            response_status="error",
            message="Internal Server Error"
        )), 500
    

@admin_driver.route('/api/admin/single/driver', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/dash_search.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def single_driver_admin():
    """
    Fetch profile details for a specific driver using driver ID.

    Args (form-data):
        driver_id (int): Unique driver ID.

    Returns:
        JSON response with:
            - success
            - message
            - data (driver profile)
            - status
    """
    try:
        payload = SingleDriverRequestSchema(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message=error_details,
            response_status="error"
        )), 422

    try:
        data = fetch_single_driver_profile(payload.driver_id)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Driver fetched successfully",
            data=data,
            response_status="success"
        )), 200
        
    except Exception as e:
        app.logger.error(f"[single_driver_admin] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message=str(e),
            response_status="error"
        )), 500
   
    
@admin_driver.route('/api/admin/driver_trips/logs', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/get_driver_trips.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def get_driver_trips():
    """
    Fetch paginated trip logs of a driver, filtered by date range and search query.

    Args (form-data):
        driver_id (int): Driver ID.
        sort_by (int, optional): Sorting criteria (default: 1).
        offset (int, optional): Pagination offset (default: 0).
        limit (int, optional): Number of logs per page (default: 10).
        start_date (date, optional): Start date of trip filter.
        end_date (date, optional): End date of trip filter.
        search_query (str, optional): Search term for trips.
        book_type (int, optional): Booking type filter.

    Headers:
        X-Timezone (str, optional): Client timezone for timestamp conversion.

    Returns:
        JSON response with:
            - success
            - message
            - data (list of trips)
            - status
    """
    try:
        payload = DriverTripsLogPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid input",
            data={"error": error_details},
            response_status="error"
        )), 422
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    try:
        result = fetch_driver_trip_logs(payload.dict(), tz)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Driver trip logs fetched successfully",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to fetch driver trip logs",
            data={"error": str(e)},
            response_status="error"
        )), 500
        
        

    
@admin_driver.route('/api/admin/driver_approval_log/all', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/get_all_driver_approval_logs.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def get_all_logs():
    """
    Fetch logs related to driver approval changes.

    Args (form-data):
        driver_id (str): Driver ID.
        sortby (str, optional): Field to sort by (default: 'timestamp').
        sorttype (str, optional): Sort order ('0' or '1').
        fromdate (str, optional): Start date (YYYY-MM-DD).
        todate (str, optional): End date (YYYY-MM-DD).
        search_query (str, optional): Search string.
        approval (str, optional): Approval type.
        editedby (str, optional): Admin who made changes.
        change_from (str, optional): Old value filter.
        change_to (str, optional): New value filter.
        offset (int, optional): Pagination offset.
        limit (int, optional): Number of logs.

    Returns:
        JSON response with:
            - success
            - message
            - data (list of approval changes)
            - status
    """
    form_data = request.form.to_dict()
    try:
        payload = DriverApprovalLogParams(**form_data)
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid input",
            data={"error": error_details},
            response_status="error"
        )), 422

    tz = request.headers.get("X-Timezone", "Asia/Kolkata")
    try:
        logs = get_all_driver_approval_logs_service(payload, tz)

        return jsonify(standard_response(
            success=1,
            status=200,
            message="Driver approval logs fetched successfully",
            data=logs
        )), 200

    except Exception as e:
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to fetch driver approval logs",
            data={"error": str(e)},
            response_status="error"
        )), 500
    
@admin_driver.route('/api/admin/driver/details', methods=['GET'])
@swag_from('/app/swagger_docs/driver_admin/driver_details.yml')
@jwt_required()
def get_driver_details():
    """
    Fetch driver details using driver_id or driver_user_id.

    Args (form-data):
        driver_id (int, optional): Driver ID.
        driver_user_id (int, optional): Driver user ID.

    Note:
        At least one of `driver_id` or `driver_user_id` must be provided.

    Returns:
        JSON response with:
            - success
            - message
            - data (driver details)
            - status
    """
    try:
        payload = DriverDetailsParams(**request.args.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid input",
            data={"error": error_details},
            response_status="error"
        )), 422

    try:
        result = get_driver_details_service(payload.driver_id, payload.driver_user_id)
        if result["status"] == "success":
            return jsonify(standard_response(
            success=1,
            status=200,
            message=result["message"],
            data=result["data"],
            response_status="success"
        )), 200
        else:
            return jsonify(standard_response(
            success=-1,
            status=400,
            message=result["message"],
            data=result,
            response_status="error"
        )), 400
    except Exception as e:
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to fetch driver details",
            data={"error": str(e)},
            response_status="error"
        )), 500
        
@admin_driver.route('/api/admin/driver/updateandlog', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/update_and_log.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def update_and_log_driver():
    """
    Update driver details (approval, region, remarks) by admin.

    Args (form-data):
        driver_id (int): Driver ID.
        approval (str): Approval status.
        editedby (str): Admin performing the update.
        search_region (str): Region context for search.
        remark (str): Remark added by admin.

        other update fiels comma sepeated
        eg approved 1,0 (new,old)
    Returns:
        JSON response with:
            - success
            - message
            - status
    """
    data = request.form.to_dict()
    files = request.files.to_dict()
    try:  
        payload = UpdateDriverRequest(**data)
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid input",
            data={"error": error_details},
            response_status="error"
        )), 422
    try:
        result = update_driver_and_log(data,files)
        if result["status_code"] == 200:
            return jsonify(standard_response(
            success=1,
            status=200,
            message=result["message"],
            data=result
        )), 200
        else:
            return jsonify(standard_response(
            success=result['success'],
            status=result['status_code'],
            message=result["message"],
            data=result
        )), result['status_code']
        
    except Exception as e:
        app.logger.error(f"[update_and_log_driver] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to update driver",
            data={"error": str(e)},
            response_status="error"
        )), 500
        
@admin_driver.route('/api/admin/getlocality', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/get_locality.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS, Tabs.DRIVER_REGISTER])
def get_locality():
    """
    Resolve location details from latitude and longitude.

    Args (form-data):
        lat (float): Latitude.
        long (float): Longitude.

    Returns:
        JSON response with:
            - success
            - message
            - data (locality info)
            - status
    """
    try:
        data = GetLocalityPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(success=-1, message='Invalid input', response_status='error', 
                                         data={"error":error_details},status=422)), 422

    try:
        result = get_locality_service(data.lat, data.long)
        return jsonify(standard_response(success=result['success'], message=result['message'], response_status=result['status'], 
                                         data=result['data'],status=result['status_code'])),result['status_code']
    except Exception as e:
        app.logger.error(f"[get_locality] Unexpected error: {str(e)}")
        return jsonify(standard_response(success=-1, message='Failed to get locality', response_status='error',status=500,
                                         data={"error": str(e)})), 500
        
    
@admin_driver.route('/api/admin/driver/dl_verify', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/verify_driving_license.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def driver_dl_verify():
    """
    Verify driver’s driving license using DL number and date of birth.

    Args (form-data):
        dl_no (str): Driving License number.
        dob (str): Date of birth (format: YYYY-MM-DD).
        driver_id (int): Driver ID.

    Returns:
        JSON response with:
            - success
            - message
            - data (DL verification result)
            - status
    """
    try:
        payload = DriverDLVerifyPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            message="Validation Error",
            response_status="error",
            data={"error": error_details},
            status=422
        )), 422

    try:
        claims = get_jwt()
        user_name = claims.get("name", "Not Known")
        result  = driver_dl_verify_service(payload.dict(), user_name)
        return jsonify(standard_response(
            success=result['success'],
            message=result['message'],
            response_status="success" if result['status'] == 200 else "error",
            data=result['data'] if 'data' in result else {},
            status=result['status']
        )), result['status']
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"[driver_dl_verify] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-99,
            message="Unexpected Error",
            response_status="error",
            data={"error": str(e)},
            status=500
        )), 500
        
        
@admin_driver.route('/api/admin/driver/dl_reverify', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/reverify_driving_license.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def driver_dl_reverify():
    """
    Reverify driver’s DL with optional remarks and force flag.

    Args (form-data):
        driver_id (int): Driver ID.
        remarks (str, optional): Admin comments.
        force_verify (int, optional): Force flag (default: 0).

    Returns:
        JSON response with:
            - success
            - message
            - status
    """
    form_data = request.form.to_dict()
    try:
        payload = DriverDLReverifySchema(**form_data).dict()
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify({
            "success": -1,
            "message": "Invalid input",
            "data":{"error": error_details},
            "status": 422,
            "response_status": "error"
        }), 422
    claims = get_jwt()
    admin_name = claims.get('name', 'Not Known')

    try:
        result = driver_dl_reverify_service(payload, admin_name)
        return jsonify(standard_response(
            success=result['success'],
            message=result['message'],
            response_status="success" if result['status'] == 200 else "error",
            data=result['data'] if 'data' in result else {},
            status=result['status']
        )), result['status']
    except Exception as e:
        return jsonify({
            "success": -99,
            "message": "Internal Server Error",
            "response_status": "error",
            "data": {"error": str(e)},
            "status": 500
        }), 500
        
@admin_driver.route('/api/admin/driver/bankdoc_verify', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def driver_bankdoc_verify():
    """
    Verify driver's bank document based on account and IFSC.

    Args (form-data):
        acc_no (str): Bank account number.
        ifsc (str): IFSC code.
        driver_id (str): Driver ID.

    Returns:
        JSON response with:
            - success
            - message
            - data (verification result)
            - status
    """
    try:
        payload = BankDocVerifyPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            message="Validation failed",
            data={"error": error_details},
            response_status="error",
            status=422
        )), 422
    user_name = get_jwt().get('name', 'Not Known')
    try:
        result = verify_driver_bank_doc(payload.dict(), user_name)
        return jsonify(standard_response(
            success=result['success'],
            message=result['message'],
            response_status="success" if result['status'] == 200 else "error",
            data=result['data'] if 'data' in result else {},
            status=result['status']
        )), result['status']
    except Exception as e:
        return jsonify(standard_response(
            success=-99,
            message= "Unexpected server error",
            data= {"error": str(e)},
            response_status= "error",
            status= 500
        )), 500