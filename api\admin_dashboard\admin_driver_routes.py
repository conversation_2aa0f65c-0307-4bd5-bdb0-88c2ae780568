#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import datetime
from pydantic import BaseModel, Field, field_validator,ValidationError
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from typing import Optional
from flasgger import swag_from
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from services.admin_dashboard.admin_driver_service import driver_search_service,fetch_single_driver_profile
from flask import current_app as app
from utils.response_utils import standard_response

admin_driver = Blueprint('admin_driver', __name__)

class DashDriverSearchParams(BaseModel):
    starting_from: int = Field(..., ge=0)
    no_of_logs: int = Field(..., ge=1)
    search_query: Optional[str] = None
    region: Optional[str] = None
    status: Optional[str] = None
    approval: Optional[str] = None
    rating_gt: Optional[float] = Field(None, ge=0)
    rating_lt: Optional[float] = Field(None, ge=0)
    timestamp_gt: Optional[str] = None
    timestamp_lt: Optional[str] = None
    is_global: Optional[int] = Field(0, ge=0, le=2)
    label: Optional[str] = None
    ongoing: Optional[str] = None
    sort_by: Optional[str] = Field("1")
    sort_order: Optional[str] = Field("desc")
    lat: Optional[float] = None
    long: Optional[float] = None
    radius_km: Optional[float] = Field(None, gt=0)

    @field_validator("timestamp_gt", "timestamp_lt", mode="before")
    @classmethod
    def validate_datetime_format(cls, v):
        if v:
            try:
                datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                raise ValueError("Invalid datetime format. Expected 'YYYY-MM-DD HH:MM:SS'")
        return v
    
    
@admin_driver.route('/api/admin/dash/search', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/dash_search.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def search_users_drivers():
    try:
        form_data = request.form.to_dict()
        validated = DashDriverSearchParams(**form_data)
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        app.logger.warning(f"Validation error in search_users_drivers: {error_details}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            response_status="error",
            message=error_details
        )), 400
    try:
        service_data, status_code = driver_search_service(validated)
        if status_code != 200:
                    return jsonify(standard_response(
                        success=-1,
                        status=status_code,
                        response_status="error",
                        message=service_data.get("error", "Failed to fetch driver search results")
                    )), status_code
        response_payload = {
            "count": service_data.get("count", 0),
            "lastentry": service_data.get("lastentry"),
            "results": service_data.get("data", [])
        }
        return jsonify(standard_response(
            success=1,
            status=200,
            response_status="success",
            message="Driver search successful",
            data=response_payload
        )), 200

    except Exception as e:
        app.logger.error(f"[search_users_drivers] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-1,
            status=500,
            response_status="error",
            message="Internal Server Error"
        )), 500



class SingleDriverRequestSchema(BaseModel):
    driver_id: int = Field(..., description="Driver ID")
    

@admin_driver.route('/api/admin/single/driver', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/dash_search.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DRIVERS])
def single_driver_admin():
    try:
        payload = SingleDriverRequestSchema(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message=error_details,
            response_status="error"
        )), 422

    try:
        data = fetch_single_driver_profile(payload.driver_id)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Driver fetched successfully",
            data=data,
            response_status="success"
        )), 200
        
    except Exception as e:
        app.logger.error(f"[single_driver_admin] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-1,
            status=500,
            message=str(e),
            response_status="error"
        )), 500