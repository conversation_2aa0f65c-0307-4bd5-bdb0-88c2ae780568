from models.models import Users, Bookings, DriverSearch,db, Drivers
from conftest import create_user_and_driver,unique_user_data

#Tests for /api/profile/change_fname
def test_change_fname_success(client,customer_login):
    auth_headers,customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    response = client.post('/api/profile/change_fname', headers=auth_headers, data={'fname': 'Alice'})
    assert response.status_code == 200
    res = response.get_json()

    assert res['success'] == 1
    assert 'updated' in res['message'].lower()
    # Verify DB update
    updated = Users.query.get(user.id)
    assert updated.fname == 'Alice'

def test_change_fname_missing_field(client, customer_login, monkeypatch):
    auth_headers, _ = customer_login()
    # make sure account is enabled
    response = client.post('/api/profile/change_fname', headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'missing' in res['message'].lower()

def test_change_fname_with_digits(client, customer_login, monkeypatch):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/profile/change_fname',
        headers=auth_headers,
        data={'fname': 'Al1ce'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'must not contain numbers' in res['message'].lower()
# 4) Restricted user (account_enabled=False)
def test_change_fname_restricted_user(client, customer_login, monkeypatch):
    auth_headers, user = customer_login()
    # simulate restricted account
    user.enabled = False
    db.session.commit()
    response = client.post(
        '/api/profile/change_fname',
        headers=auth_headers,
        data={'fname': 'Alice'}
    )
    assert response.status_code == 401
    res = response.get_json()
    assert res['success'] == -2
    assert 'restricted' in res['message'].lower()
# 5) JWT identity failure
def test_change_fname_identity_failure(client, customer_login, monkeypatch):
    # perform login first to get a token, then break identity lookup
    auth_headers, user = customer_login()
    wrong_auth_headers = {
        'Authorization': f'Bearer 12345',
        'X-CSRF-TOKEN': 'esrf4748993030'
    }
    response = client.post(
        '/api/profile/change_fname',
        headers=wrong_auth_headers,
        data={'fname': 'Alice'}
    )
    assert response.status_code == 401
    print("response:",response.get_json(),flush=True)
    res = response.get_json()
    assert res['success'] == -1
def test_change_lname_success(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    response = client.post(
        '/api/profile/change_lname',
        headers=auth_headers,
        data={'lname': 'Johnson'}
    )
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert 'updated' in res['message'].lower()
    # Verify DB update
    updated = Users.query.get(user.id)
    assert updated.lname == 'Johnson'
# 2) Missing lname field
def test_change_lname_missing_field(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/profile/change_lname',
        headers=auth_headers
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'missing' in res['message'].lower()
# 3) Last name contains digits
def test_change_lname_with_digits(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/profile/change_lname',
        headers=auth_headers,
        data={'lname': 'Sm1th'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'must not contain numbers' in res['message'].lower()
# 4) Restricted user (enabled=False)
def test_change_lname_restricted_user(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    # simulate restricted account
    user.enabled = False
    db.session.commit()
    response = client.post(
        '/api/profile/change_lname',
        headers=auth_headers,
        data={'lname': 'Johnson'}
    )
    assert response.status_code == 401
    res = response.get_json()
    assert res['success'] == -2
    assert 'restricted' in res['message'].lower()
# 5) JWT identity failure
def test_change_lname_identity_failure(client, customer_login):
    # generate a valid token, then override with a bad one
    _, _ = customer_login()  # set up user in DB and fixture side-effects
    wrong_auth_headers = {
        'Authorization': 'Bearer invalid.token.here',
        'X-CSRF-TOKEN': 'fake_csrf_token'
    }
    response = client.post(
        '/api/profile/change_lname',
        headers=wrong_auth_headers,
        data={'lname': 'Johnson'}
    )
    assert response.status_code == 401
    res = response.get_json()
    assert res['success'] == -1
def test_change_pwd_weak_password(client, customer_login):
    auth_headers,data = customer_login()
    change_pwd_data = {
        'token_and_secret':'master_token',
        'current_password': 'password',
        'new_password': 'newpassword456'
    }
    response = client.post('/api/password/change',headers=auth_headers,data=change_pwd_data)
    print("response:",response.get_json(),flush=True)
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status']=='error'
    assert res_data['success'] == -5  
    assert 'new password does not meet security requirements:' in res_data['message'].lower()
def test_change_pwd_success(client, customer_login):
    auth_headers,data = customer_login()
    change_pwd_data = {
        'token_and_secret':'master_token',
        'current_password': 'password',
        'new_password': 'newPass@456'
    }
    response = client.post('/api/password/change',headers=auth_headers,data=change_pwd_data)
    print("response:",response.get_json(),flush=True)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1  
def test_verify_email_missing_token(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.get('/api/email/verify', headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -1
    assert 'token is missing' in res['message'].lower()
def test_verify_email_invalid_token(client, customer_login, monkeypatch):
    auth_headers, customer_data = customer_login()
    response = client.get(
        '/api/email/verify',
        headers=auth_headers,
        query_string={'email_token': 'badtoken'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -8
    assert 'invalid' in res['message'].lower()
def test_verify_email_success(client, customer_login, monkeypatch):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    assert not user.is_email_verified
    response = client.get(
        '/api/email/verify',
        headers=auth_headers,
        query_string={'email_token': 'master_email_token'}
    )
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert 'verified successfully' in res['message'].lower()
    updated = Users.query.get(user.id)
    assert updated.is_email_verified
def test_unlink_email_no_email(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    # ensure no email on account
    user.email = None
    db.session.commit()
    response = client.post(
        '/api/email/unlink',
        headers=auth_headers,
        data={'email_token': 'master_email_token'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'no email is associated' in res['message'].lower()
def test_unlink_email_missing_token(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/email/unlink',
        headers=auth_headers,
        data={}  # no email_token
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -1
    assert 'missing email unlink token' in res['message'].lower()
def test_unlink_email_invalid_token(client, customer_login):
    auth_headers, customer_data = customer_login()
    # stub verify_email_token to return invalid
    response = client.post(
        '/api/email/unlink',
        headers=auth_headers,
        data={'email_token': 'badtoken'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -8
    assert 'invalid' in res['message'].lower()
def test_unlink_email_success(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    # ensure there is an email to unlink
    user.email = '<EMAIL>'
    user.is_email_verified = True
    db.session.commit()
    response = client.post(
        '/api/email/unlink',
        headers=auth_headers,
        data={'email_token': 'master_email_token'}
    )
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert 'successfully unlinked' in res['message'].lower()
    # verify DB change
    updated = Users.query.get(user.id)
    assert updated.email is None
def test_add_emergency_missing_fields(client, customer_login):
    auth_headers, _ = customer_login()
    # send no form data
    response = client.post(
        '/api/emergency_contact/update',
        headers=auth_headers,
        data={}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -3
    assert 'missing required fields' in res['message'].lower()
    assert 'country_code' in res['message'] and 'contact_number' in res['message'] and 'token_and_secret' in res['message']
def test_add_emergency_unsupported_country(client, customer_login, monkeypatch):
    auth_headers, _ = customer_login()
    # OTP valid
    response = client.post(
        '/api/emergency_contact/update',
        headers=auth_headers,
        data={'country_code': '+47', 'contact_number': '**********', 'token_and_secret': 'master_token'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -4
    assert 'country code not supported' in res['message'].lower()
def test_add_emergency_bad_number_format(client, customer_login, monkeypatch):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/emergency_contact/update',
        headers=auth_headers,
        data={'country_code': '+91', 'contact_number': '12345abcde', 'token_and_secret': 'master_token'}
    )
    assert response.status_code == 400
    print("response:",response.get_json(),flush=True)
    res = response.get_json()
    assert res['success'] == -5
    assert 'invalid emergency contact number format.' in res['message'].lower()
def test_add_emergency_already_exists(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    # set existing contact
    user.emergency_contact_country_code = '+1'
    user.emergency_contact_number = '**********'
    db.session.commit()
    response = client.post(
        '/api/emergency_contact/update',
        headers=auth_headers,
        data={'country_code': '+91', 'contact_number': '**********', 'token_and_secret': 'master_token'}
    )
    assert response.status_code == 409
    res = response.get_json()
    assert res['success'] == -6
    assert 'the emergency contact number is already added for this account' in res['message'].lower()
# 9) Success path
def test_add_emergency_success(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    # ensure no existing contact
    user.emergency_contact_country_code = None
    user.emergency_contact_number = None
    db.session.commit()
    response = client.post(
        '/api/emergency_contact/update',
        headers=auth_headers,
        data={'country_code': '+91', 'contact_number': '**********', 'token_and_secret': 'master_token'}
    )
    print("response:",response.get_json(),flush=True)
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert 'added successfully' in res['message'].lower()
    updated = db.session.query(Users).get(user.id)
    assert updated.emergency_contact_country_code == '91'
    assert updated.emergency_contact_number == '**********'
def test_update_email_missing_field(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/email/update',
        headers=auth_headers,
        data={}  # no email param
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -2
    assert 'email not provided' in res['message'].lower()
def test_update_email_invalid_format(client, customer_login):
    auth_headers, _ = customer_login()
    response = client.post(
        '/api/email/update',
        headers=auth_headers,
        data={'email': 'ankan@gmailcom'}
    )
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == -2
    assert 'invalid email format' in res['message'].lower()
# 5) Email already verified
def test_update_email_already_verified(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    user.is_email_verified = True
    db.session.commit()
    response = client.post(
        '/api/email/update',
        headers=auth_headers,
        data={'email': '<EMAIL>'}
    )
    assert response.status_code == 409
    res = response.get_json()
    assert res['success'] == -3
    assert 'cannot be updated' in res['message'].lower()
# 7) Unexpected exception in send_verification_email_link
def test_update_email_unexpected_error_on_send(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    user.is_email_verified = False
    db.session.commit()
    response = client.post(
        '/api/email/update',
        headers=auth_headers,
        data={'email': '<EMAIL>'}
    )
    assert response.status_code == 500
    res = response.get_json()
    assert res['success'] == -5
    assert 'unexpected error' in res['message'].lower()
# 8) Success path
def test_update_email_success(client, customer_login):
    auth_headers, customer_data = customer_login()
    user = Users.query.filter_by(mobile=customer_data.mobile).first()
    user.is_email_verified = False
    db.session.commit()
    response = client.post(
        '/api/email/update',
        headers=auth_headers,
        data={'email': '<EMAIL>','testing': 1}
    )
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert res['email_verified'] is False
    assert 'verification email sent' in res['message'].lower()
    # DB updated?
    updated = Users.query.get(user.id)
    assert updated.email == '<EMAIL>'
    assert updated.is_email_verified is False