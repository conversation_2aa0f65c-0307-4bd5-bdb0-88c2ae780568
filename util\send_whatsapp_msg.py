from main import app
import os
import time
from datetime import datetime, timedelta
import requests
from pytz import timezone
from models import db, Users

# --- Gupshup API Config ---
API_URL = "https://api.gupshup.io/wa/api/v1/template/msg"
API_KEY = "vk9i60br4md5x6mc5m8b5jbdamqwhmqg"
SOURCE_NUMBER = "918882012345"
SOURCE_NAME = "Drivers4Me"
TEMPLATE_ID = "76adcec3-a785-46d2-a3f0-1273dde85455"

# --- Checkpoint file to track last processed time ---
CHECKPOINT_FILE = "last_check_time.txt"

# --- Phone and name validation ---
def is_valid_10digit(phone):
    return phone and phone.isdigit() and len(phone) == 10

def get_name(name):
    return name.strip() if name and name.strip() else "User"

# --- Persistent timestamp helpers ---
def load_last_time():
    if not os.path.exists(CHECKPOINT_FILE):
        default_time = datetime.utcnow() - timedelta(minutes=5)
        with open(CHECKPOINT_FILE, 'w') as f:
            f.write(default_time.isoformat())
        print(f"📁 Checkpoint file created with default timestamp: {default_time}", flush=True)
        return default_time

    with open(CHECKPOINT_FILE, 'r') as f:
        try:
            return datetime.fromisoformat(f.read().strip())
        except Exception as e:
            print(f"⚠️ Corrupted checkpoint file. Resetting to default. Error: {e}", flush=True)
            default_time = datetime.utcnow() - timedelta(minutes=5)
            return default_time

def save_current_time(ts):
    with open(CHECKPOINT_FILE, 'w') as f:
        f.write(ts.isoformat())

# --- WhatsApp message sender ---
def send_whatsapp_message(name, phone):
    headers = {
        "accept": "application/json",
        "apikey": API_KEY,
        "content-type": "application/x-www-form-urlencoded"
    }

    payload = {
        "template": f'{{"params":["{name}"],"id":"{TEMPLATE_ID}"}}',
        "channel": "whatsapp",
        "source": SOURCE_NUMBER,
        "src.name": SOURCE_NAME,
        "destination": f"91{phone}"
    }

    try:
        response = requests.post(API_URL, headers=headers, data=payload)
        print(f"📤 Sending to 91-{phone} | Status: {response.status_code}", flush=True)
        print(f"↪️  Response: {response.text}", flush=True)

        if response.status_code in [200, 202]:
            data = response.json()
            return data.get("status") == "submitted"
        return False
    except Exception as e:
        print(f"❌ Exception during API call: {e}", flush=True)
        return False

# --- Main processing function ---
def process_new_users():
    with app.app_context():
        last_time = load_last_time()
        now = datetime.utcnow()

        new_users = Users.query.filter(
            Users.role == Users.ROLE_USER,
            Users.marked == False,
            Users.reg >= last_time,
            Users.reg < now
        ).all()
        IST = timezone('Asia/Kolkata')
        ist = datetime.now(IST).strftime('%Y-%m-%d %H:%M:%S')
        last_time_ist = last_time.astimezone(IST).strftime('%Y-%m-%d %H:%M:%S')
        now_ist = now.astimezone(IST).strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n⏱️ {ist} IST", flush=True)
        print(f"🧾 Found {len(new_users)} new users from {last_time_ist} to {now_ist} IST", flush=True)

        for user in new_users:
            if not is_valid_10digit(user.mobile):
                print(f"⚠️ Skipping user {user.id} — invalid phone: {user.mobile}", flush=True)
                continue

            name = get_name(user.fname)
            if send_whatsapp_message(name, user.mobile):
                user.marked = True
                print(f"✅ Sent to {name} (91{user.mobile})", flush=True)
            else:
                print(f"❌ Failed to send to {name} (91{user.mobile})", flush=True)

        db.session.commit()
        save_current_time(now)
        print("🗂️  Database changes committed.\n", flush=True)

# --- Loop forever ---
if __name__ == "__main__":
    while True:
        process_new_users()
        print("😴 Sleeping for 60 seconds...\n", flush=True)
        time.sleep(60)
