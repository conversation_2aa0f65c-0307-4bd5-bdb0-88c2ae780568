
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity
from flasgger import swag_from
from pydantic import ValidationError
from flask import current_app as app

from utils.auth.admin_login_utils import check_access
from schemas.admin_dashboard.due_credit_schemas import AddDriverDuePayload,SearchDriverByMobileParams, \
    DriverDueLogPayload, DuesDriverListPayload
from utils.admin_utils import Tabs
from utils.response_utils import standard_response
from services.admin_dashboard.admin_dues_service import add_driver_due_service,search_driver_by_mobile_service, \
    get_driver_due_log_service, get_dues_driver_list

admin_dues = Blueprint('admin_dues', __name__)

@admin_dues.route('/api/admin/search_driver_by_mobile', methods=['POST'])
@swag_from('/app/swagger_docs/customer_admin/search_by_mobile.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DUES_AND_CREDIT])
def search_driver_by_mobile():
    """
    Search for a driver using their mobile number.

    Args (form-data):
        mobile (str): Mobile number of the driver.

    Returns:
        JSON response with:
            - success (int): 1 on success, or error code.
            - message (str): Status message.
            - data (dict): Driver info if found.
            - response_status (str): "success" or "error"
            - status (int): HTTP status code
    """
    try:
        payload = SearchDriverByMobileParams(**request.form.to_dict())
    except ValidationError as ve:
        errors = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid input",
            data={"error": errors},
            response_status="error"
        )), 422

    try:
        mobile = int(payload.mobile)
    except ValueError:
        return jsonify(standard_response(
            success=-1,
            status=400,
            message="Invalid mobile number format",
            response_status="error"
        )), 400

    try:
        result = search_driver_by_mobile_service(mobile)
        if result["success"] == 1:
            return jsonify(standard_response(
                success=1,
                status=200,
                message=result["message"],
                data=result,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=result["success"],
                status=result["status_code"],
                message=result["message"],
                response_status="error"
            )), result["status_code"]
    except Exception as e:
        app.logger.error(f"[search_driver_by_mobile] Unexpected error: {str(e)}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to search driver",
            response_status="error",
            data={"error": str(e)}
        )), 500


@admin_dues.route("/api/admin/driver_due", methods=["POST"])
@jwt_required()
@check_access(tab_required=[Tabs.DUES_AND_CREDIT])
@swag_from('/app/swagger_docs/driver_admin/add_driver_dues.yml')
def add_driver_due():
    """
    Add or update driver dues manually by admin.

    Args (form-data):
        driver_id (int): Driver ID to assign dues to.
        amount (float): Due amount.
        reason (str): Reason for the due.
        remarks (str, optional): Additional remarks.

    Returns:
        JSON response with:
            - success (int): 1 if successful, else -1 or other error code.
            - message (str): Operation result.
            - response_status (str): "success" or "error"
            - status (int): HTTP status code
    """
    try:
        payload = AddDriverDuePayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Validation failed",
            response_status="error",
            data={"error": error_details}
        )), 422

    try:
        admin_id = get_jwt_identity()
        response = add_driver_due_service(payload, admin_id, request.form)
        if response["status_code"] == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message=response["message"],
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=response["success"],
                status=response["status_code"],
                message=response["message"],
                response_status="error"
            )), response["status_code"]

    except Exception as e:
        print(e)
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="Failed to update driver dues",
            response_status="error",
            data={"error": str(e)}
        )), 500
        
@admin_dues.route('/api/admin/driver_dues_log', methods=['POST'])
@swag_from('/app/swagger_docs/driver_admin/get_driver_dues_log.yml')
@jwt_required()
@check_access(tab_required=[Tabs.DUES_AND_CREDIT])
def driver_due_log():
    """
    Get the log/history of dues assigned to a driver.

    Args (form-data):
        driver_id (int): ID of the driver whose dues log is requested.
        offset (int, optional): Pagination offset.
        limit (int, optional): Number of records to fetch.
        from_date (str, optional): Start date in YYYY-MM-DD.
        to_date (str, optional): End date in YYYY-MM-DD.

    Headers:
        Authorization: Bearer <JWT>
        Content-Type: multipart/form-data
        X-Timezone (str, optional): Timezone for converting timestamps (default: "Asia/Kolkata")

    Returns:
        JSON response with:
            - success (int)
            - message (str)
            - data (dict): Dues log entries
            - response_status (str)
            - status (int): HTTP status code
    """
    try:
        payload = DriverDueLogPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid or missing fields",
            data={"error": error_details},
            response_status="error"
        )), 422

    timezone = request.headers.get('X-Timezone', 'Asia/Kolkata')

    result = get_driver_due_log_service(payload.dict(), timezone)
    return jsonify(standard_response(
        success=result.get("success"),
        status=result.get("status_code"),
        message=result.get("message"),
        data=result.get("data", {}),
        response_status="success" if result.get("success") == 1 else "error"
    )), result.get("status_code")
    
    
@admin_dues.route('/api/admin/dues_driver_list', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.DUES_AND_CREDIT])
def dues_driver_list():
    """
    Get a list of all drivers with outstanding dues.

    Args (form-data):
        offset (int, optional): Pagination offset.
        limit (int, optional): Page size.
        region (str, optional): Filter by region.
        search_query (str, optional): Search keyword (e.g., name or ID).
        approval (str, optional): Approval filter.
        status (str, optional): Active/inactive driver filter.
        due_gt (float, optional): Minimum due filter.
        due_lt (float, optional): Maximum due filter.

    Returns:
        JSON response with:
            - success (int): 1 on success, -1 or error code on failure.
            - message (str): Status message.
            - data (dict): List of drivers with dues.
            - response_status (str): "success" or "error"
            - status (int): HTTP status code
    """
    try:
        payload = DuesDriverListPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in ve.errors()]
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Invalid or missing fields",
            data={"error": error_details},
            response_status="error"
        )), 422

    user_id = get_jwt_identity()
    result = get_dues_driver_list(payload.dict(), user_id)
    return jsonify(standard_response(
        success=result.get("success"),
        status=result.get("status_code"),
        message=result.get("message"),
        data=result.get("data", {}),
        response_status="success" if result.get("success") == 1 else "error"
    )), result.get("status_code")