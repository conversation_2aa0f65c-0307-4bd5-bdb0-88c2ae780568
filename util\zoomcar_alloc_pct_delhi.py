import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail
from export_csv import export_csv_main
from shutil import copyfile
import os

time_now = datetime.utcnow() + timedelta(seconds=330*60)
cfg = D4M_UTIL_PATH + "configs/zoomcar_percentage_allocation_delhi.ini"
ofname = export_csv_main(cfg)
time_now_conv = time_now.strftime("%Y/%m/%d-%H:%M")
filepathD = ofname.replace(".csv", "") + "-" + time_now_conv.replace("/", "-") + ".csv"
print("Copying from", ofname, "-->", filepathD)
copyfile(ofname, filepathD)
subjectD = "Zoomcar Allocation Status - Delhi - " + time_now_conv
with open(filepathD, "r") as fd:
    content = ("Allocation percentage is %s%%.<br/><br/>--Drivers4Me team" %
               (str(fd.read().strip())))
from_addr = "<EMAIL>"
"""
to_addr_list = ["<EMAIL>","<EMAIL>",
                 "<EMAIL>","<EMAIL>",
                 "<EMAIL>","<EMAIL>",
                 "<EMAIL>","<EMAIL>",
                 "<EMAIL>","<EMAIL>",
                 "<EMAIL>"]
"""
to_addr_list = ["<EMAIL>"]
if not to_addr_list:
    print("Not sending mail")
else:
    print("Sending mail to", str(to_addr_list))
    send_mail(from_addr, to_addr_list, subjectD, content, None)
os.remove(filepathD)
