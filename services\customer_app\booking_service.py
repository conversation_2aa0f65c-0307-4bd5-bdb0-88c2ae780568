#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  booking_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: An<PERSON><PERSON>

# === Standard Library ===
import uuid
import datetime
import re
import time as tm

# === Third-Party Libraries ===
import pytz
from flask import jsonify
from sqlalchemy import func, or_, and_
from sqlalchemy.dialects import mysql
from flask import current_app as app

# === Application (Internal) Imports ===
from utils.validation_utils import validate_coupon
from utils.time_utils import strfdelta, parse_duration_to_day_and_time,convert_to_local_time
from utils.distance_utils import point_inside_polygon, get_dist_on_map
from utils.bookings.booking_params import BookingParams, Regions
from models.models import (
    db, DriverSearch, SearchDest, Users, Bookings,Drivers,
    BookPricing, UserMultipleBooking, Trip, Coupons,BookPending,BookDest
)
from models.affiliate_models import AffiliateCollections
from services.pricing.price import Price, PriceOutstation, is_trip_night,price_map
from services.socket.socketio_app import live_update_to_channel,send_notification_to_channel
from utils.redis_utils import read_redis_data
from utils.bookings.booking_utils import get_book_code, is_vip_or_faced_issue,check_ongoing_time
from services.socket.live_update_booking import send_live_update_of_booking


class DriverBookBase:
    MIN_HOURS = 6

    def __init__(self, id, fname, lname, mobile, pic, lastlat,
                 lastlong, rating, price=None,radius=0, reg=None, details_obj=None,
                 available=False, cancelled_count=0, last_month_trips=0, total_strike_count=0):
        self.id = id
        self.mobile = mobile
        self.name = fname + " " + lname
        self.pic = pic
        self.available = available
        self.homelat = lastlat
        self.homelong = lastlong
        self.rating = rating
        self.estimate = price[9]
        self.base_ch = price[1]
        self.night = price[2]
        self.dist_ch = price[3]
        self.booking_ch = price[4]
        self.car_ch = price[5]
        self.pre_tax = price[6]
        self.cgst = price[7]
        self.sgst = price[8]
        self.insurance = price[10]
        self.no_ins = price[0]
        self.radius = radius
        self.reg = reg
        self.details_obj = details_obj
        self.cancelled_count = int(cancelled_count)
        self.last_month_trips = int(last_month_trips)
        self.total_strike_count = int(total_strike_count)

class DriverBookB2C(DriverBookBase):
    def __init__(self, id, fname, lname, mobile, pic, lastlat, lastlong, rating,
                 price, radius=0, reg=None, details_obj=None,
                 available=False, cancelled_count=0, last_month_trips=0, total_strike_count=0):
        
        super().__init__(id, fname, lname, mobile, pic, lastlat, lastlong, rating,
                         price=price, radius=radius, reg=reg, details_obj=details_obj,
                         available=available, cancelled_count=cancelled_count,
                         last_month_trips=last_month_trips, total_strike_count=total_strike_count)
        
        # B2C-specific extras
        self.driver_base_ch = price[12] if len(price) > 12 else 0
        self.driver_night_ch = price[13] if len(price) > 13 else 0
        self.driver_type_ch = price[14] if len(price) > 14 else 0
        self.coupon_discount = price[15] if len(price) > 15 else 0

class DriverBookB2B(DriverBookBase):
    def __init__(self, *args, price, **kwargs):
        super().__init__(*args, **kwargs)
        self.driver_base_ch = price[13] 
        self.driver_night_ch = price[14] 



def validate_and_get_coupon(coupon_code, user_id):
    if not coupon_code:
        return None, None
    coupon, error = validate_coupon(coupon_code, user_id)
    if error:
        return None, (-14, error)
    return coupon, None

def validate_inputs(pickup_time, duration, car_type, gear_type, ninsurance, drop_lat, drop_long, pickup_long, pickup_lat, book_type, region):
    if pickup_time < datetime.datetime.utcnow().replace(tzinfo=pytz.utc):
        return -8, 'Pickup time cannot be in the past'
    if not re.match(BookingParams.DURATION_PATTERN, duration):
        return -2, 'Invalid duration format. Expected format: "Xd Xh Xm"'
    if car_type < 0 or car_type > 4 or gear_type < 0 or gear_type > 2:
        return -10, 'Car type must be between 0 and 4, and gear type between 0 and 2.'
    max_allowed = 6 if car_type == BookingParams.TYPE_CAR_SUV else 4
    if ninsurance > max_allowed:
        return -11, 'Exceeded max insurance count'
    dest_exists = bool(drop_lat and drop_long)
    if dest_exists and not point_inside_polygon(drop_long, drop_lat, pickup_long, pickup_lat, book_type, Regions.LOCATION_MAP, region):
        return -12, 'Location is outside service area'
    return None, dest_exists

def get_duration_and_endtime(pickup_time, duration, book_type):
    days, time_obj = parse_duration_to_day_and_time(duration)
    date = pickup_time.date()
    if book_type in [BookingParams.TYPE_OUTSTATION, BookingParams.TYPE_OUTSTATION_ONEWAY]:
        dur = time_obj.strftime("%H:%M:%S")
        end_time = datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute, pickup_time.second) + datetime.timedelta(days, time_obj.minute*3600)
    else:
        dur = time_obj.strftime("%H:%M:%S")
        end_time = datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute, pickup_time.second) + datetime.timedelta(seconds=(time_obj.hour * 3600 + time_obj.minute * 60 + time_obj.second))
        if BookingParams.is_cust_booktype(book_type) and not BookingParams.is_outstation(book_type) and (time_obj.hour > BookingParams.BOOKING_MAX_DUR or time_obj.hour < BookingParams.BOOKING_MIN_DUR):
            return None, None, -13, 'Invalid booking type or booking duration'
    return dur, end_time, None, None

def create_search_entry(search_id, user_id, car_type, gear_type, pickup_lat, pickup_long, pickup_time, dur, now, book_type, days, ninsurance, region, source, coupon_id, pickup_loc, drop_lat, drop_long, drop_loc):
    entry = DriverSearch(search_id, user_id, car_type, gear_type, pickup_lat, pickup_long, pickup_time.time(), pickup_time.date(), dur, now, book_type, days, ninsurance, region, source=source, coupon_id=coupon_id, reloc=pickup_loc, destlat=drop_lat, destlong=drop_long, destloc=drop_loc)
    entry.dist = get_dist_on_map(pickup_lat, pickup_long, drop_lat, drop_long) if drop_lat and drop_long else 0
    return entry

def get_user_stats(user_id, region):
    now = datetime.datetime.utcnow()
    recent_search_data = db.session.query(
        func.min(func.timestamp(DriverSearch.date, DriverSearch.time)).label('earliest_search_datetime'),
        func.max(DriverSearch.timestamp).label('recent_search_timestamp')
    ).filter(
        or_(
            DriverSearch.date > now.date(),
            and_(DriverSearch.date == now.date(), DriverSearch.time > now.time())
        ),
        DriverSearch.type < 50,
        DriverSearch.user == user_id,
        DriverSearch.region == region
    ).first()

    completed = db.session.query(func.count()).filter(
        Bookings.user == user_id,
        Bookings.enddate < now.date(),
        Bookings.valid == 1
    ).scalar()

    upcoming = db.session.query(func.count()).filter(
        Bookings.user == user_id,
        or_(
            Bookings.enddate > now.date(),
            and_(Bookings.enddate == now.date(), Bookings.endtime > now.time())
        )
    ).scalar()
    return recent_search_data, completed, upcoming

def search_booking(user_id, search_id, book_type, car_type, gear_type, pickup_time, duration, pickup_lat, pickup_long, pickup_loc, drop_lat, drop_long, drop_loc, is_immediate, region, source, ninsurance, coupon_code):
    search_id = uuid.uuid4().hex
    coupon, error = validate_and_get_coupon(coupon_code, user_id)
    if error:
        return None, *error

    validation_error, dest_exists = validate_inputs(pickup_time, duration, car_type, gear_type, ninsurance, drop_lat, drop_long, pickup_long, pickup_lat, book_type, region)
    if validation_error:
        return None, validation_error, dest_exists

    dur, end_time, err_code, err_msg = get_duration_and_endtime(pickup_time, duration, book_type)
    if err_code:
        return None, err_code, err_msg

    now = datetime.datetime.utcnow()
    coupon_id = coupon.coupon_id if coupon else None
    search_entry = create_search_entry(search_id, user_id, car_type, gear_type, pickup_lat, pickup_long, pickup_time, dur, now, book_type, 0, ninsurance, region, source, coupon_id, pickup_loc, drop_lat, drop_long, drop_loc)

    try:
        db.session.add(search_entry)
        db.session.commit()
        if dest_exists:
            search_dest = SearchDest(search_id, drop_lat, drop_long, drop_loc)
            db.session.add(search_dest)
            db.session.commit()

        user_details_estimate = db.session.query(Users).filter(Users.id == user_id).first()
        user_name = "Unknown" if user_id == 1 else user_details_estimate.get_name()

        recent_search_data, completed_bookings, upcoming_bookings = get_user_stats(user_id, region)
        car_type_combined = BookingParams.car_type_mapping[car_type][gear_type]

        if BookingParams.is_cust_booktype(book_type) and not BookingParams.is_outstation(book_type):
            min_price = Price.get_price(book_type, pickup_time.hour + pickup_time.minute/60, pickup_time, end_time, search_entry.dist, car_type_combined, pickup_time.date(), end_time.date(), 0, insurance_num=ninsurance, city=region, is_immediate=is_immediate, coupon=coupon)
            pickup_hour = pickup_time.hour + pickup_time.minute / 60
            end_hour = end_time.hour + end_time.minute / 60
            night, part_night = is_trip_night(pickup_hour, end_hour, is_immediate)

            live_update_data = {
                "userid": user_id,
                "name": user_name,
                "contactNumber": user_details_estimate.mobile,
                "region": region,
                "recentBookings": recent_search_data.earliest_search_datetime.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
                "latestSearch": recent_search_data.recent_search_timestamp.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
                "completed": completed_bookings,
                "upcoming": upcoming_bookings,
                "remark": ''
            }

            data = {
                "search_id": search_id,
                "night_flag": 1 if night or part_night else 0,
                'surge_flag': 1 if min_price[11] else 0,
                "estimate": {
                    'final_price': min_price[0],
                    "fare_breakdown": {
                        "base_fare": min_price[1] + min_price[4] + min_price[5],
                        "dist_fare": min_price[3],
                        "insurance_fee": min_price[10],
                        "night_charge": min_price[2],
                        "surge_charge": 0,
                        "driver_base_charge": min_price[12],
                        'booking_charge': min_price[4],
                        "immediate_surcharge": min_price[15],
                        "coupon_discount": min_price[16]
                    },
                    "distance_km": search_entry.dist / 1000,
                }
            }
            live_update_to_channel(live_update_data, room_name='estimate', type='estimate', region=region, channel='new_estimate')
            return data, 1, "Estimate generated successfully"

        elif BookingParams.is_outstation(book_type):
            dist = get_dist_on_map(pickup_lat, pickup_long, drop_lat, drop_long)
            min_price = PriceOutstation.get_price(pickup_time.date(), end_time.date(), 1, 0, car_type_combined, book_type, dist, insurance_num=ninsurance, city=region, is_immediate=is_immediate, coupon=coupon)

            estimate_data = {
                "userid": user_id,
                "name": user_name,
                "contactNumber": user_details_estimate.mobile,
                "region": region,
                "recentBookings": recent_search_data.earliest_search_datetime.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
                "latestSearch": recent_search_data.recent_search_timestamp.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
                "completed": completed_bookings,
                "upcoming": upcoming_bookings,
                "remark": ''
            }
            live_update_to_channel(estimate_data, room_name='estimate', type='estimate', region=region, channel='new_estimate')

            data = {
                "search_id": search_id,
                "night_flag": 0,
                'surge_flag': 1 if min_price[11] else 0,
                "estimate": {
                    'final_price_classic_driver': min_price[0],
                    'final_price_premium_driver': min_price[0] + min_price[14],
                    "fare_breakdown": {
                        "base_fare": min_price[1] + min_price[4] + min_price[5],
                        "dist_fare": min_price[3],
                        "insurance_fee": min_price[10],
                        "night_charge": min_price[2],
                        "surge_charge": 0,
                        "driver_base_charge": min_price[12],
                        'booking_charge': min_price[4],
                        "driver_type_surcharge": min_price[14],
                        "immediate_surcharge": min_price[15],
                        "coupon_discount": min_price[16]
                    },
                    "distance_km": search_entry.dist / 1000,
                }
            }
            return data, 1, "Estimate generated successfully"

    except Exception as e:
        db.session.rollback()
        return {"error": str(e)}, -99, "Unexpected server error occurred"



# def search_booking(
#     user_id: int,
#     search_id: str | None,
#     book_type: int,
#     car_type: int,
#     gear_type: int,
#     pickup_time: datetime,
#     duration: str,
#     pickup_lat: float,
#     pickup_long: float,
#     pickup_loc: str,
#     drop_lat: float | None,
#     drop_long: float | None,
#     drop_loc: str | None,
#     is_immediate: bool,
#     region: int,
#     source: str,
#     ninsurance: int,
#     coupon_code: str | None
# ) -> tuple[dict, str|None]:
#     search_id = uuid.uuid4().hex
#     coupon = None
#     if coupon_code:
#         coupon, error = validate_coupon(coupon_code, user_id)
#         if error:
#             return None,-5,error
#     if pickup_time < datetime.datetime.utcnow().replace(tzinfo=pytz.utc):
#         return None,-6,'Pickup time cannot be in the past'
#     if not re.match(BookingParams.DURATION_PATTERN, duration):
#         return None,-8,'Invalid duration format. Expected format: "Xd Xh Xm", e.g., "1d 2h 30m"'
#     if car_type < 0 or car_type > 4 or gear_type < 0 or gear_type > 2:
#         return None,-9,'Invalid car or gear type'
#     max_allowed = 6 if car_type == BookingParams.TYPE_CAR_SUV else 4
#     if ninsurance > max_allowed:
#         return None,-10,'Exceeded max insurance count'
#     if drop_lat and drop_long:
#         dest_exists = True
#     else:
#         dest_exists = False
#         drop_lat = 0.0
#         drop_long = 0.0
#     locations_data = Regions.LOCATION_MAP
#     if not point_inside_polygon(drop_long, drop_lat, pickup_long, pickup_lat, book_type, locations_data, region):
#         return None,-11,'Location is outside service area'
#     days, time_obj = parse_duration_to_day_and_time(duration)
#     if BookingParams.is_cust_booktype(book_type) and (book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY) and \
#             (time_obj.hour > BookingParams.BOOKING_MAX_DUR or time_obj.hour < BookingParams.BOOKING_MIN_DUR):
#                 return None,-12,'Invalid booking type or booking duration'
#     date = pickup_time.date()
#     now = datetime.datetime.utcnow()
#     ist = pytz.timezone('Asia/Kolkata')
#     if book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
#         # encoding: dd:hh:00
#         dur = time_obj.strftime("%H:%M:%S")
#         days = days
#         end_time = (datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute, pickup_time.second)
#                     + datetime.timedelta(days, time_obj.minute*3600))
#     else:
#         dur = time_obj.strftime("%H:%M:%S")
#         days = 0
#         end_time = (datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute, pickup_time.second)
#                     + datetime.timedelta(0, time_obj.hour * 3600 + time_obj.minute * 60 + time_obj.second))  
#     coupon_id = coupon.coupon_id if coupon else None
#     search_entry = DriverSearch(search_id, user_id, car_type,gear_type, pickup_lat, pickup_long, pickup_time.time(), date, dur, now, book_type, days, ninsurance, region, source=source,coupon_id=coupon_id,reloc=pickup_loc,destlat=drop_lat,destlong=drop_long,destloc=drop_loc)
#     if dest_exists:
#         min_dist = max_dist = get_dist_on_map(pickup_lat, pickup_long, drop_lat, drop_long)
#     else:
#         min_dist = max_dist = 0
#     search_entry.dist = max_dist
#     try:
#         db.session.add(search_entry)
#         db.session.commit()
#         if dest_exists:
#             search_dest = SearchDest(search_id, drop_lat, drop_long, drop_loc)
#             db.session.add(search_dest)
#             db.session.commit()
#         user_details_estimate=db.session.query(Users).filter(Users.id == user_id).first()
#         user_name=""
#         if user_id==1:
#             user_name="Unknown"
#         else:
#             user_name=user_details_estimate.get_name()
#         recent_search_data = db.session.query(
#             func.min(func.timestamp(DriverSearch.date, DriverSearch.time)).label('earliest_search_datetime'),
#             func.max(DriverSearch.timestamp).label('recent_search_timestamp')
#         ).filter(
#                 or_(
#                     DriverSearch.date > now.date(),
#                     and_(DriverSearch.date == now.date(), DriverSearch.time > now.time())
#                 ),
#                 DriverSearch.type < 50,
#                 DriverSearch.user == user_id,
#                 DriverSearch.region==region
#             ).first()
#     # Calculate booking details
#         completed_bookings = db.session.query(func.count()).filter(
#             Bookings.user == user_id,
#             Bookings.enddate <  datetime.datetime.utcnow().date(),
#             Bookings.valid == 1
#         ).scalar()
#         upcoming_bookings = db.session.query(func.count()).filter(
#             Bookings.user == user_id,
#             or_(
#                 Bookings.enddate > datetime.datetime.utcnow().date(),
#                 and_(Bookings.enddate ==  datetime.datetime.utcnow().date(), Bookings.endtime >  datetime.datetime.utcnow().time())
#             )
#         ).scalar()
#         car_type_combined =  BookingParams.car_type_mapping[car_type][gear_type] 
#         # Bad hack to handle night threshold
#         if BookingParams.is_cust_booktype(book_type) and book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY:
#             min_price = Price.get_price(book_type, time_obj.hour + time_obj.minute/60, pickup_time, end_time, min_dist, car_type_combined,
#                                         date, end_time.date(), 0,
#                                         insurance_num=ninsurance, city=region,is_immediate=is_immediate,coupon=coupon)
#             pickup_hour = pickup_time.hour + pickup_time.minute / 60
#             end_hour = end_time.hour + end_time.minute / 60
#             # ot_rates = Price.get_ot_rates(date, end_time.date(), city=city)
#             night, part_night = is_trip_night(pickup_hour, end_hour, is_immediate)
#             try:
#                 user_details = db.session.query(Users).filter(Users.id == user_id).first()
#                 msg_content = "*" + user_details.get_name() + " searched for a trip " + \
#                               " (type= " + str(book_type) + ") from " + \
#                               str(datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute,
#                                                     pickup_time.second)) + \
#                               " to " + str(end_time) + " with estimate " + str(min_price) +"*"
#                 # send_slack_msg(10, msg_content)
#             except Exception as e:
#                 print(e)
#                 pass
#             live_update_data = {
#                 "userid": user_id,
#                 "name": user_name,
#                 "contactNumber": user_details_estimate.mobile,
#                 "region": region,
#                 "recentBookings": recent_search_data.earliest_search_datetime.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
#                 "latestSearch": recent_search_data.recent_search_timestamp.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
#                 "completed": completed_bookings,
#                 "upcoming": upcoming_bookings,
#                 "remark": ''
#             }
#             data = {
#                 "search_id": search_id,
#                 "night_flag": 1 if night or part_night else 0,
#                 'surge_flag':1 if min_price[11] else 0,
#                 "estimate": {
#                     'final_price_classic_driver': min_price[0],
#                     'final_price_premium_driver': min_price[0]+min_price[14],
#                     "fare_breakdown": {
#                         "base_fare": min_price[1]+min_price[4] + min_price[5],
#                         "dist_fare": min_price[3],
#                         "insurance_fee": min_price[10],
#                         "night_charge": min_price[2],
#                         "surge_charge":0,
#                         "driver_base_charge": min_price[12],
#                         'booking_charge': min_price[4],
#                         "driver_type_surcharge": min_price[14],
#                         "immediate_surcharge": min_price[15],
#                         "coupon_discount": min_price[16]
#                     },
#                 "distance_km":min_dist/1000,
#                 }
#             }

#             live_update_to_channel(live_update_data, room_name='estimate', type='estimate', region=region, channel= 'new_estimate')
          
#             return data,1,"Estimate generated successfully"             
                            
#         elif book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
#             dist = get_dist_on_map(pickup_lat, pickup_long, drop_lat, drop_long)
#             min_price = PriceOutstation.get_price(date, end_time.date(), 1, days * 24 + time_obj.minute, car_type_combined, book_type, dist, insurance_num=ninsurance, city=region,is_immediate=is_immediate,coupon=coupon)
#             # ot_rates = PriceOutstation.get_ot_rates(date, end_time.date())

#             try:
#                 user_details = db.session.query(Users).filter(Users.id == user_id).first()
#                 msg_content = user_details.get_name() + " searched for a trip " + \
#                               " (type= " + str(book_type) + ") from " + \
#                               str(datetime.datetime(date.year, date.month, date.day, pickup_time.hour, pickup_time.minute,
#                                                     pickup_time.second)) + \
#                               " to " + str(end_time) + " with estimate " + str(min_price)
#                 # send_slack_msg(10, msg_content)
#             except Exception as e:
#                 print(e)
#                 pass
#             estimate_data = {
#                     "userid": user_id,
#                     "name": user_name,
#                     "contactNumber": user_details_estimate.mobile,
#                     "region": region,
#                     "recentBookings": recent_search_data.earliest_search_datetime.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
#                     "latestSearch": recent_search_data.recent_search_timestamp.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
#                     "completed": completed_bookings,
#                     "upcoming": upcoming_bookings,
#                     "remark": ''
#                 }
#             print(min_price,flush=True)
#             live_update_to_channel(estimate_data, room_name='estimate', type='estimate', region=region, channel= 'new_estimate')
#             data = {
#                 "search_id": search_id,
#                 "night_flag": 0,
#                 'surge_flag':1 if min_price[11] else 0,
#                 "estimate": {
#                     'final_price_classic_driver': min_price[0],
#                     'final_price_premium_driver': min_price[0]+min_price[14],
#                     "fare_breakdown": {
#                         "base_fare": min_price[1]+min_price[4] + min_price[5],
#                         "dist_fare": min_price[3],
#                         "insurance_fee": min_price[10],
#                         "night_charge": min_price[2],
#                         "surge_charge":0,
#                         "driver_base_charge": min_price[12],
#                         'booking_charge': min_price[4],
#                         "driver_type_surcharge": min_price[14],
#                         "immediate_surcharge": min_price[15],
#                         "coupon_discount": min_price[16]
#                     },
#                 "distance_km":min_dist/1000,
#                 }
#             }
#             return data,1,"Estimate generated successfully"
               
#     except Exception as e:
#         print(e,flush=True)
#         db.session.rollback()
#         return None,-7,"Error in registering search"
    

def can_book_search_entry(search, user_id):
    user_mul_booking = db.session.query(UserMultipleBooking).filter(UserMultipleBooking.user == user_id).first()
    if user_mul_booking:
        return True
    start_time = datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second)
    end_time = (datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second)
                + datetime.timedelta(search.days, search.dur.hour * 3600 + search.dur.minute * 60 + search.dur.second))
    have_booking = Bookings.query.filter(Bookings.user == user_id).outerjoin(Trip, Bookings.id == Trip.book_id).filter(
              or_(
                        Trip.status!=Trip.TRIP_STOPPED,
                        Trip.status==None
                    ),
            Bookings.valid >= 0,
            or_(
                and_(
                    Bookings.startdate == end_time.date(),
                    Bookings.starttime < end_time.time()
                ),
                Bookings.startdate < end_time.date()
            ),
            or_(
                    and_(
                        Bookings.enddate == start_time.date(),
                        Bookings.endtime > start_time.time()
                    ),
                    Bookings.enddate > start_time.date()
            )
        )
    hav_book = have_booking.all()
    print(
        "final search query",
        have_booking.statement.compile(
            db.engine,
            dialect=mysql.dialect(),
            compile_kwargs={"literal_binds": True}
        ),
        flush=True
    )

    # have_booking = Bookings.query.filter(Bookings.user == user_id).filter(
    #     or_(and_(Bookings.startdate == end_time.date(), Bookings.starttime < end_time.time()),
    #         (Bookings.startdate < end_time.date()))). \
    #     filter(or_(and_(Bookings.enddate == search.date, Bookings.endtime > search.time),
    #                Bookings.enddate > end_time.date())).filter(Bookings.valid >= 0).all()
    return (not hav_book)   
   
def create_pricing_entry(driver, book,driver_type=0):
    try:
        pending_pricing = BookPricing(bid=book.id, est=driver.estimate,
                               base=driver.base_ch, est_pre_tax=driver.pre_tax,
                               cgst=driver.cgst, sgst=driver.sgst,
                               cartype=driver.car_ch, night=driver.night, food=0,
                               booking=driver.booking_ch, dist=driver.dist_ch,
                               insurance=driver.insurance , driver_base=driver.driver_base_ch,
                               driver_night=driver.driver_night_ch, driver_type=driver.driver_type_ch if driver_type == 1 else 0 ,
                               coupon_discount=driver.coupon_discount)

        db.session.add(pending_pricing)
        db.session.flush()
        return True

    except exc.IntegrityError:
        db.session.rollback()
        return True

    except Exception as e:
        db.session.rollback()
        print(e)
        return False

def create_pending_entry(driver, book, pending_state, score=-99):
    if book.valid != Bookings.UNALLOCATED:
        print("Skipping", driver.id, "for booking", book.id, "because allocated")
        return False
    booking_start_minus_n_minutes, booking_end_plus_n_minutes = check_ongoing_time(book)
    """
    booked = set([drivers.driver for drivers in
                Bookings.query.filter(
                    Bookings.valid > 0
                ).filter(
                    or_(
                        func.timestamp(Bookings.startdate, Bookings.starttime) <= booking_end_plus_n_minutes,
                        Bookings.startdate < book.enddate
                    )
                ).filter(
                    or_(
                        func.timestamp(Bookings.enddate, Bookings.endtime) >= booking_start_minus_n_minutes,
                        Bookings.enddate > book.enddate
                    )
                ).all()])
    """
    booked = []
    if driver.id in booked:
        return False

    try:
        #pb = db.session.query(BookPending).filter(BookPending.book_id==book.id).filter(BookPending.driver==driver.id)
        #if pb.first():
        #    pb.update({BookPending.valid: pending_state})
        #else:
        pending_book = BookPending(bid=book.id, did=driver.id, state=pending_state, score=score)
        db.session.add(pending_book)
        db.session.flush()
    except Exception as e:
        db.session.rollback()
        print("Exception in creating pending booking:", e)
        return False
    return True

def confirm_booking_service(user_id, claims, payload,tz='Asia/Kolkata'):
    if payload.search_id == -1:
        return  None,-6,'Invalid search ID'
    cur_search = DriverSearch.query.filter_by(id=payload.search_id).first()
    if not cur_search:
        return None,-7,'Invalid search ID'
    if datetime.datetime.utcnow() - cur_search.timestamp > BookingParams.CONFIRM_THRESHOLD_SEARCH:
        return None,-8,'Booking window expired'
    user_entry = db.session.query(Users).filter(Users.id == user_id).first()
    if user_entry.credit < 0:
        return None,-9,'Not enough credits'
    end_time = (datetime.datetime(cur_search.date.year, cur_search.date.month, cur_search.date.day,
                                cur_search.time.hour, cur_search.time.minute, cur_search.time.second) +
            datetime.timedelta(cur_search.days, cur_search.dur.hour * 3600 +
                                cur_search.dur.minute * 60 + cur_search.dur.second))
    can_book = False
    drivers = None
    city = cur_search.region
    can_book = can_book_search_entry(cur_search, user_id)
    if not can_book:
        return None,-10,'User already have booking in this time frame'
    try:
        existing_booking_check = db.session.query(Bookings).filter(Bookings.search_key == payload.search_id).first()
        if existing_booking_check:
            return None,-11,'Booking already created for this search'
        book = Bookings(user_id, payload.search_id, BookingParams.BOOKING_DUMMY_ID, cur_search.reflat, cur_search.reflong,
                        cur_search.time,
                        cur_search.date, str(cur_search.dur), end_time.time(), end_time.date(),
                        BookingParams.BOOKING_DUMMY_EST, BookingParams.BOOKING_DUMMY_EST,
                        cur_search.reloc,cur_search.car_type,cur_search.gear_type,cur_search.type, cur_search.days, payment_type=payload.payment_type,
                        region=cur_search.region, insurance_num=cur_search.insurance_num, immediate=payload.is_immediate)
        db.session.add(book)
        db.session.flush()
        book_id = str(book.id)
        # Generate booking code
        get_book_code(book.id)
        book_code = book.code
        if cur_search.destlat and cur_search.destlong:
            # book_id_2 = db.session.query(Bookings).filter(Bookings.search_key == search_id).first().id
            dest = BookDest(book_id,cur_search.destlat, cur_search.destlong, cur_search.destloc)
            db.session.add(dest)
        # if not BookingParams.get_no_broadcast():
        #     pending_state = BookPending.BROADCAST
        # else:
        #     pending_state = BookPending.SUPPRESSED
        pending_state = BookPending.BROADCAST
        # Temporary hack to handle pending entries getting created
        # async
        coupon = db.session.query(Coupons).filter(Coupons.coupon_id == cur_search.coupon_id).first()
        if BookingParams.is_cust_booktype(cur_search.type) and cur_search.type != BookingParams.TYPE_OUTSTATION and cur_search.type != BookingParams.TYPE_OUTSTATION_ONEWAY:
            cur_price = Price.get_price(cur_search.type, cur_search.dur.hour + cur_search.dur.minute/60, cur_search.time, end_time, cur_search.dist, cur_search.car_type,
                                cur_search.date, end_time.date(), 0,
                                insurance_num=cur_search.insurance_num, city=city,is_immediate=payload.is_immediate,coupon=coupon)
        elif cur_search.type == BookingParams.TYPE_OUTSTATION or cur_search.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            cur_price = PriceOutstation.get_price(cur_search.date,
                            end_time.date(), 1, cur_search.days * 24 + cur_search.dur.hour,
                            cur_search.car_type, cur_search.type, cur_search.dist,
                            insurance_num=cur_search.insurance_num, city=city,is_immediate=payload.is_immediate,coupon=coupon)
        else:
            cur_price = None
        if cur_price:
            temp_driver = db.session.query(Users, Drivers).filter(Drivers.user == Users.id).filter(Drivers.id == 1).first()
            driver_book_entry = DriverBookB2C(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                            -1, -1, temp_driver[1].rating, price = cur_price, radius = 0)
            create_pricing_entry(driver_book_entry, book,0)
            create_pending_entry(driver_book_entry, book, pending_state)
        # _update_user_pending(user)
        db.session.commit()
        key  = price_map.get(city,"price_kolkata")
        data,redis_status = read_redis_data(key)
        if redis_status and isinstance(data, dict):  # proceed only if Redis read succeeded
            document = {
                "booking_id": book_id,
                "price_data": data
            }
            try:
                mongo_insert_result = AffiliateCollections.booking_price_data.insert_one(document)
            except Exception as e:
                print(f"MongoDB error: {e}", flush=True)
    except Exception as e:
        app.logger.exception(f"Unexpected error in confirm_booking_service: {e}")
        r = {"error": str(e)} ,-99,'Server error during confirmation'
        db.session.rollback()
        return r
    try:
        is_within_two_hours = (datetime.datetime.combine(cur_search.date, cur_search.time)
                        - datetime.datetime.now()) < datetime.timedelta(minutes=90)
        vip_or_issue_faced = is_vip_or_faced_issue(user_entry.label_bv)
        if is_within_two_hours or vip_or_issue_faced:
            content = f'VIP/Issue faced booking. Code:{book.code}' if vip_or_issue_faced else f'Priority booking. Code:{book.code}'
            notification = {
                'id':book_id,
                'type': claims.get('name', 'unknown'),
                'username': str(temp_driver[0].id),
                'content': content,
                'imageUrl': '/assets/icons/bticons/crown.svg' if vip_or_issue_faced else "/assets/icons/bticons/swatch.svg",
                'timestamp': int(tm.time() * 1000)
            }
            send_notification_to_channel(notification, 'Priority Booking:'+str(cur_search.region))

        send_live_update_of_booking(book.id, book.region, 'new_booking')
        live_update_data = {
                    "userid": user_id,
                    'book_timestamp':convert_to_local_time(datetime.datetime.combine(cur_search.date, cur_search.time),tz).strftime("%d %b, %y, %H:%M:%S"),
                    "search_timestamp": convert_to_local_time(cur_search.timestamp,tz).strftime("%d %b, %y, %H:%M:%S")
                    }
        live_update_to_channel(live_update_data, room_name='estimate', type='estimate',region=cur_search.region, channel= 'estimate_upcoming_booking')
    except Exception as e:
        db.session.rollback()
        print(e,flush=True)
        app.logger.exception(f"Unexpected error in confirm_booking_service: {e}")
        return {"error": str(e)} ,-99,'Server error during socket update'

    data={
        'search_id': payload.search_id,
        'reference_id': book_id,
        'id':book_code,
            "estimate": {
                'final_price': cur_price[0],
                "fare_breakdown": {
                    "base_fare": cur_price[1] + cur_price[4] + cur_price[5],
                    "dist_fare": cur_price[3],
                    "insurance_fee": cur_price[10],
                    "night_charge": cur_price[2],
                    "surge_charge":0,
                    "driver_type_surcharge": 0,
                    "driver_base_charge": cur_price[12],
                    'booking_charge': cur_price[4],
                    "immediate_surcharge": cur_price[15],
                    "coupon_discount": cur_price[16]
                },       
            }
    }
    return data,1,"Booking created successfully"