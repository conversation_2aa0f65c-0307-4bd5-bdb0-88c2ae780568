#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import re
import datetime
from flask_cors import cross_origin

from flask import Blueprint, request, jsonify
from flask_jwt_extended import set_access_cookies, set_refresh_cookies
from flasgger import swag_from
from pydantic import ValidationError
from sqlalchemy import exc
from flask_jwt_extended import jwt_required, get_jwt  
from flask import current_app as app

from services.admin_dashboard.login_service import generate_otp_service, validate_admin_otp_service, \
    login_admin_service,admin_refresh_service,validate_password_change_otp_service,password_change_service,delete_user_service   
from models.models import UserToken, db,Users
from utils.auth.admin_login_utils import check_token_revoked
from utils.response_utils import standard_response
from schemas.admin_dashboard.admin_login_schemas import GenerateOtpPayload, ValidateAdminOtpPayload, \
    AdminLoginPayload, ValidatePasswordChangeOtpPayload, AdminPasswordChangePayload, AdminUserDeletePayload

admin_login = Blueprint('admin_login', __name__)    

@admin_login.route('/token/admin/otp/generate', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/generate_otp.yml')
def generate_otp():
    """
    Generate OTP for admin mobile login.

    Args:
        form-data:
            mobile (str): The mobile number to send OTP to.

    Returns:
        JSON response with success status and message.
    """
    try:
        try:
            payload = GenerateOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                data={'error': error_details},
                response_status="error"
            )), 422

        success, message = generate_otp_service(payload)
        if success == -3:
            return jsonify(standard_response(
                success=-3,
                status=429,
                message=message,
                response_status="error"
            )), 429
        if success == -2:
            return jsonify(standard_response(
                success=-2,
                status=401,
                message=message,
                response_status="error"
            )), 401
        return jsonify(standard_response(
            success=1,
            status=200,
            message="OTP sent successfully",
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in generate_otp: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
    
@admin_login.route('/token/admin/otp/validate', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/validate_otp.yml')
def val_otp():
    """
    Validate OTP for admin mobile login and issue tokens.

    Args:
        form-data:
            mobile (str): The mobile number used for login.
            otp (str): OTP received on mobile.

    Returns:
        JSON response with access/refresh tokens set in cookies and user data.
    """
    try:
        try:
            payload = ValidateAdminOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                data={'error': error_details},
                response_status="error"
            )), 422

        user_agent = request.headers.get('User-Agent')
        result = validate_admin_otp_service(payload)

        if isinstance(result, tuple) and len(result) == 3 and isinstance(result[2], dict):
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code

        # result is (access_token, refresh_token, refresh_expiry, user_data)
        access_token, refresh_token, refresh_expiry, user_data = result

        expiry = datetime.datetime.now() + refresh_expiry
        token_entry = UserToken(
            user_data['id'],  # cur_user.id
            refresh_token,
            user_agent,
            expiry,
            login_from=UserToken.ADMIN_LOGIN
        )

        try:
            db.session.add(token_entry)
            db.session.commit()
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Invalid refresh token. Please login again.",
                response_status="error"
            )), 401
        # resp = jsonify(user_data)
        resp = jsonify(standard_response(
            success=1,
            status=200,
            message="OTP validated successfully",
            data=user_data,
            response_status="success"
        ))
        set_access_cookies(resp, access_token)
        set_refresh_cookies(resp, refresh_token)
        return resp

    except Exception as e:
        app.logger.exception(f"Unexpected error in val_otp: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
@admin_login.route('/token/admin/login', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/login_for_admin.yml')
@cross_origin(supports_credentials=True)
def login_admin():
    """
    Admin login with mobile and password.

    Args:
        form-data:
            mobile (str): Admin's mobile number.
            pwd (str): Admin's password.

    Returns:
        JSON response with access/refresh tokens set in cookies and user data.
    """
    try:
        try:
            payload = AdminLoginPayload(**request.form.to_dict())
        except ValidationError as ve:
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                data={'error': ve.errors()},
                response_status="error"
            )), 422

        user_agent = request.headers.get('User-Agent')
        result = login_admin_service(payload)

        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code

        access_token, refresh_token, refresh_expiry, user_data = result
        expiry = datetime.datetime.now() + refresh_expiry

        token_entry = UserToken(
            user_data['id'],
            refresh_token,
            user_agent,
            expiry,
            login_from=UserToken.ADMIN_LOGIN
        )

        try:
            db.session.add(token_entry)
            db.session.commit()
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Invalid refresh token. Please login again.",
                response_status="error"
            )), 401

        # resp = jsonify(user_data)
        resp = jsonify(standard_response(
            success=1,
            status=200,
            message="Login successful",
            data=user_data,
            response_status="success"
        ))
        set_access_cookies(resp, access_token)
        set_refresh_cookies(resp, refresh_token)
        return resp

    except Exception as e:
        db.session.rollback()
        app.logger.exception(f"Unexpected error in login_admin: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
@admin_login.route('/api/admin/refresh', methods=['POST'])   
@swag_from('/app/swagger_docs/login_admin/refresh_admin_token.yml')
@jwt_required(refresh=True)
def admin_refresh():
    """
    Refresh access token using refresh token cookie.

    Args:
        Cookie:
            refresh_token_cookie (str): Refresh token stored in HTTP-only cookie.

    Returns:
        JSON response with new access token in cookie.
    """
    try:
        refresh_token = request.cookies.get("refresh_token_cookie")
        if not refresh_token:
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Refresh token not found",
                response_status="error"
            )), 401
            # return jsonify({'success': 0, 'refresh': False}), 401

        result = admin_refresh_service(refresh_token)
        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code
            # return jsonify({'success': success, **data}), status_code

        access_token = result
        resp = jsonify({'success': 1, 'refresh': True})
        set_access_cookies(resp, access_token)
        # return jsonify(standard_response(
        #     success=1,
        #     status=200,
        #     message="Token refreshed successfully",
        #     data={'refresh': True},
        #     response_status="success"
        # )), 200
        return resp, 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_refresh: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': -4, 'error': str(e), 'refresh': False}), 500
    
@admin_login.route('/token/admin/password_change/otp/validate', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/password_change_validate_otp.yml')
def password_change_val_otp():
    """
    Validate OTP before allowing admin to change password.

    Args:
        form-data:
            mobile (str): Mobile number.
            otp (str): OTP received for password change.

    Returns:
        JSON response indicating OTP validation status.
    """
    try:
        try:
            payload = ValidatePasswordChangeOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="Invalid input",
                response_status="error",
                data={'error': ve.errors()}
            )), 422
            # return jsonify({'success': 0}), 400

        result = validate_password_change_otp_service(payload)

        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code
            # return jsonify({'success': success, **data}), status_code
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Otp validated successfully",
            response_status="success"
        )), 200
        # return jsonify({'success': 1, "message": "Otp validated successfully"})

    except Exception as e:
        app.logger.exception(f"Unexpected error in password_change_val_otp: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': -5, 'error': str(e)}), 500
    
@admin_login.route('/token/admin/password_change', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/password_change.yml')
def password_change():
    """
    Change admin password after successful OTP validation.

    Args:
        form-data:
            mobile (str): Mobile number.
            new_pwd (str): New password to set.

    Returns:
        JSON response indicating password change success/failure.
    """
    try:
        try:
            payload = AdminPasswordChangePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=400,
                message="Invalid input",
                data={'error': error_details},
                response_status="error"
            )), 400
            # return jsonify({'success': 0}), 400

        success, status_code, data = password_change_service(payload)
        return jsonify(standard_response(
            success=success,
            status=status_code,
            message=data.get('message') or data.get('error', 'Failed'),
            data=data,
            response_status="error"
        )), status_code
        # return jsonify({'success': success, **data}), status_code

    except Exception as e:
        app.logger.exception(f"Unexpected error in password_change: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': 0, 'error': str(e)}), 500
    
@admin_login.route('/token/admin/user/delete', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/delete_user.yml')
@jwt_required()
@check_token_revoked
def delete_user():
    """
    Delete admin user and invalidate associated tokens.

    Only accessible by users with SUPERADMIN role.

    Args:
        form-data:
            user_id (int): ID of the user to delete.

    Returns:
        JSON response indicating user deletion status.
    """
    claims = get_jwt()
    if claims.get('roles') != Users.ROLE_SUPERADMIN:
        return jsonify(standard_response(
            success=-2,
            status=403,
            message="This is not valid api call",
            response_status="error"
        )), 403
        # return jsonify({"status": "error", "message": "This is not valid api call"}), 500

    try:
        try:
            payload = AdminUserDeletePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-1,
                status=422,
                message="User ID required",
                data={'error': error_details},
                response_status="error"
            )), 422
            # return jsonify({"status": 400, "result": "FAILURE", "message": "User ID required"}), 400

        status_code, data = delete_user_service(payload)
        return jsonify(standard_response(
            success=1,
            status=status_code,
            message="User deleted and tokens invalidated",
            data=data,
            response_status="success"
        )), status_code
        # return jsonify({"status": status_code, **data}), status_code

    except Exception as e:
        app.logger.exception(f"Unexpected error in delete_user: {e}")
        return jsonify(standard_response(
            success=-99,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({"status": 500, "result": "FAILURE", "message": {"message": str(e)}}), 500