import pytest
from models.affiliate_models import Affiliate,AffiliateRep
from db_config import db
from unittest.mock import patch

""" Test cases for api: /api/affiliate/credits/start """


def test_credit_start_invalid_jwt_claims(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        "aff_id": rep.affiliate_id,
        "amount": 2,
        "gateway": 0
    }
    response = client.post("/api/affiliate/credits/start", data=form_data)

    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'


def test_credit_start_affiliate_account_disabled(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == rep.affiliate_id).first()
    affiliate.enabled = False
    db.session.commit()
    form_data = {
        "aff_id": rep.affiliate_id,
        "amount": 2,
        "gateway": 0
    }
    response = client.post("/api/affiliate/credits/start", data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 403
    
    assert json_data['success'] == -1

def test_credit_start_missing_fields(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    
    response = client.post("/api/affiliate/credits/start", data={}, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 201
    assert json_data["success"] == -2
    assert "incomplete" in json_data["message"].lower()

def test_unauthorized_aff_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    # Use different affiliate ID not in allowed slaves
    form_data = {
        "aff_id": 51,
        "amount": "100",
        "gateway": 0
    }

    response = client.post("/api/affiliate/credits/start", data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 403
    assert json_data["success"] == -3
    assert "unauthorized" in json_data["message"].lower()

def test_gateway_not_supported(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        "aff_id": rep.affiliate_id,
        "amount": 2,
        "gateway": 10
    }

    response = client.post("/api/affiliate/credits/start", data=form_data, headers=auth_headers)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data["success"] == -5
    assert "no payment gateway" in json_data["message"].lower()

def test_gateway_order_creation_failure(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        "aff_id": rep.affiliate_id,
        "amount": 2,
        "gateway": 0
    }
    with patch("affiliate_b2b.affiliate_wallet.create_order") as mock_create_order:
        mock_create_order.return_value.status_code = 500

        response = client.post("/api/affiliate/credits/start", data=form_data, headers=auth_headers)
        json_data = response.get_json()

        assert response.status_code == 200
        assert json_data["order_id"] == -4
        assert "razorpay" in json_data["message"].lower()

def test_successful_initiate_credit_order(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        "aff_id": rep.affiliate_id,
        "amount": 2,
        "gateway": 0
    }
    with patch("affiliate_b2b.affiliate_wallet.create_order") as mock_create_order:
        mock_create_order.return_value.status_code = 200
        mock_create_order.return_value.json.return_value = {
            "id": "order_QETfLva8AdVmi0"
        }

        response = client.post("/api/affiliate/credits/start", data=form_data, headers=auth_headers)
        json_data = response.get_json()
        print(json_data)

        assert response.status_code == 200
        assert json_data["success"] == 1
        assert "order_id" in json_data
        assert json_data["order_id"] == "order_QETfLva8AdVmi0"

""" Test cases for api: /api/affiliate/transfer_money """

@pytest.fixture
def create_affiliate():
    affiliate = Affiliate("Test_Dummy2",  "Test Dummy2", 0, -1, 10, None, notification=None,  enabled=True, 
        wallet_threshold=1000)
    db.session.add(affiliate)
    db.session.flush()
    affiliate.mapped_wallet_affiliate = affiliate.id
    db.session.commit()

    return affiliate

def test_transfer_money_invalid_jwt_claims(client):
    form_data = {
        "from_aff_id": 3,
        "to_aff_id": 2,
        "amount": 100
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data)

    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'


def test_transfer_money_affiliate_account_disabled(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == rep.affiliate_id).first()
    affiliate.enabled = False
    db.session.commit()
    form_data = {
        "from_aff_id": rep.id,
        "to_aff_id": 2,
        "amount": 100
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)
    json_data = response.get_json()
    assert response.status_code == 403
    
    assert json_data['success'] == -1

def test_transfer_money_missing_fields(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    
    response = client.post("/api/affiliate/transfer_money", data={}, headers=auth_headers)

    assert response.status_code == 400
    assert response.get_json()['success'] == -2

def test_transfer_money_invalid_field_types(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data = {
        "from_aff_id": "abc", 
        "to_aff_id": "xyz", 
        "amount": "ten"
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)

    assert response.status_code == 400
    assert response.get_json()['success'] == -3

def test_transfer_money_unauthorized_affiliate(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login

    form_data = {
        "from_aff_id": 3,
        "to_aff_id": 2,
        "amount": 100
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)

    assert response.status_code == 403
    assert response.get_json()['success'] == -4

def test_transfer_money_affiliate_not_found(client, affiliate_rep_login, create_affiliate):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    to_affiliate = create_affiliate

    form_data = {
        "from_aff_id": rep.affiliate_id,
        "to_aff_id": 999,  # non-existent
        "amount": 100
    }

    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)
    json_data = response.get_json()

    print("Response:", json_data)
    assert response.status_code == 404
    assert json_data['success'] == -6


def test_transfer_money_invalid_amount(client, affiliate_rep_login, create_affiliate):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    to_affiliate = create_affiliate
    to_affiliate.id = 20
    db.session.commit()
    form_data = {
        "from_aff_id": rep.affiliate_id,
        "to_aff_id": 20,
        "amount": -100
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)

    assert response.status_code == 400
    assert response.get_json()['success'] == -5

def test_transfer_money_success(client, affiliate_rep_login, create_affiliate):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    to_affiliate = create_affiliate
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == rep.affiliate_id).first()
    affiliate.wallet = 5
    db.session.commit()
    to_affiliate.id = 20
    db.session.commit()
    form_data = {
        "from_aff_id": rep.affiliate_id,
        "to_aff_id": 20,
        "amount": 2
    }
    response = client.post("/api/affiliate/transfer_money", data=form_data, headers=auth_headers)

    data = response.get_json()
    print("response", data)
    assert response.status_code == 200
    assert data["success"] == 1
  

