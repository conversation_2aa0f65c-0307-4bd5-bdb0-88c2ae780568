import datetime 
from utils.bookings.booking_params import BookingParams,BookingParams
from utils.time_utils import get_dt_ist
from utils.redis_utils import read_redis_data
from db_config import db
from models.models import BookingCancelled, Bookings, Trip, Pricing,Coupons
import math
import json
import pytz

def parse_time(time_str):
    if isinstance(time_str, datetime.time):
        return time_str
    return datetime.datetime.strptime(time_str, "%H:%M:%S").time()

def fetch_pricing_data(key):
    data, redis_status = read_redis_data(key)
    if data and redis_status:
        return data, True # already decoded JSON
    else:
        city = key.split("_")
        pricing = db.session.query(Pricing).filter(Pricing.city == city[1]).all()
        return pricing if pricing else [], False


class PriceCity:

    DIST_CHARGE_ON = [BookingParams.is_dist_on(i) for i in BookingParams.ALL_CUST_TYPES]
    CGST = 0
    SGST = 0

    def __init__(self, price_city):
        city_key = price_city
        if city_key == 19 or city_key == 'price_alampur':
            city_data, redis_status = fetch_pricing_data('price_kolkata') # take kolkata pricing if alampur selected - no prcing for alampur
            city_key = 'price_kolkata'
        else:
            city_data, redis_status = fetch_pricing_data(city_key)
        price_data, config_status  = read_redis_data("price_config")
        self.OP_DAYS = []
        self.MIN_TRAVEL_OP = []
        self.TRAVEL_FEE_OP = []
        self.BASE_MINIMUM_OP = []
        self.BASE_FLAT_OP = []
        self.BASE_MINIOS_MINIMUM_OP = []
        self.BASE_MINIOS_FLAT_OP = []
        self.FLAT_ONEWAY_HIKE_OP = []
        self.OP_BOOKING_PERC = []
        self.OP_OT_MULT = []
        self.CAR_FARE_OP = []
        self.OP_CAR_FARE = []
        self.PART_NIGHT_HIKE_OP = []
        self.NIGHT_HIKE_OP = [] 
        self.CANCEL_THRESH = []
        self.CANCEL_CHARGES_VAR = [[], [], [], []]
        self.CANCEL_CHARGES_FLAT = []

        if redis_status and config_status:
            print("Redis connected", flush=True)
            trip_data = city_data['trip_types']
            trip_type = trip_data['inCity']
            fare_details = trip_type['fare_details']
            night_details = trip_type['night_details']
            cancellation_details = trip_type['cancellation_details']
            basic_fare = fare_details['basic_fares']
            car_fare = fare_details['car_fares']
            extra_fare = fare_details['extra_fares']
            insurance_fare = fare_details['insurance_fares']
            variable_charges = cancellation_details['variable_charges']
            static_charges = cancellation_details['static_charges']

            self.MIN_TRAVEL_COST = int(basic_fare.get('min_travel_cost', '30'))
            self.TRAVEL_FEE = int(basic_fare.get('travel_cost', '4'))
            self.NIGHT_HIKE = int(night_details.get('night_charge', '100'))
            self.PART_NIGHT_HIKE = int(night_details.get('part_night_charge', '50'))

            self.BASE_MINIMUM =  int(basic_fare.get('base_fare', '148'))
            self.BASE_FLAT = int(basic_fare.get('add_fare', '72'))
            self.BASE_MINIOS_MINIMUM = int(basic_fare.get('minios_base_fare', '248'))
            self.BASE_MINIOS_FLAT = int(basic_fare.get('minios_add_fare', '72'))
            
            self.PREMIUM_DRIVER_PERCENT = int(extra_fare.get('premium_driver_percent', '15'))
            self.PREMIUM_DRIVER_FLAT_CHARGE = int(extra_fare.get('premium_driver_flat', '100'))
            self.THRESHOLD_FORWARD_NIGHT = int(extra_fare.get('threshold_forward_night', '30'))
            self.THRESHOLD_BACKWARD_NIGHT = int(extra_fare.get('threshold_backward_night', '30'))
            self.THRESHOLD_END_NIGHT_FORWARD = int(extra_fare.get('threshold_end_night_forward', '30'))
            self.THRESHOLD_FORWARD_OP = int(extra_fare.get('threshold_forward_op', '30'))
            self.THRESHOLD_BACKWARD_OP = int(extra_fare.get('threshold_backward_op', '30'))



            self.FLAT_ONEWAY_HIKE = int(basic_fare.get('oneway_fare', '50'))
            self.CAR_FARE = ( int(car_fare.get('hatch_man', '0')),
                        int(car_fare.get('sedan_man', '25')),
                        int(car_fare.get('suv_man', '50')),
                        int(car_fare.get('lux_man', '100')),
                        int(car_fare.get('hatch_auto', '20')),
                        int(car_fare.get('sedan_auto', '45')),
                        int(car_fare.get('suv_auto', '70')),
                        int(car_fare.get('lux_auto', '120')),
                        int(car_fare.get('hatch_ev', '0')),
                        int(car_fare.get('sedan_ev', '25')),
                        int(car_fare.get('suv_ev', '50')),
                        int(car_fare.get('lux_ev', '100')),
                        int(car_fare.get('muv_man', '0')),
                        int(car_fare.get('muv_auto', '20')),
                        int(car_fare.get('muv_ev', '30')) )
            self.INSURANCE_CHARGE = (   int(insurance_fare.get('round', '18')),
                                int(insurance_fare.get('out_station', '50')),
                                int(insurance_fare.get('one_way', '18')),
                                int(insurance_fare.get('mini_os', '18')),
                                int(insurance_fare.get('oustation_oneway', '50')),
                                int(insurance_fare.get('minios_oneway', '18')) )
            
            self.BOOKING_PERC = float(basic_fare.get('booking_percent', 10))/100
            self.NIGHT_THRESH_0 = parse_time(night_details.get("start_night_time", datetime.time(18, 0, 0)))
            self.NIGHT_THRESH_1 = parse_time(night_details.get("end_night_time", datetime.time(0, 0, 0)))
            self.PART_NIGHT_THRESH = parse_time(night_details.get("part_night_time", datetime.time(17, 0, 0)))
            self.EXTRA_RATE_FIRST = int(basic_fare.get('first_overtime_charge', '90'))  # Charge 60 / HOUR_RATIO for 1st hour
            self.EXTRA_RATE_SECOND = int(basic_fare.get('second_overtime_charge', '90'))
            self.EXTRA_RATE_THIRD = int(basic_fare.get('add_overtime_charge', '90'))
            # extra due for OT
            self.OVERTIME_FIRST_THRESH = int(basic_fare.get('first_overtime', '60'))
            self.OVERTIME_SEC_THRESH = int(basic_fare.get('second_overtime', '120'))

            self.CANCEL_THRESH = list(reversed([
                            float(charge.get("hour_range", 0)) * 60 * 60
                            for charge in variable_charges
                            if charge.get("hour_range") != "Max"
                        ]))

            for charge in reversed(variable_charges):
                customer_cancel = int(float(charge.get("cust_charge", 0)))
                driver_cancel = int(float(charge.get("driver_charge", 0)))
                both_cust_cancel = int(float(charge.get("both_cust_charge", 0)))
                both_driver_cancel = int(float(charge.get("both_driver_charge", 0)))

                self.CANCEL_CHARGES_VAR[0].append((customer_cancel, 0))  # Customer cancel
                self.CANCEL_CHARGES_VAR[1].append((0, driver_cancel))  # Driver cancel
                self.CANCEL_CHARGES_VAR[2].append((both_cust_cancel, both_driver_cancel))  # Both
                self.CANCEL_CHARGES_VAR[3].append((0, 0))  # Neither

            customer_cancel = int(float(static_charges.get("cust_charge", 0)))
            driver_cancel = int(float(static_charges.get("driver_charge", 0)))
            both_cancel_customer = int(float(static_charges.get("both_cust_charge", 0)))
            both_cancel_driver = int(float(static_charges.get("both_driver_charge", 0)))

            self.CANCEL_CHARGES_FLAT = [(customer_cancel, 0), (0, driver_cancel), (both_cancel_customer, both_cancel_driver), (0, 0)]
            # Occasion
            cities = price_data['cities']
            city_occasion = cities[city_key.split('_')[1]]
            occasion_list = city_occasion['inCity']
            # occasion calc
            for event, details in occasion_list.items():
                try:
                    dates = details.get("dates", [])
                    event_dates = []
                    for date in dates:
                        parsed_date = datetime.datetime.strptime(date, "%d/%m/%Y")
                        event_dates.append(parsed_date)
                except Exception as e:
                    event_dates = []
                    print(e)
                self.OP_DAYS.append(event_dates)
                # Price of Occasion
                op_fare = trip_type.get(event)  # Get the fare details for the current event
                if op_fare:
                    op_night_details = op_fare.get('night_details', {})
                    op_fare_details = op_fare.get('fare_details', {})
                    op_basic_fare = op_fare_details.get('basic_fares', {})
                    op_car_fare = op_fare_details.get('car_fares', {})
                    self.MIN_TRAVEL_OP.append(int(op_basic_fare.get('min_travel_cost', '60')))
                    self.TRAVEL_FEE_OP.append(int(op_basic_fare.get('travel_cost', '5')))
                    self.BASE_MINIMUM_OP.append(int(op_basic_fare.get('base_fare', '200')))
                    self.BASE_FLAT_OP.append(int(op_basic_fare.get('add_fare', '100')))
                    self.BASE_MINIOS_MINIMUM_OP.append(int(op_basic_fare.get('minios_base_fare', '300')))
                    self.BASE_MINIOS_FLAT_OP.append(int(op_basic_fare.get('minios_add_fare', '100')))
                    self.FLAT_ONEWAY_HIKE_OP.append(int(op_basic_fare.get('oneway_fare', '100')))
                    self.OP_BOOKING_PERC.append(float(op_basic_fare.get('booking_percent', 10))/100)
                    self.OP_OT_MULT.append((int(op_basic_fare.get('first_overtime_charge', '120')),
                                            int(op_basic_fare.get('second_overtime_charge', '120')),
                                            int(op_basic_fare.get('add_overtime_charge', '120'))))
                    self.CAR_FARE_OP.append([   int(op_car_fare.get('hatch_man', '0')),
                                                int(op_car_fare.get('sedan_man', '25')),
                                                int(op_car_fare.get('suv_man', '50')),
                                                int(op_car_fare.get('lux_man', '100')),
                                                int(op_car_fare.get('hatch_auto', '20')),
                                                int(op_car_fare.get('sedan_auto', '45')),
                                                int(op_car_fare.get('suv_auto', '70')),
                                                int(op_car_fare.get('lux_auto', '120')) ])
                    self.PART_NIGHT_HIKE_OP.append(int(op_night_details.get('night_charge', '130')))
                    self.NIGHT_HIKE_OP.append(int(op_night_details.get('part_night_charge', '60')))

                    self.OP_CAR_FARE.append([x for x in self.CAR_FARE])
                    
        else:
            # mysql data
            print("Redis unavailable", flush=True)
            for data in city_data:
                # Parse basic fare, car fare, and insurance fare from JSON
                basic_fare = data.basic_fare if data.basic_fare else {}
                night_fare = data.night_fare if data.night_fare else {}
                variable_ch = data.variable_charges if data.variable_charges else {}
                static_ch = data.static_charges if data.static_charges else {}
                car_fare = data.car_fare if data.car_fare else {}
                insurance_fare = data.insurance_fare if data.insurance_fare else {}
                if data.trip_type == "inCity" and not data.occasion:
                    self.MIN_TRAVEL_COST = int(basic_fare.get("min_travel_cost", 30))
                    self.TRAVEL_FEE = int(basic_fare.get("travel_cost", 4))
                    self.NIGHT_HIKE = int(night_fare.get("night_charge", 100))
                    self.PART_NIGHT_HIKE = int(night_fare.get("part_night_charge", 50))
                    self.BASE_MINIMUM = int(basic_fare.get("base_fare", 148))
                    self.BASE_FLAT = int(basic_fare.get("add_fare", 72))
                    self.BASE_MINIOS_MINIMUM = int(basic_fare.get("minios_base_fare", 248))
                    self.BASE_MINIOS_FLAT = int(basic_fare.get("minios_add_fare", 72))
                    self.FLAT_ONEWAY_HIKE = int(basic_fare.get("oneway_fare", 50))

                    self.CAR_FARE = (
                        int(car_fare.get("hatch_man", 0)),
                        int(car_fare.get("sedan_man", 25)),
                        int(car_fare.get("suv_man", 50)),
                        int(car_fare.get("lux_man", 100)),
                        int(car_fare.get("hatch_auto", 20)),
                        int(car_fare.get("sedan_auto", 45)),
                        int(car_fare.get("suv_auto", 70)),
                        int(car_fare.get("lux_auto", 120)),
                        int(car_fare.get("hatch_ev", 0)),
                        int(car_fare.get("sedan_ev", 25)),
                        int(car_fare.get("suv_ev", 50)),
                        int(car_fare.get("lux_ev", 100)),
                        int(car_fare.get("muv_man", 0)),
                        int(car_fare.get("muv_auto", 20)),
                        int(car_fare.get("muv_ev", 30))
                    )
                    self.INSURANCE_CHARGE = (
                        int(insurance_fare.get("round", 18)),
                        int(insurance_fare.get("out_station", 50)),
                        int(insurance_fare.get("one_way", 18)),
                        int(insurance_fare.get("mini_os", 18)),
                        int(insurance_fare.get("oustation_oneway", 50)),
                        int(insurance_fare.get("minios_oneway", 18))
                    )
                    self.BOOKING_PERC = float(basic_fare.get('booking_percent', 10))/100

                    self.NIGHT_THRESH_0 = parse_time(night_fare.get("start_night_time", datetime.time(18, 0, 0)))
                    self.NIGHT_THRESH_1 = parse_time(night_fare.get("end_night_time", datetime.time(0, 0, 0)))
                    self.PART_NIGHT_THRESH = parse_time(night_fare.get("part_night_time", datetime.time(17, 0, 0)))

                    self.EXTRA_RATE_FIRST = int(basic_fare.get('first_overtime_charge', '90'))  # Charge 60 / HOUR_RATIO for 1st hour
                    self.EXTRA_RATE_SECOND = int(basic_fare.get('second_overtime_charge', '90'))
                    self.EXTRA_RATE_THIRD = int(basic_fare.get('add_overtime_charge', '90'))
                    # extra due for OT
                    self.OVERTIME_FIRST_THRESH = int(basic_fare.get('first_overtime', '60'))
                    self.OVERTIME_SEC_THRESH = int(basic_fare.get('second_overtime', '120'))

                    self.CANCEL_THRESH = list(reversed([
                            float(charge.get("hour_range", 0)) * 60 * 60
                            for charge in variable_ch
                            if charge.get("hour_range") != "Max"
                        ]))

        
                    for charge in reversed(variable_ch):
                        customer_cancel = int(float(charge.get("cust_charge", 0)))
                        driver_cancel = int(float(charge.get("driver_charge", 0)))
                        both_cust_cancel = int(float(charge.get("both_cust_charge", 0)))
                        both_driver_cancel = int(float(charge.get("both_driver_charge", 0)))

                        self.CANCEL_CHARGES_VAR[0].append((customer_cancel, 0))  # Customer cancel
                        self.CANCEL_CHARGES_VAR[1].append((0, driver_cancel))  # Driver cancel
                        self.CANCEL_CHARGES_VAR[2].append((both_cust_cancel, both_driver_cancel))  # Both
                        self.CANCEL_CHARGES_VAR[3].append((0, 0))  # Neither

                    customer_cancel = int(float(static_ch.get("cust_charge", 0)))
                    driver_cancel = int(float(static_ch.get("driver_charge", 0)))
                    both_cancel_customer = int(float(static_ch.get("both_cust_charge", 0)))
                    both_cancel_driver = int(float(static_ch.get("both_driver_charge", 0)))

                    self.CANCEL_CHARGES_FLAT = [(customer_cancel, 0), (0, driver_cancel), (both_cancel_customer, both_cancel_driver), (0, 0)]
                # occasion calc
                elif data.trip_type == "inCity" and data.occasion:
                    details = data.occasion
                    try:
                        dates = details.get("dates", [])
                        event_dates = []
                        for date in dates:
                            parsed_date = datetime.datetime.strptime(date, "%d/%m/%Y")
                            event_dates.append(parsed_date)
                    except Exception as e:
                        event_dates = []
                        print(e)
                    self.OP_DAYS.append(event_dates)

                    self.MIN_TRAVEL_OP.append(int(basic_fare.get('min_travel_cost', '60')))
                    self.TRAVEL_FEE_OP.append(int(basic_fare.get('travel_cost', '5')))
                    self.BASE_MINIMUM_OP.append(int(basic_fare.get('base_fare', '200')))
                    self.BASE_FLAT_OP.append(int(basic_fare.get('add_fare', '100')))
                    self.BASE_MINIOS_MINIMUM_OP.append(int(basic_fare.get('minios_base_fare', '300')))
                    self.BASE_MINIOS_FLAT_OP.append(int(basic_fare.get('minios_add_fare', '100')))
                    self.FLAT_ONEWAY_HIKE_OP.append(int(basic_fare.get('oneway_fare', '100')))
                    self.OP_BOOKING_PERC.append(float(basic_fare.get('booking_percent', 10))/100)
                    self.OP_OT_MULT.append(( int(basic_fare.get('first_overtime_charge', '120')),
                                            int(basic_fare.get('second_overtime_charge', '120')),
                                            int(basic_fare.get('add_overtime_charge', '120')) ))
                    self.CAR_FARE_OP.append([   int(car_fare.get('hatch_man', '0')),
                                                int(car_fare.get('sedan_man', '25')),
                                                int(car_fare.get('suv_man', '50')),
                                                int(car_fare.get('lux_man', '100')),
                                                int(car_fare.get('hatch_auto', '20')),
                                                int(car_fare.get('sedan_auto', '45')),
                                                int(car_fare.get('suv_auto', '70')),
                                                int(car_fare.get('lux_auto', '120')),
                                                int(car_fare.get('hatch_ev', '0')),
                                                int(car_fare.get('sedan_ev', '25')),
                                                int(car_fare.get('suv_ev', '50')),
                                                int(car_fare.get('lux_ev', '100')),
                                                int(car_fare.get('muv_man', '0')),
                                                int(car_fare.get('muv_auto', '20')),
                                                int(car_fare.get('muv_ev', '30')) ])
                    self.PART_NIGHT_HIKE_OP.append(int(night_fare.get('night_charge', '130')))
                    self.NIGHT_HIKE_OP.append(int(night_fare.get('part_night_charge', '60')))
                    self.OP_CAR_FARE.append([x for x in self.CAR_FARE])

        self.BASE_THRESHOLD = 1
        self.BASE_MINIOS_THRESHOLD = 1
        self.BOOKING_CHARGE = (12, 12, 17, 45, 12, 12, 17, 45)
        self.BOOKING_CHARGE_DISC = 0
        self.CAR_CHARGE_DISC = 1  # set these to 1 when no discount
        self.EARLY_MORN_THRESH_0 = datetime.time(1, 0, 0)
        self.EARLY_MORN_THRESH_1 = datetime.time(1, 0, 0)  
        self.SPECIAL_TRIP = [1496, 1498]

    @staticmethod
    def get_cgst():
        return PriceCity.CGST

    @staticmethod
    def get_sgst():
        return PriceCity.SGST

    @staticmethod
    def get_hour_ratio():
        return BookingParams.HOUR_RATIO


    def get_op_level(self, startdate, starttime, enddate=None, endtime=None, is_immediate=False,
        forward_threshold_minutes=30,backward_threshold_minutes=30):
        start_ist = get_dt_ist(startdate, starttime)

        if not is_immediate:
            for level in range(len(self.OP_DAYS) - 1, -1, -1):
                op_dates = [dt.date() for dt in self.OP_DAYS[level]]
                if start_ist.date() in op_dates:
                    return level
            return -1

        # For is_immediate bookings
        reference_time = pytz.timezone("Asia/Kolkata").localize(start_ist)
        forward_threshold = datetime.timedelta(minutes=forward_threshold_minutes)
        backward_threshold = datetime.timedelta(minutes=backward_threshold_minutes)
        
        for level in range(len(self.OP_DAYS) - 1, -1, -1):
            for dt in self.OP_DAYS[level]:
                dt_local = dt if dt.tzinfo else pytz.timezone("Asia/Kolkata").localize(dt)
                if dt_local.date() == reference_time.date():
                    return level

        best_match = -1
        min_diff = datetime.timedelta.max

        for level in range(len(self.OP_DAYS) - 1, -1, -1):
            for dt in self.OP_DAYS[level]:
                dt_local = dt if dt.tzinfo else pytz.timezone("Asia/Kolkata").localize(dt)

                for variant in [dt_local, dt_local + datetime.timedelta(days=1)]:
                    diff = variant - reference_time

                    if datetime.timedelta(0) <= diff <= forward_threshold:
                        if diff < min_diff:
                            best_match = level
                            min_diff = diff

                    elif -backward_threshold <= diff < datetime.timedelta(0):
                        if abs(diff) < min_diff:
                            best_match = level
                            min_diff = abs(diff)

        return best_match

    def get_cancel_level(self, curtime, starttime, has_trip=False):
        if has_trip:
            return len(self.CANCEL_THRESH) - 1
        if curtime > starttime:
            return len(self.CANCEL_THRESH) - 1
        delta = starttime - curtime
        if delta.days > 0:
            return 0
        sec_delta = delta.seconds
        for idx in range(len(self.CANCEL_THRESH)):
            if sec_delta > self.CANCEL_THRESH[idx]:
                return idx
        return -1
    
    def get_cancel_ch(self, curtime, starttime, has_trip=False, cancel_cat=0, is_ontrip=False):
        to_charge, fixed = BookingCancelled.reason_type(cancel_cat)
        charges = (0, 0)
        if to_charge < 0:
            return [-1, -1], -1
        if not fixed:
            level = self.get_cancel_level(curtime, starttime, is_ontrip)
            charges = self.CANCEL_CHARGES_VAR[to_charge][level]
        else:
            level = len(self.CANCEL_THRESH) - 1
            charges = self.CANCEL_CHARGES_FLAT[to_charge]
        return tuple(int(c) for c in charges), level

    def get_booking_ch(self, car_type, driver_id, booking_id):
        book = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        if book.type >= 50:
            return 0
        has_trip = db.session.query(Trip, Bookings).filter(Bookings.id == Trip.book_id). \
            filter(Bookings.id == booking_id).first()
        if has_trip and has_trip[0].endtime:
            return has_trip[0].due
        else:
            try:
                op_level = self.get_op_level(book.startdate, book.starttime)
                if op_level > -1:
                    book_due = book.estimate * self.OP_BOOKING_PERC[op_level] + book.insurance_cost
                else:
                    book_due = book.estimate * self.BOOKING_PERC + book.insurance_cost
            except Exception:
                book_due = 0
            return round(book_due)

    def get_booking_percent(self, booking_id, book = None):
        try:
            if not book:
                book = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
            op_level = self.get_op_level(book.startdate, book.starttime, None, None)
            if op_level > -1:
                book_percent =  self.OP_BOOKING_PERC[op_level]
            else:
                book_percent = self.BOOKING_PERC
        except Exception as e:
            print('percent code', str(e), flush = True)
            book_percent = 0
        return book_percent

    def get_booking_ch_new(self, booking_info):
        if booking_info.trip_endtime:
            return booking_info.trip_due
        else:
            try:
                op_level = self.get_op_level(booking_info.booking_startdate, booking_info.booking_starttime)
                if op_level > -1:
                    book_due = booking_info.booking_estimate * self.OP_BOOKING_PERC[op_level] + booking_info.booking_insurance_cost
                else:
                    book_due = booking_info.booking_estimate * self.BOOKING_PERC + booking_info.booking_insurance_cost
            except Exception as e:
                print(e)
                book_due = 0
            return round(book_due)


    def get_insurance_ch(self,booking_id):
        book = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        if not book:
            return 0
        return book.insurance_cost

    def get_dist_fare(self, dist):
        dist_km = dist / 1000
        dist_fare = dist_km * self.TRAVEL_FEE
        dist_fare = math.ceil(dist_fare)
        if dist_fare < 0:
            dist_fare = 0
        dist_fare = max(self.MIN_TRAVEL_COST, dist_fare)
        return dist_fare


    def get_dist_fare_op(self, dist, op_level):
        dist_km = dist / 1000
        dist_fare = dist_km * self.TRAVEL_FEE_OP[op_level]
        dist_fare = math.ceil(dist_fare)
        if dist_fare < 0:
            dist_fare = 0
        dist_fare = max(self.MIN_TRAVEL_OP[op_level], dist_fare)
        return dist_fare

    @staticmethod
    def get_gst(base_price):
        cgst = round(base_price * PriceCity.CGST)
        sgst = round(base_price * PriceCity.SGST)
        if cgst + sgst < base_price * (PriceCity.CGST + PriceCity.SGST):
            cgst += 1
        final = base_price + cgst + sgst
        return base_price, cgst, sgst, final


    def get_price(self, type, dur, starttime, endtime, dist, car_type, startdate, enddate, loc_cluster,insurance_num=0, is_immediate=False,coupon=None):
        op_level = self.get_op_level(
            startdate, starttime, enddate, endtime,
            is_immediate=is_immediate,
            forward_threshold_minutes=self.THRESHOLD_FORWARD_OP,
            backward_threshold_minutes=self.THRESHOLD_BACKWARD_OP
        )
        if op_level == -1:
            return self.get_price_flat(type, dur, starttime, endtime, dist, car_type,insurance_num,is_immediate,coupon)
        else:
            return self.get_price_op(type, dur, starttime, endtime, dist, car_type, loc_cluster, op_level,insurance_num,is_immediate,coupon)


    def get_price_flat(self, type, dur, starttime, endtime, dist, car_type, insurance_num, is_immediate=False,coupon=None):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0
        car_fare = 0
        immediate_surcharge = 0
        driver_type_surcharge = 0
        applied_discount = 0
        if int(car_type) > len(self.CAR_FARE) - 1:
            car_type = 0
        pickup_hour = starttime.hour + starttime.minute / 60
        end_hour = endtime.hour + endtime.minute / 60
        night, part_night = self.is_trip_night(pickup_hour, end_hour, is_immediate)
        base_fare = self.BASE_MINIMUM
        if dur > self.BASE_THRESHOLD:
            base_fare += self.BASE_FLAT * (dur - self.BASE_THRESHOLD)
        night_fare = self.NIGHT_HIKE * night + self.PART_NIGHT_HIKE * part_night
        if self.starts_at_night(starttime.hour, is_immediate):
            night_fare *= 1.2
        car_fare = self.CAR_FARE[int(car_type)] * self.CAR_CHARGE_DISC
        if type == BookingParams.TYPE_ONEWAY:
            base_fare += self.FLAT_ONEWAY_HIKE
        elif type == BookingParams.TYPE_MINIOS or type == BookingParams.TYPE_MINIOS_ONEWAY:
            base_fare = self.BASE_MINIOS_MINIMUM
            if dur > self.BASE_MINIOS_THRESHOLD:
                base_fare += self.BASE_MINIOS_FLAT * (dur - self.BASE_MINIOS_THRESHOLD)
        dist_fare = PriceCity.DIST_CHARGE_ON[type-1] * self.get_dist_fare(dist)
        total_fare = base_fare + night_fare + dist_fare + car_fare
        total_no_tax, cgst, sgst, total_fare = PriceCity.get_gst(total_fare)
        booking_ch_perc = round(self.BOOKING_PERC * total_fare)
        booking_ch = booking_ch_perc - cgst - sgst
        base_fare -= booking_ch
        total_with_ins = total_fare
        driver_base_ch = total_fare - booking_ch
        driver_night_ch = 0
        insurance_ch = 0
        if insurance_num > 0:
            total_with_ins += insurance_num * self.INSURANCE_CHARGE[type-1]
            insurance_ch = insurance_num*self.INSURANCE_CHARGE[type-1]
        # if is_immediate:
        #     print("sasss",is_immediate,flush=True)
        #     if BookingParamsApp.CHARGE_TYPE == "Flat":
        #         immediate_surcharge = BookingParamsApp.IMMEDIATE_FLAT_CHARGE
        #         total_fare = total_fare + immediate_surcharge
        #     else:
        #         base_fare = base_fare + base_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         night_fare = night_fare + night_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         dist_fare = dist_fare + dist_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         car_fare = car_fare + car_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         total_fare = total_fare + total_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC
        #         immediate_surcharge = int(round(base_fare + night_fare + dist_fare + car_fare))
                

        if BookingParams.CHARGE_TYPE == "Flat":
            driver_type_surcharge = self.PREMIUM_DRIVER_FLAT_CHARGE
            # total_fare = total_fare + driver_type_surcharge
        else:
            base_fare_inc =  base_fare * self.PREMIUM_DRIVER_PERCENT 
            night_fare_inc =  night_fare * self.PREMIUM_DRIVER_PERCENT 
            dist_fare_inc = dist_fare * self.PREMIUM_DRIVER_PERCENT 
            car_fare_inc = car_fare * self.PREMIUM_DRIVER_PERCENT  
            driver_type_surcharge = int(round(base_fare_inc + night_fare_inc + dist_fare_inc + car_fare_inc))
        if coupon:
            if coupon and total_fare >= (coupon.min_trip_price or 0):
                discount = 0.0

                # Apply flat off if defined and > 0
                if coupon.flat_off and coupon.flat_off > 0:
                    discount = coupon.flat_off

                # Else apply percent off (capped by max_off)
                elif coupon.percent_off and coupon.percent_off > 0:
                    raw_discount = total_fare * (coupon.percent_off / 100.0)
                    max_allowed = coupon.max_off or raw_discount
                    discount = min(raw_discount, max_allowed)
                discount = round(discount, 2)
                total_fare = max(0, total_fare - discount)
                applied_discount = discount

                
        # or percentage: driver_type_surcharge = int(round((base_fare + night_fare + dist_fare + car_fare) * self.CLASSIC_DRIVER_PERCENT_INC))
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), int(round(dist_fare)), int(
            round(booking_ch)), int(round(car_fare)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), \
            int(round(total_with_ins)), int(round(insurance_ch)), False,int(round(driver_base_ch)),int(round(driver_night_ch)), int(round(driver_type_surcharge)), int(round(immediate_surcharge)) , int(round(applied_discount))


    def get_price_op(self, type, dur, starttime, endtime, dist, car_type, loc_cluster, op_level, insurance_num, is_immediate=False):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0
        booking_ch = 0
        car_fare = 0
        immediate_surcharge = 0
        driver_type_surcharge = 0
        if int(car_type) > len(self.CAR_FARE) - 1:
            car_type = 0
        night, part_night = self.is_trip_night(starttime.hour, endtime.hour, is_immediate)
        base_fare = self.BASE_MINIMUM_OP[op_level]
        if dur > self.BASE_THRESHOLD:
            base_fare += self.BASE_FLAT_OP[op_level] * (dur - self.BASE_THRESHOLD)
        night_fare = self.NIGHT_HIKE_OP[op_level] * night + self.PART_NIGHT_HIKE_OP[op_level] * part_night
        if self.starts_at_night(starttime.hour):
            night_fare *= 1.2
        booking_ch = 0
        car_fare = self.CAR_FARE_OP[op_level][int(car_type)]
        if type == BookingParams.TYPE_ONEWAY:
            base_fare += self.FLAT_ONEWAY_HIKE_OP[op_level]
        elif type == BookingParams.TYPE_MINIOS or type == BookingParams.TYPE_MINIOS_ONEWAY:
            base_fare = self.BASE_MINIOS_MINIMUM_OP[op_level]
            if dur > self.BASE_MINIOS_THRESHOLD:
                base_fare += self.BASE_MINIOS_FLAT_OP[op_level] * (dur - self.BASE_MINIOS_THRESHOLD)
        dist_fare = PriceCity.DIST_CHARGE_ON[type-1] * self.get_dist_fare_op(dist, op_level)

        total_fare = base_fare + night_fare + dist_fare + car_fare
        total_no_tax, cgst, sgst, total_fare = PriceCity.get_gst(total_fare)
        booking_ch_perc = round(self.OP_BOOKING_PERC[op_level] * total_fare)
        booking_ch = booking_ch_perc - cgst - sgst
        base_fare -= booking_ch
        total_with_ins = total_fare
        driver_base_ch = total_fare - booking_ch
        driver_night_ch = 0
        insurance_ch = 0
        if insurance_num > 0:
            total_with_ins += insurance_num*self.INSURANCE_CHARGE[type-1]
            insurance_ch = insurance_num*self.INSURANCE_CHARGE[type-1]
            
        # if is_immediate:
        #     if BookingParamsApp.CHARGE_TYPE == "Flat":
        #         immediate_surcharge = BookingParamsApp.IMMEDIATE_FLAT_CHARGE
        #         total_fare = total_fare + immediate_surcharge
        #     else:
        #         base_fare = base_fare + base_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         night_fare = night_fare + night_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         dist_fare = dist_fare + dist_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         car_fare = car_fare + car_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         total_fare = total_fare + total_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC
        #         immediate_surcharge = int(round(base_fare + night_fare + dist_fare + car_fare))
                

        if BookingParams.CHARGE_TYPE == "Flat":
            driver_type_surcharge = self.PREMIUM_DRIVER_FLAT_CHARGE
            # total_fare = total_fare + driver_type_surcharge
        else:
            base_fare_inc =  base_fare * self.PREMIUM_DRIVER_PERCENT 
            night_fare_inc =  night_fare * self.PREMIUM_DRIVER_PERCENT 
            dist_fare_inc = dist_fare * self.PREMIUM_DRIVER_PERCENT 
            car_fare_inc = car_fare * self.PREMIUM_DRIVER_PERCENT  
            # total_fare = total_fare + total_fare * self.PREMIUM_DRIVER_PERCENT_INC 
            driver_type_surcharge = int(round(base_fare_inc + night_fare_inc + dist_fare_inc + car_fare_inc))
            
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), int(round(dist_fare)), int(
            round(booking_ch)), int(round(car_fare)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), \
            int(round(total_with_ins)), int(round(insurance_ch)), True,int(round(driver_base_ch)),int(round(driver_night_ch)), int(round(driver_type_surcharge)), int(round(immediate_surcharge))


    def get_overtime_charge_gen(self, real_delta, book_delta):
        overtime = (real_delta - book_delta)
        if overtime <= self.OVERTIME_FIRST_THRESH:
            return round(overtime * self.EXTRA_RATE_FIRST / BookingParams.HOUR_RATIO)
        elif overtime <= self.OVERTIME_SEC_THRESH:
            return round(self.OVERTIME_FIRST_THRESH * self.EXTRA_RATE_FIRST / BookingParams.HOUR_RATIO + \
                         (overtime - self.OVERTIME_FIRST_THRESH) * self.EXTRA_RATE_SECOND / BookingParams.HOUR_RATIO)
        else:
            return round(self.OVERTIME_FIRST_THRESH * (self.EXTRA_RATE_FIRST / BookingParams.HOUR_RATIO) + \
                         (self.OVERTIME_SEC_THRESH - self.OVERTIME_FIRST_THRESH) * \
                         (self.EXTRA_RATE_SECOND / BookingParams.HOUR_RATIO) + \
                         (overtime - self.OVERTIME_SEC_THRESH) * self.EXTRA_RATE_THIRD / BookingParams.HOUR_RATIO)


    def get_overtime_charge_op(self, real_delta, book_delta, op_level):
        overtime = (real_delta - book_delta)
        if overtime <= self.OVERTIME_FIRST_THRESH:
            return overtime * self.OP_OT_MULT[op_level][0]  / BookingParams.HOUR_RATIO
        elif overtime <= self.OVERTIME_SEC_THRESH:
            return self.OVERTIME_FIRST_THRESH * self.OP_OT_MULT[
                op_level][0]  / BookingParams.HOUR_RATIO + \
                   (overtime - self.OVERTIME_FIRST_THRESH) * \
                   self.OP_OT_MULT[op_level][1]  / BookingParams.HOUR_RATIO
        else:
            return self.OVERTIME_FIRST_THRESH * self.OP_OT_MULT[
                op_level][0]  / BookingParams.HOUR_RATIO + \
                   (self.OVERTIME_SEC_THRESH - self.OVERTIME_FIRST_THRESH) * \
                   self.OP_OT_MULT[op_level][1]  / BookingParams.HOUR_RATIO + \
                   (overtime - self.OVERTIME_SEC_THRESH) * \
                   self.OP_OT_MULT[op_level][2]  / BookingParams.HOUR_RATIO


    def get_trip_price(self, book_id, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime,
                       startdate, enddate, insurance_ch=0):

        op_level = self.get_op_level(startdate, trip_starttime, enddate, trip_stoptime)
        if op_level == -1:
            return self.get_trip_price_gen(book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime, insurance_ch)
        else:
            # assume no 7 day roundtrip (lol)
            return self.get_trip_price_gen(book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime, insurance_ch)


    def get_trip_price_gen(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime, trip_stoptime, insurance_ch):
        ot_fare = 0
        night_fare = 0
        if real_delta > book_delta:
            ot_fare = self.get_overtime_charge_gen(real_delta, book_delta)
            price = est + ot_fare
        else:
            price = est
        night, part_night = self.is_trip_night(trip_starttime.hour, trip_stoptime.hour)
        est_night, est_pnight = self.is_trip_night(book_starttime.hour, book_stoptime.hour)
        if night and not est_night and not est_pnight:
            night_fare = self.NIGHT_HIKE + self.PART_NIGHT_HIKE
            price += night_fare
        elif night and est_pnight and not est_night:
            night_fare = self.NIGHT_HIKE
            price += night_fare
        elif part_night and not night and not est_pnight:
            night_fare = self.PART_NIGHT_HIKE
            price += night_fare
        total_no_tax, cgst, sgst, price = PriceCity.get_gst(price)
        add_due = self.BOOKING_PERC * (price) + insurance_ch
        price += insurance_ch
        return int(round(price)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), int(round(add_due)), \
                int(round(ot_fare)), int(round(night_fare))


    def get_trip_price_op(self, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime, trip_stoptime,
                          op_level, insurance_ch):
        ot_fare = 0
        night_fare = 0
        if real_delta > book_delta:
            ot_fare = self.get_overtime_charge_op(real_delta, book_delta, op_level)
            price = est + ot_fare
        else:
            price = est
        night, part_night = self.is_trip_night(trip_starttime.hour, trip_stoptime.hour)
        est_night, est_pnight = self.is_trip_night(book_starttime.hour, book_stoptime.hour)
        if night and not est_night and not est_pnight:
            night_fare = self.NIGHT_HIKE_OP[op_level] + self.PART_NIGHT_HIKE_OP[op_level]
            price += night_fare
        elif night and est_pnight and not est_night:
            night_fare = self.NIGHT_HIKE_OP[op_level]
            price += night_fare
        elif part_night and not night and not est_pnight:
            night_fare = self.PART_NIGHT_HIKE_OP[op_level]
            price += night_fare
        total_no_tax, cgst, sgst, price = PriceCity.get_gst(price)
        #add_due = self.BOOKING_PERC * (price) + insurance_ch
        add_due = self.OP_BOOKING_PERC[op_level] * (price) + insurance_ch
        price += insurance_ch
        return int(round(price)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), int(round(add_due)), \
            int(round(ot_fare)), int(round(night_fare))


    def get_trip_price_sp(self, book_id, book_delta, real_delta, est, book_starttime, book_stoptime, trip_starttime,
                          trip_stoptime):
        index = self.SPECIAL_TRIP.index(book_id)
        if index == 0 or index == 1:
            hours = math.ceil(real_delta / 60)
            return 70 * hours, 0


    def get_ot_rates(self, startdate, enddate):
        op_level = self.get_op_level(startdate, datetime.time(0, 0, 0), enddate, datetime.time(0, 0, 0))
        if op_level == -1:
            # assume no 7 day roundtrip (lol)
            return self.EXTRA_RATE_FIRST, self.EXTRA_RATE_SECOND, self.EXTRA_RATE_THIRD
        else:
            return self.OP_OT_MULT[op_level][0] , self.OP_OT_MULT[
                op_level][1] , \
                   self.OP_OT_MULT[op_level][2]


    # def starts_at_night(self, start_hour, is_immediate=False, threshold_hours=1):
    #     # Build night hour range from PART_NIGHT_THRESH to NIGHT_THRESH_1
    #     if self.PART_NIGHT_THRESH.hour > self.NIGHT_THRESH_1.hour:
    #         night_hours = set(range(self.PART_NIGHT_THRESH.hour, 24)).union(
    #                     range(0, self.NIGHT_THRESH_1.hour))
    #     else:
    #         night_hours = set(range(self.PART_NIGHT_THRESH.hour,
    #                                 self.NIGHT_THRESH_1.hour + 1))

    #     if is_immediate:
    #         # Simulate ±threshold_hours window
    #         low = int((start_hour - threshold_hours) // 1)
    #         high = int((start_hour + threshold_hours) // 1 + 1)
    #         hours_to_check = {(h % 24) for h in range(low, high)}
    #         return any(h in night_hours for h in hours_to_check)

    #     # Normal case
    #     return start_hour in night_hours
    
    
    def starts_at_night(
        self,
        start_hour,
        is_immediate=False,
    ):
        # Convert PART_NIGHT_THRESH → NIGHT_THRESH_1 to hour set
        start_h = self.PART_NIGHT_THRESH.hour
        end_h = self.NIGHT_THRESH_1.hour

        if start_h > end_h:
            night_hours = set(range(start_h, 24)).union(range(0, end_h))
        else:
            night_hours = set(range(start_h, end_h + 1))

        if is_immediate:
            # Convert start hour to minutes
            start_min = int(start_hour * 60)

            # Calculate minute range: [start - back, start + forward]
            low = (start_min - self.THRESHOLD_BACKWARD_NIGHT) % 1440
            high = (start_min + self.THRESHOLD_FORWARD_NIGHT) % 1440

            if low <= high:
                minutes_range = range(low, high + 1)
            else:
                minutes_range = list(range(low, 1440)) + list(range(0, high + 1))

            # Convert to hours and check
            checked_hours = {(minute // 60) % 24 for minute in minutes_range}
            return any(h in night_hours for h in checked_hours)

        # Regular check (non-immediate)
        return int(start_hour) in night_hours


    # def is_trip_night(self, start_hour, end_hour, is_immediate=False, threshold_hours=1):
    #     night = False
    #     part_night = False

    #     # Extract hour thresholds
    #     night_start = self.NIGHT_THRESH_0.hour
    #     night_end = self.NIGHT_THRESH_1.hour
    #     part_start = self.PART_NIGHT_THRESH.hour

    #     # Create sets for night and part-night hours
    #     if night_start > night_end:
    #         night_hours = set(range(night_start, 24)).union(range(0, night_end))
    #     else:
    #         night_hours = set(range(night_start, night_end + 1))

    #     if part_start > night_end:
    #         part_night_hours = set(range(part_start, 24)).union(range(0, night_end))
    #     else:
    #         part_night_hours = set(range(part_start, night_end + 1))
    #     print(start_hour,end_hour,flush=True)
    #     print(night_hours,part_night_hours,flush=True)
    #     if is_immediate:
    #         print(start_hour,end_hour,flush=True)
    #         print("nighttt",flush=True)
    #         # Calculate threshold range around start_hour (rounded to nearest full hours)
    #         low = int((start_hour - threshold_hours) // 1)
    #         high = int((start_hour + threshold_hours) // 1 + 1)
    #         print(low,high,flush=True)
    #         print(night_hours,part_night_hours,flush=True)

    #         hours_to_check = {(h % 24) for h in range(low, high)}
    #         print(hours_to_check,flush=True)

    #         for h in hours_to_check:
    #             if h in night_hours:
    #                 night = True
    #             if h in part_night_hours:
    #                 part_night = True

    #         part_night = part_night or night
    #         print(night,part_night,flush=True)
    #         return night, part_night

    #     # Regular non-immediate check
    #     while start_hour != end_hour:
    #         if start_hour in part_night_hours:
    #             part_night = True
    #         if start_hour in night_hours:
    #             night = True
    #             break
    #         start_hour = (start_hour + 1) % 24

    #     if end_hour in night_hours:
    #         night = True
    #     if end_hour in part_night_hours:
    #         part_night = True

    #     part_night = part_night or night
    #     print(night,part_night,flush=True)
    #     return night, part_night
    
    
    def is_trip_night(
            self,
            start_hour,  # float (e.g., 23.5)
            end_hour,    # float
            is_immediate=False
        ):
            night = False
            part_night = False

            # Convert NIGHT/PART_NIGHT thresholds to minute sets
            def get_minute_range(start, end):
                start_min = start.hour * 60 + start.minute
                end_min = end.hour * 60 + end.minute
                if start_min > end_min:
                    return set(range(start_min, 1440)).union(set(range(0, end_min)))
                return set(range(start_min, end_min + 1))

            def get_part_night_range(part_start, night_end):
                part_min = part_start.hour * 60 + part_start.minute
                night_end_min = night_end.hour * 60 + night_end.minute
                if part_min > night_end_min:
                    return set(range(part_min, 1440)).union(set(range(0, night_end_min)))
                return set(range(part_min, night_end_min + 1))

            night_minutes = get_minute_range(self.NIGHT_THRESH_0, self.NIGHT_THRESH_1)
            part_night_minutes = get_part_night_range(self.PART_NIGHT_THRESH, self.NIGHT_THRESH_1)
            # Convert float hours to minutes
            start_minute = int(start_hour * 60)
            end_minute = int(end_hour * 60)

            if is_immediate:
                # Range around start time
                start_low = (start_minute - self.THRESHOLD_BACKWARD_NIGHT) % 1440
                start_high = (start_minute + self.THRESHOLD_FORWARD_NIGHT) % 1440
                if start_low <= start_high:
                    start_range = set(range(start_low, start_high + 1))
                else:
                    start_range = set(range(start_low, 1440)).union(set(range(0, start_high + 1)))

                # Range after end time
                end_high = (end_minute + self.THRESHOLD_END_NIGHT_FORWARD) % 1440
                if end_minute <= end_high:
                    end_range = set(range(end_minute, end_high + 1))
                else:
                    end_range = set(range(end_minute, 1440)).union(set(range(0, end_high + 1)))

                # Combine all checked minutes
                minutes_to_check = start_range.union(end_range)

                for minute in minutes_to_check:
                    if minute in night_minutes:
                        night = True
                    if minute in part_night_minutes:
                        part_night = True

                part_night = part_night or night
                return night, part_night

            # Non-immediate: Check from start to end, minute by minute
            curr_min = start_minute
            while curr_min != end_minute:
                if curr_min in night_minutes:
                    night = True
                if curr_min in part_night_minutes:
                    part_night = True
                curr_min = (curr_min + 1) % 1440

            # Also check the exact end time
            if end_minute in night_minutes:
                night = True
            if end_minute in part_night_minutes:
                part_night = True

            part_night = part_night or night
            return night, part_night


    def is_trip_early_morn(self, start_hour, end_hour):
        night, part_time = self.is_trip_night(start_hour, end_hour)
        if night or part_time:
            return False
        morn = False
        if self.EARLY_MORN_THRESH_0.hour >= self.EARLY_MORN_THRESH_1.hour:
            morn_hour = list(set([x for x in range(self.EARLY_MORN_THRESH_0.hour,  self.EARLY_MORN_THRESH_1.hour)]).union
                                (set([self.EARLY_MORN_THRESH_0.hour])))
        else:
            morn_hour = list(
                set([x for x in range(self.EARLY_MORN_THRESH_0.hour, 24)]).union(
                    set([x for x in range(0, self.EARLY_MORN_THRESH_1.hour + 1)])))
        while start_hour != end_hour:
            if start_hour in morn_hour:
                morn = True
                break
            start_hour = (start_hour + 1) % 24
        if end_hour in morn_hour:
            morn = True
        return morn


class PriceCityOutstation:

    def __init__(self, price_city):

        self.price_city = PriceCity(price_city)
        city_key = price_city
        city_data, redis_status = fetch_pricing_data(city_key)
        price_data, config_status = read_redis_data("price_config")
        self.OP_DAYS = []
        self.OP_BASE_FARE = []
        self.OP_CAR_FARE = []
        self.OP_EXTRA_RATE = []
        self.OP_NIGHT_HIKE = []
        self.OP_BOOKING_PERC = []
        self.CANCEL_THRESH = []
        self.CANCEL_CHARGES_VAR = [[], [], [], []]
        self.CANCEL_CHARGES_FLAT = []
        if redis_status and config_status:
            print("Redis connected", flush=True)
            trip_data = city_data['trip_types']
            trip_type = trip_data['outStation']
            fare_details = trip_type['fare_details']
            cancellation_details = trip_type['cancellation_details']
            basic_fare = fare_details['basic_fares']
            car_fare = fare_details['car_fares']
            extra_fare = fare_details['extra_fares']
            variable_charges = cancellation_details['variable_charges']
            static_charges = cancellation_details['static_charges']

            # all 12 hr calcs
            self.BASE_FARE =  int(basic_fare.get('base_fare', '600'))
            self.CAR_FARE = ( int(car_fare.get('hatch_man', '0')),
                                int(car_fare.get('sedan_man', '40')),
                                int(car_fare.get('suv_man', '80')),
                                int(car_fare.get('lux_man', '200')),
                                int(car_fare.get('hatch_auto', '30')),
                                int(car_fare.get('sedan_auto', '70')),
                                int(car_fare.get('suv_auto', '110')),
                                int(car_fare.get('lux_auto', '230')))  # hatchback, sedan, SUV, luxury, manual then auto

            self.EXTRA_RATE = int(basic_fare.get('extra_fare', '60'))
            self.BOOKING_PERC = float(basic_fare.get('booking_percent', '10'))/100

            self.CANCEL_THRESH = list(reversed([
                        float(charge.get("hour_range", 0)) * 60 * 60
                        for charge in variable_charges
                        if charge.get("hour_range") != "Max"
                    ]))

            self.PREMIUM_DRIVER_PERCENT = int(extra_fare.get('premium_driver_percent', '15'))
            self.PREMIUM_DRIVER_FLAT_CHARGE = int(extra_fare.get('premium_driver_flat', '100'))
            self.THRESHOLD_FORWARD_NIGHT = int(extra_fare.get('threshold_forward_night', '30'))
            self.THRESHOLD_BACKWARD_NIGHT = int(extra_fare.get('threshold_backward_night', '30'))
            self.THRESHOLD_END_NIGHT_FORWARD = int(extra_fare.get('threshold_end_night_forward', '30'))
            self.THRESHOLD_FORWARD_OP = int(extra_fare.get('threshold_forward_op', '30'))
            self.THRESHOLD_BACKWARD_OP = int(extra_fare.get('threshold_backward_op', '30'))
        
            for charge in reversed(variable_charges):
                customer_cancel = int(float(charge.get("cust_charge", 0)))
                driver_cancel = int(float(charge.get("driver_charge", 0)))
                both_cust_cancel = int(float(charge.get("both_cust_charge", 0)))
                both_driver_cancel = int(float(charge.get("both_driver_charge", 0)))

                self.CANCEL_CHARGES_VAR[0].append((customer_cancel, 0))  # Customer cancel
                self.CANCEL_CHARGES_VAR[1].append((0, driver_cancel))  # Driver cancel
                self.CANCEL_CHARGES_VAR[2].append((both_cust_cancel, both_driver_cancel))  # Both
                self.CANCEL_CHARGES_VAR[3].append((0, 0))  # Neither

            customer_cancel = int(float(static_charges.get("cust_charge", 0)))
            driver_cancel = int(float(static_charges.get("driver_charge", 0)))
            both_cancel_customer = int(float(static_charges.get("both_cust_charge", 0)))
            both_cancel_driver = int(float(static_charges.get("both_driver_charge", 0)))

            self.CANCEL_CHARGES_FLAT = [(customer_cancel, 0), (0, driver_cancel), (both_cancel_customer, both_cancel_driver), (0, 0)]

            # Occasion
            cities = price_data['cities']
            city_occasion = cities[price_city.split('_')[1]]
            occasion_list = city_occasion['outStation']
            for event, details in occasion_list.items():
                try:
                    dates = details.get("dates", [])
                    event_dates = []
                    for date in dates:
                        parsed_date = datetime.datetime.strptime(date, "%d/%m/%Y")
                        event_dates.append(parsed_date)
                except Exception as e:
                    event_dates = []
                    print(e)
                self.OP_DAYS.append(event_dates)

                op_fare = trip_type.get(event)
                if op_fare:
                    op_fare_details = op_fare.get('fare_details', {})
                    op_basic_fare = op_fare_details.get('basic_fares', {})
                    op_car_fare = op_fare_details.get('car_fares', {})
                    self.OP_BASE_FARE.append(int(op_basic_fare.get('base_fare', 850)))
                    self.OP_BOOKING_PERC.append(int(op_basic_fare.get('booking_percent', 15)))
                    self.OP_EXTRA_RATE.append(int(op_basic_fare.get('extra_fare', 84)))
                    self.OP_NIGHT_HIKE.append(int(op_basic_fare.get('night_hike', 0)))
                    self.OP_CAR_FARE.append((
                                int(op_car_fare.get('hatch_man', '0')),
                                int(op_car_fare.get('sedan_man', '25')),
                                int(op_car_fare.get('suv_man', '50')),
                                int(op_car_fare.get('lux_man', '100')),
                                int(op_car_fare.get('hatch_auto', '20')),
                                int(op_car_fare.get('sedan_auto', '45')),
                                int(op_car_fare.get('suv_auto', '70')),
                                int(op_car_fare.get('lux_auto', '120'))
                    ))
        else:
            print("Redis unavailable", flush=True)
            for data in city_data:
                # Parse basic fare, car fare, and insurance fare from JSON
                basic_fare = data.basic_fare if data.basic_fare else {}
                car_fare = data.car_fare if data.car_fare else {}
                variable_ch = data.variable_charges if data.variable_charges else {}
                static_ch = data.static_charges if data.static_charges else {}
                if data.trip_type == "outStation" and not data.occasion:
                    self.BASE_FARE =  int(basic_fare.get('base_fare', '600'))
                    self.CAR_FARE = ( int(car_fare.get('hatch_man', '0')),
                                int(car_fare.get('sedan_man', '40')),
                                int(car_fare.get('suv_man', '80')),
                                int(car_fare.get('lux_man', '200')),
                                int(car_fare.get('hatch_auto', '30')),
                                int(car_fare.get('sedan_auto', '70')),
                                int(car_fare.get('suv_auto', '110')),
                                int(car_fare.get('lux_auto', '230')))  # hatchback, sedan, SUV, luxury, manual then auto

                    self.EXTRA_RATE = int(basic_fare.get('extra_fare', '60'))
                    self.BOOKING_PERC = float(basic_fare.get('booking_percent', '10'))/100

                    self.CANCEL_THRESH = list(reversed([
                            float(charge.get("hour_range", 0)) * 60 * 60
                            for charge in variable_ch
                            if charge.get("hour_range") != "Max"
                        ]))

        
                    for charge in reversed(variable_ch):
                        customer_cancel = int(float(charge.get("cust_charge", 0)))
                        driver_cancel = int(float(charge.get("driver_charge", 0)))
                        both_cust_cancel = int(float(charge.get("both_cust_charge", 0)))
                        both_driver_cancel = int(float(charge.get("both_driver_charge", 0)))

                        self.CANCEL_CHARGES_VAR[0].append((customer_cancel, 0))  # Customer cancel
                        self.CANCEL_CHARGES_VAR[1].append((0, driver_cancel))  # Driver cancel
                        self.CANCEL_CHARGES_VAR[2].append((both_cust_cancel, both_driver_cancel))  # Both
                        self.CANCEL_CHARGES_VAR[3].append((0, 0))  # Neither

                    customer_cancel = int(float(static_ch.get("cust_charge", 0)))
                    driver_cancel = int(float(static_ch.get("driver_charge", 0)))
                    both_cancel_customer = int(float(static_ch.get("both_cust_charge", 0)))
                    both_cancel_driver = int(float(static_ch.get("both_driver_charge", 0)))

                    self.CANCEL_CHARGES_FLAT = [(customer_cancel, 0), (0, driver_cancel), (both_cancel_customer, both_cancel_driver), (0, 0)]
                elif data.trip_type == "outStation" and data.occasion:
                    details = data.occasion
                    try:
                        dates = details.get("dates", [])
                        event_dates = []
                        for date in dates:
                            parsed_date = datetime.datetime.strptime(date, "%d/%m/%Y")
                            event_dates.append(parsed_date)
                    except Exception as e:
                        event_dates = []
                        print(e)
                    self.OP_DAYS.append(event_dates)

                    self.OP_BASE_FARE.append(int(basic_fare.get('base_fare', 850)))
                    self.OP_BOOKING_PERC.append(int(basic_fare.get('booking_percent', 15)))
                    self.OP_EXTRA_RATE.append(int(basic_fare.get('extra_fare', 84)))
                    self.OP_NIGHT_HIKE.append(int(basic_fare.get('night_hike', 0)))
                    self.OP_CAR_FARE.append((
                                int(car_fare.get('hatch_man', '0')),
                                int(car_fare.get('sedan_man', '25')),
                                int(car_fare.get('suv_man', '50')),
                                int(car_fare.get('lux_man', '100')),
                                int(car_fare.get('hatch_auto', '20')),
                                int(car_fare.get('sedan_auto', '45')),
                                int(car_fare.get('suv_auto', '70')),
                                int(car_fare.get('lux_auto', '120')),
                                int(car_fare.get('hatch_ev', '0')),
                                int(car_fare.get('sedan_ev', '25')),
                                int(car_fare.get('suv_ev', '50')),
                                int(car_fare.get('lux_ev', '100')),
                                int(car_fare.get('muv_man', '0')),
                                int(car_fare.get('muv_auto', '20')),
                                int(car_fare.get('muv_ev', '30'))
                    ))

        self.NIGHT_HIKE = 0
        self.HOUR_RATIO = 60
        self.CAR_CHARGE_DISC = 1  # set these to 1 when no discount
        self.BOOKING_CHARGE = (0, 0, 0, 0, 0, 0, 0, 0)
        self.BOOKING_CHARGE_DISC = 1
        self.OP_BOOKING_CHARGE = [1.0 * x for x in self.BOOKING_CHARGE]

    def get_op_level(self, startdate, enddate, is_immediate=False,
                    forward_threshold_minutes=30, backward_threshold_minutes=30):

        ist = pytz.timezone("Asia/Kolkata")
        reference_time = ist.localize(datetime.datetime.combine(startdate, datetime.time.min))

        forward_threshold = datetime.timedelta(minutes=forward_threshold_minutes)
        backward_threshold = datetime.timedelta(minutes=backward_threshold_minutes)

        # Case 1: Non-immediate logic (check if any date in range is in OP_DAYS)
        if not is_immediate:
            for level in range(len(self.OP_DAYS) - 1, -1, -1):
                cur_date = startdate
                while cur_date <= enddate:
                    op_dates = [dt.date() for dt in self.OP_DAYS[level]]
                    if cur_date in op_dates:
                        return level
                    cur_date += datetime.timedelta(days=1)
            return -1

        # Case 2: Immediate logic with forward/backward threshold
        best_match = -1
        min_diff = datetime.timedelta.max

        for level in range(len(self.OP_DAYS) - 1, -1, -1):
            for dt in self.OP_DAYS[level]:
                dt_local = dt if dt.tzinfo else ist.localize(dt)

                # Only consider OP dates within same day as reference_time
                if dt_local.date() == reference_time.date():
                    return level  # best possible match

                # Try both the same day and next day (handles late-night edge cases)
                for variant in [dt_local, dt_local + datetime.timedelta(days=1)]:
                    diff = variant - reference_time

                    if datetime.timedelta(0) <= diff <= forward_threshold:
                        if diff < min_diff:
                            best_match = level
                            min_diff = diff

                    elif -backward_threshold <= diff < datetime.timedelta(0):
                        if abs(diff) < min_diff:
                            best_match = level
                            min_diff = abs(diff)

        return best_match

    def get_booking_ch(self, car_type, driver_id, booking_id):
        has_trip = db.session.query(Trip, Bookings).filter(Bookings.id == Trip.book_id). \
            filter(Bookings.id == booking_id).first()
        if has_trip and has_trip[0].endtime:
            return has_trip[0].due
        else:
            try:
                book_due = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
                op_level = self.get_op_level(book_due.startdate, book_due.enddate)
                if op_level > -1:
                    book_due = book_due.estimate * self.OP_BOOKING_PERC[op_level]
                else:
                    book_due = book_due.estimate * self.BOOKING_PERC
            except Exception:
                book_due = 0
            return round(book_due)

    def get_booking_ch_new(self, booking_info):
        if booking_info.trip_endtime:
            return booking_info.trip_due
        else:
            try:
                op_level = self.get_op_level(booking_info.booking_startdate, booking_info.booking_enddate)
                if op_level > -1:
                    book_due = booking_info.booking_estimate * self.OP_BOOKING_PERC[op_level]
                else:
                    book_due = booking_info.booking_estimate * self.BOOKING_PERC
            except Exception as e:
                print(e)
                book_due = 0
            return round(book_due)

    def get_price(self, startdate, enddate, nofood, dur, car_type, type=BookingParams.TYPE_OUTSTATION, dist=0,insurance_num=0, is_immediate=False,coupon=None):
        op_level = self.get_op_level(startdate, enddate,is_immediate=is_immediate,forward_threshold_minutes=self.THRESHOLD_FORWARD_OP,backward_threshold_minutes=self.THRESHOLD_BACKWARD_OP)
        if op_level > -1:
            return self.get_price_op(nofood, dur, car_type, type, dist, insurance_num, op_level, is_immediate,coupon)
        else:
            return self.get_price_gen(nofood, dur, car_type, type, dist, insurance_num, is_immediate,coupon)
 

    def get_price_gen(self, nofood, dur, car_type, type, dist, insurance_num, is_immediate=False,coupon=None):
        dur = math.ceil(dur / 12)
        immediate_surcharge = 0
        applied_discount = 0
        total_fare = 0
        if nofood:
            food_ch = self.NIGHT_HIKE * dur
        else:
            food_ch = 0
        base_fare = self.BASE_FARE * dur
        booking_ch = self.BOOKING_CHARGE[int(car_type)] * self.BOOKING_CHARGE_DISC * dur
        car_fare = self.CAR_FARE[int(car_type)] * self.CAR_CHARGE_DISC
        dist_fare = PriceCity.DIST_CHARGE_ON[type-1] * self.price_city.get_dist_fare(dist)
        total_fare = total_fare + base_fare + food_ch + booking_ch + car_fare + dist_fare
        total_no_tax, cgst, sgst, total_fare = PriceCity.get_gst(total_fare)
        booking_ch_perc = round(self.BOOKING_PERC * total_fare)
        booking_ch = booking_ch_perc - cgst - sgst
        base_fare -= booking_ch
        total_with_ins = total_fare
        insurance_ch = 0
        if insurance_num>0:
            total_with_ins += insurance_num * self.price_city.INSURANCE_CHARGE[type-1]
            insurance_ch = insurance_num * self.price_city.INSURANCE_CHARGE[type-1]
        # if is_immediate:
        #     if BookingParamsApp.CHARGE_TYPE == "Flat":
        #         immediate_surcharge = BookingParamsApp.IMMEDIATE_FLAT_CHARGE
        #         total_fare = total_fare + immediate_surcharge
        #     else:
        #         base_fare = base_fare + base_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         night_fare = night_fare + night_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         dist_fare = dist_fare + dist_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         car_fare = car_fare + car_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         total_fare = total_fare + total_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC
        #         immediate_surcharge = int(round(base_fare + night_fare + dist_fare + car_fare))
        # if driver_type == BookingParamsApp.PREMIUM_DRIVER:
        if BookingParams.CHARGE_TYPE == "Flat":
            driver_type_surcharge = self.PREMIUM_DRIVER_FLAT_CHARGE
            # total_fare = total_fare + driver_type_surcharge
        else:
            base_fare_inc =  base_fare * self.PREMIUM_DRIVER_PERCENT 
            dist_fare_inc = dist_fare * self.PREMIUM_DRIVER_PERCENT 
            car_fare_inc = car_fare * self.PREMIUM_DRIVER_PERCENT  
            driver_type_surcharge = int(round(base_fare_inc + dist_fare_inc + car_fare_inc))
            
        if coupon:
            if coupon and total_fare >= (coupon.min_trip_price or 0):
                discount = 0.0

                # Apply flat off if defined and > 0
                if coupon.flat_off and coupon.flat_off > 0:
                    discount = coupon.flat_off

                # Else apply percent off (capped by max_off)
                elif coupon.percent_off and coupon.percent_off > 0:
                    raw_discount = total_fare * (coupon.percent_off / 100.0)
                    max_allowed = coupon.max_off or raw_discount
                    discount = min(raw_discount, max_allowed)
                discount = round(discount, 2)
                total_fare = max(0, total_fare - discount)
                applied_discount = discount
        return int(round(total_fare)), int(round(base_fare)), 0, int(round(dist_fare)), int(
            round(booking_ch)), int(round(car_fare)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), \
            int(round(total_with_ins)), int(round(insurance_ch)),False,int(round(driver_type_surcharge)), int(round(immediate_surcharge)), int(round(applied_discount))


    def get_price_op(self, nofood, dur, car_type, type, dist, insurance_num, op_level, is_immediate=False,coupon=None):
        dur = math.ceil(dur / 12)
        immediate_surcharge = 0
        applied_discount = 0
        total_fare = 0
        if nofood:
            food_ch = self.OP_NIGHT_HIKE[op_level] * 99 * dur
        else:
            food_ch = 0
        base_fare = self.OP_BASE_FARE[op_level] * dur
        booking_ch = self.OP_BOOKING_CHARGE[int(car_type)] * dur
        car_fare = self.OP_CAR_FARE[op_level][int(car_type)]
        dist_fare = PriceCity.DIST_CHARGE_ON[type-1] * self.price_city.get_dist_fare(dist)
        total_fare = total_fare + base_fare + food_ch + booking_ch + car_fare + dist_fare
        total_no_tax, cgst, sgst, total_fare = PriceCity.get_gst(total_fare)
        booking_ch_perc = round(self.OP_BOOKING_PERC[op_level] * total_fare)
        booking_ch = booking_ch_perc - cgst - sgst
        base_fare -= booking_ch
        total_with_ins = total_fare
        insurance_ch = 0
        if insurance_num>0:
            total_with_ins += insurance_num * self.price_city.INSURANCE_CHARGE[type-1]
            insurance_ch = insurance_num * self.price_city.INSURANCE_CHARGE[type-1]
        # if is_immediate:
        #     if BookingParamsApp.CHARGE_TYPE == "Flat":
        #         immediate_surcharge = BookingParamsApp.IMMEDIATE_FLAT_CHARGE
        #         total_fare = total_fare + immediate_surcharge
        #     else:
        #         base_fare = base_fare + base_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         night_fare = night_fare + night_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         dist_fare = dist_fare + dist_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         car_fare = car_fare + car_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC 
        #         total_fare = total_fare + total_fare * BookingParamsApp.IMMEDIATE_PERCENT_INC
        #         immediate_surcharge = int(round(base_fare + night_fare + dist_fare + car_fare))
        if BookingParams.CHARGE_TYPE == "Flat":
            driver_type_surcharge = self.PREMIUM_DRIVER_FLAT_CHARGE
            # total_fare = total_fare + driver_type_surcharge
        else:
            base_fare_inc =  base_fare * self.PREMIUM_DRIVER_PERCENT 
            dist_fare_inc = dist_fare * self.PREMIUM_DRIVER_PERCENT 
            car_fare_inc = car_fare * self.PREMIUM_DRIVER_PERCENT  
            driver_type_surcharge = int(round(base_fare_inc + dist_fare_inc + car_fare_inc))
            
        if coupon:
            if coupon and total_fare >= (coupon.min_trip_price or 0):
                discount = 0.0

                # Apply flat off if defined and > 0
                if coupon.flat_off and coupon.flat_off > 0:
                    discount = coupon.flat_off

                # Else apply percent off (capped by max_off)
                elif coupon.percent_off and coupon.percent_off > 0:
                    raw_discount = total_fare * (coupon.percent_off / 100.0)
                    max_allowed = coupon.max_off or raw_discount
                    discount = min(raw_discount, max_allowed)
                discount = round(discount, 2)
                total_fare = max(0, total_fare - discount)
                applied_discount = discount
            
        return int(round(total_fare)), int(round(base_fare)), 0, int(round(dist_fare)), int(
            round(booking_ch)), int(round(car_fare)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), \
            int(round(total_with_ins)), int(round(insurance_ch)),True,int(round(driver_type_surcharge)), int(round(immediate_surcharge)), int(round(applied_discount))


    def get_trip_price(self, book_delta, real_delta, est, startdate, enddate, insurance_ch=0):
        op_level = self.get_op_level(startdate, enddate)
        if op_level > -1:
            # assume no 7 day roundtrip (lol)
            return self.get_trip_price_op(book_delta, real_delta, est, insurance_ch, op_level)
        else:
            return self.get_trip_price_gen(book_delta, real_delta, est, insurance_ch)


    def get_trip_price_gen(self, book_delta, real_delta, est, insurance_ch):
        ot_fare = 0
        night_fare = 0
        if real_delta > book_delta:
            ot_fare = (real_delta - book_delta) * self.EXTRA_RATE / BookingParams.HOUR_RATIO
            price = est + ot_fare
        else:
            price = est
        total_no_tax, cgst, sgst, price = PriceCity.get_gst(price)
        add_due = self.BOOKING_PERC * (price) + insurance_ch
        price += insurance_ch
        return int(round(price)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), int(round(add_due)), \
            int(round(ot_fare)), int(round(night_fare))

    def get_trip_price_op(self, book_delta, real_delta, est, insurance_ch, op_level):
        ot_fare = 0
        night_fare = 0
        if real_delta > book_delta:
            ot_fare = (real_delta - book_delta) * \
                    self.OP_EXTRA_RATE[op_level] / BookingParams.HOUR_RATIO
            price = est + ot_fare
        else:
            price = est
        total_no_tax, cgst, sgst, price = PriceCity.get_gst(price)
        add_due = self.OP_BOOKING_PERC[op_level] * (price) + insurance_ch
        price += insurance_ch
        return int(round(price)), int(round(total_no_tax)), int(round(cgst)), int(round(sgst)), int(round(add_due)), \
            int(round(ot_fare)), int(round(night_fare))


    def get_ot_rates(self, startdate, enddate):
        op_level = self.get_op_level(startdate, enddate)
        if op_level > -1:
            # assume no 7 day roundtrip (lol)
            return self.OP_EXTRA_RATE[op_level]
        else:
            return self.EXTRA_RATE

    def get_cancel_level(self, curtime, starttime, has_trip=False):
        if has_trip:
            return len(self.CANCEL_THRESH) - 1
        if curtime > starttime:
            return len(self.CANCEL_THRESH) - 1
        delta = starttime - curtime
        if delta.days > 0:
            return 0
        sec_delta = delta.seconds
        for idx in range(len(self.CANCEL_THRESH)):
            if sec_delta > self.CANCEL_THRESH[idx]:
                return idx
        return -1
    
    def get_cancel_ch(self, curtime, starttime, has_trip=False, cancel_cat=0, is_ontrip=False):
        to_charge, fixed = BookingCancelled.reason_type(cancel_cat)
        charges = (0, 0)
        if to_charge < 0:
            return [-1, -1], -1
        if not fixed:
            level = self.get_cancel_level(curtime, starttime, is_ontrip)
            charges = self.CANCEL_CHARGES_VAR[to_charge][level]
        else:
            level = len(self.CANCEL_THRESH) - 1
            charges = self.CANCEL_CHARGES_FLAT[to_charge]
        return tuple(int(c) for c in charges), level