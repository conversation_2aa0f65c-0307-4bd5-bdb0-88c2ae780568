tags:
  - User
summary: Reset user password
description: |
  Allows a user to reset their password using a valid verification token and secret. 
  The new password is applied only if the token is verified successfully.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Registered mobile number of the user
  - name: pwd
    in: formData
    type: string
    required: true
    description: New password to set
  - name: token_and_secret
    in: formData
    type: string
    required: true
    description: Token and secret string used to verify identity
  - name: countrycode
    in: formData
    type: string
    required: false
    description: Country code (default is "91")

responses:
  200:
    description: Password updated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Password has been successfully updated."

  400:
    description: Validation error in input data
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Missing required field: token_and_secret"

  401:
    description: Invalid token or secret
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid token or secret. Verification failed."

  403:
    description: Token expired
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -8
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "The token has expired. Please request a new password reset link."

  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -8
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
