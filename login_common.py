from functools import wraps
from flask_cors import cross_origin
import datetime
import hashlib
import http.client
import json
import html
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, create_access_token,
    create_refresh_token, get_jwt, verify_jwt_in_request,
    get_jwt_identity, set_access_cookies, set_refresh_cookies, get_jwt, decode_token, unset_jwt_cookies
)
from sqlalchemy.sql import func, or_, desc, asc, and_, exists
from sqlalchemy import exc
from _ops_message import send_slack_msg
import _sms
from flask import current_app as app
from redis_config import execute_with_fallback
import time
from socketio_app import send_notification_to_channel, live_update_to_channel
from flasgger import swag_from
from sendotp import sendotp
from referral import get_user_ref_code
import _sms
from _utils_acc import account_enabled, validate_role
from __init__ import jwt
from _utils import complete, get_pic_url, get_safe
from models import Users, Drivers, UserToken, db, <PERSON><PERSON><PERSON>, UsernameMap, UserFCM, DriverFCM, DriverVerify, AdminUserLog, AdminAccess

loginn = Blueprint('newlogin', __name__)
MASTER_PWD = 'a@ufD4M!2EX'
MASTER_PWD_2 = '348787'
MASTER_OTP = '3487'

BIT_0_TO_14 = 16383
BIT_1_TO_15 = 65534
BIT_1_TO_6 = 126
BIT_0_TO_19 = 1048575
restricted_roles = [
            Users.ROLE_USER, Users.ROLE_DRIVER, Users.ROLE_C24, Users.ROLE_REVV,
            Users.ROLE_ZOOMCAR, Users.ROLE_GUJRAL, Users.ROLE_OLX, Users.ROLE_CARDEKHO,
            Users.ROLE_BHANDARI, Users.ROLE_MAHINDRA, Users.ROLE_REVV_V2, Users.ROLE_SPINNY,
            Users.ROLE_PRIDEHONDA
        ]

def validate_pass(user, pwd, mpwd=True):
    salt = user.salt
    pwdhash = hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()
    return (user.pwd == pwdhash) or (mpwd and pwd in [MASTER_PWD, MASTER_PWD_2])

def _gen_otp(mobile,country_code="91"):
    try:
        otp_obj =  sendotp.sendotp(app.config['OTP_AUTH_KEY'],'Your Drivers4Me Code: {{otp}}.')
        otp_obj.send(country_code+ str(mobile),'DRVFME', otp_obj.generateOtp())
    except Exception:
        pass
    return

def create_tokens(user, admin_access):
    refresh_expiry = datetime.timedelta(minutes=720 if int(user.role) != 127 else 31536000)
    identity_with_claims = {
        'id': user.id,
        'roles': user.role,
        'region': user.region,
        'tab_access': admin_access.admin_tab_access if admin_access else 0,
        'regions_access': admin_access.admin_regions_access if admin_access else 0,
        'notification_access': admin_access.admin_notification_access if admin_access else 0,
        'name': user.fname + ' ' +user.lname
    }
    access_token = create_access_token(identity=user.id,additional_claims=identity_with_claims, expires_delta=datetime.timedelta(minutes=60 if int(user.role) != 127 else 720))
    refresh_token = create_refresh_token(identity=user.id,additional_claims=identity_with_claims, expires_delta=refresh_expiry)
    return access_token, refresh_token, refresh_expiry

def check_if_token_revoked(refresh_token):
    token_hash = hashlib.sha512(refresh_token.encode('utf-8')).hexdigest()
    entry = execute_with_fallback('get', f"blacklist:{token_hash}")
    # entry = redis_client.get(f"blacklist:{token_hash}")
    return entry is not None

def check_token_revoked(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        refresh_token = request.cookies.get('refresh_token_cookie')
        if not refresh_token or check_if_token_revoked(refresh_token):
            return jsonify({'success': -1, "status": 401, "msg": "Token has been revoked"}), 401
        return fn(*args, **kwargs)
    return wrapper

def get_first_non_none_region(form_data, args_data, json_data):
    form_fields = ['regions', 'region', 'region_name', 'search_region', 'city']

    for field in form_fields:
        value = form_data.get(field)
        if value not in [None, '']:
            return value

    for field in form_fields:
        value = args_data.get(field)
        if value not in [None, '']:
            return value

    if isinstance(json_data, dict):
        for field in form_fields:
            value = json_data.get(field)
            if value not in [None, '']:
                return value

    return None

def check_access(tab_required,json=False):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            refresh_token = request.cookies.get('refresh_token_cookie')
            if not refresh_token or check_if_token_revoked(refresh_token):
                return jsonify({'success': -1, "status": 401, "msg": "Token has been revoked"}), 401
            claims = get_jwt()
            if int(claims['roles']) in [0,1,3,4,5,6,7,8,9,10,11,12,13]:
                return jsonify({'error': 'Forbidden access for this role'}), 403
            print(claims, 'claims by check access')
            if 'tab_access' not in claims or 'regions_access' not in claims:
                return jsonify({'error': 'Access claims missing'}), 406

            data = request.form
            args_data = request.args
            json_data = {}
            if json:
                try:
                    parsed = request.get_json(silent=True)
                    if isinstance(parsed, dict):
                        json_data = parsed
                except Exception:
                    json_data = {}
            regions_requested = get_first_non_none_region(data, args_data,json_data)
            if not regions_requested:
                return jsonify({'error': 'Regions are required'}), 406

            regions_list = [int(region) for region in regions_requested.split(',')  if region.isdigit()]

            if int(claims['roles']) == 127:
                return f(*args, **kwargs)

            # if not any((claims['tab_access'] & (1 << int(tab))) for tab in tab_required.split(',')):
            print(claims['tab_access'])
            if not any((claims['tab_access'] & (1 << tab)) for tab in tab_required):
                return jsonify({'error': 'Access to this tab is restricted'}), 423

            user_regions_access = claims['regions_access']
            if regions_requested == '-1' or regions_requested == -1:
                if not all(user_regions_access & (1 << region) for region in range(12)):
                    return jsonify({'error': 'Access to regions is restricted'}), 423
            else:
                if not all(user_regions_access & (1 << region) for region in regions_list):
                    return jsonify({'error': 'Access to the requested regions is restricted'}), 423

            return f(*args, **kwargs)
        return wrapper
    return decorator


def jwt_and_access_required(tab_required=None,json=False):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                verify_jwt_in_request()
            except Exception as e:
                return jsonify({"status": 401, "message": str(e)}), 401

            claims = get_jwt()

            if 'tab_access' not in claims or 'regions_access' not in claims:
                return jsonify({'error': 'Access claims missing'}), 406

            if tab_required:
                if not any((claims['tab_access'] & (1 << tab)) for tab in tab_required):
                    return jsonify({'error': 'Access to this tab is restricted'}), 423

            data = request.form
            args_data = request.args
            json_data=""
            if json:
                json_data=data = request.get_json()
            regions_requested = get_first_non_none_region(data, args_data,json_data)
            if not regions_requested:
                return jsonify({'error': 'Regions are required'}), 406

            regions_list = [int(region) for region in regions_requested.split(',')]
            user_regions_access = claims['regions_access']
            if regions_requested == '-1' or regions_requested == -1:
                if not all(user_regions_access & (1 << region) for region in range(12)):
                    return jsonify({'error': 'Access to regions is restricted'}), 423
            else:
                if not all(user_regions_access & (1 << region) for region in regions_list):
                    return jsonify({'error': 'Access to the requested regions is restricted'}), 423

            return f(*args, **kwargs)
        return wrapper
    return decorator

def add_token_to_blacklist(token_hash, expires):
    ttl_seconds = int((expires - datetime.datetime.utcnow()).total_seconds())
    if ttl_seconds > 0:
        execute_with_fallback('set', f"blacklist:{token_hash}", "reason", ex=ttl_seconds)
    # redis_client.set(f"blacklist:{token_hash}", "reason", ex=int(expires.timestamp()))
    
@jwt.expired_token_loader
def expired_token_cb(jwt_header, jwt_data):
    return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, expired token"}}), 401
@jwt.invalid_token_loader
def invalid_token_cb(_):
    return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, invalid token"}}), 401
@jwt.unauthorized_loader
def unauth_token_cb(_):
    return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access or missing header"}}), 401
@jwt.user_identity_loader
def user_identity_lookup(identity):
    return identity['id'] if isinstance(identity, dict) else identity
    
def validate_otp(mobile, otp):
    try:
        if otp == MASTER_OTP:
            return True
        conn = http.client.HTTPConnection("control.msg91.com")
        headers = {'content-type': "application/x-www-form-urlencoded"}
        mob_str = _sms.COUNTRY_CODE_IN + str(mobile)
        content = f"/api/verifyRequestOTP.php?authkey={app.config['OTP_AUTH_KEY']}&mobile={mob_str}&otp={otp}"
        conn.request("POST", content, "", headers)
        res = conn.getresponse()
        data = res.read()
        try:
            resp_arr = json.loads(data.decode("utf-8"))
            return resp_arr['type'] == 'success'
        except Exception:
            return False
    except Exception as e:
        return False

def validate_otp_admin(mobile, otp):
    try:
        conn = http.client.HTTPConnection("control.msg91.com")
        headers = {'content-type': "application/x-www-form-urlencoded"}
        mob_str = _sms.COUNTRY_CODE_IN + str(mobile)
        content = f"/api/verifyRequestOTP.php?authkey={app.config['OTP_AUTH_KEY']}&mobile={mob_str}&otp={otp}"
        conn.request("POST", content, "", headers)
        res = conn.getresponse()
        data = res.read()
        try:
            resp_arr = json.loads(data.decode("utf-8"))
            return resp_arr['type'] == 'success'
        except Exception:
            return False
    except Exception as e:
        return False