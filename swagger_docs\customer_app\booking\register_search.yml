
tags:
  - Booking
summary: Register a search for a booking
description: This API registers a new search based on the user's location and booking details.
consumes:
  - application/x-www-form-urlencoded
produces:
  - application/json
parameters:
  - name: pickup_lat
    in: formData
    type: number
    required: true
    description: Latitude of the pickup location
  - name: pickup_long
    in: formData
    type: number
    required: true
    description: Longitude of the pickup location
  - name: pickup_loc
    in: formData
    type: string
    required: true
    description: Address of the pickup location
  - name: drop_lat
    in: formData
    type: number
    required: false
    description: Latitude of the drop location
  - name: drop_long
    in: formData
    type: number
    required: false
    description: Longitude of the drop location
  - name: drop_loc
    in: formData
    type: string
    required: false
    description: Address of the drop location
  - name: booking_type
    in: formData
    type: integer
    required: true
    description: Type of booking (e.g., roundtrip, oneway, outstation)
  - name: car_type
    in: formData
    type: integer
    required: true
    description: Type of car (e.g., hatchback, sedan, SUV)
  - name: gear_type
    in: formData
    type: integer
    required: true
    description: Gear type (e.g., manual, automatic)
  - name: duration
    in: formData
    type: string
    required: true
    description: "Duration of the trip (format: '1d 2h 30m')"
  - name: pickup_time
    in: formData
    type: string
    required: true
    description: "Start time of the trip (format: 'YYYY-MM-DD HH:MM:SS')"
  - name: is_immediate
    in: formData
    type: boolean
    required: false
    default: false
    description: Flag to indicate if booking is immediate
  - name: region
    in: formData
    type: integer
    required: true
    description: City/region ID
  - name: source
    in: formData
    type: string
    required: false
    description: Source of the request (e.g., web, app)
  - name: ninsurance
    in: formData
    type: integer
    required: false
    description: Number of insurances
  - name: coupon_code
    in: formData
    type: string
    required: false
    description: Applied coupon code
  - name: X-Timezone
    in: header
    type: string
    required: false
    description: "Client timezone (default: Asia/Kolkata)"
responses:
  200:
    description: Estimate generated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Estimate generated successfully
        data:
          type: object
          properties:
            search_id:
              type: string
            night_flag:
              type: integer
            surge_flag:
              type: integer
            estimate:
              type: object
              properties:
                final_price_classic_driver:
                  type: number
                final_price_premium_driver:
                  type: number
                fare_breakdown:
                  type: object
                  properties:
                    base_fare:
                      type: number
                    dist_fare:
                      type: number
                    insurance_fee:
                      type: number
                    night_charge:
                      type: number
                    surge_charge:
                      type: number
                    driver_base_charge:
                      type: number
                    booking_charge:
                      type: number
                    driver_type_surcharge:
                      type: number
                    immediate_surcharge:
                      type: number
                    coupon_discount:
                      type: number
                distance_km:
                  type: number
  400:
    description: Bad Request or Validation Failed
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid duration format
  401:
    description: Unauthorized
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: User restricted or token invalid
  403:
    description: Forbidden
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Account access denied or user flagged
  422:
    description: Unprocessable Entity (Validation Error)
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: array
          items:
            type: string
  500:
    description: Internal Server Error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        message:
          type: string
          example: Error in registering search
