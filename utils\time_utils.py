#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  time_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

"""
Utility functions for datetime and timezone conversions.
"""

# ──────────── Built‑ins ────────────
from datetime import datetime, timedelta, time
from dateutil import parser
import re

# ────────── Third‑party ────────────
import pytz

# ─────────── Internal ─────────────



IST_OFFSET_TIMEDELTA = timedelta(hours=5, minutes=30)

def strfdelta(tdelta, fmt):
    d = {"days": tdelta.days}
    d["hours"], rem = divmod(tdelta.seconds, 3600)
    d["minutes"], d["seconds"] = divmod(rem, 60)
    return fmt.format(**d)


def strfdelta2(tdelta, fmt):
    d = {"days": tdelta.days}
    d["hours"], rem = divmod(tdelta.seconds, 3600)
    d["hours"] += d["days"]*24
    d["minutes"], d["seconds"] = divmod(rem, 60)
    if d["hours"] < 10:
        d["hours"] = '0' + str(d["hours"])
    if d["minutes"] < 10:
        d["minutes"] = '0' + str(d["minutes"])
    if d["seconds"] < 10:
        d["seconds"] = '0' + str(d["seconds"])
    if d["days"] < 0:
        return "00:00:00"
    return fmt.format(**d)


def get_dt_ist(dt, tm):
    start_time = datetime(year=dt.year, month=dt.month,
                          day=dt.day, hour=tm.hour,
                          minute=tm.minute, second=tm.second)
    start_time_ist = start_time + IST_OFFSET_TIMEDELTA
    return start_time_ist


def convert_time_to_local(time_str: str, tz: str = 'Asia/Kolkata') -> str:
    """
    Converts a UTC time string 'HH:MM:SS' to the specified local timezone.

    Args:
        time_str (str): Time string in 'HH:MM:SS' format (assumed UTC)
        tz (str): Target timezone string (e.g., 'Asia/Kolkata', 'Asia/Dubai')

    Returns:
        str: Time string in local timezone, formatted as 'HH:MM:SS'
    """
    try:
        utc_time = datetime.strptime(time_str, "%H:%M:%S").time()
    except ValueError:
        raise ValueError("Invalid time format, expected HH:MM:SS")

    today = datetime.utcnow().date()
    utc_datetime = datetime.combine(today, utc_time)
    utc_datetime = pytz.utc.localize(utc_datetime)

    try:
        target_tz = pytz.timezone(tz)
    except pytz.UnknownTimeZoneError:
        raise ValueError(f"Unknown timezone: {tz}")

    local_dt = utc_datetime.astimezone(target_tz)
    return local_dt.strftime("%H:%M:%S")


def convert_to_local_time(dt, tz='Asia/Kolkata'):
    """
    Converts a datetime (naive or aware, or a string) to the given timezone.

    Args:
        dt (datetime or str): Input datetime (can be naive or UTC-aware, or a string)
        tz (str): Target timezone (e.g., 'Asia/Dubai', 'Asia/Kolkata')

    Returns:
        datetime: Datetime converted to the target timezone
    """
    if dt is None:
        return None

    if isinstance(dt, str):
        dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")

    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)

    try:
        target_tz = pytz.timezone(tz)
    except pytz.UnknownTimeZoneError:
        raise ValueError(f"Unknown timezone: {tz}")

    return dt.astimezone(target_tz)


def convert_ist_to_utc(time_str, tz='Asia/Kolkata'):

    if not time_str:
        return None
    local_timezone = pytz.timezone(tz)

    today_date = datetime.now().strftime('%Y-%m-%d')
    datetime_str = f"{today_date} {time_str}"

    local_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    localized_datetime = local_timezone.localize(local_datetime)
    utc_datetime = localized_datetime.astimezone(pytz.utc)

    utc_time = utc_datetime.strftime('%H:%M:%S')

    return utc_time


def convert_utc_to_ist(time_str, tz='Asia/Kolkata'):

    if not time_str:
        return None
    utc_timezone = pytz.utc
    local_timezone = pytz.timezone(tz)

    today_date = datetime.now().strftime('%Y-%m-%d')
    datetime_str = f"{today_date} {time_str}"

    utc_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    localized_utc_datetime = utc_timezone.localize(utc_datetime)
    ist_datetime = localized_utc_datetime.astimezone(local_timezone)

    ist_time = ist_datetime.strftime('%H:%M:%S')
    return ist_time


def convert_to_utc(date_str: str, time_str: str, tz='Asia/Kolkata'):
    """
    Convert a local date and time in any timezone to UTC date and time.

    Args:
        date_str (str): Date string in 'YYYY-MM-DD' or 'DD/MM/YYYY' format
        time_str (str): Time string in 'HH:MM:SS' format
        tz (str): Local timezone string (e.g., 'Asia/Kolkata', 'America/New_York')

    Returns:
        (str, str): Tuple of (UTC date string, UTC time string)
    """
    datetime_str = f"{date_str} {time_str}"
    utc_datetime = convert_datetime_to_utc(datetime_str, tz)

    if utc_datetime is None:
        raise ValueError("Could not parse or convert the datetime to UTC.")

    utc_date = utc_datetime.strftime('%Y-%m-%d')
    utc_time = utc_datetime.strftime('%H:%M:%S')

    return utc_date, utc_time


def combine_and_convert_to_local(date_val, time_val, tz='Asia/Kolkata'):
    if isinstance(time_val, datetime):
        time_val = time_val.time()
    combined = datetime.combine(date_val, time_val)
    return convert_to_local_time(combined, tz)


def split_date_time(dt):
    return dt.date(), dt.time()


def convert_datetime_to_utc(date_str: str, tz='Asia/Kolkata'):
    """
    Converts a datetime string (with or without offset) to a UTC datetime object.

    Supports ISO strings with or without offset and multiple formats.

    Returns:
        datetime (UTC) or None
    """
    if not date_str:
        return None
    if tz not in pytz.all_timezones:
        raise ValueError(f"Unknown timezone: {tz}")

    try:
        dt = parser.parse(date_str)
        if dt.tzinfo:
            return dt.astimezone(pytz.utc)
    except Exception:
        pass

    known_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%d/%m/%Y %H:%M:%S",
        "%d/%m/%Y %I:%M %p",
        "%d/%m/%Y %I:%M %p %z",
    ]

    for fmt in known_formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            if '%z' in fmt:
                return dt.astimezone(pytz.utc)
            else:
                local_tz = pytz.timezone(tz)
                localized = local_tz.localize(dt)
                return localized.astimezone(pytz.utc)
        except Exception:
            continue

    raise ValueError("Date format not recognized or unsupported.")


def convert_time_to_utc(time_str: str, tz='Asia/Kolkata') -> str:
    """
    Converts a time string in 'HH:MM:SS' from the given timezone to UTC.
    """
    try:
        local_time = datetime.strptime(time_str, "%H:%M:%S").time()
    except ValueError:
        raise ValueError("Invalid time format, expected HH:MM:SS")

    today = datetime.now().date()
    local_datetime = datetime.combine(today, local_time)

    try:
        local_tz = pytz.timezone(tz)
    except pytz.UnknownTimeZoneError:
        raise ValueError(f"Unknown timezone: {tz}")

    localized_dt = local_tz.localize(local_datetime)
    utc_dt = localized_dt.astimezone(pytz.utc)

    return utc_dt.strftime("%H:%M:%S")


def get_trip_duration(start_time: datetime, end_time: datetime) -> str:
    if not start_time or not end_time:
        return ''

    try:
        duration_seconds = abs((end_time - start_time).total_seconds())

        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)

        return f"{hours} hr {minutes} min"
    except ValueError:
        return "N/A"


def convert_timedelta(duration):
    try:
        days, seconds = duration.days, duration.seconds
    except AttributeError:
        days = 0
        seconds = duration.seconds
    hours = days * 24 + seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = (seconds % 60)
    if hours < 10:
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    if seconds < 10:
        seconds = '0' + str(seconds)
    return str(hours), str(minutes), str(seconds)


def ist_to_gmt(ist_time: str) -> str:
    dt = pytz.timezone("Asia/Kolkata").localize(datetime.strptime(ist_time, "%Y-%m-%d %H:%M:%S"))
    return dt.astimezone(pytz.timezone("GMT")).strftime("%Y-%m-%d %H:%M:%S")


def convert_utc_to_local_marker_str(utc_dt, tz):
    if not utc_dt:
        return None

    ist_dt = convert_to_local_time(utc_dt, tz)
    return ist_dt.strftime('%d/%m/%Y %I:%M %p')

def parse_duration_to_day_and_time(duration_str):
    match = re.match(r"(?:(\d+)d)?\s*(?:(\d+)h)?\s*(?:(\d+)m)?", duration_str.strip())
    if not match:
        raise ValueError("Invalid duration format")

    days = int(match.group(1) or 0)
    hours = int(match.group(2) or 0)
    minutes = int(match.group(3) or 0)

    # Return as (days, time)
    time_part = time(hour=hours, minute=minutes)
    return days, time_part