tags:
  - Login_admin
summary: Admin OTP Validation
description: >
  This endpoint allows admins to validate a One-Time Password (OTP) for login. Upon successful OTP validation, the response includes JWT tokens and user details.
parameters:
  - in: formData
    name: mobile
    type: string
    required: true
    description: <PERSON><PERSON>'s mobile number used for OTP validation.
    example: "9876543210"
  - in: formData
    name: otp
    type: string
    required: true
    description: One-Time Password (OTP) sent to the admin's mobile.
    example: "123456"
responses:
  200:
    description: OTP successfully validated. Returns access and refresh tokens along with user details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: OTP validated successfully
        data:
          type: object
          properties:
            user_fname:
              type: string
              example: "John"
            user_mobile:
              type: string
              example: "9876543210"
            user_email:
              type: string
              example: "<EMAIL>"
            user_lname:
              type: string
              example: "Doe"
            user_region:
              type: string
              example: "Region 1"
            tabs:
              type: string
              example: "0,1,2,3,4"
            regions:
              type: string
              example: "0,1,2,3"
            notification:
              type: string
              example: "0,1,2"
            id:
              type: integer
              example: 1001
            code:
              type: string
              example: "eyJhbGciOiJIUzI1NiIsInR..."
            role:
              type: integer
              example: 1
  400:
    description: Bad request due to missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Invalid parameters
  401:
    description: Unauthorized access or invalid OTP.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Invalid OTP
  403:
    description: Forbidden access for users with invalid roles or no admin access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        error:
          type: string
          example: Forbidden access for this role
  422:
    description: Validation error in request parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid input
        data:
          type: object
          properties:
            error:
              type: array
              items:
                type: string
              example: ["mobile: field required", "otp: invalid format"]
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
