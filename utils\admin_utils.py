#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mitra


from flask import  jsonify
from flask_jwt_extended import get_jwt_identity
from functools import wraps

from models.models import Users, db

checkadmin =[*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]
# check_op_team =[*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_OPTEAM]


BIT_0_TO_14 = 16383
BIT_1_TO_15 = 65534
BIT_1_TO_6 = 126
BIT_0_TO_19 = 1048575

class Tabs:
    DRIVERS = 1
    CUSTOMERS = 2
    BOOKINGS = 3
    CREATE_BOOKINGS = 4
    DUES_AND_CREDIT = 5
    DRIVER_REGISTER = 6
    PRICING = 7
    COUPONS = 8
    UTILITIES = 9
    ESTIMATE = 10
    ANALYTICS = 11
    AFFILIATE = 12
    BOOKINGS_AFFILIATE = 13
    AFFILIATE_WALLET = 14
    ANALYTICS_B2B = 15
    

    
    
