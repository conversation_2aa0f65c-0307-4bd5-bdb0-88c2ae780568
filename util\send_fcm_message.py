import sys
sys.path.append("/app/")
import json, requests
import configparser
from datetime import datetime, timedelta

D4M_UTIL_PATH = "/app/util/"
key = "key=AAAAlB1ceVI:APA91bFkzi3JpjvESiEtSybTlixwg0LpGJ0gAPtlq6JzKY5SxdD5GPeNnJ2T21b7qwUbmE3H1pQY8X5NvbXcTO3-GoVIbIGQCgQYc2o2Hwl4hHSjWkEZ0rdTZb1jfykVYdQwVDFJiGrL"

def send_fcm_msg_android(token, title, smalltext, bigtext, pic="", url=""):
    url = "https://fcm.googleapis.com/fcm/send"
    headers = {'Authorization': key, 'content-type': "application/json" }
    body = {
        "to": token,
        "data":
        {
            "title": title,
            "smallText": smalltext,
            "bigText": bigtext,
            "pic" : pic,
            "click": 1,
            "url": url,
            "pending": 1
        }
    }
    try:
        response = requests.post(url, json=body, headers=headers)
        if response.status_code != 200:
            raise Exception(response.content)
    except Exception as e:
        print(e)
    return


def send_fcm_msg_ios(token, title, smalltext, bigtext, pic=""):
    url = "https://fcm.googleapis.com/fcm/send"
    headers = {'Authorization': key, 'content-type': "application/json" }
    body = {
        "to": token,
        "content_available" : True,
        "mutable_content": True,
        "priority" : "high",
        "notification":
        {
            "body": bigtext,
            "subtitle" : smalltext,
            "title" : title,
        },
        "data":
        {
            "attachment" : pic,
            "media_type":"image"

        }
    }
    try:
        response = requests.post(url, json=body, headers=headers)
        if response.status_code != 200:
            raise Exception(response.content)
    except Exception as e:
        print(e)
    return

def send_fcm_msg_from_config(cfg, sched_allowed=True):
    config_parser = configparser.ConfigParser()
    config_parser.read(cfg, encoding='utf8')
    mode_arr = json.loads(config_parser.get('general', 'mode'))
    token_arr = json.loads(config_parser.get('general', 'token'))
    title = config_parser.get('general', 'title')
    smallText = config_parser.get('general', 'smallText')
    bigText = config_parser.get('general', 'bigText')
    pic = config_parser.get('general', 'pic')
    sched = config_parser.get('general', 'sched')

    assert(len(token_arr) == len(mode_arr))

    if sched_allowed and sched and config_parser.has_section("sched"):
        print("Only sending if around scheduled time")
        date_today_ist = datetime.utcnow() + timedelta(seconds=330*60)
        if config_parser.has_section("date"):
            sched_date_arr = json.loads(config_parser.get('sched', 'date'))
            if str(date_today_ist.date()) not in sched_date_arr:
                print("Schedule does not have this date")
                return False
        else:
            sched_day = json.loads(config_parser.get('sched', 'day_of_week'))
            if date_today_ist.weekday() not in sched_day:
                print("Schedule does not have this date")
                return False
        sched_time_str = config_parser.get('sched', 'time_ist')
        sched_time =  datetime.strptime(
                        str(date_today_ist.date()) + " " + sched_time_str,
                        "%Y-%m-%d %H:%M:%S")
        delta = abs(sched_time - date_today_ist).total_seconds()
        if delta > 10 * 60:
            print("Total time delta of", str(delta), "seconds, beyond threshold")
            return False
        print("Time for scheduling matched at", date_today_ist)
    for i in range(len(token_arr)):
        mode = mode_arr[i]
        token = token_arr[i]
        if mode.lower() == "android":
            send_fcm_msg_android(token, title, smallText, bigText, pic, pic)
        else:
            send_fcm_msg_ios(token, title, smallText, bigText, pic)
    return True

if __name__ == '__main__':
    if len(sys.argv) < 2:
        cfg = D4M_UTIL_PATH + 'configs/fcm_config.ini'
    else:
        cfg = sys.argv[1]
    send_fcm_msg_from_config(cfg)


