from datetime import datetime
import requests
from fuzzywuzzy import fuzz
from models.models import db, Drivers, Users, DriverInfo, DriverGLDrivLicDetails, DriverGLBankDetails, DriverApprovalLog, DriverBank
from utils.driver.driver_utils import dl_fetch_url, face_match_url, driver_pic_base_url, bank_verify_url
    
from utils.s3_utils import upload_pic_base64,pic_base_url
from schemas.admin_dashboard.driver_schemas import DriverDLVerifyPayload, DriverDLReverifySchema,BankDocVerifyPayload
from flask import current_app as app


def match_details(doc_type,driver_id,name,pic,dob,lic_exp,ph_status=0,ph_score=0.0):
    '''
    Match details based on the document type.
    Args:
        doc_type (str): Type of document.
        driver_id (int): ID of the driver.
        name (str): Name on the document.
        pic (str): Picture on the document.
        dob (date): Date of birth.
        lic_exp (date): License expiry date.
        ph_status (int, optional): Photo status. Defaults to 0. 
        ph_score (float, optional): Photo match score. Defaults to 0.0.

    Returns:
        tuple: Status and details of the match.
    '''
    face_match_score = 0.0
    dob_match = False
    name_match = False
    photo_match=False
    lic_exp_match=False
    driver_current_info=db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).first()
    driver_user = db.session.query(Users).join(Drivers, Users.id == Drivers.user).filter(Drivers.id == driver_id).first()
    if not driver_current_info or not driver_user:
        return -1, "Driver information not found"
    db_full_name = f"{driver_user.fname.strip()} {driver_user.lname.strip()}".lower()
    name_match_score = fuzz.token_sort_ratio(db_full_name, name.lower())
        # Consider the name matched if the similarity score is above a threshold
    if name_match_score >= 80:  # Assuming 80% is the threshold for a match
        name_match = True
    else:
        name_match = False
    if doc_type.lower() in ["dl", "aadhar","pan"] and dob:
        if driver_current_info.dob == dob:
            dob_match = True
    if doc_type.lower() == "dl":
        if driver_current_info.license_exp == lic_exp:
            lic_exp_match = True
    if doc_type.lower() in ["dl", "aadhar"]:
        if ph_status:
            photo_match=True
            face_match_score=ph_score
        else:
            if driver_current_info.pic and pic:
                url = face_match_url
                payload = {
                "file_1_url":pic_base_url + driver_current_info.pic,
                "file_2_url": driver_pic_base_url+pic,
                "consent": "Y"
                }
                headers = {
                    "X-Auth-Type": "API-Key",
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "X-API-Key": app.config['GRID_LINES_KEY']
                }

                try:
                    response = requests.post(url, json=payload, headers=headers)
                    response_data = response.json()

                    if response.status_code == 200 and response_data.get('data', {}).get('code') == "1000":
                        face_match_score = response_data.get('data', {}).get('confidence', 0.0)
                        if face_match_score >= 0.75:
                            photo_match=True
                    else:
                        return -2, f"Unexpected error: {response.text}"
                except requests.RequestException as e:
                    return -3, f"Request failed: {str(e)}"
            # Final decision based on all matches

    if doc_type.lower() == "dl":
        if name_match and dob_match and lic_exp_match and face_match_score >= 0.75:  # Assuming 0.8 (80%) is the threshold for face match
            return 1, {"name_match": name_match, "dob_match": dob_match,"lic_exp_match":lic_exp_match,"photo_match":photo_match,"face_match_score": face_match_score}
        else:
            return 0, {"name_match": name_match, "dob_match": dob_match,"lic_exp_match":lic_exp_match,"photo_match":photo_match, "face_match_score": face_match_score}
    if doc_type.lower() == "aadhar":
            if name_match and dob_match and face_match_score >= 0.75:  # Assuming 0.8 (80%) is the threshold for face match
                return 1, {"name_match": name_match, "dob_match": dob_match,"photo_match":photo_match,"face_match_score": face_match_score}
            else:
                return 0, {"name_match": name_match, "dob_match": dob_match,"photo_match":photo_match, "face_match_score": face_match_score}
    if doc_type.lower() in ["bank", "voter"]:
            if name_match:  # Assuming 0.8 (80%) is the threshold for face match
                return 1, {"name_match": name_match}
            else:
                return 0, {"name_match": name_match}
    if doc_type.lower() == "pan":
            if name_match and dob_match:  # Assuming 0.8 (80%) is the threshold for face match
                return 1, {"name_match": name_match, "dob_match": dob_match}
            else:
                return 0, {"name_match": name_match, "dob_match": dob_match}
            
def add_driver_log_func(driver, approval, editedby, remark, changes, change_from, change_to,verf=0):
    try:
        if verf==1:
            try:
                new_log = DriverApprovalLog(
                    driver=int(driver),
                    changes=changes,
                    approval=int(approval),
                    editedby=editedby.strip(),
                    change_from=str(change_from).strip(),
                    change_to=str(change_to).strip(),
                    remark=remark.strip()
                    )
                db.session.add(new_log)
                db.session.commit()
                print('Logs added successfully')
            except Exception as e:
                print(f'Error: {str(e)}')
            return
        if not all([driver, approval, editedby, remark, changes, change_from, change_to]):
            print('All fields are required!!!')
            return
        changes_list = changes
        change_from_list = change_from
        change_to_list = change_to

        if len(changes_list) != len(change_from_list) or len(changes_list) != len(change_to_list):
            print('Check lengths of changes, change_from, and change_to are same')
            return

        for change, change_fr, change_to in zip(changes_list, change_from_list, change_to_list):
            new_log = DriverApprovalLog(
                driver=int(driver),
                changes=change.strip(),
                approval=int(approval),
                editedby=editedby.strip(),
                change_from=str(change_fr).strip(),
                change_to=str(change_to).strip(),
                remark=remark.strip()
            )
            db.session.add(new_log)

        db.session.commit()

    except Exception as e:
        app.logger.error(f"[add_driver_log_func] Unexpected error: {str(e)}")
        raise e

     
def driver_dl_verify_service(payload: DriverDLVerifyPayload, user_name: str):
    """
    Verify driver’s driving license using DL number and date of birth.
    Args:
        payload (DriverDLVerifyPayload): Payload containing driver ID, DL number, and DOB.
        user_name (str): Name of the user performing the verification.
    Returns:
        dict: Response containing status, message, and HTTP code.
    """
    driver_id = payload['driver_id']
    dl_no = payload['dl_no']
    dob = payload['dob']

    driver = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    if not driver:
        return {"success": -1, "status": 404, "message": "Driver not found"}

    driver_info = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).first()
    if not driver_info:
        return {"success": -9,"status": 404, "message": "Driver Previous Details Not Found"}

    existing_record = db.session.query(DriverGLDrivLicDetails).filter(
        DriverGLDrivLicDetails.driver_id == driver_id).first()
    if existing_record:
        return {"success": -7,"status": 200, "message": "Driving Licence Already Fetched"}

    headers = {
        "X-Auth-Type": "API-Key",
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-API-Key": app.config['GRID_LINES_KEY']
    }
    payload_req = {
        "driving_license_number": dl_no,
        "date_of_birth": str(dob),
        "consent": "Y"
    }

    try:
        response = requests.post(dl_fetch_url, json=payload_req, headers=headers)
        response_data = response.json()

        if response.status_code == 200:
            code = response_data.get('data', {}).get('code')
            if code == "1000":
                data = response_data['data']['driving_license_data']
                validity = data.get('validity', {}).get('non_transport', {})
                rto = data.get('rto_details', {})

                name = data['name']
                lic_exp = datetime.strptime(validity.get('expiry_date'), "%Y-%m-%d").date() if validity.get('expiry_date') else None
                lic_issue = datetime.strptime(validity.get('issue_date'), "%Y-%m-%d").date() if validity.get('issue_date') else None
                pic = upload_pic_base64(data["photo_base64"]) if data.get("photo_base64") else None
                dob_parsed = datetime.strptime(data.get("date_of_birth"), "%Y-%m-%d").date() if data.get("date_of_birth") else None

                data.pop("photo_base64", None)

                new_record = DriverGLDrivLicDetails(
                    driver_id=driver_id,
                    dl_no=data['document_id'],
                    name=name,
                    driver_pic=pic,
                    dob=dob_parsed,
                    addr=data.get("address"),
                    pincode=data.get("pincode"),
                    license_exp=lic_exp,
                    state=rto.get('state'),
                    license_issue=lic_issue,
                    name_status=False,
                    photo_status=False,
                    photo_score=0.0,
                    dob_status=False,
                    lic_exp_status=False,
                    json_dump=data
                )
                db.session.add(new_record)

                status, details = match_details("dl", driver_id, name, pic, dob_parsed, lic_exp)
                if status in [0, 1]:
                    new_record.name_status = details["name_match"]
                    new_record.photo_status = details["photo_match"]
                    new_record.photo_score = details["face_match_score"]
                    new_record.dob_status = details["dob_match"]
                    new_record.lic_exp_status = details["lic_exp_match"]

                    verification_status = DriverInfo.DOC_VERIFIED if status == 1 else DriverInfo.DOC_DISPUTED
                    message = "Driving Licence Verified" if status == 1 else "Driving Licence Verified but Disputed"
                    add_driver_log_func(driver_id, driver.approved, user_name, "dl_doc_verify", "dl_doc_verify",
                                        str(driver_info.license_verified), str(verification_status), 1)
                    driver_info.license_verified = verification_status
                else:
                    driver_info.license_verified = DriverInfo.DOC_DISPUTED
                    db.session.commit()
                    add_driver_log_func(driver_id, driver.approved, user_name, "dl_doc fetched but failed to verify",
                                        "dl_doc_verify", str(driver_info.license_verified), str(DriverInfo.DOC_DISPUTED), 1)
                    return {"success": 10, "message": "Driving Licence Fetched but failed to Verify","status": 200}

                db.session.commit()
                return {"success": status,"status": 200, "message": message, "data": {"details": details}}

            elif code == "1001":
                add_driver_log_func(driver_id, driver.approved, user_name, "dl_doc_verify", "dl_doc_verify",
                                    str(driver_info.license_verified), str(DriverInfo.DOC_NOT_FOUND), 1)
                driver_info.license_verified = DriverInfo.DOC_NOT_FOUND
                db.session.commit()
                return {"success": -3,"status": 200, "message": "Driving Licence Not Exists, Please Check DOB"}

            return {"success": -6, "message": str(response_data),"status": 200}

        elif response.status_code == 400:
            error_code = response_data.get('error', {}).get('code')
            if error_code == "INVALID_DRIVING_LICENSE":
                add_driver_log_func(driver_id, driver.approved, user_name, "dl_doc_verify", "dl_doc_verify",
                                    str(driver_info.license_verified), str(DriverInfo.DOC_NOT_FOUND), 1)
                driver_info.license_verified = DriverInfo.DOC_NOT_FOUND
                db.session.commit()
                return {"success": -1, "message": "Invalid Driving Licence Number","status": 400}
            return {"success": -4, "message": "Bad request", "data": {"error": error_code},"status": 400}

        return {"success": -5, "message": f"Unexpected error {response.text} ", "data":{"error": response.text},"status": response.status_code}

    except requests.RequestException as e:
        return {"success": -99,"message": f"Request failed due to error {str(e)}","data":{"error": str(e)},"status": 500 }
    
    
    
def driver_dl_reverify_service(payload: DriverDLReverifySchema, admin_name: str):
    """
    Reverify driver’s driving license with optional remarks and force flag.
    Args:
        payload (DriverDLReverifySchema): Payload containing driver ID, optional remarks, and force flag.
        admin_name (str): Name of the admin performing the action.
    Returns:
        dict: Response containing status, message, and HTTP code.
    """
    driver_id = payload['driver_id']
    remarks = payload.get('remarks')
    force_verify = int(payload.get('force_verify', 0))

    drivers = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    if not drivers:
        return {"success": 0, "message": "Driver not found","status": 404}

    driver_info = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).first()
    if not driver_info:
        return {"success": 0, "message": "Driver info not found","status": 404}

    driver_grid_info = db.session.query(DriverGLDrivLicDetails).filter(
        DriverGLDrivLicDetails.driver_id == driver_id
    ).first()

    if force_verify:
        driver_grid_info.name_status = True
        driver_grid_info.photo_status = True
        driver_grid_info.photo_score = 0.81
        driver_grid_info.dob_status = True
        driver_grid_info.lic_exp_status = True

        add_driver_log_func(
            driver_id, drivers.approved, admin_name, remarks, "dl_doc_verify",
            str(driver_info.license_verified), str(DriverInfo.DOC_VERIFIED), 1
        )
        driver_info.license_verified = DriverInfo.DOC_VERIFIED

        try:
            db.session.commit()
        except Exception as db_error:
            db.session.rollback()
            return {
                "status": 500,
                "success": -99 ,
                "message": "Database commit failed.",
                "data": {"error": str(db_error)}
            }, 500

        return {"success": 3, "message": "Driving Licence Forced Verified","status": 200}

    try:
        status, details = match_details(
            "dl", driver_id, driver_grid_info.name, driver_grid_info.driver_pic,
            driver_grid_info.dob, driver_grid_info.license_exp,
            driver_grid_info.photo_status, driver_grid_info.photo_score
        )

        if status == 1:
            driver_grid_info.name_status = details["name_match"]
            driver_grid_info.photo_status = details["photo_match"]
            driver_grid_info.photo_score = details["face_match_score"]
            driver_grid_info.dob_status = details["dob_match"]
            driver_grid_info.lic_exp_status = details["lic_exp_match"]

            add_driver_log_func(
                driver_id, drivers.approved, admin_name, "dl_doc_verify", "dl_doc_verify",
                str(driver_info.license_verified), str(DriverInfo.DOC_VERIFIED), 1
            )
            driver_info.license_verified = DriverInfo.DOC_VERIFIED
            message = "Driving Licence Verified"

        elif status == 0:
            driver_grid_info.name_status = details["name_match"]
            driver_grid_info.photo_status = details["photo_match"]
            driver_grid_info.photo_score = details["face_match_score"]
            driver_grid_info.dob_status = details["dob_match"]
            driver_grid_info.lic_exp_status = details["lic_exp_match"]

            add_driver_log_func(
                driver_id, drivers.approved, admin_name, "dl_doc_verify", "dl_doc_verify",
                str(driver_info.license_verified), str(DriverInfo.DOC_DISPUTED), 1
            )
            driver_info.license_verified = DriverInfo.DOC_DISPUTED
            message = "Driving Licence Still Disputed"

        else:
            return {"success": -1, "message": "Driving Licence Failed to Reverify","status": 400}

        try:
            db.session.commit()
        except Exception as db_error:
            db.session.rollback()
            return {
                "success": -2,
                "message": "Database commit failed.",
                "data": {"error": str(db_error)},
                "status": 500
            }

        return {
            "success": status,
            "message": message,
            "data": {"details": details},
            "status": 200
        }

    except Exception as e:
        app.logger.error(f"[driver_dl_reverify_service] Unexpected error: {str(e)}")
        return {"success": -99, "message": "Internal error", "data": {"error": str(e)},"status": 500 }


def verify_driver_bank_doc(payload: BankDocVerifyPayload, user_name: str):
    """
    Verify driver’s bank account using account number and IFSC code.
    Args:
        payload (BankDocVerifyPayload): Payload containing driver ID, account number, and IFSC code.
        user_name (str): Name of the user performing the verification.
    Returns:
        dict: Response containing status, message, and HTTP code.
    """
    acc_no = payload["acc_no"]
    ifsc = payload["ifsc"]
    driver_id = payload["driver_id"]

    drivers = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    if not drivers:
        return {"success": -1, "message": "Driver not found","status": 404}

    bank_info = db.session.query(DriverBank).filter(DriverBank.driver_id == driver_id).first()
    if not bank_info:
        return {"success": -9, "message": "Driver PrevDetails Not Found", "status": 404}

    if db.session.query(DriverGLBankDetails).filter_by(driver_id=driver_id).first():
        return {"success": -7, "message": "Bank details already fetched", "status": 200}

    headers = {
        "X-Auth-Type": "API-Key",
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-API-Key": app.config["GRID_LINES_KEY"]
    }
    payload_api = {
        "account_number": acc_no,
        "ifsc": ifsc,
        "consent": "Y"
    }

    try:
        response = requests.post(bank_verify_url, json=payload_api, headers=headers)
        res_json = response.json()

        if response.status_code != 200:
            error_fields = res_json.get("error", {}).get("metadata", {}).get("fields", [])
            if error_fields:
                message = error_fields[0].get("message", "Unknown error")
            else:
                message = res_json.get("error", {}).get("message", "Unknown error")
            return {"success": -1, "message": message, "data": {"details": response.text}, "status": response.status_code}

        code = res_json.get('data', {}).get('code')
        if code != "1000":
            msg_map = {
                "1001": "Provided invalid Account Number.",
                "1002": "Provided invalid IFSC.",
                "1003": "Account is blocked.",
                "1004": "Account is closed.",
                "1006": "Source bank declined",
                "1007": "IMPS mode failed",
                "1008": "Bank failed",
                "1009": "Verification attempt failed",
                "1010": "Bank offline",
                "1011": "NPCI unavailable",
                "1012": "Invalid NRE Account"
            }
            msg = msg_map.get(code, "Unknown error from Gridlines")
            bank_info.bank_verified = DriverBank.DOC_NOT_FOUND
            add_driver_log_func(driver_id, drivers.approved, user_name, "bank_doc_verify", "bank_doc_verify", str(bank_info.bank_verified), str(DriverBank.DOC_NOT_FOUND), 1)
            db.session.commit()
            return {"success": 2, "message": msg, "status": 200}

        # Successful verification
        data = res_json["data"]["bank_account_data"]
        name = data.get("name")
        new_entry = DriverGLBankDetails(
            driver_id=driver_id,
            name=name,
            acc_no=acc_no,
            ifsc=ifsc,
            bank_name=data.get("bank_name"),
            district=data.get("city"),
            bank_branch=data.get("branch"),
            json_dump=data
        )

        db.session.add(new_entry)

        status, details = match_details("bank", driver_id, name, None, None, None)
        if status == 1:
            new_entry.name_status = details["name_match"]
            bank_info.bank_verified = DriverBank.DOC_VERIFIED
            message = "Bank Details Verified"
        elif status == 0:
            new_entry.name_status = details["name_match"]
            bank_info.bank_verified = DriverBank.DOC_DISPUTED
            message = "Bank Details Disputed"
        else:
            bank_info.bank_verified = DriverBank.DOC_DISPUTED
            add_driver_log_func(driver_id, drivers.approved, user_name, "bank_doc_verify_failed", "bank_doc_verify", str(bank_info.bank_verified), str(DriverBank.DOC_DISPUTED), 1)
            db.session.commit()
            return {"success": 10, "message": "Fetched but failed to verify", "status": 200}

        add_driver_log_func(driver_id, drivers.approved, user_name, "bank_doc_verify", "bank_doc_verify", str(bank_info.bank_verified), str(bank_info.bank_verified), 1)
        db.session.commit()
        return {"success": status, "message": message, "data":{"details": details}, "status": 200}

    except requests.RequestException as e:
        app.logger.error(f"[verify_driver_bank_doc] Request failed due to error {str(e)}")
        return {"success": -99, "message": f"Request failed error {str(e)}", "status": 500}