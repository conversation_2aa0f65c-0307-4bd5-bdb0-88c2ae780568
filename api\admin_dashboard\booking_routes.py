from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from sqlalchemy.orm import sessionmaker
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from schemas.admin_dashboard.booking_schemas import AdminCancelBookingRequest

from utils.response_utils import standard_response
from models.models import db

admin_booking = Blueprint('admin_booking', __name__)

# @admin_booking.route('/api/admin/cancel/new', methods=['POST'])
# @jwt_required()
# @check_access(tab_required=[Tabs.BOOKINGS, Tabs.BOOKINGS_AFFILIATE])
# def admin_cancel_booking():
#     try:
#         data = AdminCancelBookingRequest(**request.form)
#     except Exception as e:
#         return jsonify(standard_response(
#             success=-1,
#             status=422,
#             message="Invalid input",
#             response_status="error"
#         )), 422

#     current_user = get_jwt_identity()
#     claims = get_jwt()

#     engine = db.engine.execution_options(timeout=30)
#     Session = sessionmaker(bind=engine)
#     session = Session()

#     try:
#         with session.begin(), session.begin_nested():
#             response = cancel_booking_admin(session, current_user, claims, data.dict())
#         return jsonify(standard_response(
#             success=response['success'],
#             status=response['status'],
#             message=response['message'],
#             response_status="success" if response['success'] == 1 else "error"
#         )), response['status']
#     except Exception as e:
#         session.rollback()
#         return jsonify({'success': -3, 'message': 'Internal Server Error', 'error': str(e)}), 500