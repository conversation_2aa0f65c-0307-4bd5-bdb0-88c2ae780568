
tags:
  - Login_admin
summary: Validate OTP for Admin Password Change
description: >
  This endpoint validates the OTP submitted by the admin before allowing a password change.
consumes:
  - multipart/form-data
parameters:
  - name: mobile
    in: formData
    required: true
    type: string
    description: Mobile number of the admin.
  - name: otp
    in: formData
    required: true
    type: string
    description: OTP received by the admin.
responses:
  200:
    description: OTP validated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Otp validated successfully
        response_status:
          type: string
          example: success
  422:
    description: Invalid input data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 422
        message:
          type: string
          example: Invalid input
        response_status:
          type: string
          example: error
        data:
          type: object
          properties:
            error:
              type: array
              items:
                type: object
  400:
    description: OTP validation failed.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        status:
          type: integer
          example: 400
        message:
          type: string
          example: OTP does not match or is expired
        response_status:
          type: string
          example: error
  500:
    description: Unexpected server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        status:
          type: integer
          example: 500
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
        response_status:
          type: string
          example: error
