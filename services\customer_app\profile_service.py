#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra# === Standard Library ===

# === Standard Library ===

import http.client
from datetime import timedelta,datetime
from flask import current_app as app
from flask_jwt_extended import create_access_token
import base64
import os
import json
import re, requests
import hashlib

# === Third-Party Libraries ===

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

# === Application (Internal) Imports ===

from db_config import db
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from utils.auth.login_utils import create_token
from utils.response_utils import standard_response
from utils.profile.profile_utils import account_enabled,get_region_info
from utils.auth.login_params import ExpiryTime,TokenValidity
from models.models import Users, UserToken, AdminUserLog, Drivers
from api.customer_app.profile_routes import EmergencyContactSchema
from urllib.parse import urlencode
from urllib.parse import quote
from config import BaseConfig


def handle_add_emergency_contact(data: EmergencyContactSchema, user_id: int) -> dict:
    # Fetch user
    user = db.session.get(Users, user_id)
    if not user:
        return standard_response(
            success=-2,
            status=404,
            message="User not found."
        )

    country_code = data.country_code
    contact_number = data.contact_number

    # Check existing contact
    if (user.emergency_contact_country_code == country_code and
            user.emergency_contact_number == contact_number):
        return standard_response(
            success=-6,
            status=409,
            message="This emergency contact number is already added."
        )

    # Update fields
    user.emergency_contact_country_code = country_code
    user.emergency_contact_number = contact_number

    try:
        db.session.commit()
        return standard_response(
            success=1,
            status=200,
            message="Emergency contact added successfully."
        )
    except Exception:
        db.session.rollback()
        return standard_response(
            success=-9,
            status=500,
            message="An unexpected error occurred. Please try again later."
        )

def get_driver_approval_status(user_id: int) -> dict:

    result = db.session.query(Drivers, Users) \
        .join(Users, Drivers.user == Users.id) \
        .filter(Users.id == user_id).first()

    if not result:
        return standard_response(
            success=-3,
            status=404,
            message="Driver not found for the given token."
        )

    driver, user = result
    region = user.region
    address, phone = get_region_info(region)

    # Base response
    resp = standard_response(
        success=1,
        status=200,
        message="",
        data={
            'region': region,
            'details': {'address': address, 'phone_no': phone}
        }
    )

    # Map approval status
    status_map = {
        Drivers.APPROVED: ("Driver is approved.", Drivers.APPROVED),
        Drivers.PENDING: ("Registration approval is pending.", Drivers.PENDING),
        Drivers.UNAPPROVED: ("Driver is registered but not approved yet.", Drivers.UNAPPROVED),
        Drivers.INACTIVE: ("Driver is registered but currently inactive.", Drivers.INACTIVE)
    }

    msg, code = status_map.get(driver.approved, (None, None))
    if msg is None:
        return standard_response(
            success=-4,
            status=500,
            message="An unexpected error occurred. Invalid approval status."
        )

    resp['message'] = msg
    resp['status_code'] = code
    return resp
