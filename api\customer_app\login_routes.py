#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _sms.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra


# #=== Standard Library ===
from typing import Optional
import re
import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity,set_access_cookies, set_refresh_cookies, create_access_token, create_refresh_token
from datetime import timedelta,datetime

# === Third-Party Libraries ===
from pydantic import BaseModel, ValidationError,Field, validator, field_validator
from flasgger import swag_from

# === Application (Internal) Imports ===
from utils.redis_utils import read_redis_data, update_redis_data, execute_with_fallback, write_redis_data
from models.models import Users
from services.customer_app.otp_service import process_otp_request, process_otp_validation
from services.customer_app.token_service import handle_token_refresh, check_mobile_existence, check_driver_existence
from services.customer_app.register_login_service import register_customer,handle_login, handle_driver_login, handle_forgot_password
from utils.response_utils import standard_response

customer_login = Blueprint('customer_app_login', __name__)
logger = logging.getLogger(__name__)


class MobileCountryValidationSchema(BaseModel):
    mobile: str
    countrycode: str = "91"

    @field_validator('mobile')
    @classmethod
    def validate_mobile(cls, v):
        if not re.match(r'^\d{10}$', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('countrycode')
    @classmethod
    def validate_country_code(cls, v):
        cleaned = v.replace("+", "")
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid.")
        return cleaned

@customer_login.route('/auth/token/otp/generate', methods=['POST'])
@swag_from('/app/swagger_docs/otp/generate_otp.yml')
def gen_otp():
    """
    Generate OTP for login or registration via mobile number.
    """
    try:
        form_data = request.form.to_dict()
        validated_data = MobileCountryValidationSchema(**form_data)
        response = process_otp_request(validated_data)
        return jsonify(response), response["status"]
    
    except ValidationError as ve:
        logger.warning(f"Validation error: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message= ve.errors()[0]['msg'],
            response_status="error"
        )), 400
    
    except Exception as e:
        logger.exception("Unhandled exception in gen_otp")
        return jsonify(standard_response(
            success=-6,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500

class MobileCountryOtpValidationSchema(BaseModel):
    mobile: str
    countrycode: str = "91"
    otp: str=""

    @field_validator('mobile')
    @classmethod
    def validate_mobile(cls, v):
        if not re.match(r'^\d{10}$', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('countrycode')
    @classmethod
    def validate_country_code(cls, v):
        cleaned = v.replace("+", "")
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid.")
        return cleaned
    
@customer_login.route('/auth/token/otp/validate', methods=['POST'])
@swag_from('/app/swagger_docs/otp/validate_otp.yml')
def val_otp():
    """
    Validate OTP for login or registration via mobile number.
    """
    try:
        form_data = request.form.to_dict()
        validated_data = MobileCountryOtpValidationSchema(**form_data)

        response = process_otp_validation(
            mobile=validated_data.mobile,
            otp=validated_data.otp,
            countrycode=validated_data.countrycode
        )

        return jsonify(response), response["status"]

    except ValidationError as ve:
        logger.warning(f"Validation error in val_otp: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=ve.errors()[0]['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unhandled exception in val_otp")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500

@customer_login.route('/auth/token/refresh', methods=['POST'])
@swag_from('/app/swagger_docs/auth/refresh_token.yml')
@jwt_required(refresh=True)
def refresh():
    """
    Refreshes the access token using the provided refresh token.
    """
    try:
        auth_header = request.headers.get("Authorization", "")
        current_user = get_jwt_identity()

        response = handle_token_refresh(auth_header, current_user, request.headers.get("User-Agent"))
        return jsonify(response), response["status"]

    except Exception as e:
        logger.exception("Unexpected error during token refresh")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500        

@customer_login.route('/auth/token/exists', methods=['POST'])
@swag_from('/app/swagger_docs/auth/check_exists.yml')
def check_exists():
    """
    Checks if a mobile number is already registered.
    """
    try:
        form_data = request.form.to_dict()
        validated = MobileCountryValidationSchema(**form_data)

        response = check_mobile_existence(validated.mobile)
        return jsonify(response), response["status"]

    except ValidationError as ve:
        logger.warning(f"Validation error in check_exists: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=ve.errors()[0]['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unhandled exception in check_exists")
        return jsonify(standard_response(
            success=-4,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500        
    

class RegisterCustomerSchema(BaseModel):
    mobile: str
    pwd: str = Field(min_length=6)
    fname: str
    token_and_secret: str
    lname: Optional[str] = ""
    email: Optional[str] = ""
    device: Optional[str] = None
    lat: Optional[str] = None
    lng: Optional[str] = None
    countrycode: str = "91"

    @field_validator('mobile')
    @classmethod
    def validate_mobile(cls, v):
        if not re.match(r'^\d{10}$', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('countrycode')
    @classmethod
    def validate_country_code(cls, v):
        cleaned = v.replace("+", "")
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid.")
        return cleaned

@customer_login.route('/api/user/register', methods=['POST'])
@swag_from('/app/swagger_docs/users/register_cust.yml')
def register_cust_scr():
    """
    Registers a new customer using mobile, password, and name.
    """
    try:
        form_data = request.form.to_dict()
        validated = RegisterCustomerSchema(**form_data)

        response = register_customer(validated.dict())
        return jsonify(response), response.get("status", 200)

    except ValidationError as ve:
        logger.warning(f"Validation error during registration: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=ve.errors()[0]['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unexpected error in customer registration")
        return jsonify(standard_response(
            success=-9,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500         


class LoginSchema(BaseModel):
    mobile: str
    auth_type: str
    password: str
    countrycode: str = "91"
    remember: bool = False

    @field_validator('mobile')
    @classmethod
    def validate_mobile(cls, v):
        if not re.match(r'^\d{10}$', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('auth_type')
    @classmethod
    def validate_auth_type(cls, v):
        if v not in ["password", "otp"]:
            raise ValueError("Invalid auth_type. Allowed values are 'otp' or 'password'.")
        return v

    @field_validator('countrycode')
    @classmethod
    def validate_country_code(cls, v):
        cleaned = v.replace("+", "")
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid")
        return cleaned

@customer_login.route('/auth/token/login', methods=['POST'])
@swag_from('/app/swagger_docs/auth/user_login.yml')
def login():
    try:
        form_data = request.form.to_dict()
        validated = LoginSchema(**form_data)

        return handle_login(
            validated_data=validated,
            user_agent=request.headers.get('User-Agent')
        )

    except ValidationError as ve:
        logger.warning(f"Validation error during login: {ve}")
        first_error = ve.errors()[0]
        field = first_error.get("loc", [None])[0]
        message = first_error.get("msg", "Invalid input.")
        error_type = first_error.get("type", "")

        # Handle missing fields
        if error_type == "missing":
            return jsonify({
                "success": -7,
                "status": "error",
                "message": "Missing or invalid request parameters.",
                "response_status": "error"
            }), 400
        # Customize response based on which field failed
        if field == "mobile":
            return jsonify(standard_response(
                success=-1,
                status=400,
                message=message,
                response_status="error"
            )), 400

        elif field == "countrycode":
            return jsonify(standard_response(
                success=-11,
                status=400,
                message=message,
                response_status="error"
            )), 400

        elif field == "auth_type":
            return jsonify(standard_response(
                success=-6,
                status=400,
                message=message,
                response_status="error"
            )), 400

        else:
            return jsonify(standard_response(
                success=-15,
                status=400,
                message=message,
                response_status="error"
            )), 400

    except Exception as e:
        logger.exception("Unexpected error in user login")
        return jsonify(standard_response(
            success=-10,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500


class DriverLoginSchema(BaseModel):
    mobile: str
    password: str
    auth_type: str
    device_id: str
    device_type: str
    countrycode: str = "91"

    @field_validator('mobile')
    @classmethod
    def validate_mobile(cls, v):
        if not re.match(r'\d{10}', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('auth_type')
    @classmethod
    def validate_auth_type(cls, v):
        if v not in ["password", "otp"]:
            raise ValueError("Invalid auth_type. Must be 'password' or 'otp'.")
        return v

    @field_validator('device_type')
    @classmethod
    def validate_device_type(cls, v):
        if v not in ["ios", "android", "web"]:
            raise ValueError("Invalid device_type. Must be 'ios', 'android' or 'web'.")
        return v

    @field_validator('countrycode')
    @classmethod
    def validate_country_code(cls, v):
        cleaned = v.replace("+", "")
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid.")
        return cleaned

@customer_login.route('/auth/token/driver/login', methods=['POST'])
@swag_from('/app/swagger_docs/driver_auth/login_driv.yml')
def login_driver():
    """
    Authenticates a driver using password or OTP and returns access/refresh tokens.
    """
    try:
        form_data = request.form.to_dict()
        validated = DriverLoginSchema(**form_data)

        # Delegate core logic to service function
        response = handle_driver_login(validated, request.headers.get('User-Agent'))
        return jsonify(response), response.get('status_code', 200)

    except ValidationError as ve:
        logger.warning(f"Validation error in login_driver: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=ve.errors()[0]['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unexpected error in login_driver")
        return jsonify(standard_response(
            success=-7,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500

class ForgotPasswordSchema(BaseModel):
    mobile: str
    password: str
    token_and_secret: str
    countrycode: str = "91"

    @field_validator('mobile')
    @classmethod
    def mobile_format(cls, v):
        if not re.match(r'\d{10}', v):
            raise ValueError("Invalid mobile number format.")
        return v

    @field_validator('password')
    @classmethod
    def password_strength(cls, v):
        if len(v) < 6:
            raise ValueError("Password must be at least 6 characters.")
        return v

    @field_validator('countrycode')
    @classmethod
    def country_code_valid(cls, v):
        cleaned = v.replace('+', '') or '91'
        if cleaned not in Users.VALID_COUNTRY_CODES:
            raise ValueError("Country code not supported or invalid country code.")
        return cleaned

@customer_login.route('/auth/token/forgot/password', methods=['POST'])
@jwt_required()
# @swag_from('/app/swagger_docs/auth/user_forgot_password.yml')
def user_forgot_pass():
    """
    Resets a user's password given a valid token and secret.
    """
    try:
        form_data = request.form.to_dict()
        validated = ForgotPasswordSchema(**form_data)
        response = handle_forgot_password(validated)
        return jsonify(response), response.get('status_code', 200)

    except ValidationError as ve:
        logger.warning(f"Validation error in user_forgot_pass: {ve}")
        err = ve.errors()[0]
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=err['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unexpected error in user_forgot_pass")
        return jsonify(standard_response(
            success=-8,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500

@customer_login.route('/auth/token/exists/driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/check_exists_driver.yml')
def check_exists_driver():
    """
    Checks if a user exists and whether they are registered as a driver.
    """
    try:
        form_data = request.form.to_dict()
        validated = MobileCountryValidationSchema(**form_data)

        response = check_driver_existence(
            mobile=validated.mobile,
            countrycode=validated.countrycode
        )
        return jsonify(response), response.get('status_code', 200)

    except ValidationError as ve:
        logger.warning(f"Validation error in check_exists_driver: {ve}")
        return jsonify(standard_response(
            success=-1,
            status=400,
            message=ve.errors()[0]['msg'],
            response_status="error"
        )), 400

    except Exception as e:
        logger.exception("Unexpected error in check_exists_driver")
        return jsonify(standard_response(
            success=-3,
            status=500,
            message='An unexpected error occurred. Please try again later.',
            response_status="error"
        )), 500


