tags:
  - Login_admin
summary: Change admin password
description: |
  This endpoint allows an admin to change their password after successful OTP validation.
consumes:
  - multipart/form-data
produces:
  - application/json
parameters:
  - name: mobile
    in: formData
    required: true
    type: string
    description: Admin's mobile number
  - name: new_pwd
    in: formData
    required: true
    type: string
    description: New password to set
responses:
  200:
    description: Password changed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Password changed successfully
        response_status:
          type: string
          example: success
  400:
    description: Validation error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 400
        message:
          type: string
          example: Invalid input
        response_status:
          type: string
          example: error
        data:
          type: object
          example:
            error:
              - mobile: field required
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        status:
          type: integer
          example: 500
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
        response_status:
          type: string
          example: error
