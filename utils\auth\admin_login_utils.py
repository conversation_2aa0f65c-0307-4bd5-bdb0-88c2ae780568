from flask_jwt_extended import create_access_token, create_refresh_token
import datetime
from models.models import Users
from flask import request, jsonify
from functools import wraps
from flask_jwt_extended import get_jwt
from redis_config import execute_with_fallback
import hashlib

def check_if_token_revoked(refresh_token):
    token_hash = hashlib.sha512(refresh_token.encode('utf-8')).hexdigest()
    entry = execute_with_fallback('get', f"blacklist:{token_hash}")
    # entry = redis_client.get(f"blacklist:{token_hash}")
    return entry is not None

def check_token_revoked(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        refresh_token = request.cookies.get('refresh_token_cookie')
        if not refresh_token or check_if_token_revoked(refresh_token):
            return jsonify({'success': -1, "status": 401, "msg": "Token has been revoked"}), 401
        return fn(*args, **kwargs)
    return wrapper

def get_first_non_none_region(form_data, args_data,json_data):
    form_fields = ['regions', 'region', 'region_name', 'search_region', 'city']

    for field in form_fields:
        value = form_data.get(field)
        if value:
            return value

    for field in form_fields:
        value = args_data.get(field)
        if value:
            return value

    for field in form_fields:
        value = json_data.get(field)
        if value:
            return value
    return None


def check_access(tab_required,json=False):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            refresh_token = request.cookies.get('refresh_token_cookie')
            if not refresh_token or check_if_token_revoked(refresh_token):
                return jsonify({'success': -1, "status": 401, "msg": "Token has been revoked"}), 401
            claims = get_jwt()
            if int(claims['roles']) in [0,1,3,4,5,6,7,8,9,10,11,12,13]:
                return jsonify({'error': 'Forbidden access for this role'}), 403
            print(claims, 'claims by check access')
            if 'tab_access' not in claims or 'regions_access' not in claims:
                return jsonify({'error': 'Access claims missing'}), 406

            data = request.form
            args_data = request.args
            json_data=""
            if json:
                json_data=data = request.get_json()
            regions_requested = get_first_non_none_region(data, args_data,json_data)
            if not regions_requested:
                return jsonify({'error': 'Regions are required'}), 406

            regions_list = [int(region) for region in regions_requested.split(',')  if region.isdigit()]

            if int(claims['roles']) == 127:
                return f(*args, **kwargs)

            # if not any((claims['tab_access'] & (1 << int(tab))) for tab in tab_required.split(',')):
            print(claims['tab_access'])
            if not any((claims['tab_access'] & (1 << tab)) for tab in tab_required):
                return jsonify({'error': 'Access to this tab is restricted'}), 423

            user_regions_access = claims['regions_access']
            if regions_requested == '-1' or regions_requested == -1:
                if not all(user_regions_access & (1 << region) for region in range(12)):
                    return jsonify({'error': 'Access to regions is restricted'}), 423
            else:
                if not all(user_regions_access & (1 << region) for region in regions_list):
                    return jsonify({'error': 'Access to the requested regions is restricted'}), 423

            return f(*args, **kwargs)
        return wrapper
    return decorator


def create_token_admin(user, admin_access):
    refresh_expiry = datetime.timedelta(minutes=720 if int(user.role) != Users.ROLE_SUPERADMIN else 31536000)
    identity_with_claims = {
        'id': user.id,
        'roles': user.role,
        'region': user.region,
        'tab_access': admin_access.admin_tab_access if admin_access else 0,
        'regions_access': admin_access.admin_regions_access if admin_access else 0,
        'notification_access': admin_access.admin_notification_access if admin_access else 0,
        'name': user.fname + ' ' +user.lname
    }
    access_token = create_access_token(identity=user.id,additional_claims=identity_with_claims, expires_delta=datetime.timedelta(minutes=60 if int(user.role) != 127 else 720))
    refresh_token = create_refresh_token(identity=user.id,additional_claims=identity_with_claims, expires_delta=refresh_expiry)
    return access_token, refresh_token, refresh_expiry