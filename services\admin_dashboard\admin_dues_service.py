import re
from datetime import datetime, timedelta
import time
import json
from flask import current_app as app
from sqlalchemy.sql import func, or_, desc, asc, and_, exists
from sqlalchemy.exc import SQLAlchemyError
from models.models import db, Users, Drivers, DriverDetails, DriverTrans, AdminLog, DriverPaid, DriverDetails, \
    DriverCancelled,Bookings
from utils.slack_utils import send_slack_msg
from utils.fcm_utils import send_fcm_msg_driver
from utils.driver.driver_utils import compute_driver_wallet
from utils.time_utils import combine_and_convert_to_local
from utils.bookings.booking_params import Regions
from schemas.admin_dashboard.due_credit_schemas import AddDriverDuePayload, DriverDueLogPayload, \
    DuesDriverListPayload, FILTER_BY_AVAILABLE, FILTER_BY_NOT_AVAILABLE, SORT_BY_DRIVER_ID_ASC, SORT_BY_DRIVER_ID_DESC, \
    SORT_BY_SCORE_DESC, SORT_BY_SCORE_ASC, SORT_BY_RATING_DESC, SORT_BY_RATING_ASC


def search_driver_by_mobile_service(mobile: int) -> dict:
    try:
        driver = db.session.query(Users, Drivers, DriverDetails).filter(
            Drivers.user == Users.id,
            Drivers.id == DriverDetails.driver_id,
            Users.mobile == mobile
        ).first()
    except SQLAlchemyError as e:
        return {"success": -2, "message": "Failed to fetch driver data","status_code": 500}

    if not driver:
        return {"success": -3, "message": "Driver not found","status_code": 404}

    user, driver_model, driver_details = driver
    return {
        "success": 1,
        "status_code": 200,
        "message": "Driver found",
        "data": {
        "id": driver_model.id,
        "name": user.get_name(),
        "mobile": user.mobile,
        "driver_license": driver_model.licenseNo,
        "due": driver_details.owed,
        "wallet": driver_details.wallet,
        "withdrawable": driver_details.withdrawable
    }
    }



def add_driver_due_service(payload: AddDriverDuePayload, admin_user, raw_form):
    admin = db.session.query(Users).filter(Users.id == admin_user).first()
    if not admin:
        return {"success": -1, "message": "Admin user not found","status_code": 400}

    admin_name = admin.get_name()
    driver_id = payload.driver_id
    amount = payload.amount
    trans_id = payload.trans_id
    due_type = payload.due_type
    remarks = str(payload.remark or "").strip()
    due_type_reason = str(payload.due_type_reason or "").strip()

    driver_e = db.session.query(Drivers, Users).filter(Drivers.id == driver_id).filter(Users.id == Drivers.user).first()
    if not driver_e:
        return {"success": -2, "message": "Driver not found","status_code": 400}

    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).first()
    if not driver_details:
        return {"success": -3, "message": "Driver details not found","status_code": 400}

    driver_trans_amt = amount * 100
    method = due_type
    desc = ""

    if due_type == "Withdraw":
        if driver_details.withdrawable - amount < 0:
            raise Exception("Withdrawable amount should be positive")
        wallet = driver_details.wallet
        withdrawable = driver_details.withdrawable - amount
        driver_trans_amt = -(amount * 100)

    elif due_type == "Due deduction":
        method = "Due Deduction"
        wallet = driver_details.wallet + amount
        withdrawable = driver_details.withdrawable

    elif due_type == "Fine":
        method = "Admin Fine"
        wallet, withdrawable = compute_driver_wallet(driver_details, amount)
        driver_trans_amt = -(amount * 100)

    elif due_type == "Gift":
        wallet = driver_details.wallet + amount
        withdrawable = driver_details.withdrawable

    else:
        if due_type.startswith("T-Shirt"):
            method = "T-Shirt"
            desc = extract_desc(due_type)
        elif due_type.startswith("Registration"):
            method = "Registration"
            desc = extract_desc(due_type)
        elif due_type.startswith("Bag"):
            method = "Bag"
            match = re.search(r'Bag-(\d+)', due_type)
            if match:
                desc = match.group(1)
        wallet = driver_details.wallet
        withdrawable = driver_details.withdrawable
        driver_trans_amt = -(amount * 100)

    if due_type in ["Reactivation"] or due_type.startswith("Registration"):
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).update({
            DriverDetails.reactivation_ts: datetime.utcnow()
        })

    # Create transaction log
    dt = DriverTrans(
        did=driver_id,
        amt=driver_trans_amt,
        wall_a=wallet, wall_b=driver_details.wallet,
        with_a=withdrawable, with_b=driver_details.withdrawable,
        method=method, admin_user_id=admin_user,
        admin_name=admin_name, status=DriverTrans.COMPLETED,
        description=desc, remarks=remarks,
        trans_payment_id=trans_id, due_type_reason=due_type_reason
    )
    db.session.add(dt)

    # Update wallet values
    db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id).update({
        DriverDetails.wallet: wallet,
        DriverDetails.withdrawable: withdrawable
    })

    # Add admin log
    admin_log = AdminLog(admin_user, f"d4m-{due_type}", json.dumps(str(raw_form)))
    db.session.add(admin_log)
    db.session.commit()

    # Notification logic
    notify_driver_and_slack(driver_id, due_type, amount, wallet, withdrawable, admin_name, driver_e[1].get_name(), remarks)

    return {"success": 1, "message": "Driver dues updated successfully!","status_code": 200}

def extract_desc(due_type: str):
    return ",".join(part.strip("()") for part in due_type.split("(")[1:]) if "(" in due_type else ""

def notify_driver_and_slack(driver_id, due_type, amount, wallet, withdrawable, admin_name, driver_name, remarks):
    title, smalltext, bigtext = "", "", ""
    bal = round(wallet + withdrawable, 2)

    if due_type == "Withdraw":
        title = "Withdrawal Successful"
        smalltext = f"₹{amount} has been withdrawn from your wallet."
        bigtext = f"₹{amount} has been successfully withdrawn. Your remaining balance is ₹{bal}."
    elif due_type == "Due deduction":
        title = "Wallet Credit Added"
        smalltext = f"₹{amount} has been credited to your wallet."
        bigtext = f"Your wallet has been credited with ₹{amount}. Your remaining balance is ₹{bal}."
    elif due_type == "Fine":
        title = "Fine Deducted"
        smalltext = f"A fine of ₹{amount} has been deducted from your wallet."
        bigtext = f"₹{amount} was deducted from your wallet as a fine. Your remaining balance is ₹{bal}."
    elif due_type == "Gift":
        title = "Gift Added"
        smalltext = f"A gift of ₹{amount} has been added to your wallet."
        bigtext = f"₹{amount} has been gifted to you. Your remaining balance is ₹{bal}."
    elif due_type.startswith("T-Shirt"):
        title = "T-Shirt Provided"
        smalltext = "T-Shirt has been provided to you."
        bigtext = "T-Shirt has been successfully provided."
    elif due_type.startswith("Registration"):
        title = "Registration Fee Paid"
        smalltext = f"₹{amount} has been deducted for your registration."
        bigtext = f"Your registration fee of ₹{amount} has been successfully processed. Thank you for completing the registration process."
    elif due_type.startswith("Reactivation"):
        title = "Reactivation Fee Paid"
        smalltext = f"₹{amount} has been deducted for your reactivation."
        bigtext = f"Your reactivation fee of ₹{amount} has been successfully processed. Welcome back!"

    send_fcm_msg_driver(int(driver_id), title=title, smalltext=smalltext, bigtext=bigtext)

    slack_message = f"{admin_name} processed a '{due_type}' for {driver_name} with an amount of ₹{amount}."
    if remarks:
        slack_message += f" Remarks: {remarks}"
    send_slack_msg(0, slack_message)
    
def get_driver_due_log_service(payload: DriverDueLogPayload, timezone: str):
    try:
        mobile = payload["mobile"]

        user = db.session.query(Users).filter(Users.mobile == int(mobile)).first()
        if not user:
            return {"success": -2, "status_code": 404, "message": "User not found"}

        driver_query = db.session.query(Drivers, Users).filter(
            Users.mobile == int(mobile),
            Drivers.user == Users.id
        )

        driver = driver_query.first()
        if not driver:
            return {"success": -3, "status_code": 404, "message": "Driver not found"}

        driver_id = driver[0].id
        old_format = False

        driver_dues = db.session.query(DriverPaid).filter(
            DriverPaid.driver_id == driver_id
        ).all() if old_format else []

        driver_trans = db.session.query(DriverTrans).filter(
            DriverTrans.driver_id == driver_id
        ).all()

        entries = driver_dues + driver_trans
        entries.sort(key=lambda x: x.timestamp, reverse=True)

        result = [{
            'changed_for': f"{user.fname} {user.lname}",
            'user_id': user.id
        }]

        for e in entries:
            if hasattr(e, "source"):
                amount = e.amount
                remarks = "Old Format"
            else:
                amount = e.amount / 100
                remarks = e.remarks

            result.append({
                'amt': amount,
                'method': e.method,
                'description': e.description,
                'timestamp': combine_and_convert_to_local(e.timestamp.date(), e.timestamp.time(), timezone),
                'remark': remarks,
                'changed_by': e.admin_name,
                'trans_id': getattr(e, 'trans_payment_id', None),
                'status': getattr(e, 'status', None),
                'due_type_reason': getattr(e, 'due_type_reason', None),
                'current_balance': getattr(e, 'wallet_after', None),
                'withdrawble_after': getattr(e, 'withdrawable_after', None)
            })

        return {"success": 1, "status_code": 200, "message": "Driver dues log fetched successfully", "data": result}

    except SQLAlchemyError as e:
        app.logger.error(f"[get_driver_due_log_service] Database error: {str(e)}")
        return {"success": -4, "status_code": 500, "message": "Database error"}
    except Exception as e:
        app.logger.error(f"[get_driver_due_log_service] Unexpected error: {str(e)}")
        return {"success": -99, "status_code": 500, "message": "Unexpected error"}
    
def get_dues_driver_list(payload: DuesDriverListPayload, user_id: int):
    region = payload.get("region", int(Regions.ALL_REGIONS_ACCESS))
    search_query = payload.get("search_query")
    filter_by = payload.get("filter_by")
    sort_by = payload.get("sort_by")
    l_limit = payload.get("l_limit", 0)
    u_limit = payload.get("u_limit", 50)

    if region == int(Regions.ALL_REGIONS_ACCESS):
        region = db.session.query(Users.region).filter(Users.id == user_id).scalar()

    region_list = Regions.driver_service_regions(region) if region != int(Regions.ALL_REGIONS_ACCESS) else list(range(len(Regions.REGN_NAME)))


    try:
        query = db.session.query(
            Drivers.id.label('driver_id'),
            Drivers.pic.label('driver_pic'),
            Users.mobile.label('driver_mobile'),
            func.concat(Users.fname, ' ', Users.lname).label('driver_name'),
            Users.reg.label("driver_user_reg"),
            Users.label_bv.label("driver_labels"),
            Drivers.available.label("driver_available"),
            Drivers.rating.label("driver_rating"),
            Drivers.licenseNo.label("driver_licence"),
            DriverDetails.ride_count.label("driver_trip_count"),
            DriverDetails.rating_count.label("driver_rating_count"),
            DriverDetails.approval_ts.label("driver_approval_ts"),
            DriverDetails.timestamp.label("driver_det_timestamp"),
            DriverDetails.wallet.label("total_balance"),
            DriverDetails.withdrawable.label("withdrawable"),
            DriverDetails.owed.label("total_dues")
        ).join(Users, Drivers.user == Users.id) \
        .join(DriverDetails, DriverDetails.driver_id == Drivers.id) \
        .filter(Users.region.in_(region_list), Drivers.approved == 1)

        if filter_by:
            filter_values = [int(val) for val in filter_by.split(',')]
            or_conditions = []
            if FILTER_BY_AVAILABLE in filter_values:
                or_conditions.append(Drivers.available == 1)
            if FILTER_BY_NOT_AVAILABLE in filter_values:
                or_conditions.append(Drivers.available == 0)
            if or_conditions:
                query = query.filter(or_(*or_conditions))

        if search_query:
            try:
                search_id = int(search_query)
                query = query.filter(or_(Drivers.id == search_id, Users.mobile == search_id))
            except ValueError:
                name_parts = search_query.split()
                if len(name_parts) == 2:
                    query = query.filter(or_(
                        and_(Users.fname.ilike(f"%{name_parts[0]}%"), Users.lname.ilike(f"%{name_parts[1]}%")),
                        and_(Users.fname.ilike(f"%{name_parts[1]}%"), Users.lname.ilike(f"%{name_parts[0]}%"))
                    ))
                else:
                    query = query.filter(or_(
                        Users.fname.ilike(f"%{search_query}%"),
                        Users.lname.ilike(f"%{search_query}%")
                    ))


        driver_cancelled = dict(
            db.session.query(Drivers.id, func.count(DriverCancelled.id))
            .filter(DriverCancelled.driver == Drivers.id)
            .group_by(Drivers.id).all()
        )

        start_time = time.time()
        results = query.all()
        query_time = round(time.time() - start_time, 3)

        response = []
        for driver in results:
            score_arr = []  # Add custom score logic if needed
            response.append({
                "driver_id": driver.driver_id,
                "driver_pic": driver.driver_pic,
                "driver_name": driver.driver_name,
                "driver_mobile": driver.driver_mobile,
                "availablity": driver.driver_available,
                "license": driver.driver_licence,
                "driver_labels": driver.driver_labels,
                "driver_score": round(sum(score_arr), 2),
                "driver_rating": round(driver.driver_rating or 0, 2),
                "driver_rating_count": driver.driver_rating_count,
                "driver_cancelled_trips": driver_cancelled.get(driver.driver_id, 0),
                "driver_trip_count": driver.driver_trip_count,
                "total_balance": driver.total_balance,
                "withdrawable": driver.withdrawable,
                "total_dues": driver.total_dues,
            })

        # Sorting
        if sort_by:
            sort_by = int(sort_by)
            if sort_by == SORT_BY_DRIVER_ID_ASC:
                response.sort(key=lambda x: x['driver_id'])
            elif sort_by == SORT_BY_DRIVER_ID_DESC:
                response.sort(key=lambda x: x['driver_id'], reverse=True)
            elif sort_by == SORT_BY_SCORE_DESC:
                response.sort(key=lambda x: x['driver_score'], reverse=True)
            elif sort_by == SORT_BY_SCORE_ASC:
                response.sort(key=lambda x: x['driver_score'])
            elif sort_by == SORT_BY_RATING_DESC:
                response.sort(key=lambda x: x['driver_rating'], reverse=True)
            elif sort_by == SORT_BY_RATING_ASC:
                response.sort(key=lambda x: x['driver_rating'])

        return {"success": 1, "data": response, "status_code": 200,"message": "Driver dues list fetched successfully"}
    except Exception as e:
        return {"success": -99, "status_code": 500, "message": "Unexpected error","data": str(e)}