import datetime
import pytest
import jsonpickle
from flask import jsonify
from flask_jwt_extended import create_access_token
from models.models import DriverDetails,Users,Drivers,db,DriverTrans, BookPending, DriverFCM,DriverAnalyticsToken
import random
from sqlalchemy import exc
from conftest import driver_bookings


# Utility functions
def create_driver_with_wallet_and_due(wallet, withdrawable, due_threshold, driver_id):
    driver_details = DriverDetails(driver_id, wallet=wallet, withdrawable=withdrawable)
    db.session.add(driver_details)
    db.session.commit()
    driver = Drivers.query.filter_by(id=driver_id).first()
    return driver

def create_driver_with_driver_trans(driver_id,amount):
    driver_transac = DriverTrans(driver_id, amt=amount,status =1)
    db.session.add(driver_transac)
    db.session.commit()
    driver = Drivers.query.filter_by(id=driver_id).first()
    return driver

def create_driver_with_pending_booking (driver_id,booking_id):
    pending_booking = BookPending(bid=booking_id, did=driver_id, state=1)
    db.session.add(pending_booking)
    db.session.commit()
    driver = Drivers.query.filter_by(id=driver_id).first()
    return driver

def create_mock_b2b_booking(booking_model, booking_id, ref_id):
    booking = booking_model(id=booking_id, ref=ref_id)
    db.session.add(booking)
    db.session.commit()

def create_access_token_for_driver(driver):
    identity_with_claims = {
        'id': driver.id,
        'roles': driver.role,
        'region': driver.region,
        'fname': driver.fname,
        'lname': driver.lname,
    }
    expires_access = datetime.timedelta(days=365)
    return create_access_token(identity=identity_with_claims, expires_delta=expires_access)

# Test cases

def test_confirm_book_driver_not_enabled(client,driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=500, withdrawable=200, due_threshold=1000, driver_id=driver_id)
    user = Users.query.filter_by(id=driver.user).first()
    if user:
        user.enabled = False
    db.session.commit()
    booking_id = driver_bookings(user_id,driver_id)
    headers = {'Authorization': f'Bearer {token}'}

    response = client.post('/api/driver/confirm', headers=headers, data={'booking_id': booking_id})

    assert response.status_code == 401
    assert response.get_json()['message'] == "Driver account not enabled"

def test_confirm_book_due_above_threshold(client,driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=-200, withdrawable=-150, due_threshold=200, driver_id=driver_id)
    booking_id = driver_bookings(user_id,driver_id)
    headers = {'Authorization': f'Bearer {token}'}

    # Assuming max_driver_owed(1) returns 1000 for this booking
    response = client.post('/api/driver/confirm', headers=headers, data={'booking_id': booking_id})

    assert response.status_code == 200
    assert response.get_json()['message'] == "Due is above threshold"

def test_book_missing_booking_id(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id,driver_id)

    # Create a valid booking for this test
    #create_mock_b2b_booking(ZoomcarBookings, booking_id= booking_id)

    headers = {'Authorization': f'Bearer {token}'}

    response = client.post('/api/driver/confirm', headers=headers, data={})

    assert response.status_code == 201
    assert response.get_json()['success'] == -1
    assert response.get_json()['message'] == "Could not find booking id"








# def test_confirm_book_invalid_b2b_id(client):
#     driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000)

#     token = create_access_token_for_driver(driver)
#     headers = {'Authorization': f'Bearer {token}'}

#     # Test invalid B2B booking case (Zoomcar, in this example)
#     response = client.post('/api/driver/confirm', headers=headers, data={'booking_id': 1, 'zoomcar': 1})

#     assert response.status_code == 200
#     assert response.get_json()['message'] == "Invalid B2B id"


# def test_confirm_book_b2b_accept_sms_notification(client):
#     driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000)

#     # Create a valid Bhandari booking for this test
#     create_mock_b2b_booking(BhandariBookings, booking_id=1, ref_id=1001)

#     token = create_access_token_for_driver(driver)
#     headers = {'Authorization': f'Bearer {token}'}

#     response = client.post('/api/driver/confirm', headers=headers, data={'booking_id': 1, 'bhandari': 1})

#     assert response.status_code == 200
#     assert response.get_json()['success'] == 1

    # Verify that SMS notification was sent (mock or spy the SMS function)
    # assert mock_send_sms.called_once_with("Bhandari", 1, driver.user)

#B2B
# def test_confirm_book_successful(client,driver_login):
#     state = driver_login
#     token = state['access_token']
#     user_id = state['user_id']
#     driver_id = state['driver_id']
#     driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
#     booking_id = driver_bookings(user_id,driver_id)

#     # Create a valid booking for this test
#     #create_mock_b2b_booking(ZoomcarBookings, booking_id= booking_id)

#     headers = {'Authorization': f'Bearer {token}'}

#     response = client.post('/api/driver/confirm', headers=headers, data={'booking_id': booking_id, 'zoomcar': 1})

#     assert response.status_code == 200
#     assert response.get_json()['success'] == 1
#     assert response.get_json()['message'] == "Booking confirmed"







def test_view_credits(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)

    headers = {'Authorization': f'Bearer {token}'}

    response = client.post('/api/user/credits/view', headers=headers, data={})
    print("Response: ",response)
    #assert response.status_code == 201
    assert response.get_json()['success'] == 1
    #assert response.get_json()['message'] == "Could not find booking id"

def test_start_credits(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    order_data = {
        'amount': '500',  # Example amount
        'gateway': str(0) # Use default gateway
    }

    headers = {'Authorization': f'Bearer {token}'}

    response = client.post('/api/user/credits/start', headers=headers, data=order_data)
    print("Response: ",response)
    assert response.status_code == 200
    assert 'trans_id' in response.get_json()
    assert 'order_id' in response.get_json()
    assert 'checksumhash' in response.get_json()

def test_driver_trans_log(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    driver = create_driver_with_driver_trans(driver_id=driver_id, amount=200)


    headers = {'Authorization': f'Bearer {token}'}
    response = client.post('/api/driver/trans_log', headers=headers)

    # Assert the status code is 200
    assert response.status_code == 200
    print("response:",response.status_code)
    res_data = response.get_json()

    # Assert success response
    assert res_data['success'] == 1

    # Assert the wallet and withdrawable amounts are present and correctly formatted
    assert 'wallet' in res_data
    assert 'withdrawable' in res_data


def test_reject_booking_success(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id,driver_id)
    driver =create_driver_with_pending_booking(booking_id=booking_id,driver_id=driver_id)

    headers = {'Authorization': f'Bearer {token}'}
    response = client.post('/api/driver/reject', headers=headers, data={'booking_id': booking_id})

    # Assert the status code is 200
    assert response.status_code == 200
    print("response:",response.status_code)
    res_data = response.get_json()
    assert res_data['success'] == 1

def test_reject_booking_invalid_role(client,driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=500, withdrawable=200, due_threshold=1000, driver_id=driver_id)
    user = Users.query.filter_by(id=driver.user).first()
    if user:
        user.role = 0
    db.session.commit()
    booking_id = driver_bookings(user_id,driver_id)
    headers = {'Authorization': f'Bearer {token}'}

    data = {'booking_id': booking_id}

    response = client.post('/api/driver/reject', data=data, headers=headers)
    res_data = response.get_json()

    # Assert unauthorized response
    assert response.status_code == 401
    assert res_data['success'] ==  -1

def test_reject_booking_invalid_booking_id(client,driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id,driver_id)
    driver =create_driver_with_pending_booking(booking_id=booking_id,driver_id=driver_id)

    headers = {'Authorization': f'Bearer {token}'}
    data = {'booking_id': 9999}  # Non-existent booking_id

    response = client.post('/api/driver/reject', data=data, headers=headers)
    res_data = response.get_json()

    # Assert failure response
    assert res_data['success'] ==  -1

def test_reject_booking_account_disabled(client, driver_login):
    state = driver_login
    token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id,driver_id)
    driver =create_driver_with_pending_booking(booking_id=booking_id,driver_id=driver_id)


    user = Users.query.filter_by(id=driver.user).first()
    if user:
        user.enabled = False
    db.session.commit()

    headers = {'Authorization': f'Bearer {token}'}
    data = {'booking_id': booking_id}

    response = client.post('/api/driver/reject', data=data, headers=headers)
    res_data = response.get_json()

    # Assert unauthorized response
    assert response.status_code == 401
    assert res_data['success'] ==  -1

def test_set_fcm_token_success(client, driver_login):
    # Setup driver login state and access token
    state = driver_login
    token = state['access_token']
    driver_id = state['driver_id']
    user_id = state['user_id']

    # Create a driver and associate with booking and pending booking
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id, driver_id)
    driver = create_driver_with_pending_booking(booking_id=booking_id, driver_id=driver_id)

    # Define the device and FCM token for the test
    device = 'android'
    fcm_token = 'valid_token'

    headers = {'Authorization': f'Bearer {token}'}

    # Make the POST request to set FCM token for the driver
    response = client.post('/token/login/set_fcm_driver', headers=headers, data={'fcm_token': fcm_token, 'device': device})

    # Assert success response
    res_data = response.get_json()
    assert response.status_code == 200
    assert res_data['success'] == 1

    # Query the database to verify that the FCM token was set correctly
    res = db.session.query(DriverFCM).filter(DriverFCM.device == device).filter(DriverFCM.driver_id == driver_id).first()

    # Assert that the record exists and the token is correct
    assert res is not None, "FCM token record should exist in the database"
    assert res.token == fcm_token, f"Expected FCM token to be '{fcm_token}', but got '{res.token}'"
    
    
def test_set_analytics_token_success(client, driver_login):
    # Setup driver login state and access token
    state = driver_login
    token = state['access_token']
    driver_id = state['driver_id']
    user_id = state['user_id']

    # Create a driver and associate with booking and pending booking
    driver = create_driver_with_wallet_and_due(wallet=1500, withdrawable=500, due_threshold=2000, driver_id=driver_id)
    booking_id = driver_bookings(user_id, driver_id)
    driver = create_driver_with_pending_booking(booking_id=booking_id, driver_id=driver_id)

    # Define the device and FCM token for the test
    device = 'android'
    analytics_token = 'valid_token'

    headers = {'Authorization': f'Bearer {token}'}

    # Make the POST request to set FCM token for the driver
    response = client.post('/token/login/set_analytics_token_driver', headers=headers, data={'analytics_token': analytics_token, 'device': device})

    # Assert success response
    res_data = response.get_json()
    assert response.status_code == 200
    assert res_data['success'] == 1

    # Query the database to verify that the FCM token was set correctly
    res = db.session.query(DriverAnalyticsToken).filter(DriverAnalyticsToken.device == device).filter(DriverAnalyticsToken.driver_id == driver_id).first()

    # Assert that the record exists and the token is correct
    assert res is not None, "Analytics token record should exist in the database"
    assert res.analytics_token == analytics_token, f"Expected Analytics token to be '{analytics_token}', but got '{res.analytics_token}'"