tags:
  - Authentication
summary: User login using mobile number with password or OTP
description: |
  Authenticates a user using their mobile number and either a password or OTP. 
  Returns access and refresh tokens if authentication is successful. Handles various error cases including missing fields, account ban, failed attempts, and registration status.
consumes:
  - multipart/form-data
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: password
    in: formData
    type: string
    required: true
    description: Password or OTP of the user
  - name: auth_type
    in: formData
    type: string
    required: true
    enum: [password, otp]
    description: Authentication type to use ("password" or "otp")
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")
  - name: remember
    in: formData
    type: boolean
    required: false
    description: Whether to remember the user for a long time (affects token expiry)
responses:
  200:
    description: Successful login
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Login successful."
        data:
          type: object
          properties:
            user_id:
              type: integer
              example: 12345
            user_fname:
              type: string
              example: "<PERSON>"
            user_lname:
              type: string
              example: "Doe"
            user_mobile:
              type: string
              example: "9876543210"
            user_countrycode:
              type: string
              example: "+91"
            user_email:
              type: string
              example: "<EMAIL>"
            user_restore_id:
              type: string
              example: "restore_12345"
            user_ref_code:
              type: string
              example: "REF12345"
            user_credit:
              type: number
              example: 500.0
            access_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            refresh_token:
              type: string
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            access_expiry:
              type: string
              example: "2025-07-18T12:00:00Z"
            refresh_expiry:
              type: string
              example: "2035-07-18T12:00:00Z"
  400:
    description: Missing or invalid request parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Missing or invalid request parameters."
  401:
    description: Incorrect password or OTP
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Incorrect password/otp. Failed attempt: 2."
  404:
    description: User mobile number not found
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Mobile number not found. Please sign up first."
  423:
    description: Account locked or banned
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -8
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Account locked due to multiple failed attempts. Try again in 600 seconds."
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -10
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
