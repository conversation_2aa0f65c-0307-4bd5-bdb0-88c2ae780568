from models.models import Users,AdminAccess,db
from conftest import create_user,unique_user_data
import json

def test_add_admin_access_success(client, admin_login):
    auth_headers, admin = admin_login

    # Prepare form data for adding new admin access
    form_data = {
        'fname': '<PERSON>',
        'lname': 'Doe',
        'mobile': '9876543210',
        'email': '<EMAIL>',
        'role': '2',
        'regions': '0,1',
        'tab_access': '1,3,5',  # Example tab access IDs
        'notifications': '1,4',  # Example notification access IDs
        'calling_no': '1234567890',
        'agent_id': 'AG001'
    }

    # Call the API to add admin access
    response = client.post('/api/admin/access/add', data=form_data, headers=auth_headers)
    print("Response:", response.get_json())
    assert response.status_code == 201
    res_data = response.get_json()
    print("Response:", res_data)

    # Verify the success response and message
    assert res_data['success'] == 1
    assert 'message' in res_data
    assert res_data['message'] == 'Admin access added and logged successfully'

    # Verify if the user is created and role is assigned properly
    created_user = Users.query.filter_by(mobile='9876543210').first()
    assert created_user is not None
    assert created_user.role == 2

    # Verify if admin access is created
    admin_access = AdminAccess.query.filter_by(admin_user_id=created_user.id).first()
    assert admin_access is not None
    assert admin_access.admin_tab_access == (1 << 1) | (1 << 3) | (1 << 5)
    assert admin_access.admin_regions_access == (1 << 0) | (1 << 1)
    assert admin_access.admin_notification_access == (1 << 1) | (1 << 4)


def test_get_admin_details_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    admin_id = admin.id
    form_data={
        'region':'0'
    }
    # Fetch admin details via API
    response = client.get(f'/api/admin/access/fetch?user_id={admin.id}', data=form_data,headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    # Verify the success response and admin details
    assert res_data['success'] == 1
    assert 'admin' in res_data
    admin_details = res_data['admin']
    admin = db.session.query(Users).filter_by(id=admin_id).first()
    # Verify that the correct admin details are returned
    assert admin_details['fname'] == admin.fname
    assert admin_details['lname'] == admin.lname
    assert admin_details['mobile'] == admin.mobile
    assert admin_details['email'] == admin.email
    assert admin_details['role'] == admin.role
    assert admin_details['tab_access'] == '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15'
    assert admin_details['regions_access'] == '0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19'
    assert admin_details['notification_access'] == '1,2,3,4,5,6'


def test_edit_admin_access_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    admin_id = admin.id
    # Set up test data for editing admin access
    edit_data = {
        'user_id': admin.id,
        'fname': 'UpdatedFirstName',
        'lname': 'UpdatedLastName',
        'email': '<EMAIL>',
        'mobile': '1234567890',
        'role': 2,
        'calling_no': '987654321',
        'agent_id': '1234',
        'tab_access': '1,2,3,4,5,6,7,8,9',
        'regions': '1',
        'notifications': '0,1'
    }

    # Send POST request to edit admin access
    response = client.post('/api/admin/access/edit', data=edit_data, headers=auth_headers)

    # Verify the response status code
    assert response.status_code == 200
    res_data = response.get_json()

    # Verify the success message
    assert res_data['success'] == 1
    assert res_data['message'] == 'Admin access updated and logged successfully'

    # Manually query the database to verify the changes
    updated_admin = Users.query.filter_by(id=admin_id).first()
    updated_admin_access = AdminAccess.query.filter_by(admin_user_id=admin_id).first()

    # Verify that the admin details are updated correctly in the Users table
    assert updated_admin.fname == 'UpdatedFirstName'
    assert updated_admin.lname == 'UpdatedLastName'
    assert updated_admin.email == '<EMAIL>'
    assert updated_admin.mobile == '1234567890'
    assert updated_admin.role == 2

    # Verify the updated access details in the AdminAccess table
    expected_tab_access = sum(1 << int(tab) for tab in '1,2,3,4,5,6,7,8,9'.split(','))
    expected_regions_access = sum(1 << int(region) for region in '1'.split(','))
    expected_notifications_access = sum(1 << int(notif) for notif in '0,1'.split(','))

    assert updated_admin_access.admin_tab_access == expected_tab_access
    assert updated_admin_access.admin_regions_access == expected_regions_access
    assert updated_admin_access.admin_notification_access == expected_notifications_access
    assert updated_admin_access.calling_number == '987654321'
    assert updated_admin_access.agent_id == '1234'


def test_list_admins_success(client, admin_login):
    auth_headers, admin = admin_login

    # Step 1: Manually create a user and an admin access record in the database
    with db.session.begin():
        # Create a new user
        user = Users(
            fname='John',
            lname='Doe',
            mobile='9876543210',
            email='<EMAIL>',
            pwd='9876543210John',  # Password can be hashed in actual usage
            role=1  # Assuming role 1 is for admin
        )
        db.session.add(user)
        db.session.flush()  # Ensure the user gets an ID before the next step

        # Create a new admin access record for the user
        admin_access = AdminAccess(
            admin_user_id=user.id,
            admin_tab_access=sum(1 << int(tab) for tab in '1,2'.split(',')),  # Access to tabs 1 and 2
            admin_regions_access=sum(1 << int(region) for region in '0,1,2'.split(',')),  # Access to regions 0, 1, and 2
            admin_notification_access=sum(1 << int(notif) for notif in '0'.split(',')),  # Access to notification 0
            calling_number='1234567890',
            agent_id=1
        )
        db.session.add(admin_access)

    # Step 2: Prepare test data for the list_admins query
    list_data = {
        'search_query': '',  # Test with empty search
        'search_by': '2',  # Search by user mobile and name
        'regions': '0,1,2',  # Filter by regions
        'role': '1'  # Filter by role
    }

    # Step 3: Send POST request to fetch the admin list
    response = client.post('/api/admin/access/list', data=list_data, headers=auth_headers)

    # Verify the response status code
    assert response.status_code == 200
    res_data = response.get_json()

    # Verify the success message
    assert res_data['success'] == 1
    assert 'admins' in res_data
    admin_list = res_data['admins']

    # Verify that admin list is not empty
    assert len(admin_list) >= 0

    # Verify the structure and content of one admin entry
    for admin_item in admin_list:
        assert 'admin_id' in admin_item
        assert 'user_id' in admin_item
        assert 'name' in admin_item
        assert 'mobile' in admin_item
        assert 'role' in admin_item
        assert 'tab_access' in admin_item
        assert 'regions_access' in admin_item
        assert 'notification_access' in admin_item
        assert 'created_at' in admin_item
        assert 'recent_edited_at' in admin_item

    # Optionally: Add more specific checks for admin list content
    if admin_list:
        first_admin = admin_list[0]
        assert first_admin['regions_access'] == '0,1,2'
        assert first_admin['role'] == 1
