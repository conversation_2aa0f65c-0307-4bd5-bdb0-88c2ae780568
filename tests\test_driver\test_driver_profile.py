import pytest
from flask import Flask
from models.models import Users, Drivers, db,AdminAccess,UserToken,DriverVerify,Bookings,BookDest,Trip,UserTrans,DriverLoc,DriverDetails,BookPending,BookPricing
from utils.security_utils import get_pwd,get_salt
import random
import hashlib
import jsonpickle
import json
import datetime
from io import BytesIO
from sqlalchemy import exc
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
from conftest import driver_bookings,driver_trip

#  API - /api/profile/check

@pytest.mark.parametrize("available_status", [0, 1])
def test_check_driver_available_success(client, driver_login, available_status):
    state = driver_login
    user_id = state['user_id']
    driver_id = state['driver_id']
    access_token = state['access_token']

    # Set the driver's availability to the param value (either 0 or 1)
    try:
        driver = db.session.query(Drivers).filter(Drivers.user == user_id).first()
        driver.available = available_status
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/profile/check', headers=headers)

    assert response.status_code == 200

    # Assert the success value in the response is 1
    response_data = response.get_json()
    assert response_data['success'] == 1

    assert 'available' in response_data
    assert response_data['available'] == available_status  # Available status should match the one we set

# ---------------------------------

#  API - /api/profile/available -----------

@pytest.mark.parametrize("available_status, expected_success", [
    ('1', 1),  # Available
    ('0', 1)   # Unavailable
])
def test_change_driver_available_success(client,driver_login,available_status,expected_success):
    state=driver_login
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    data = {
        'available': available_status,  # Parametrized value for available ('1' or '0')
    }
    response = client.post('/api/profile/available', data=data, headers=headers)

    # Assert the response status code is 200 OK
    assert response.status_code == 200
    response_json = response.get_json()
    assert response_json['success'] == expected_success

# ---------------------------------