from models.models import EstimateRemarks,DriverSearch,Bookings,db,Users
import pytz
from datetime import datetime,timedelta
from unittest.mock import patch
import uuid
from conftest import create_user_and_driver,unique_user_data,create_user

def test_add_remark_success(client, admin_login):
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    admin_id = admin.id
    # Set up test data
    remark_data = {
        'user_id': str(admin.id),
        'remark': 'This is a test remark',
        'change_by_name': 'AdminUser',
        'expiry_time': '10 Oct, 24, 12:30:00',  # Make sure the format matches the code's expectation
        'display': '1',
        'change_by_id': str(admin.id),
        'region': '0'
    }

    # Send POST request to add the remark
    response = client.post('/api/add-remark', data=remark_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)

    # Verify the response status code and success message
    assert response.status_code == 201
    assert res_data['success'] is True

    # Manually query the database to verify the remark was added
    new_remark = EstimateRemarks.query.filter_by(user_id=admin_id).first()

    # Verify the remark details
    assert new_remark is not None
    assert new_remark.remark == 'This is a test remark'
    assert new_remark.change_by_name == 'AdminUser'

    # Parse the expiry time to match the stored format
    expected_expiry_time = datetime.strptime('10 Oct, 24, 12:30:00', '%d %b, %y, %H:%M:%S')

    # Remove timezone info from both expiry_time and expected_expiry_time
    assert new_remark.expiry_time.replace(tzinfo=None) == expected_expiry_time
    assert new_remark.display is True
    assert new_remark.change_by_id == admin_id


def test_get_estimate_details_user_success(client, admin_login):
    auth_headers, admin = admin_login
    ist = pytz.timezone('Asia/Kolkata')
    data = unique_user_data()
    driver_user,driver =create_user_and_driver(data)
    # Mock data
    user_id = 1
    regions = '1,2'
    
    # Mock DriverSearch and Bookings data
    mock_driver_search = DriverSearch(
        id=uuid.uuid4().urn[9:],
        user=user_id,
        timestamp=datetime(2024, 10, 10, 12, 30, 00, tzinfo=pytz.utc),
        date=datetime(2024, 10, 11).date(),
        time=datetime(2024, 10, 11, 9, 0).time(),
        type= 1,
        car_type= '1',
        dur=datetime(2024, 10, 10, 1, 30, 0).time(),
        region=1,
        reflat=28.7041,
        reflong=77.1025
    )
    mock_booking = Bookings(
        user=user_id,
        skey='some_secret_key',
        driver=driver.id,
        lat=0.0,
        long=0.0,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )

    mock_query_results = [(mock_driver_search, mock_booking)]

    # Patch the query to return mock results
    with patch('models.db.session.query') as mock_query:
        mock_query.return_value.filter.return_value.filter.return_value.filter.return_value.order_by.return_value.all.return_value = mock_query_results

        # Send POST request
        response = client.post('/api/admin/user_estimate_details', data={
            'user_id': str(user_id),
            'regions': regions
        }, headers=auth_headers)

        # Verify the response status code
        assert response.status_code == 200
        res_data = response.get_json()

        # Check success value
        assert res_data['success'] == 1

def test_get_user_search_booking_data_success(client,admin_login):
    # Create test data in the database for the query
    auth_headers, admin = admin_login
    admin = db.session.query(Users).filter_by(id=admin).first()
    data = unique_user_data()
    driver_user,driver =create_user_and_driver(data)

    driver_search = DriverSearch(
        id=uuid.uuid4().urn[9:],
        user=admin.id,
        timestamp=datetime(2024, 10, 10, 12, 30, 00, tzinfo=pytz.utc),
        date=datetime(2024, 10, 11).date(),
        time=datetime(2024, 10, 11, 9, 0).time(),
        type= 1,
        car_type= '1',
        dur=datetime(2024, 10, 10, 1, 30, 0).time(),
        region=0,
        reflat=22.5135084,
        reflong=88.402884
    )
    db.session.add(driver_search)

    booking_upcoming = Bookings(
        user=admin.id,
        skey='some_secret_key',
        driver=driver.id,
        lat=22.5135084,
        long=88.402884,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        enddate=(datetime.utcnow() + timedelta(days=1)).date(),  # Future booking
        endtime=(datetime.utcnow() + timedelta(days=1)).time(),  # Time after now,
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
    db.session.add(booking_upcoming)

    booking_completed = Bookings(
        user=admin.id,
        skey='some_secret_key2',
        driver=driver.id,
        lat=22.5135084,
        long=88.402884,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        enddate=(datetime.utcnow() - timedelta(days=1)).date(),  # Past booking
        endtime=(datetime.utcnow() - timedelta(days=1)).time(),  # Time before now
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
    db.session.add(booking_completed)

    remark = EstimateRemarks(
        user_id=admin.id,
        remark="Test remark",
        expiry_time=datetime.utcnow() + timedelta(days=1),  # Future expiry
        display=True,
        change_by_name= 'AdminUser',
        change_by_id= str(admin.id),
    )
    db.session.add(remark)
    db.session.commit()

    # Set up test data for the POST request
    request_data = {
        'page': 1,
        'per_page': 10,
        'filter': 1,
        'regions': '0'  # Filter by region 0
    }

    # Send POST request to the endpoint
    response = client.post('/api/user_search_bookings_data', data=request_data,headers=auth_headers)

    # Check if the response is successful
    assert response.status_code == 200
    res_data = response.get_json()

    # Assert success response
    assert res_data['success'] == 1
    # assert res_data['length'] == 1 
    # user_data = res_data['data'][0]

    # # Validate user data
    # assert user_data['userid'] == admin.id
    # assert user_data['contactNumber'] == admin.mobile
    # assert user_data['region'] == 0
    # assert user_data['remark'] == 'Test remark'

