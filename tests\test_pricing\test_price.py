from datetime import datetime, timedelta

# When booking is created

def test_price_redis_fail(client, customer_login):  
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5797',
        'dest_long': '88.4598',
        'dest_loc': "Howrah",
        'region': 0,
        'type': 3,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_round_trip(client, customer_login): 
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5797',
        'dest_long': '88.4598',
        'region': 0,
        'type': 1,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_os(client, customer_login):   
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': "Lucknow",
        'region': 0,
        'type': 2,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_oneway(client, customer_login):  
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5797',
        'dest_long': '88.4598',
        'dest_loc': "Howrah",
        'region': 0,
        'type': 3,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_minos(client, customer_login):   
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': "Lucknow",
        'region': 0,
        'type': 4,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_os_oneway(client, customer_login):  
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': "Lucknow",
        'region': 0,
        'type': 5,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1

def test_price_minios_oneway(client, customer_login):  
    auth_headers, user = customer_login()
    search_data = {
        'reflat': '22.5875',
        'reflong': '88.4541',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        'dest_lat': '22.5135084',
        'dest_long': '88.402884',
        'dest_loc': "Lucknow",
        'region': 0,
        'type': 6,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'id' in res_data
    assert res_data['id'] != -1
