#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  app.py
#
# <AUTHOR> <EMAIL>
#
#  This program is free software; you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation; either version 2 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program; if not, write to the Free Software
#  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
#  MA 02110-1301, USA.
#
#from flask_sqlalchemy import SQLAlchemy

from flask_sqlalchemy import SQLAlchemy
from pymongo import MongoClient
from firebase_admin import credentials, initialize_app, firestore, _apps

db = SQLAlchemy()

# Actual storage
_mongo_client = None
_mdb = None
_fb_db = None

class _Proxy:
    def __init__(self, name):
        self._name = name
        
    def _get_obj(self):
        obj = globals().get(f"_{self._name}")
        if obj is None:
            raise Exception(f"{self._name} not initialized. Call init_dbs(app) first.")
        return obj
    
    def __getattr__(self, item):
        # delegate normal attribute access
        return getattr(self._get_obj(), item)

    def __call__(self, *args, **kwargs):
        obj = globals()[f"_{self._name}"]
        if obj is None:
            raise Exception(f"{self._name} not initialized. Call init_dbs(app) first.")
        return obj(*args, **kwargs)

    def __getitem__(self, item):
        return self._get_obj()[item]

    def __call__(self, *args, **kwargs):
        return self._get_obj()(*args, **kwargs)

mongo_client = _Proxy("mongo_client")
mdb = _Proxy("mdb")
fb_db = _Proxy("fb_db")

def init_dbs(app):
    global _mongo_client, _mdb, _fb_db

    db.init_app(app)

    # Mongo
    _mongo_client = MongoClient(app.config['MONGO_URI'])
    mongo_name = app.config.get("MAIN_MONGO_DB")
    _mdb = _mongo_client[mongo_name]
    print(_mdb, "set to")

    # Firebase
    if not _apps:
        cred = credentials.Certificate(app.config['FIREBASE_CRED_PATH'])
        initialize_app(cred, {
            'databaseURL': app.config['FIREBASE_DB_URL']
        })
    _fb_db = firestore.client()

    app.mdb = _mdb
    app.db = db
