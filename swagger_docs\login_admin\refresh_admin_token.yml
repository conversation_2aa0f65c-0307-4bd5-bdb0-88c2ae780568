tags:
  - Login_admin
summary: Refresh Admin Tokens
description: >
  This endpoint refreshes the admin's access token using the refresh token stored in the cookies. It requires the refresh token to be valid and not revoked, and it checks the CSRF token to prevent cross-site request forgery.
parameters:
  - in: header
    name: X-CSRF-Token
    type: string
    required: true
    description: CSRF token to verify the validity of the request.
    example: "sometoken12345"
  - in: cookie
    name: refresh_token_cookie
    type: string
    required: true
    description: The refresh token stored in cookies, used to generate a new access token.
responses:
  200:
    description: Access token successfully refreshed.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        refresh:
          type: boolean
          example: true
  400:
    description: Missing or invalid CSRF token.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Missing CSRF token
        refresh:
          type: boolean
          example: false
  401:
    description: Unauthorized due to missing or revoked refresh token.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Token has been revoked or is invalid
        refresh:
          type: boolean
          example: false
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
        refresh:
          type: boolean
          example: false
