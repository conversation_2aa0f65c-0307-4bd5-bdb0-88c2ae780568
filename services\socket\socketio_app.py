from flask_socketio import emit, join_room, leave_room, disconnect, rooms, close_room
from flask import current_app as app
from redis_config import execute_with_fallback
from flask import request, jsonify
import json
from jwt import ExpiredSignatureError, InvalidTokenError
import jwt
from datetime import datetime, timezone
import base64
from flask_jwt_extended import (
    jwt_required, create_access_token,
    create_refresh_token,
    get_jwt_identity, set_access_cookies, set_refresh_cookies, get_jwt, decode_token
)
import time
from socketio_config import socketio
from concurrent.futures import ThreadPoolExecutor
from gevent import monkey, spawn, sleep
import gevent
import random
from services.socket.socketio_b2b import add_to_room, get_unexpired_notifications

notification_greenlets = []

def send_developer_notification(exception):
    for username in [10,8,2]:
        notification = {
            'id':random.randint(1, 2000),
            'type': 'Admin notice',
            'username': username,
            'content': exception,
            'imageUrl': '/assets/icons/bticons/warn.svg',
            'timestamp': int(time.time() * 1000)
        }
        spawn(send_notification_to_username, notification, username, 127)

def send_notification_to_channel(notificationdata, room_name):
    notification_type = get_notification_type_from_room_name(room_name=room_name)

    notification_id = create_notification(notification_data=notificationdata, room_name=room_name, notification_type=notification_type)
    if(not notification_id):
        return { "status": f"Failed to send notificaiton"}
    notificationdata['notification_id'] = notification_id
    notificationdata['sound'] = 0

    socketio.emit('channel_data', {'info': notificationdata, 'channel': room_name}, room=room_name)

def send_notification_to_username(notificationdata, user, notification_type):

    notification_id = create_notification(notification_data=notificationdata, room_name='room_name', notification_type=notification_type, to_specific_user= True, user_name=user)
    if(not notification_id):
        return { "status": f"Failed to send notificaiton"}
    notificationdata['notification_id'] = notification_id
    notificationdata['sound'] = 0
    sids = execute_with_fallback('smembers', f"user:{user}")
    for sid in sids:
        socketio.emit('channel_data', {'info': notificationdata, 'channel': 'Notice'}, to=sid)

    return { "status": f"Notification sent to channel {user} subscribers" }

#keeping extra to not mess up in working code will create common function later
def send_notification_to_all(notificationdata, type, region):
    notification_id = create_notification(notification_data=notificationdata, room_name='room_name', notification_type=type, to_all=True)
    if(not notification_id):
        return { "status": f"Failed to send notificaiton"}
    notificationdata['notification_id'] = notification_id
    notificationdata['sound'] = 0
    socketio.emit('channel_data', {'info': notificationdata, 'channel': 'Notice', 'region': region})
    return { "status": f"Notification sent to all users" }

def live_update_to_channel(data, room_name, type =None, region = None, channel = 'live_data'):
    if not type:
        type = room_name
    socketio.emit(channel, {'info': data, 'channel': room_name, 'type':type, 'region':region,'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, room=room_name)
    return {"status": f"Live update sent to channel {room_name} subscribers"}

notificationMapping = {
    1: 'Low Rating', # persistant until user is not sending confirmation that they read this notification
    2: 'Priority Booking', #1 hours valid expiry
    3: 'Cancelled by User', # 4 hours valid expiry
    4: 'Cancelled by Driver', # 4 hours valid expiry
    5: 'Driver Register', # 8 hours valid expiry
    6: 'Customer Register', # 8 hours valid expiry
}
notification_expiry = {
    1: 372800,  # Persistent to 3 days
    2: 3600,  # 1 hour
    3: 14400, # 4 hours
    4: 14400, # 4 hours
    5: 186400, # 24 hours
    6: 28800, # 8 hours
    127: 186400, # 24 hours
}

@socketio.on("connect")
def handle_connect():
    try:
        global notification_greenlets

        source = request.args.get('source', '1')
        token = request.args.get('token')
        print("SOuce of conn", source, flush=True)

        if not token:
            emit('response', {'data': 'No token provided'}, to=request.sid)
            disconnect()
            return

        decoded_token = jwt.decode(
            token,
            app.config['JWT_SECRET_KEY'],
            algorithms=["HS256"]
        )
        if decoded_token.get('type') != 'access':
            emit('response', {'data': 'Invalid token type'}, to=request.sid)
            disconnect()
            return

        exp = decoded_token.get('exp')

        if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
            emit('response', {'data': 'Token has expired'}, to=request.sid)
            disconnect()
            return

        if source == '1': #1 means request came from admin portal
            username = decoded_token['id']
            notification_access = decoded_token.get('notification_access', 0)
            regions_access = decoded_token.get('regions_access', 0)

            execute_with_fallback('sadd', f'user:{username}', request.sid)
            g1 = spawn(send_unread_notifications, username, request.sid)
            notification_greenlets.extend([g1])
            g1.join()
            notification_greenlets = []

            channels_subscribed = []
            commands = []
            for notif_bit, channel_name in notificationMapping.items():
                if channel_name == 'Customer Register':
                    room_name = f"{channel_name}:{0}"
                    join_room(room_name, sid=request.sid)
                    commands.append(("sadd", (f'room:{room_name}', username), {}))
                    channels_subscribed.append(room_name)
                    continue
                if notification_access & (1 << notif_bit):
                    for region_bit in range(32):
                        if regions_access & (1 << region_bit):
                            room_name = f"{channel_name}:{region_bit}"
                            join_room(room_name, sid=request.sid)
                            commands.append(("sadd", (f'room:{room_name}', username), {}))
                            channels_subscribed.append(room_name)

            execute_with_fallback("pipeline", commands=commands)
            sid = request.sid
            execute_with_fallback('set', f"sid:{sid}", username)
            emit('response', {'data': 'Connected and authenticated', 'channels_subscribed': 'channels_subscribed'}, to=request.sid)
        elif source == '2':
            session_id = request.sid
            aff_id = decoded_token.get('aff_id')
            rep_id = decoded_token.get('id')

            if aff_id:
                room = f"affiliate_{aff_id}"
                add_to_room(room, session_id, rep_id, aff_id)
                # Fetch unexpired notifications
                active_notifications = get_unexpired_notifications(aff_id, rep_id)
                # Send active notifications to the connected user
                emit('notification_history', {'notifications': active_notifications}, to=session_id)

            emit('response', {'data': f'Connected and rooms: {aff_id}'}, to=session_id)
        else:
            pass
    except ExpiredSignatureError:
        print('ExpiredSignatureError', flush=True)
        emit('response', {'data': 'Token has expired'}, to=request.sid)
        disconnect()

    except InvalidTokenError as e:
        emit('response', {'data': f'Invalid token: {e}'}, to=request.sid)
        disconnect()

    except Exception as e:
        emit('response', {'data': f'Failed to authenticate: {e}'}, to=request.sid)
        disconnect()

@socketio.on("disconnect")
def handle_disconnect():
    session_id = request.sid
    room_name = execute_with_fallback("get", f"session_rooms:{session_id}")
    if room_name:
        leave_room(room_name)
        affiliate_sets = execute_with_fallback("smembers", f"session_affiliate_sets:{session_id}")
        if affiliate_sets:
            for aff_key in affiliate_sets:
                live_room = f"live_affiliate_{aff_key.split('_')[2]}"
                leave_room(live_room)
                execute_with_fallback("srem", aff_key, session_id)
            execute_with_fallback("delete", f"session_affiliate_sets:{session_id}")

        execute_with_fallback("hdel", f"room_reps:{room_name}", session_id)
        execute_with_fallback("delete", f"session_rooms:{session_id}")
        print(f"User (Session ID: {session_id}) disconnected from {room_name}", flush=True)

        emit('response', {'data': f'User {session_id} disconnected from {room_name}'}, to=session_id)
    else:
        sid = request.sid
        execute_with_fallback('delete', f"sid:{sid}")
        try:
            rooms = execute_with_fallback('scan_iter', 'room:*')

            commands = []
            for room in rooms:
                if room != sid:
                    leave_room(room,sid=sid)
            username = execute_with_fallback('get', f"sid:{sid}")
            execute_with_fallback('srem', f"user:{username}",sid)
            execute_with_fallback('delete', f"sid:{sid}")

        except Exception as e:
            print(f"Error during disconnect handling: {e}",flush=True)

@socketio.on('join_rooms')
def handle_join_rooms(data):
    rooms = data.get('rooms', [])
    username = data.get('username')
    commands = []
    for room in rooms:
        join_room(room, sid=request.sid)
        commands.append(("sadd", (f'room:{room}', username), {}))
    if commands:
        execute_with_fallback("pipeline", commands=commands)
    return {"status": "success", "joined_rooms": rooms}

@socketio.on('leave_room')
def handle_leave_room(data):
    room = data['room']
    leave_room(room)
    room_members = execute_with_fallback('smembers', f'room:{room}')
    if not room_members:
        execute_with_fallback('delete', f'room:{room}')

def create_notification( notification_data, room_name, notification_type = 5, to_specific_user = False, user_name = 0, to_all = False):
    try:
        global_id = execute_with_fallback('incr', 'global_notification_counter')

        notification_id = f"{notification_type}:{global_id}"
        key = f"notification:{notification_id}"

        expiry = notification_expiry.get(notification_type)
        current_timestamp = int(time.time() * 1000)
        notification_data['expiry'] = current_timestamp + (expiry * 1000) if expiry else None
        notification_data['notification_id'] = notification_id
        if expiry:
            execute_with_fallback('set', key, json.dumps(notification_data), ex=expiry)
        else:
            execute_with_fallback('set', key, json.dumps(notification_data))

        if to_specific_user:
            execute_with_fallback('hset', f"user_unread:{user_name}", notification_id, 1)
            return notification_id
        if to_all:
            execute_with_fallback('sadd', 'notifications:127', key)
            return notification_id
        users = execute_with_fallback('smembers', f"room:{room_name}")
        if users:
            commands = []
            for user in users:
                commands.append(("hset", (f"user_unread:{user}", notification_id, 1), {}))
            if commands:
                execute_with_fallback("pipeline", commands=commands)

        return notification_id
    except Exception as e:
        return None

@socketio.on('notification_read')
def handle_notification_read(data):
    try:
        username = data.get('username')
        notification_id = data.get('notification_id')

        notification_key = f"notification:{notification_id}"

        notification_data = execute_with_fallback('get', notification_key)

        if not notification_data:
            execute_with_fallback('delete', f"notification_users:{notification_id}")

            execute_with_fallback('hdel', f"user_unread:{username}", notification_id)

            return {"status": "success", "notification_id": notification_id}

        user_read_notify_id = execute_with_fallback('hdel', f"user_unread:{username}", notification_id)
        return {"status": "success", "notification_id": notification_id}
    except Exception as e:
        print('error', str(e),flush=True)

def process_notification(username, notification_id):
    try:
        notification_key = f"notification:{notification_id}"
        notification_data = execute_with_fallback('get', notification_key)

        if not notification_data:
            execute_with_fallback('hdel', f"user_unread:{username}", notification_id)
            return
        execute_with_fallback('hdel', f"user_unread:{username}", notification_id)

    except Exception as e:
        print(f"Error processing notification {notification_id}: {e}", flush=True)

@socketio.on('real_all')
def handle_real_all(data):
    try:
        username = data.get('username')
        notification_ids = data.get('notification_ids')

        if not username or not notification_ids:
            return {"status": "error", "message": "Username or notification IDs not provided"}

        greenlets = [gevent.spawn(process_notification, username, notification_id) for notification_id in notification_ids]
        gevent.joinall(greenlets)

        return {"status": "success", "message": "All notifications marked as read"}
    except Exception as e:
        print(f"Error in handle_real_all: {e}", flush=True)
        return {"status": "error", "message": "An error occurred"}

def send_unread_notifications(username, sid):
    unread_notifications = execute_with_fallback('hkeys', f"user_unread:{username}")
    if unread_notifications:
        for notification_id in unread_notifications:
            notification_data = execute_with_fallback('get', f"notification:{notification_id}")
            if notification_data:
                notification_type, _ = notification_id.split(":", 1)
                room_name = notificationMapping.get(int(notification_type))
                notification_data = json.loads(notification_data)
                notification_data['sound'] = 1
                socketio.emit('channel_data', {'info': notification_data, 'channel': room_name}, to=sid)
            else:
                execute_with_fallback('hdel', f"user_unread:{username}", notification_id)

    keys = execute_with_fallback('smembers', 'notifications:127')
    if keys:
        with ThreadPoolExecutor(max_workers=3) as executor:
            notification_data = list(executor.map(
                lambda key: execute_with_fallback('get', key),
                keys
            ))
        for data in notification_data:
            if data:
                notification = json.loads(data)
                socketio.emit('channel_data', {
                    'info': notification,
                    'channel': 'admin_notice'
                }, to=sid)

def get_notification_type_from_room_name(room_name):
    notificationMapping = {
        1: 'Low Rating',
        2: 'Priority Booking',
        3: 'Cancelled by User',
        4: 'Cancelled by Driver',
        5: 'Driver Register',
        6: 'Customer Register',
    }
    for type_id, notification_name in notificationMapping.items():
        if notification_name in room_name:
            return type_id
    return None