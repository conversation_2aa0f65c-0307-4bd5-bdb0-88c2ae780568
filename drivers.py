#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  drivers.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
import json

import jsonpickle
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt
)

from affiliate_api.hook.utils import (
    ZOOMCAR_STATE_CREATED,
    ZOOMCAR_STATE_ASSIGNED, ZOOMCAR_STATE_ACCEPTED,
    ZOOMCAR_STATE_REJECTED, ZOOMCAR_STATE_CANCELED,
    _zoomcar_change_state
)
from sqlalchemy import or_, and_, extract, exc, func,select,create_engine
from sqlalchemy.pool import NullPool
from sqlalchemy.orm import aliased
from google.cloud import firestore
from dateutil.relativedelta import relativedelta
from _rtdb import _update_user_pending, _update_driver_pending
import _sms
from _fcm import send_fcm_msg
from _ops_message import send_slack_msg
from payments import PaymentType
from trip import convert_to_semihours
import math
from booking_params import BookingParams
from _utils_booking import get_car_type, driver_accept_booking, get_book_code, get_car_type_skey, booking_has_trip
from _utils_booking import max_driver_owed, _mark_zoomcar_booking_accepted, _get_zoomcar_booking_state, check_ongoing_time
from register_driver import create_driver_exp
from price import Price, PriceOutstation
from db_config import fb_db
from models import Users, Drivers, Bookings, Trip, DriverDetails, BookPending, DriverLog, BookDest, DriverIdle, DriverVerify, BookingCancelled
from models import db, DriverCancelled, C24Bookings, C24Rep, UserTrans, RevvBookings, GujralBookings, BookingAlloc, DriverSkill
from models import OLXBookings, OLXRep, ZoomcarBookings, ZoomcarRep, CardekhoBookings, CardekhoRep, BhandariBookings, BhandariRep, BookPricing
from models import MahindraBookings, MahindraRep, RevvV2Bookings, RevvV2Rep, SpinnyBookings, SpinnyRep, DriverTrans, DriverLoc, DriverSetLoc
from models import PrideHondaBookings, PrideHondaRep, DriverInfo
from _utils import complete, strfdelta, get_safe, get_dt_ist,combine_and_convert_to_local,convert_to_local_time
from _utils_acc import account_enabled, validate_role, get_driver_user_id
import _sms
import time as tm
from call_masking import assign_unique_did
from flasgger import swag_from
from socketio_app import live_update_to_channel
from live_update_booking import send_live_update_of_booking
from gevent import spawn, sleep
import pytz
from sqlalchemy.orm import sessionmaker
from flask import current_app as app
from affiliate_b2b.affiliate_models import Affiliate,AffiliateRep,AffBookingLogs,AffiliateCollections

# def convert_to_ist(dt):
#     if dt is None:
#         return None
#     ist = pytz.timezone('Asia/Kolkata')
#     return dt.astimezone(ist)

# def combine_and_convert_to_ist(date_val, time_val):
#     combined = datetime.datetime.combine(date_val, time_val)
#     return convert_to_ist(combined) # timedelta(hours=5, minutes=30)

def split_date_time(dt):
    return dt.date(), dt.time()

drivers = Blueprint('drivers', __name__)

class DriverTransWrap:
    def __init__(self, id, amt, method, dt):
        self.id = id
        self.amt = amt
        self.credit = amt > 0
        self.text = str(method)
        self.timestamp = str(dt)

class BookingDriver:
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate, car_type, loc,
                 mobile, did_no="", trip_type=1, days=0, due=0, payment=0, driver_rating=0, driver_type=0, pre_tax=0, sgst=0, cgst=0, insurance=0, cash=0, region=-1,
                 code=None):
        self.id = id
        self.name = name
        self.startdate = str(startdate).strip()
        self.starttime = str(starttime).strip()
        self.endtime = str(endtime).strip()
        self.enddate = str(enddate).strip()
        self.dur = str(dur)
        self.lat = str(lat)
        self.long = str(long)
        self.estimate = round(estimate, 2)
        self.car_type = car_type
        if loc:
            self.loc = loc
        else:
            self.loc = 'N/A'
        self.mobile = mobile
        self.did_no = did_no
        self.trip_type = trip_type
        if not code:
            self.code = get_book_code(id)
        else:
            self.code = code
        self.days = days
        self.due = due
        self.payment = payment
        self.driver_rating = driver_rating
        self.driver_type = driver_type
        self.pre_tax = round(pre_tax)
        self.sgst = round(sgst)
        self.cgst = round(cgst)
        self.sgst_pct = Price.SGST
        self.cgst_pct = Price.CGST
        self.insurance = insurance
        self.insurance_ch = Price.get_insurance_ch(id)
        self.cash = round(cash)
        self.region = region


class BookingDriverTrip(BookingDriver):
    def __init__(self, trip_id, booking_id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate,
                 car_type, loc, mobile, did_no="", elapsed=0, trip_type=1, days=0, payment=0, driver_type=0, insurance=0, region=-1, code=None, status=0,
                 dest_lat=0.0, dest_long=0.0, dest_loc='N/A'):
        super().__init__(booking_id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate,
                         car_type, loc, mobile, did_no, payment=payment, driver_type=driver_type, insurance=insurance, region=region, code=code)
        self.trip_id = trip_id
        self.book_id = booking_id
        self.elapsed = elapsed
        self.trip_type = trip_type
        self.due = 0
        self.days = days
        self.status = status
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc


class BookingDriverDest(BookingDriver):
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate, car_type, loc,
                 mobile, did_no="", trip_type=1, days=0, dest_lat=0.0, dest_long=0.0, dest_loc='N/A', payment=0, driver_rating=0, driver_type=0, insurance=0, due=0, region=-1,
                 code=None):
        super().__init__(id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate,
                         car_type, loc, mobile, did_no=did_no, payment=payment, driver_rating=driver_rating, driver_type=driver_type, insurance=insurance, due=due, region=region,
                         code=code)
        self.trip_type = trip_type
        self.days = days
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc

class B2BDriver(BookingDriverDest):
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate, car_type, loc,
                 mobile, did_no="", trip_type=1, days=0, dest_lat=0.0, dest_long=0.0, dest_loc='N/A', payment=0, driver_rating=0, driver_type=0,
                 veh_no="",veh_model="", b2b_type=0, b2b_dest_mob=0, appt_id="", trip_id=-1,  due=0, region=-1,code=None,source_name="",source_mob=0,dest_name="",affiliate_name=""):
        super().__init__(id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate,
                             car_type, loc, mobile, did_no, payment=payment, driver_rating=driver_rating, driver_type=driver_type,
                             trip_type=trip_type, days=days, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                             due=due, region=region, code=code)
        self.veh_no = veh_no
        self.appt_id = appt_id
        self.veh_model = veh_model
        self.b2b_type = b2b_type
        self.dest_mob = b2b_dest_mob
        self.trip_id = trip_id
        self.source_name=source_name
        self.source_mob=source_mob
        self.dest_name=dest_name
        self.affiliate_name=affiliate_name
        if not code:
            self.code = get_book_code(trip_id)
        else:
            self.code = code


class B2BDriverTrip(B2BDriver):
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate, car_type, loc,
                 mobile, did_no="", trip_type=1, days=0, dest_lat=0.0, dest_long=0.0, dest_loc='N/A', payment=0,
                 driver_rating=0, driver_type=0, veh_no="", veh_model="", b2b_type=0, b2b_dest_mob=0, appt_id="",
                 trip_id=-1, elapsed="", region=-1, code=None, status=0, trip_start_images=None, trip_stop_images=None,
                 source_name="", source_mob=0):
        super().__init__(id, name, startdate, starttime, enddate, endtime, dur, lat, long, estimate, car_type, loc,
                         mobile, did_no, trip_type, days, dest_lat, dest_long, dest_loc, payment, driver_rating,
                         driver_type, veh_no, veh_model, b2b_type, b2b_dest_mob, appt_id, trip_id, due=0,
                         region=region, code=code, source_name=source_name, source_mob=source_mob)
        self.book_id = id
        self.elapsed = elapsed
        self.status = status
        self.trip_start_images = trip_start_images
        self.trip_stop_images = trip_stop_images


# no validation required
def _add_driver_idle(driver_id, idle_date):
    d = DriverIdle.query.filter(DriverIdle.driver_id == driver_id). \
                    filter(DriverIdle.idle_date == idle_date)
    if not d.first():
        d = DriverIdle(driver_id, idle_date)
        db.session.add(d)
        db.session.commit()
    return

# no validation required
def _delete_driver_idle(driver_id, idle_date):
    d = DriverIdle.query.filter(DriverIdle.driver_id == driver_id). \
                    filter(DriverIdle.idle_date == idle_date)
    if d.first():
        d.delete()
        db.session.commit()
    return

# Accept trip for given driver id, booking id pair.
# Booking ID can be sent as raw booking id or internal B2B id.
def cust_driver_accept_booking(booking_id, driver):
    with app.app_context():
        engine = db.engine.execution_options(timeout=30)
        Session = sessionmaker(bind=engine)
        session = Session()
        driver_user = get_driver_user_id(driver)

        # Initialize these variables to be used later.
        user_id = None
        book_code = None
        booking_region = None
        is_user = False

        try:
            # Begin the main transaction. The context managers will handle commit or rollback.
            with session.begin(),session.begin_nested():
                # Retrieve and lock the booking record.
                booking = (
                    session.query(Bookings)
                    .filter(Bookings.id == booking_id)
                    .with_for_update()
                    .first()
                )
                if not booking:
                    print("Could not accept: Booking does not exist")
                    return {'success': -1, "message": "Booking does not exist", 'code': 201}

                # Capture key booking values for notifications.
                user_id = booking.user      # Either an ID or adjust accordingly (e.g. booking.user.id)
                book_code = booking.code
                booking_region = booking.region

                # Retrieve the booking user for further use.
                book_user = booking.user

                booking_start_minus_n_minutes, booking_end_plus_n_minutes = check_ongoing_time(booking)

                # Build the set of drivers with active bookings overlapping the current booking time window.
                booked = set(
                    driver_row.driver
                    for driver_row in session.query(Bookings)
                        .filter(Bookings.valid > 0)
                        .filter(
                            or_(
                                func.timestamp(Bookings.startdate, Bookings.starttime) <= booking_end_plus_n_minutes,
                                Bookings.startdate < booking.enddate,
                            )
                        )
                        .filter(
                            or_(
                                func.timestamp(Bookings.enddate, Bookings.endtime) >= booking_start_minus_n_minutes,
                                Bookings.enddate > booking.enddate,
                            )
                        )
                        .all()
                )
                if driver in booked:
                    # Delete any pending booking for this driver and booking id.
                    session.query(BookPending).filter(
                        BookPending.driver == driver,
                        BookPending.book_id == booking_id
                    ).delete(synchronize_session=False)
                    # No explicit commit here; the context manager will handle it.
                    print("Could not accept: Already have booking")
                    return {'success': -5, "message": "Already have booking"}

                if booking.valid < 0:
                    print("Could not accept: Booking is cancelled by user")
                    return {'success': -6, "message": "Booking is cancelled by user"}
                if booking.valid == 1 and booking.driver != 1:
                    print("Could not accept: Booking is allocated")
                    return {'success': -5, "message": "Booking is allocated"}

                # Retrieve related pricing information.
                check_exists = (
                    session.query(BookPricing, Bookings)
                    .filter(Bookings.id == booking_id)
                    .filter(BookPricing.book_id == booking_id)
                    .first()
                )
                if not check_exists:
                    print("Could not accept: Booking does not exist")
                    return {'success': -1, "message": "Booking does not exist"}

                # Determine the unique did from the current bookings.
                cust_current_bookings = (
                    session.query(Bookings)
                    .filter(Bookings.user == book_user)
                    .filter(Bookings.did_release == False)
                    .all()
                )
                driver_current_bookings = (
                    session.query(Bookings)
                    .filter(Bookings.driver == driver)
                    .filter(Bookings.did_release == False)
                    .all()
                )
                cust_did = [cust_book.did for cust_book in cust_current_bookings]
                driver_did = [drv_book.did for drv_book in driver_current_bookings]
                unique_did = assign_unique_did(cust_did, driver_did)

                # Update all required booking fields.
                session.query(Bookings).filter(Bookings.id == booking_id).update({
                    Bookings.estimate:         check_exists[0].estimate,
                    Bookings.estimate_pre_tax:   check_exists[0].est_pre_tax,
                    Bookings.insurance_cost:     check_exists[0].insurance_ch,
                    Bookings.valid:              1,
                    Bookings.driver:             driver,
                    Bookings.did:                unique_did,
                    Bookings.did_release:        False,
                }, synchronize_session=False)

                # Handle the pending booking for the driver.
                pending_query = session.query(BookPending).filter(BookPending.book_id == booking_id)
                try:
                    pending_record = pending_query.filter(BookPending.driver == driver).first()
                    pending_phase = pending_record.phase if pending_record else -1
                    pending_score = pending_record.score if pending_record else -99
                except Exception:
                    pending_phase = -1
                    pending_score = -99

                pending_query.update({BookPending.valid: 0}, synchronize_session=False)

                # Create a booking allocation record.
                ba = BookingAlloc(booking_id, driver, driver_user, phase=pending_phase, score=pending_score)
                session.add(ba)

                # All changes will be committed automatically at the end of the context managers.

                # Check booking type to decide if further notification updates are needed.
                is_user = check_exists[1].type < BookingParams.TYPE_B2B

                if is_user:
                    _update_user_pending(booking.user)
                    _update_driver_pending(driver)
                    try:
                        fb_db.collection(u'book_pending').document(str(driver)).update({
                            str(booking_id): firestore.DELETE_FIELD
                        })
                    except Exception:
                        pass

        # After transactional operations have committed, perform the notification queries.
            driver_det = session.query(Users).filter(Users.id == driver_user).first()
            driver_name = driver_det.get_name()

            if is_user and user_id:
                fb_db.collection(u'trip_set').document(str(user_id)).set(
                    {str(booking_id): driver_name}, merge=True)

            target_user = session.query(Users).filter(Users.id == user_id).first()
            user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
            msg_content = (
                "Hi " + target_user.get_name() +
                "! Your trip with DRIVERS4ME (ID: #" + str(book_code) +
                ") has been assigned to " + driver_name +
                " (" + str(driver_det.mobile) + ")"
            )

            msg_content_json2 = {
                    "name": target_user.get_name(),
                    "code": book_code,
                    "driver-name": driver_name,
                    "driver-mobile": str(driver_det.mobile)
                }
            # Create a human-readable message a/c template
            message = (
                    f"Hi, {msg_content_json2['name']}! Your trip with Drivers4Me (ID: {msg_content_json2['code']}) has been assigned "
                    f"to {msg_content_json2['driver-name']} ({msg_content_json2['driver-mobile']})."
                )
            # response = _sms.send_bulk_message_gupshup(
            #         phone_numbers=[str(user_mobile)],
            #         message=message,
            #         mask= _sms.MASK,
            #         dltTemplateId=_sms.TEMPLATE_ID_MAPPING['trip-allocated'],
            #         principalEntityId= _sms.PRINCIPAL_ENTITY_ID
            #     )
            
            send_slack_msg(1, msg_content)
            send_live_update_of_booking(booking_id, booking_region)

            if is_user:
                smalltext = "Your driver for booking #" + str(book_code) + ": " + driver_name
                send_fcm_msg(user_id, title="Drivers4Me - Booking confirmed!", smalltext=smalltext, bigtext=msg_content)

            return {'success': 1, 'msg': 1}

        except Exception as e:
            session.rollback()
            print("Encountered exception during booking acceptance:", e)
            return {'success': -1, 'message': str(e)}
        finally:
            session.close()

@drivers.route('/api/driver/confirm', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/confirm_book.yml')
@jwt_required()
def confirm_book():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        print("Could not accept: Not a driver")
        return jsonify({'success': -1, "message": "Not a driver"}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        print("Could not accept: Driver account not enabled")
        return jsonify({'success': -1, "message": "Driver account not enabled"}), 401
    driver = Drivers.query.filter_by(user=driver_user).first().id
    try:
        if not complete(request.form, ['booking_id']):
            return jsonify({'success': -1, "message": "Could not find booking id"}), 201
        booking_id = int(request.form['booking_id'])
        #driver_due = DriverDetails.query.filter_by(driver_id=driver).first()
        dd = DriverDetails.query.filter_by(driver_id=driver).first()
        if dd.wallet + dd.withdrawable < max_driver_owed(booking_id):
            print("Could not accept: Due is above threshold")
            return jsonify({'success': -2, 'message': "Due is above threshold"})
        c24 = int(get_safe(request.form, 'c24', 0))
        if c24 == 1:
            c24_b = db.session.query(C24Bookings).filter(C24Bookings.id == booking_id).first()
            if c24_b:
                booking_id = c24_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        zc = int(get_safe(request.form, 'zoomcar', 0))
        if zc == 1:
            zc_b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.id == booking_id).first()
            if zc_b:
                booking_id = zc_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        olx = int(get_safe(request.form, 'olx', 0))
        if olx == 1:
            olx_b = db.session.query(OLXBookings).filter(OLXBookings.id == booking_id).first()
            if olx_b:
                booking_id = olx_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        cardekho = int(get_safe(request.form, 'cardekho', 0))
        if cardekho == 1:
            cardekho_b = db.session.query(CardekhoBookings).filter(CardekhoBookings.id == booking_id).first()
            if cardekho_b:
                booking_id = cardekho_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        bhandari = int(get_safe(request.form, 'bhandari', 0))
        if bhandari == 1:
            bhandari_b = db.session.query(BhandariBookings).filter(BhandariBookings.id == booking_id).first()
            if bhandari_b:
                booking_id = bhandari_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        mahindra = int(get_safe(request.form, 'mahindra', 0))
        if mahindra == 1:
            mahindra_b = db.session.query(MahindraBookings).filter(MahindraBookings.id == booking_id).first()
            if mahindra_b:
                booking_id = mahindra_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        revv_v2 = int(get_safe(request.form, 'revv_v2', 0))
        if revv_v2 == 1:
            revv_v2_b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.id == booking_id).first()
            if revv_v2_b:
                booking_id = revv_v2_b.ref
            else:
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        spinny = int(get_safe(request.form, 'spinny', 0))
        if spinny == 1:
            spinny_b = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == booking_id).first()
            if spinny_b:
                booking_id = spinny_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        pridehonda = int(get_safe(request.form, 'pridehonda', 0))
        if pridehonda == 1:
            pridehonda_b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.id == booking_id).first()
            if pridehonda_b:
                booking_id = pridehonda_b.ref
            else:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -3, 'message': "Invalid B2B id"})
        booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
        book_type=BookingParams.determine_booking_type(booking.type)
        # affiliate = int(get_safe(request.form, 'affiliate', 0))
        if book_type == 1 and not zc and not cardekho and not bhandari and not mahindra and not revv_v2 and not olx and not spinny and not pridehonda:
            # print(affiliate, booking_id, driver)
            ret_val = cust_driver_accept_booking(booking_id, driver)
        elif book_type == 0:
                print("B@2b New",flush=True)
                affiliate_id = db.session.query(AffBookingLogs.aff_id).filter(AffBookingLogs.book_id == booking_id).scalar()
                # print(affiliate, booking_id, driver)
                affiliate= db.session.query(Affiliate).filter(Affiliate.id==affiliate_id).first()
                if not affiliate:
                    print("Could not accept: Invalid B2B id")
                    return jsonify({'success': -3, 'message': "Invalid B2B id"})
                client_name=affiliate.display_name
                ret_val = driver_accept_booking(booking_id, driver, update_pending=False, is_user=False)
                if ret_val.get("success") == 1 and affiliate:
                    send_booking_confirm_sms_affiliate(affiliate_id,client_name,booking_id, driver_user)
        else:
            print(revv_v2, mahindra, spinny, booking_id, driver)
            ret_val = driver_accept_booking(booking_id, driver, update_pending=False, is_user=False)

            if ret_val.get("success") == 1 and zc:
                try:
                    if _get_zoomcar_booking_state(booking_id) != ZoomcarBookings.STATUS_ACCEPTED:
                        _zoomcar_change_state(ZOOMCAR_STATE_CREATED, booking_id)
                        _zoomcar_change_state(ZOOMCAR_STATE_ACCEPTED, booking_id)
                        _mark_zoomcar_booking_accepted(booking_id)
                    _zoomcar_change_state(ZOOMCAR_STATE_ASSIGNED, booking_id)
                except Exception as e:
                    print("Could not set state: error %s" % str(e))
            if ret_val.get("success") == 1 and bhandari:
                send_booking_confirm_sms("Bhandari", booking_id, driver_user)
            if ret_val.get("success") == 1 and pridehonda:
                send_booking_confirm_sms("Pride Honda", booking_id, driver_user)
        if 'code' in ret_val:
            return jsonify(ret_val), ret_val['code']
        else:
            return jsonify(ret_val)
    except Exception as e:
        print("Failed to accept", str(e),flush=True)
        return jsonify({'success': -4, "message": "Failed: reason is unknown"}), 201


@drivers.route('/api/driver/reject', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/reject_book.yml')
@jwt_required()
def reject_book():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    driver_user = get_jwt_identity()

    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver = Drivers.query.filter_by(user=driver_user).first().id
    try:
        if not complete(request.form, ['booking_id']):
            return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201
        booking_id = int(request.form['booking_id'])
        c24 = int(get_safe(request.form, 'c24', 0))
        if c24 == 1:
            c24_b = db.session.query(C24Bookings).filter(C24Bookings.id == booking_id).first()
            if c24_b:
                booking_id = c24_b.ref
            else:
                return jsonify({'success': -1, 'message': 'Car24 booking not exist'})
        zc = int(get_safe(request.form, 'zoomcar', 0))
        if zc == 1:
            zc_b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.id == booking_id).first()
            if zc_b:
                booking_id = zc_b.ref
            else:
                return jsonify({'success': -1, 'message': 'Zoomcar booking not exist'})
        olx = int(get_safe(request.form, 'olx', 0))
        if olx == 1:
            olx_b = db.session.query(OLXBookings).filter(OLXBookings.id == booking_id).first()
            if olx_b:
                booking_id = olx_b.ref
            else:
                return jsonify({'success': -1, 'message': 'olx booking not exist'})
        cardekho = int(get_safe(request.form, 'cardekho', 0))
        if cardekho == 1:
            cardekho_b = db.session.query(CardekhoBookings).filter(CardekhoBookings.id == booking_id).first()
            if cardekho_b:
                booking_id = cardekho_b.ref
            else:
                return jsonify({'success': -1, 'message': 'cardekho booking not exist'})
        bhandari = int(get_safe(request.form, 'bhandari', 0))
        if bhandari == 1:
            bhandari_b = db.session.query(BhandariBookings).filter(BhandariBookings.id == booking_id).first()
            if bhandari_b:
                booking_id = bhandari_b.ref
            else:
                return jsonify({'success': -1, 'message': 'bhandari booking not exist'})
        mahindra = int(get_safe(request.form, 'mahindra', 0))
        if mahindra == 1:
            mahindra_b = db.session.query(MahindraBookings).filter(MahindraBookings.id == booking_id).first()
            if mahindra_b:
                booking_id = mahindra_b.ref
            else:
                return jsonify({'success': -1, 'message': 'mahindra booking not exist'})
        revv_v2 = int(get_safe(request.form, 'revv_v2', 0))
        if revv_v2 == 1:
            revv_v2_b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.id == booking_id).first()
            if revv_v2_b:
                booking_id = revv_v2_b.ref
            else:
                return jsonify({'success': -1, 'message': 'revv_v2 booking not exist'})
        spinny = int(get_safe(request.form, 'spinny', 0))
        if spinny == 1:
            spinny_b = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == booking_id).first()
            if spinny_b:
                booking_id = spinny_b.ref
            else:
                return jsonify({'success': -1, 'message': 'spinny booking not exist'})
        pridehonda = int(get_safe(request.form, 'pridehonda', 0))
        if pridehonda == 1:
            pridehonda_b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.id == booking_id).first()
            if pridehonda_b:
                booking_id = pridehonda_b.ref
            else:
                return jsonify({'success': -1, 'message': 'pridehonda booking not exist'})
        booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
        book_type=BookingParams.determine_booking_type(booking.type)
        if book_type==0:
            affiliate_id = db.session.query(AffBookingLogs.aff_id).filter(AffBookingLogs.book_id == booking_id).scalar()
            affiliate= db.session.query(Affiliate.id==affiliate_id).first()
            if not affiliate:
                print("Could not accept: Invalid B2B id")
                return jsonify({'success': -1, 'message': 'Affiliate booking not exist'})
        user_name = db.session.query(Users).filter(Users.id == driver_user).first().get_name()
        send_slack_msg(0, user_name + " rejected booking #" + str(booking_id))
        check_exists = db.session.query(BookPending).filter(BookPending.driver == driver). \
            filter(BookPending.valid == 1). \
            filter(BookPending.book_id == booking_id).first()
        if not check_exists:
            return jsonify({'success': 0, 'message': 'BookPending does not exist'})
        else:
            # ok db calls now
            db.session.query(BookPending).filter(BookPending.driver == driver).filter(BookPending.valid == 1). \
                filter(BookPending.book_id == booking_id).delete()
            driver_log = DriverLog(driver, 'reject', json.dumps(str(request.form)))
            db.session.add(driver_log)
            db.session.commit()
            try:
                fb_db.collection(u'book_pending').document(str(driver)).update(
                    {str(booking_id): firestore.DELETE_FIELD})
            except Exception:
                pass
            return jsonify({'success': 1, 'message': 'DB Error'})
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message': 'Server error'}), 201


def send_booking_confirm_sms_affiliate(affiliate_id,client_name,booking_id, driver_user,tz='Asia/Kolkata'):
    # affiliates_book = app.mdb["affiliate_book"]
    driver = db.session.query(Users).filter(Users.id == driver_user).first()
    driver_details = f"{driver.get_name()}({driver.mobile})"
    # if b2b_client == "Bhandari":
    #     b2b_booking = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking_id).first()
    # if b2b_client == "Pride Honda":
    #     b2b_booking = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == booking_id).first()
    affiliate_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': booking_id})
    if affiliate_mongo:
        spoc_data = affiliate_mongo.get('spoc_data',{})
    else:
        print("Affiliate not found.")
    user_mobile = spoc_data.get('dest_spoc_contact')
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    start_datetime = datetime.datetime(year=booking.startdate.year, month=booking.startdate.month,
                                day=booking.startdate.day, hour=booking.starttime.hour,
                                minute=booking.starttime.minute, second=booking.starttime.second)
    start_time_ist = convert_to_local_time(start_datetime,tz)
    formatted_start_datetime = start_time_ist.strftime("%I:%M %p %d/%m/%Y")
    msg_content = {
        "b2b_client": client_name, "driver_details": driver_details,
        "pickup_date_time": formatted_start_datetime
    }
    _sms.send_msg_flow(str(user_mobile), _sms.FLOWS["b2b_allocate"], msg_content)


def send_booking_confirm_sms(b2b_client, booking_id, driver_user,tz='Asia/Kolkata'):
    driver = db.session.query(Users).filter(Users.id == driver_user).first()
    driver_details = f"{driver.get_name()}({driver.mobile})"
    if b2b_client == "Bhandari":
        b2b_booking = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking_id).first()
    if b2b_client == "Pride Honda":
        b2b_booking = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == booking_id).first()
    user_mobile = b2b_booking.drop_mob
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    start_datetime = datetime.datetime(year=booking.startdate.year, month=booking.startdate.month,
                                day=booking.startdate.day, hour=booking.starttime.hour,
                                minute=booking.starttime.minute, second=booking.starttime.second)
    start_time_ist = convert_to_local_time(start_datetime,tz)
    formatted_start_datetime = start_time_ist.strftime("%I:%M %p %d/%m/%Y")
    msg_content = {
        "b2b_client": b2b_client, "driver_details": driver_details,
        "pickup_date_time": formatted_start_datetime
    }
    _sms.send_msg_flow(str(user_mobile), _sms.FLOWS["b2b_allocate"], msg_content)


@drivers.route('/api/driver/reverify', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/refresh_token.yml')
@jwt_required()
def reverify():
    claims = get_jwt()

    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'bitstring': "00000"}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -2, 'bitstring': "00000"}), 401
    cur_driver = Drivers.query.filter_by(user=driver_user).first()
    if cur_driver.approved != 1:
        verif = db.session.query(DriverVerify).filter(DriverVerify.driver_id == cur_driver.id).first()
        if not verif:
            return jsonify({'success': -2, 'bitstring': "00000"})
        else:
            bitstring = str(int(verif.id_card)) + "" + str(int(verif.photo)) + "" + str(int(verif.ref)) + \
                        "" + str(int(verif.lic)) + "" + str(int(verif.bank))
    else:
        return jsonify({'success': 1, 'bitstring': "11111"})
    return jsonify({'success': 1, 'bitstring': bitstring})


@drivers.route('/api/driver/experience/set', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/exp_set.yml')
@jwt_required()
def exp_set():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': "Unauthorized role"}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -2, 'message': "User restricted"}), 401
    cur_driver = Drivers.query.filter_by(user=driver_user).first()
    try:
        create_driver_exp(cur_driver.id, request.form['hb_m'], request.form['sed_m'], request.form['suv_m'],
                        request.form['lux_m'], request.form['hb_a'], request.form['sed_a'],
                        request.form['suv_a'], request.form['lux_a'])
    except Exception:
        return jsonify({'success': -3, 'message': "DB Error"})
    return jsonify({'success': 1, 'message': "Added driver experience successfully"})


@drivers.route('/api/driver/experience/get', methods=['POST'])
@jwt_required()
def exp_get():
    claims = get_jwt()

    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'numstring': "********"}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -2, 'numstring': "********"}), 401
    cur_driver = Drivers.query.filter_by(user=driver_user).first()
    skill = db.session.query(DriverSkill).filter(DriverSkill.driver_id == cur_driver.id).first()
    if not skill:
        return jsonify({'success': -2, 'numstring': "********"})
    else:
        numstring = str(int(skill.hb_m)) + "" + str(int(skill.sed_m)) + "" + str(int(skill.suv_m)) + \
                    "" + str(int(skill.lux_m)) + "" + str(int(skill.hb_a)) + "" + str(int(skill.sed_a)) + \
                    "" + str(int(skill.suv_m)) + "" + str(int(skill.lux_m))
    return jsonify({'success': 1, 'numstring': numstring})



@drivers.route('/api/driver/pending_cust', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/pending_cust.yml')
@jwt_required()
def pending_cust():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'User not authorized'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver_acc = Drivers.query.filter_by(user=driver_user).first()
    driver = driver_acc.id
    prev_dt = datetime.datetime.utcnow() - datetime.timedelta(days=1)
    prevdate = prev_dt.strftime("%Y-%m-%d")
    prevtime = prev_dt.strftime("%H:%M:%S")
    results = (
        db.session.query(Bookings, Users,BookPricing)
        .outerjoin(Users, Users.id == Bookings.user)  # Allows Bookings without Users
        .outerjoin(BookPricing,BookPricing.book_id==Bookings.id)
        .filter(Bookings.driver == driver)
        .filter(Bookings.valid == 1)
        .filter(Bookings.startdate >= prevdate)
        .order_by(Bookings.starttime.desc())
        .all()
    )
    result_json = []
    b2b_refs = {int(result[0].id) for result in results if result[0].type == BookingParams.TYPE_B2B}
    affiliates_cursor = AffiliateCollections.affiliates_book.find({'book_ref': {'$in': list(b2b_refs)}})
    affiliates_data = {doc['book_ref']: doc for doc in affiliates_cursor}
    for result in results:
        if result[0].type < BookingParams.TYPE_C24 or result[0].type == BookingParams.TYPE_B2B:
            car_type = result[0].car_type
        else:
            car_type = 0
        if not booking_has_trip(result[0].id):
            if result[0].type == BookingParams.TYPE_OUTSTATION or result[0].type == BookingParams.TYPE_ONEWAY or \
                    result[0].type == BookingParams.TYPE_MINIOS \
                    or result[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY or result[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
                try:
                    dest_data = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                    dest_lat = dest_data.lat
                    dest_long = dest_data.lng
                    dest_loc = dest_data.name
                except Exception as e:
                    print(e)
                    dest_lat = dest_long = 0.0
                    dest_loc = ''
                res_data = {
                    "id": result[0].id,
                    "name": result[1].get_name(),
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": result[0].estimate,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": result[1].mobile,
                    "did_no": result[0].did,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "insurance": result[0].insurance,
                    "insurance_ch": result[0].insurance_cost,
                    "code": result[0].code,
                    "region": result[0].region,
                }
            elif (result[0].type >= BookingParams.TYPE_C24 and result[0].type!=BookingParams.TYPE_B2B ):    # is b2b
                # Ok, we need to fetch SPOC nos, SPOC name, dest lat long and C24 internal type
                try:
                    dest_data = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                    dest_lat = dest_data.lat
                    dest_long = dest_data.lng
                    dest_loc = dest_data.name
                except Exception:
                    dest_lat = dest_long = 0.0
                    dest_loc = ''

                if result[0].type == BookingParams.TYPE_C24:
                    b2b_data = db.session.query(C24Bookings).filter(C24Bookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(C24Rep).filter(C24Rep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_OLX:
                    b2b_data = db.session.query(OLXBookings).filter(OLXBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(OLXRep).filter(OLXRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_CARDEKHO:
                    b2b_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(CardekhoRep).filter(CardekhoRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_ZOOMCAR:
                    b2b_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_BHANDARI:
                    b2b_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(BhandariRep).filter(BhandariRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_MAHINDRA:
                    b2b_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(MahindraRep).filter(MahindraRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_REVV_V2:
                    b2b_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_SPINNY:
                    b2b_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == b2b_data.rep).first()
                elif result[0].type == BookingParams.TYPE_PRIDEHONDA:
                    b2b_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == result[0].id).first()
                    b2b_rep = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == b2b_data.rep).first()
                b2b_id = b2b_data.id
                if b2b_rep:
                    b2b_mobile = b2b_rep.mobile
                    b2b_did_no = result[0].did
                    b2b_name = b2b_rep.name
                if b2b_data:
                    b2b_type = b2b_data.trip_type
                    b2b_dest_spoc = b2b_data.drop_mob
                    b2b_veh_no = b2b_data.veh_no
                    b2b_veh_model = b2b_data.veh_model
                    b2b_appt_id = b2b_data.appt
                else:
                    b2b_type = 0
                    b2b_dest_spoc = 0
                    b2b_veh_no = 0
                    b2b_veh_model = "UNKNOWN"
                    b2b_appt_id = -1
                b2b_est = 0
                if (result[0].type == BookingParams.TYPE_OLX or
                    result[0].type == BookingParams.TYPE_ZOOMCAR or result[0].type == BookingParams.TYPE_CARDEKHO or
                    result[0].type == BookingParams.TYPE_BHANDARI or result[0].type == BookingParams.TYPE_MAHINDRA or
                    result[0].type == BookingParams.TYPE_REVV_V2 or result[0].type == BookingParams.TYPE_SPINNY or
                    result[0].type == BookingParams.TYPE_PRIDEHONDA):
                    b2b_est = result[0].estimate

                res_data = {
                    "id": b2b_id,
                    "name": b2b_name,
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": b2b_est,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": b2b_mobile,
                    "did_no": b2b_did_no,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "driver_type": driver_acc.perma,
                    "veh_no": b2b_veh_no,
                    "veh_model": b2b_veh_model,
                    "b2b_type": b2b_type,
                    "dest_mob": b2b_dest_spoc,
                    "appt_id": b2b_appt_id,
                    "trip_id": result[0].id,
                    "code": result[0].code,
                    "region": result[0].region,
                }
            elif result[0].type == BookingParams.TYPE_REVV:
                revv_data = db.session.query(RevvBookings).filter(RevvBookings.ref == result[0].id).first()
                if not revv_data:
                    continue
                revv_id = revv_data.id
                res_data = {
                    "id": revv_id,
                    "name": result[1].get_name(),
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": 0,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": result[1].mobile,
                    "did_no": result[0].did,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "trip_id": result[0].id,
                    "code": result[0].code,
                    "region": result[0].region,
                }
            elif result[0].type == BookingParams.TYPE_GUJRAL:
                gujral_data = db.session.query(GujralBookings).filter(GujralBookings.ref == result[0].id).first()
                if not gujral_data:
                    continue
                gujral_id = gujral_data.id
                res_data = {
                    "id": gujral_id,
                    "name": result[1].get_name(),
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": 0,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": result[1].mobile,
                    "did_no": result[0].did,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "trip_id": result[0].id,
                    "code": result[0].code,
                    "region": result[0].region,
                }
            elif (result[0].type == BookingParams.TYPE_B2B):    # is b2b
                # Ok, we need to fetch SPOC nos, SPOC name, dest lat long and C24 internal type
                try:
                    dest_data = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                    dest_lat = dest_data.lat
                    dest_long = dest_data.lng
                    dest_loc = dest_data.name
                except Exception:
                    dest_lat = dest_long = 0.0
                    dest_loc = ''

                affiliate_mongo = affiliates_data.get(int(result[0].id))

                if not affiliate_mongo:
                    return jsonify({'success': -1, 'message': 'B2B data not found'})
                b2b_id = affiliate_mongo.get("affiliate_id") if affiliate_mongo else ""
                b2b_mobile = affiliate_mongo.get("rep_mobile","") if affiliate_mongo else ""
                b2b_name = affiliate_mongo.get("rep_fullname","") if affiliate_mongo else ""
                b2b_type = affiliate_mongo.get("trip_type") if affiliate_mongo else ""
                b2b_mapped_trip_type =  affiliate_mongo.get("trip_name") if affiliate_mongo else ""
                b2b_dest_spoc_number = affiliate_mongo.get("spoc_data").get("dest_spoc_contact","") if affiliate_mongo else ""
                b2b_dest_spoc_name = affiliate_mongo.get("spoc_data").get("dest_spoc_name","") if affiliate_mongo else ""
                b2b_source_spoc_number = affiliate_mongo.get("spoc_data").get("source_spoc_contact","") if affiliate_mongo else ""
                b2b_source_spoc_name = affiliate_mongo.get("spoc_data").get("source_spoc_name","") if affiliate_mongo else ""
                b2b_veh_no = affiliate_mongo.get("vehicle_no","") if affiliate_mongo else ""
                b2b_veh_model =  affiliate_mongo.get("vehicle_model","") if affiliate_mongo else ""
                client_name = affiliate_mongo.get("client_name", "") if affiliate_mongo else ""
                b2b_appt_id = affiliate_mongo.get("appointment_id", "") if affiliate_mongo else ""
                b2b_est = result[2].driver_base_ch + result[2].driver_night_ch
                # affiliate_data_mongo = affiliates_collection.find_one({
                #     "affiliate_id": int(affiliate_mongo.get("affiliate_id")),
                #     "trip_type.trip_type_name": affiliate_mongo.get("trip_name")
                # })
                # matching_trip_types = [
                #     t for t in affiliate_data_mongo.get("trip_type", [])
                #     if t.get("trip_type_name") == affiliate_mongo.get("trip_name")
                # ]
                # all_start_images = [t.get("startImages", []) for t in matching_trip_types]
                # all_stop_images = [t.get("stopImages", []) for t in matching_trip_types]
                all_start_images = affiliate_mongo.get("trip_start_images_structure", {})
                all_stop_images = affiliate_mongo.get("trip_stop_images_structure", {})
                res_data = {
                    "id": result[0].id,
                    "name": b2b_name,
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": b2b_est,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile":b2b_source_spoc_number,
                    "did_no": result[0].did,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "driver_type": driver_acc.perma,
                    "veh_no": b2b_veh_no,
                    "veh_model": b2b_veh_model,
                    "b2b_type": b2b_type,
                    "b2b_mapped_trip_type":b2b_mapped_trip_type,
                    "dest_mob": b2b_dest_spoc_number,
                    "appt_id": b2b_appt_id,
                    "trip_id": result[0].id,
                    "region": result[0].region,
                    "code": result[0].code,
                    "source_name": b2b_source_spoc_name,
                    "rep_mob": b2b_mobile,
                    "dest_name": b2b_dest_spoc_name,
                    "trip_start_images": all_start_images,
                    "trip_stop_images": all_stop_images,
                    "affiliate_name": client_name,
                }
            else:
                try:
                    dest_data = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                    dest_lat = dest_data.lat
                    dest_long = dest_data.lng
                    dest_loc = dest_data.name
                except Exception:
                    dest_lat = dest_long = 0.0
                    dest_loc = ''
                res_data = {
                    "id": result[0].id,
                    "name": result[1].get_name(),
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": result[0].estimate,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": result[1].mobile,
                    "did_no": result[0].did,
                    "trip_type": result[0].type,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "days": result[0].days,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "code": result[0].code,
                    "region": result[0].region,
                }
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'driver_id': - 1, 'message': 'No pending customers found'})
    return jsonify(result_json)


@drivers.route('/api/driver/booking_info', methods=['POST'])
@jwt_required()
def booking_info():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'error': 1}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver_acc = Drivers.query.filter_by(user=driver_user).first()
    driver = driver_acc.id
    try:
        book_id = request.form['book_id']
        result = db.session.query(Bookings, Users).filter(Bookings.driver == driver). \
            filter(Bookings.user == Users.id).filter(Bookings.id == book_id).first()
        car_type = get_car_type(result[0].id)
        res_data = BookingDriver(id=result[0].id, name=result[1].get_name(),
                                 startdate=result[0].startdate, starttime=result[0].starttime,
                                 enddate=result[0].enddate, endtime=result[0].endtime,
                                 dur=result[0].dur, lat=result[0].lat, long=result[0].long,
                                 estimate=result[0].estimate, car_type=car_type,
                                 loc=result[0].loc, mobile=result[1].mobile, trip_type=result[0].type,
                                 days=result[0].days, driver_type=driver_acc.perma, payment=result[0].payment_type)
        if not result:
            return jsonify({'driver_id': - 1})
        return jsonpickle.encode(res_data)
    except Exception:
        return jsonify({'driver_id': - 1}), 201


@drivers.route('/api/driver/past_cust', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/past_cust.yml')
@jwt_required()
def past_cust():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'User not authorized'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    llimit = int(get_safe(request.form, 'llimit', 0))
    ulimit = int(get_safe(request.form, 'u limit', 170))
    driver_acc = Drivers.query.filter_by(user=driver_user).first()
    driver = driver_acc.id
    cur_dt = datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
    curdate = datetime.datetime.utcnow().strftime("%Y-%m-%d")
    curtime = datetime.datetime.utcnow().strftime("%H:%M:%S")
    six_months_back = (datetime.datetime.utcnow() - relativedelta(months=6)).date()
    print(six_months_back)
    start=tm.time()
    filtered_bookings = select(Bookings).where(Bookings.driver == driver).subquery()
    FilteredBookings = aliased(Bookings, filtered_bookings)

    results = (
        db.session.query(FilteredBookings, Users, Trip,BookPricing,UserTrans)
        .outerjoin(Users, Users.id == FilteredBookings.user)  # Outer join to include Bookings without Users
        .join(Trip, Trip.book_id == FilteredBookings.id)  # Ensure only bookings with trips are included
        .outerjoin(BookPricing,BookPricing.book_id==FilteredBookings.id)
        .outerjoin(UserTrans,UserTrans.id==Trip.trans)
        # .filter(filtered_bookings.driver == driver)
        .filter(FilteredBookings.startdate >= six_months_back)
        .filter(Trip.endtime < cur_dt)
        .filter(Trip.endtime.isnot(None))
        .order_by(Trip.starttime.desc())
        .all()
    )
    print("1'st query",start-tm.time())
    start=tm.time()

    print("2'nd query",start-tm.time())
    start=tm.time()

    results_canceled_realloc = (
        db.session.query(Bookings, BookingCancelled, Users,BookPricing)
        .join(BookingCancelled, Bookings.id == BookingCancelled.booking)  # Inner join for cancellations
        .outerjoin(Users, Users.id == Bookings.user)  # Outer join to include Bookings without Users
        .outerjoin(BookPricing,BookPricing.book_id==Bookings.id)
        # .filter(Bookings.driver != driver)
        .filter(BookingCancelled.did == driver)
        # .filter(Bookings.valid >= 0)
        .filter(Bookings.startdate >= six_months_back)
        .order_by(Bookings.startdate.desc(), Bookings.starttime.desc())
        .all()
    )
    print("3'rd query",start-tm.time())
    start=tm.time()
    b2b_refs = {int(result[0].id) for result in results if result[0].type == BookingParams.TYPE_B2B}
    # b2b_refs_cancel = {int(result[0].id) for result in results_canceled if result[0].type == BookingParams.TYPE_B2B}
    b2b_refs_cancel_realloc = {int(result[0].id) for result in results_canceled_realloc if result[0].type == BookingParams.TYPE_B2B}
    print("b2b id loop time",start-tm.time())
    # Fetch all documents with these book_refs in one query
    start=tm.time()
    affiliates_cursor = AffiliateCollections.affiliates_book.find({'book_ref': {'$in': list(b2b_refs)}})
    affiliates_cursor_cancel_realloc = AffiliateCollections.affiliates_book.find({'book_ref': {'$in': list(b2b_refs_cancel_realloc)}})
    print("b2b all data fetch",start-tm.time())
    affiliates_data = {doc['book_ref']: doc for doc in affiliates_cursor}
    affiliates_data_cancel_realloc = {doc['book_ref']: doc for doc in affiliates_cursor_cancel_realloc}

    # affiliate_mongo = affiliates_book.find({"book_ref": int(result[0].id,)},{"book_ref":int(result[0].id,)})
    result_json = []
    results = results[llimit:ulimit]
    # TODO: Add destination info for trips with a BookDest entry.
    for result in results:
        start=tm.time()
        if result[0].type < BookingParams.TYPE_C24 or result[0].type == BookingParams.TYPE_B2B:
            car_type = result[0].car_type
        else:
            car_type = 0
        from trip import convert_timedelta
        d_hr, d_min, d_sec = convert_timedelta(result[2].endtime - result[2].starttime)
        dur = str(d_hr) + ':' + d_min + ':' + d_sec
        try:
            dest_data = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
            dest_lat = dest_data.lat
            dest_long = dest_data.lng
            dest_loc = dest_data.name
        except Exception:
            dest_lat = dest_long = 0.0
            dest_loc = ''
        if (result[0].type >= BookingParams.TYPE_C24 and result[0].type!=BookingParams.TYPE_B2B ):  # is b2b
            if result[0].type == BookingParams.TYPE_C24:
                b2b_data = db.session.query(C24Bookings).filter(C24Bookings.ref == result[0].id).first()
                b2b_rep = db.session.query(C24Rep).filter(C24Rep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_OLX:
                b2b_data = db.session.query(OLXBookings).filter(OLXBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(OLXRep).filter(OLXRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_CARDEKHO:
                b2b_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(CardekhoRep).filter(CardekhoRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_ZOOMCAR:
                b2b_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_BHANDARI:
                b2b_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(BhandariRep).filter(BhandariRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_MAHINDRA:
                b2b_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(MahindraRep).filter(MahindraRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_REVV_V2:
                b2b_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == result[0].id).first()
                b2b_rep = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_SPINNY:
                b2b_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == b2b_data.rep).first()
            elif result[0].type == BookingParams.TYPE_PRIDEHONDA:
                b2b_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == result[0].id).first()
                b2b_rep = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == b2b_data.rep).first()
            if not b2b_data:
                return jsonify({'success': -1, 'message': 'B2B data not found'})
            try:
                b2b_id = b2b_data.id
                b2b_mobile = b2b_rep.mobile
                b2b_name = b2b_rep.name
                b2b_type = b2b_data.trip_type
                b2b_dest_spoc = b2b_data.drop_mob
                b2b_veh_no = b2b_data.veh_no
                b2b_veh_model = b2b_data.veh_model

                b2b_appt_id = b2b_data.appt
                b2b_est = 0
            except Exception as e:
                print("Exception in booking", str(e), str(result))
                continue
            if (result[0].type == BookingParams.TYPE_OLX or
                result[0].type == BookingParams.TYPE_ZOOMCAR or result[0].type == BookingParams.TYPE_CARDEKHO or
                result[0].type == BookingParams.TYPE_BHANDARI or result[0].type == BookingParams.TYPE_MAHINDRA or
                result[0].type == BookingParams.TYPE_REVV_V2 or result[0].type == BookingParams.TYPE_SPINNY or
                result[0].type == BookingParams.TYPE_PRIDEHONDA) and result[2].price == 0:
                b2b_est = result[0].estimate
            else:
                b2b_est = result[2].price
            res_data = {
                "id": b2b_id,
                "trip_status":result[0].valid,
                "name": b2b_name,
                "startdate": result[2].starttime.strftime("%Y-%m-%d"),
                "starttime": result[2].starttime.strftime("%H:%M:%S"),
                "enddate": result[2].endtime.strftime("%Y-%m-%d"),
                "endtime": result[2].endtime.strftime("%H:%M:%S"),
                "dur": dur,
                "lat": result[0].lat,
                "long": result[0].long,
                "estimate": b2b_est,
                "car_type": car_type,
                "loc": result[0].loc,
                "mobile": b2b_mobile,
                "trip_type": result[0].type,
                "days": result[0].days,
                "driver_type": driver_acc.perma,
                "veh_no": b2b_veh_no,
                "veh_model": b2b_veh_model,
                "b2b_type": b2b_type,
                "dest_mob": b2b_dest_spoc,
                "appt_id": b2b_appt_id,
                "due": result[2].due,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "trip_id": result[0].id,
                "code": result[0].code,
            }
        elif result[0].type == BookingParams.TYPE_REVV:
                revv_data = db.session.query(RevvBookings).filter(RevvBookings.ref == result[0].id).first()
                if not revv_data:
                    return jsonify({'success': -1, 'message': 'Revv data not found'})
                revv_id = revv_data.id
                res_data = {
                    "id": revv_id,
                    "trip_status":result[0].valid,
                    "name": result[1].get_name(),
                    "startdate": result[2].starttime.strftime("%Y-%m-%d"),
                    "starttime": result[2].starttime.strftime("%H:%M:%S"),
                    "enddate": result[2].endtime.strftime("%Y-%m-%d"),
                    "endtime": result[2].endtime.strftime("%H:%M:%S"),
                    "dur": dur,
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": 0,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": "",
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "due": result[2].due,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "trip_id": result[0].id,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "code": result[0].code,
                }
        elif result[0].type == BookingParams.TYPE_GUJRAL:
                gujral_data = db.session.query(GujralBookings).filter(GujralBookings.ref == result[0].id).first()
                if not gujral_data:
                    return jsonify({'success': -1, 'message': 'Gujral data not found'})
                gujral_id = gujral_data.id
                res_data = {
                    "id": gujral_id,
                    "trip_status":result[0].valid,
                    "name": result[1].get_name(),
                    "startdate": result[2].starttime.strftime("%Y-%m-%d"),
                    "starttime": result[2].starttime.strftime("%H:%M:%S"),
                    "enddate": result[2].endtime.strftime("%Y-%m-%d"),
                    "endtime": result[2].endtime.strftime("%H:%M:%S"),
                    "dur": dur,
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": 0,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": "",
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "due": result[2].due,
                    "driver_type": driver_acc.perma,
                    "payment": result[0].payment_type,
                    "trip_id": result[0].id,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "code": result[0].code,
                }
        elif (result[0].type == BookingParams.TYPE_B2B):  # is b2b
            start=tm.time()
            affiliate_mongo = affiliates_data.get(int(result[0].id))
            try:
                b2b_id = affiliate_mongo.get("affiliate_id") if affiliate_mongo else ""
                b2b_mobile = affiliate_mongo.get("rep_mobile","") if affiliate_mongo else ""
                b2b_name = affiliate_mongo.get("rep_fullname","") if affiliate_mongo else ""
                b2b_type = affiliate_mongo.get("trip_type") if affiliate_mongo else ""
                b2b_mapped_trip_type =  affiliate_mongo.get("trip_name") if affiliate_mongo else ""
                b2b_dest_spoc_number = affiliate_mongo.get("spoc_data").get("dest_spoc_contact","") if affiliate_mongo else ""
                b2b_dest_spoc_name = affiliate_mongo.get("spoc_data").get("dest_spoc_name    ","") if affiliate_mongo else ""
                b2b_source_spoc_number = affiliate_mongo.get("spoc_data").get("source_spoc_contact","") if affiliate_mongo else ""
                b2b_source_spoc_name = affiliate_mongo.get("spoc_data").get("source_spoc_name","") if affiliate_mongo else ""
                b2b_veh_no = affiliate_mongo.get("vehicle_no","") if affiliate_mongo else ""
                b2b_veh_model =  affiliate_mongo.get("vehicle_model","") if affiliate_mongo else ""
                client_name =  affiliate_mongo.get("client_name","") if affiliate_mongo else ""
                b2b_appt_id = affiliate_mongo.get("appointment_id", "") if affiliate_mongo else ""
            except Exception as e:
                print("Exception in booking", str(e), str(result))
                continue
            if result[2]:
                # driver_base_ch = result[3].driver_base_ch if result[3].driver_base_ch is not None else ""
                # driver_night_ch = result[3].driver_night_ch if result[3].driver_night_ch is not None else ""
                b2b_est = result[2].price - result[2].net_rev
            else:
                driver_base_ch = result[3].driver_base_ch if result[3].driver_base_ch is not None else ""
                driver_night_ch = result[3].driver_night_ch if result[3].driver_night_ch is not None else ""
                b2b_est = driver_base_ch + driver_night_ch
            res_data = {
                "id": result[0].id,
                "name": b2b_name,
                "trip_status":result[0].valid,
                "startdate": result[2].starttime.strftime("%Y-%m-%d"),
                "starttime": result[2].starttime.strftime("%H:%M:%S"),
                "enddate": result[2].endtime.strftime("%Y-%m-%d"),
                "endtime": result[2].endtime.strftime("%H:%M:%S"),
                "dur": dur,
                "lat": result[0].lat,
                "long": result[0].long,
                "estimate": b2b_est,
                "car_type": car_type,
                "loc": result[0].loc,
                "mobile": b2b_source_spoc_number,
                "trip_type": result[0].type,
                "days": result[0].days,
                "driver_type": driver_acc.perma,
                "veh_no": b2b_veh_no,
                "veh_model": b2b_veh_model,
                "b2b_type": b2b_type,
                "b2b_mapped_trip_type":b2b_mapped_trip_type,
                "dest_mob": b2b_dest_spoc_number,
                "appt_id": b2b_appt_id,
                "trip_id": result[0].id,
                "code": result[0].code,
                "due": result[2].due,
                "source_name": b2b_source_spoc_name,
                "rep_mob": b2b_mobile,
                "dest_name": b2b_dest_spoc_name,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "affiliate_name": client_name,
            }
            print("first mongo loop",start-tm.time())
        else:
            start=tm.time()
            trans = result[4]
            if driver_acc.perma:
                est = result[2].price
                if not trans:
                    est = result[2].price
                else:
                    if round(trans.cash) < 0:
                        est = trans.cash/100 * (-1)
                    else:
                        est = trans.cash/100
                pre_tax = 0
                cgst = 0
                sgst = 0
                cash = est
            else:
                est = result[2].price
                pre_tax = result[2].price_pre_tax
                sgst = result[2].sgst
                cgst = result[2].cgst
                if not trans:
                    est = result[2].price
                    cash = est
                else:
                    if round(trans.cash) < 0:
                        cash = trans.cash/100 * (-1)
                    else:
                        cash = trans.cash/100
            start=tm.time()
            res_data= None
            res_data = {
                "id": result[0].id,
                "trip_status":result[0].valid,
                "name": result[1].get_name(),
                "startdate": result[2].starttime.strftime("%Y-%m-%d"),
                "starttime": result[2].starttime.strftime("%H:%M:%S"),
                "enddate": result[2].endtime.strftime("%Y-%m-%d"),
                "endtime": result[2].endtime.strftime("%H:%M:%S"),
                "dur": dur,
                "lat": result[0].lat,
                "long": result[0].long,
                "estimate": est,
                "car_type": car_type,
                "loc": result[0].loc,
                "mobile": "",
                "trip_type": result[0].type,
                "days": result[0].days,
                "due": result[2].due,
                "driver_type": driver_acc.perma,
                "payment": result[0].payment_type,
                "pre_tax": pre_tax,
                "sgst": sgst,
                "cgst": cgst,
                "insurance": result[0].insurance,
                "insurance_ch": result[0].insurance_cost,
                "cash": cash,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "code": result[0].code,
            }
            print("b2c json loop time",start-tm.time())
            start=tm.time()
        start=tm.time()
        result_json.append(jsonpickle.encode(res_data))
        print("b2c json pickle time",start-tm.time())

    for result_c in results_canceled_realloc:
        if result_c[0].type < BookingParams.TYPE_C24 and result_c[0].type == BookingParams.TYPE_B2B:
            car_type = result_c[0].car_type
        else:
            car_type = 0
        try:
            dest_data = db.session.query(BookDest).filter(BookDest.book_id == result_c[0].id).first()
            dest_lat = dest_data.lat
            dest_long = dest_data.lng
            dest_loc = dest_data.name
        except Exception:
            dest_lat = dest_long = 0.0
            dest_loc = ''
        if  (result_c[0].type == BookingParams.TYPE_C24 or result_c[0].type == BookingParams.TYPE_OLX or
             result_c[0].type == BookingParams.TYPE_ZOOMCAR or result_c[0].type == BookingParams.TYPE_CARDEKHO or
             result_c[0].type == BookingParams.TYPE_BHANDARI):
            if result_c[0].type == BookingParams.TYPE_C24:
                b2b_data = db.session.query(C24Bookings).filter(C24Bookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(C24Rep).filter(C24Rep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_OLX:
                b2b_data = db.session.query(OLXBookings).filter(OLXBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(OLXRep).filter(OLXRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_CARDEKHO:
                b2b_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(CardekhoRep).filter(CardekhoRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_BHANDARI:
                b2b_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(BhandariRep).filter(BhandariRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_MAHINDRA:
                b2b_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(MahindraRep).filter(MahindraRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_REVV_V2:
                b2b_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_SPINNY:
                b2b_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_PRIDEHONDA:
                b2b_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == result_c[0].id).first()
                b2b_rep = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == b2b_data.rep).first()
            elif result_c[0].type == BookingParams.TYPE_ZOOMCAR:
                b2b_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == result_c[0].id).first()
                if not b2b_data:
                    print("Error for booking", str(result_c))
                    continue
                b2b_rep = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == b2b_data.rep).first()
            if not b2b_data:
                continue
            b2b_id = b2b_data.id
            b2b_name = b2b_rep.name
            b2b_type = b2b_data.trip_type
            b2b_veh_no = b2b_data.veh_no
            b2b_veh_model = b2b_data.veh_model

            b2b_appt_id = b2b_data.appt
            b2b_est = 0

            if (result_c[0].type == BookingParams.TYPE_OLX or
                result_c[0].type == BookingParams.TYPE_ZOOMCAR or result_c[0].type == BookingParams.TYPE_CARDEKHO or
                result_c[0].type == BookingParams.TYPE_BHANDARI or result_c[0].type == BookingParams.TYPE_MAHINDRA or
                result_c[0].type == BookingParams.TYPE_REVV_V2 or result_c[0].type == BookingParams.TYPE_SPINNY or
                result_c[0].type == BookingParams.TYPE_PRIDEHONDA):
                b2b_est = result_c[0].estimate
            res_data = {
                "id": b2b_id,
                "name": b2b_name,
                "trip_status": "-4" if result_c[0].driver != result_c[1].did else ("-4" if result_c[0].valid > 0 else result_c[0].valid),
                "startdate": result_c[0].startdate.strftime("%Y-%m-%d"),
                "starttime": result_c[0].starttime.strftime("%H:%M:%S"),
                "enddate": result_c[0].enddate.strftime("%Y-%m-%d"),
                "endtime": result_c[0].endtime.strftime("%H:%M:%S"),
                "book_cancel_time":result_c[1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "dur": result_c[0].dur.strftime('%H:%M:%S'),
                "lat": result_c[0].lat,
                "long": result_c[0].long,
                "estimate": b2b_est,
                "car_type": car_type,
                "loc": result_c[0].loc,
                "mobile": "",
                "trip_type": result_c[0].type,
                "days": result_c[0].days,
                "due": result_c[1].penalty_driver,
                "driver_type": driver_acc.perma,
                "payment": result_c[0].payment_type,
                "trip_id": result_c[0].id,
                "appt_id": b2b_appt_id,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "code": result_c[0].code,
            }
        elif result_c[0].type == BookingParams.TYPE_REVV:
            revv_data = db.session.query(RevvBookings).filter(RevvBookings.ref == result_c[0].id).first()
            if not revv_data:
                continue
            revv_id = revv_data.id
            res_data = {
                    "id": revv_id,
                    "name": result_c[2].get_name(),
                    "trip_status": "-4" if result_c[0].driver != result_c[1].did else ("-4" if result_c[0].valid > 0 else result_c[0].valid),
                    "startdate": result_c[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result_c[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result_c[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result_c[0].endtime.strftime("%H:%M:%S"),
                    "book_cancel_time":result_c[1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    "dur": result_c[0].dur.strftime('%H:%M:%S'),
                    "lat": result_c[0].lat,
                    "long": result_c[0].long,
                    "estimate": 0,
                    "car_type": car_type,
                    "loc": result_c[0].loc,
                    "mobile": "",
                    "trip_type": result_c[0].type,
                    "days": result_c[0].days,
                    "due": result_c[1].penalty,
                    "driver_type": driver_acc.perma,
                    "payment": result_c[0].payment_type,
                    "trip_id": result_c[0].id,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "code": result_c[0].code,
                }
        elif result_c[0].type == BookingParams.TYPE_GUJRAL:
            gujral_data = db.session.query(GujralBookings).filter(GujralBookings.ref == result_c[0].id).first()
            if not gujral_data:
                continue
            gujral_id = gujral_data.id
            res_data = {
                "id": gujral_id,
                "name": result_c[2].get_name(),
                "trip_status": "-4" if result_c[0].driver != result_c[1].did else ("-4" if result_c[0].valid > 0 else result_c[0].valid),
                "startdate": result_c[0].startdate.strftime("%Y-%m-%d"),
                "starttime": result_c[0].starttime.strftime("%H:%M:%S"),
                "enddate": result_c[0].enddate.strftime("%Y-%m-%d"),
                "endtime": result_c[0].endtime.strftime("%H:%M:%S"),
                "book_cancel_time":result_c[1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "dur": result_c[0].dur.strftime('%H:%M:%S'),
                "lat": result_c[0].lat,
                "long": result_c[0].long,
                "estimate": 0,
                "car_type": car_type,
                "loc": result_c[0].loc,
                "mobile": "",
                "trip_type": result_c[0].type,
                "days": result_c[0].days,
                "due": result_c[1].penalty,
                "driver_type": driver_acc.perma,
                "payment": result_c[0].payment_type,
                "trip_id": result_c[0].id,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "code": result_c[0].code,
            }

        elif (result_c[0].type == BookingParams.TYPE_B2B):
            start=tm.time()
            affiliate_mongo = affiliates_data_cancel_realloc.get(int(result_c[0].id))
            print("fetch mongo time",start-tm.time())
            b2b_id = affiliate_mongo.get("affiliate_id")
            b2b_mobile = affiliate_mongo.get("rep_mobile","") if affiliate_mongo else ""
            b2b_name = affiliate_mongo.get("rep_fullname","") if affiliate_mongo else ""
            b2b_type = affiliate_mongo.get("trip_type") if affiliate_mongo else ""
            b2b_mapped_trip_type =  affiliate_mongo.get("trip_name") if affiliate_mongo else ""
            b2b_dest_spoc_number = affiliate_mongo.get("spoc_data").get("dest_spoc_contact","") if affiliate_mongo else ""
            b2b_dest_spoc_name = affiliate_mongo.get("spoc_data").get("dest_spoc_name","") if affiliate_mongo else ""
            b2b_source_spoc_number = affiliate_mongo.get("spoc_data").get("source_spoc_contact","") if affiliate_mongo else ""
            b2b_source_spoc_name = affiliate_mongo.get("spoc_data").get("source_spoc_name","") if affiliate_mongo else ""
            b2b_veh_no = affiliate_mongo.get("vehicle_no","") if affiliate_mongo else ""
            b2b_veh_model =  affiliate_mongo.get("vehicle_model","") if affiliate_mongo else ""
            client_name =  affiliate_mongo.get("client_name","") if affiliate_mongo else ""
            b2b_appt_id = affiliate_mongo.get("appointment_id", "") if affiliate_mongo else ""
            if result[3]:
                driver_base_ch = result[3].driver_base_ch if result[3].driver_base_ch is not None else ""
                driver_night_ch = result[3].driver_night_ch if result[3].driver_night_ch is not None else ""
                b2b_est = driver_base_ch + driver_night_ch
            else:
                b2b_est = 0
            res_data = {
                "id": result_c[0].id,
                "name": b2b_name,
                "trip_status": "-4" if result_c[0].driver != result_c[1].did else ("-4" if result_c[0].valid > 0 else result_c[0].valid),
                "startdate": result_c[0].startdate.strftime("%Y-%m-%d"),
                "starttime": result_c[0].starttime.strftime("%H:%M:%S"),
                "enddate": result_c[0].enddate.strftime("%Y-%m-%d"),
                "endtime": result_c[0].endtime.strftime("%H:%M:%S"),
                "book_cancel_time":result_c[1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "dur": result_c[0].dur.strftime('%H:%M:%S'),
                "lat": result_c[0].lat,
                "long": result_c[0].long,
                "estimate": b2b_est,
                "car_type": car_type,
                "loc": result_c[0].loc,
                "mobile": b2b_source_spoc_number,
                "trip_type": result_c[0].type,
                "b2b_type":b2b_type,
                "veh_no": b2b_veh_no,
                "veh_model": b2b_veh_model,
                "b2b_mapped_trip_type":b2b_mapped_trip_type,
                "days": result_c[0].days,
                "due": result_c[1].penalty_driver,
                "driver_type": driver_acc.perma,
                "payment": result_c[0].payment_type,
                "trip_id": result_c[0].id,
                "appt_id": b2b_appt_id,
                "code": result_c[0].code,
                "source_name": b2b_source_spoc_name,
                "rep_mob": b2b_mobile,
                "dest_name": b2b_dest_spoc_name,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "affiliate_name": client_name,
            }
            print("third mongo loop",start-tm.time())
        else:
            start=tm.time()
            res_data = {
                "id": result_c[0].id,
                "trip_status": "-4" if result_c[0].driver != result_c[1].did else ("-4" if result_c[0].valid > 0 else result_c[0].valid),
                "name": result_c[2].get_name(),
                "startdate": result_c[0].startdate.strftime("%Y-%m-%d"),
                "starttime": result_c[0].starttime.strftime("%H:%M:%S"),
                "enddate": result_c[0].enddate.strftime("%Y-%m-%d"),
                "endtime": result_c[0].endtime.strftime("%H:%M:%S"),
                "book_cancel_time":result_c[1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "dur": result_c[0].dur.strftime('%H:%M:%S'),
                "lat": result_c[0].lat,
                "long": result_c[0].long,
                "estimate": result_c[3].estimate,
                "car_type": car_type,
                "loc": result_c[0].loc,
                "mobile": "",
                "trip_type": result_c[0].type,
                "days": result_c[0].days,
                "due": result_c[1].penalty_driver,
                "driver_type": driver_acc.perma,
                "payment": result_c[0].payment_type,
                "insurance": result_c[0].insurance,
                "insurance_ch": result_c[0].insurance_cost,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "code": result_c[0].code,
            }
            print("third b2c loop",start-tm.time())
        result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'driver_id': - 1, 'message': 'No past customers found'})
    return jsonify(result_json)



@drivers.route('/api/driver/earning/total', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/get_earning.yml')
@jwt_required()
def get_earning():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver_e = Drivers.query.filter_by(user=driver_user).first()
    driver = driver_e.id
    try:
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver).first()
        earning = details.earning
        owed = details.owed
        rides = details.ride_count
        hours = details.hour_count
        withdrawable = details.withdrawable
        wallet = details.wallet
        try:
            avg_hours = hours / rides
            avg_earning = earning / rides
        except ZeroDivisionError:
            avg_hours = 0
            avg_earning = 0
        return jsonify({'success': 1, 'trip_count': rides, 'earning': earning, 'hour_count': hours,
                        'avg_earning': avg_earning, 'owed': owed, 'avg_hours': avg_hours,
                        'withdrawable': withdrawable, 'wallet': wallet, 'approved': driver_e.approved,
                        'available': driver_e.available})
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message': 'Server error'}), 201


@drivers.route('/api/driver/earning/monthly', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/get_earning_monthly.yml')
@jwt_required()
def get_earning_monthly():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message':'Unauthorised role: not Driver'}), 401
    if not complete(request.form, ['month', 'year']):
        return jsonify({'success': -2, 'message':'Incomplete form details'}), 201

    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    driver = Drivers.query.filter_by(user=driver_user).first().id
    trips = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
        filter(Bookings.payment_type == 0). \
        filter(Bookings.driver == driver).filter(extract('year', Trip.starttime) == int(request.form['year'])). \
        filter(extract('month', Trip.starttime) == int(request.form['month'])).all()
    try:
        earning = 0
        rides = 0
        hours = 0
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver).first()
        owed = details.owed
        withdrawable = details.withdrawable
        wallet = details.wallet
        for trip in trips:
            if not trip[0].price: continue
            from trip import convert_timedelta
            try:
                rides = rides + 1
                earning = earning + trip[0].price
                hours = hours + int(convert_timedelta(trip[0].endtime - trip[0].starttime)[0])
            except AttributeError:
                pass
        try:
            avg_earning = earning / rides
            avg_hours = hours / rides
        except ZeroDivisionError:
            avg_earning = avg_hours = 0
        return jsonify({'success': 1, 'trip_count': rides, 'earning': earning, 'hour_count': hours,
                        'avg_earning': avg_earning, 'avg_hours': avg_hours, 'owed': owed,
                        'withdrawable': withdrawable, 'wallet': wallet})
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message':'Server error'}), 201


@drivers.route('/api/driver/trans_log', methods=['POST'])
@jwt_required()
def driver_trans_log():
    claims = get_jwt()
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver = db.session.query(Drivers).filter(Drivers.user==user_id)
    if not driver.first():
        return jsonify({'success': -1, 'message': 'Driver does not exist'}), 401
    driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver.first().id).first()
    wallet, withdrawable = driver_details.wallet, driver_details.withdrawable
    driver_trans = db.session.query(DriverTrans).filter(DriverTrans.driver_id==driver.first().id). \
                    filter(DriverTrans.status==DriverTrans.COMPLETED). \
                    order_by(DriverTrans.timestamp.desc()). \
                    all()
    if not driver_trans:
        return jsonify({'success': -1, 'message': 'No completed transaction found'}), 200
    result_json = []
    for dtx in driver_trans:
        res_data = DriverTransWrap(id=dtx.id.replace("-", ""),
                                   amt=round(dtx.amount/100,2),
                                   dt=combine_and_convert_to_local(dtx.start_timestamp, dtx.start_timestamp,tz),
                                   method=dtx.method)
        result_json.append(jsonpickle.encode(res_data))
    return jsonify({'success': 1, 'wallet': round(wallet, 2),
                    'withdrawable': round(withdrawable, 2), 'data': result_json})





# @drivers.route('/api/driver/set_loc', methods=['POST'])
# @swag_from('/app/swagger_docs/drivers/set_driver_loc.yml')
# @jwt_required()
# def set_driver_loc():
#     user_id = get_jwt_identity()
#     claims = get_jwt()

#     if not validate_role(user_id, claims['roles'], [Users.ROLE_DRIVER]):
#         return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401

#     if not account_enabled(user_id):
#         return jsonify({'success': -1, 'message': 'User restricted'}), 401

#     if not complete(request.form, ['lat', 'lng']):
#         return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

#     try:
#         lat = float(get_safe(request.form, 'lat', 0))
#         lng = float(get_safe(request.form, 'lng', 0))

#         with db.session.no_autoflush:
#             driver_id = db.session.query(Drivers.id).filter(Drivers.user == user_id).scalar()
#             if not driver_id:
#                 return jsonify({'success': -1, 'message': 'Driver not found'}), 401

#         # Update DriverLoc
#         rows_updated = (
#             db.session.query(DriverLoc)
#             .filter(DriverLoc.driver_id == driver_id)
#             .update({'lat': lat, 'lng': lng})
#         )
#         if not rows_updated:
#             db.session.add(DriverLoc(driver_id=driver_id, lat=lat, lng=lng))

#         # Update DriverInfo registration location if missing
#         info_entry = (
#             db.session.query(DriverInfo)
#             .filter(DriverInfo.driver_id == driver_id)
#             .with_for_update(read=True)
#             .first()
#         )
#         if info_entry and info_entry.reg_lat is None and info_entry.reg_lng is None:
#             info_entry.reg_lat = lat
#             info_entry.reg_lng = lng

#         db.session.commit()
#         return jsonify({'success': 1, 'message': 'Location updated successfully'})

#     except Exception as e:
#         db.session.rollback()
#         return jsonify({'success': -1, 'message': 'DB Error', 'error': str(e)})
    
    
    




@drivers.route('/api/driver/set_loc', methods=['POST'])
@jwt_required()
def set_driver_loc():
    """Set driver’s current location (lat/lng). Uses NullPool to avoid holding pooled connections."""
    user_id = get_jwt_identity()
    claims = get_jwt()

    if not validate_role(user_id, claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401

    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    if not complete(request.form, ['lat', 'lng']):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201

    session = app.LocSession()
    try:
        lat = float(get_safe(request.form, 'lat', 0))
        lng = float(get_safe(request.form, 'lng', 0))

        driver_id = session.query(Drivers.id).filter(Drivers.user == user_id).scalar()
        if not driver_id:
            return jsonify({'success': -1, 'message': 'Driver not found'}), 401

        # Update or insert DriverLoc
        rows_updated = (
            session.query(DriverLoc)
            .filter(DriverLoc.driver_id == driver_id)
            .update({'lat': lat, 'lng': lng})
        )
        if not rows_updated:
            session.add(DriverLoc(driver=driver_id, lat=lat, lng=lng))

        # Update DriverInfo registration location if missing
        info_entry = (
            session.query(DriverInfo)
            .filter(DriverInfo.driver_id == driver_id)
            .with_for_update(read=True)
            .first()
        )
        if info_entry and info_entry.reg_lat is None and info_entry.reg_lng is None:
            info_entry.reg_lat = lat
            info_entry.reg_lng = lng

        session.commit()
        return jsonify({'success': 1, 'message': 'Location updated successfully'})

    except Exception as e:
        session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error', 'error': str(e)})
    finally:
        session.close()


@drivers.route('/api/driver/ongoing', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/ongoing_trip.yml')
@jwt_required()
def ongoing_trip():
    # Validate driver role and retrieve driver account
    driver_user = get_jwt_identity()
    claims = get_jwt()
    if not validate_role(driver_user, claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401

    driver_acc = Drivers.query.filter_by(user=driver_user).first()
    if not driver_acc:
        return jsonify({'success': -1, 'message': 'Driver account not found'}), 404
    driver = driver_acc.id

    # Fetch the ongoing trip for the driver
    cur_trip = (
        db.session.query(Bookings, Trip, BookPricing)
        .filter(Bookings.id == Trip.book_id)
        .filter(Bookings.driver == driver)
        .filter(Trip.endtime.is_(None))
        .filter(BookPricing.book_id == Bookings.id)
        .order_by(Trip.id.desc())
        .first()
    )
    if not cur_trip:
        return jsonify({'trip_id': -1, 'message': 'Trip does not exist'})

    try:
        # Unpack the queried objects
        booking, trip, pricing = cur_trip
        has_started = True

        # If trip.starttime is not set, combine booking date and time
        if not trip.starttime:
            trip.starttime = datetime.datetime.combine(booking.startdate, booking.starttime)
            has_started = False
        # Calculate elapsed duration
        elapsed_dur = strfdelta(datetime.datetime.utcnow() - trip.starttime, "{hours}:{minutes}:{seconds}")

        # Format start and end times/dates once
        starttime_str = trip.starttime.strftime("%H:%M:%S")
        startdate_str = trip.starttime.strftime("%Y-%m-%d")
        enddate_str = trip.endtime.strftime("%Y-%m-%d") if trip.endtime else ""
        endtime_str = trip.endtime.strftime("%H:%M:%S") if trip.endtime else ""

        # Get basic trip details
        lat = booking.lat
        long_val = booking.long
        est = booking.estimate
        stop_time = datetime.datetime.utcnow()
        delta = stop_time - trip.starttime

        pre_tax = booking.estimate_pre_tax
        if pre_tax == 0 and booking.type != BookingParams.TYPE_B2B:
            pre_tax = 0.95 * est

        time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
        price = est
        estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.HOUR_RATIO + \
                         math.ceil((booking.dur.minute * Price.HOUR_RATIO) / 60)

        # Adjust estimate for drivers with "perma" flag (unless paying cash)
        if driver_acc.perma and booking.payment_type != PaymentType.PAY_CASH:
            est = 0

        # Calculate trip price based on booking type
        if booking.type in [BookingParams.TYPE_ROUNDTRIP, BookingParams.TYPE_ONEWAY,
                            BookingParams.TYPE_MINIOS, BookingParams.TYPE_MINIOS_ONEWAY]:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = Price.get_trip_price(
                book_id=booking.id,
                book_delta=estimate_delta,
                real_delta=time_delta,
                est=pre_tax,
                book_starttime=booking.starttime,
                book_stoptime=booking.endtime,
                trip_starttime=trip.starttime.time(),
                trip_stoptime=stop_time.time(),
                startdate=trip.starttime.date(),
                enddate=stop_time.date(),
                city=booking.region,
                insurance_ch=booking.insurance_cost
            )
        elif booking.type in [BookingParams.TYPE_OUTSTATION, BookingParams.TYPE_OUTSTATION_ONEWAY]:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = PriceOutstation.get_trip_price(
                startdate=trip.starttime.date(),
                enddate=stop_time.date(),
                book_delta=estimate_delta,
                real_delta=time_delta,
                est=pre_tax,
                city=booking.region,
                insurance_ch=booking.insurance_cost
            )
        elif booking.type >= BookingParams.TYPE_C24:
            price, add_due = 0, 0

        # If trip hasn't started and it's not a C24 type, reset price and pre_tax
        if not has_started and booking.type < BookingParams.TYPE_C24:
            price = est
            pre_tax = booking.estimate_pre_tax

        if driver_acc.perma and booking.payment_type != PaymentType.PAY_CASH:
            price = 0

        # Get additional details
        if booking.type < BookingParams.TYPE_C24 or booking.type == BookingParams.TYPE_B2B:
            car_type = booking.car_type
        else:
            car_type = 0
        loc = booking.loc
        if booking.type != BookingParams.TYPE_B2B:
            user = db.session.query(Users).filter(Users.id == booking.user).first()
            mobile = user.mobile
            did_no = booking.did
            name = user.get_name()
        # Retrieve destination information
        try:
            dest_data = db.session.query(BookDest).filter(BookDest.book_id == booking.id).first()
            dest_lat = dest_data.lat
            dest_long = dest_data.lng
            dest_loc = dest_data.name
        except Exception:
            print("Could not find destination entry for", str(booking.id))
            dest_lat, dest_long = 0.0, 0.0
            dest_loc = ''

        # Initialize response dictionary
        driver_trip = {}

        # Branch logic based on booking type
        if booking.type in [BookingParams.TYPE_C24, BookingParams.TYPE_OLX,
                            BookingParams.TYPE_ZOOMCAR, BookingParams.TYPE_CARDEKHO,
                            BookingParams.TYPE_BHANDARI, BookingParams.TYPE_REVV_V2,
                            BookingParams.TYPE_MAHINDRA, BookingParams.TYPE_SPINNY]:
            # Handle B2B-like trips for various types
            if booking.type == BookingParams.TYPE_C24:
                b2b_data = db.session.query(C24Bookings).filter(C24Bookings.ref == booking.id).first()
                b2b_rep = db.session.query(C24Rep).filter(C24Rep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_OLX:
                b2b_data = db.session.query(OLXBookings).filter(OLXBookings.ref == booking.id).first()
                b2b_rep = db.session.query(OLXRep).filter(OLXRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_CARDEKHO:
                b2b_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == booking.id).first()
                b2b_rep = db.session.query(CardekhoRep).filter(CardekhoRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_BHANDARI:
                b2b_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking.id).first()
                b2b_rep = db.session.query(BhandariRep).filter(BhandariRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_MAHINDRA:
                b2b_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == booking.id).first()
                b2b_rep = db.session.query(MahindraRep).filter(MahindraRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_REVV_V2:
                b2b_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == booking.id).first()
                b2b_rep = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_SPINNY:
                b2b_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == booking.id).first()
                b2b_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_PRIDEHONDA:
                b2b_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == booking.id).first()
                b2b_rep = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == b2b_data.rep).first()
            elif booking.type == BookingParams.TYPE_ZOOMCAR:
                b2b_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == booking.id).first()
                b2b_rep = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == b2b_data.rep).first()

            if not b2b_data:
                return jsonify({'trip_id': -1, 'message': 'B2B data not found'})
            driver_trip = {
                "id": b2b_data.id,
                "code": booking.code,
                "region": booking.region,
                "name": b2b_rep.name,
                "startdate": startdate_str,
                "starttime": starttime_str,
                "enddate": enddate_str,
                "endtime": endtime_str,
                "dur": booking.dur.strftime('%H:%M:%S'),
                "lat": lat,
                "long": long_val,
                "estimate": booking.estimate if booking.type in [BookingParams.TYPE_ZOOMCAR,
                                                                   BookingParams.TYPE_CARDEKHO,
                                                                   BookingParams.TYPE_BHANDARI,
                                                                   BookingParams.TYPE_OLX,
                                                                   BookingParams.TYPE_REVV_V2,
                                                                   BookingParams.TYPE_MAHINDRA,
                                                                   BookingParams.TYPE_SPINNY,
                                                                   BookingParams.TYPE_PRIDEHONDA] else 0,
                "car_type": car_type,
                "loc": loc,
                "mobile": b2b_rep.mobile,
                "did_no": booking.did,
                "driver_type": driver_acc.perma,
                "veh_no": b2b_data.veh_no,
                "veh_model": b2b_data.veh_model,
                "b2b_type": b2b_data.trip_type,
                "dest_mob": b2b_data.drop_mob,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "appt_id": b2b_data.appt,
                "trip_id": booking.id,
                "elapsed": elapsed_dur,
                "trip_type": booking.type,
                "days": booking.days,
                "status": trip.status,
            }
        elif booking.type == BookingParams.TYPE_REVV:
            revv_data = db.session.query(RevvBookings).filter(RevvBookings.ref == booking.id).first()
            if not revv_data:
                return jsonify({'trip_id': -1, 'message': 'Revv data not found'})
            driver_trip = {
                "id": revv_data.id,
                "code": booking.code,
                "region": booking.region,
                "dur": booking.dur.strftime('%H:%M:%S'),
                "starttime": starttime_str,
                "startdate": startdate_str,
                "enddate": enddate_str,
                "endtime": endtime_str,
                "lat": lat,
                "long": long_val,
                "estimate": price,
                "loc": loc,
                "mobile": mobile,
                "did_no": booking.did,
                "car_type": car_type,
                "name": name,
                "elapsed": elapsed_dur,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "trip_type": booking.type,
                "days": booking.days,
                "payment": booking.payment_type,
                "trip_id": booking.id,
                "status": trip.status,
            }
        elif booking.type == BookingParams.TYPE_GUJRAL:
            gujral_data = db.session.query(GujralBookings).filter(GujralBookings.ref == booking.id).first()
            if not gujral_data:
                return jsonify({'trip_id': -1, 'message': 'Gujral data not found'})
            driver_trip = {
                "id": gujral_data.id,
                "code": booking.code,
                "region": booking.region,
                "dur": booking.dur.strftime('%H:%M:%S'),
                "starttime": starttime_str,
                "startdate": startdate_str,
                "enddate": enddate_str,
                "endtime": endtime_str,
                "lat": lat,
                "long": long_val,
                "estimate": price,
                "loc": loc,
                "mobile": mobile,
                "did_no": booking.did,
                "car_type": car_type,
                "name": name,
                "elapsed": elapsed_dur,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "trip_type": booking.type,
                "days": booking.days,
                "payment": booking.payment_type,
                "trip_id": booking.id,
                "status": trip.status,
            }
        elif booking.type == BookingParams.TYPE_B2B:
            # Process B2B trip pricing and affiliate data
            # affiliate_id = db.session.query(AffBookingLogs.aff_id).filter(
            #     AffBookingLogs.book_id == int(booking.id)
            # ).scalar()
            # affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
            affiliate_book_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': int(booking.id)})
            trip_type = BookingParams.get_b2b_trip_type(affiliate_book_mongo.get("trip_type", ""))
            price, pre_tax, cgst, sgst, add_due, driver_fare, cust_night, cust_ot, driver_night, driver_ot = Price.get_trip_price(
                book_id=int(booking.id),
                book_delta=estimate_delta,
                real_delta=time_delta,
                est=pre_tax,
                book_starttime=booking.starttime,
                book_stoptime=booking.endtime,
                trip_starttime=trip.starttime.time(),
                trip_stoptime=stop_time.time(),
                startdate=trip.starttime.date(),
                enddate=stop_time.date(),
                insurance_ch=booking.insurance_cost,
                city=booking.region,
                type=booking.type,
                client_name=affiliate_book_mongo.get("client_name", "") if affiliate_book_mongo else "",
                client_trip_type=trip_type
            )
            if not has_started and booking.type != BookingParams.TYPE_B2B:
                price = est
                pre_tax = booking.estimate_pre_tax
            if driver_acc.perma and booking.payment_type != PaymentType.PAY_CASH:
                price = 0

            # Re-fetch destination in case it changed
            try:
                dest_data = db.session.query(BookDest).filter(BookDest.book_id == booking.id).first()
                dest_lat = dest_data.lat
                dest_long = dest_data.lng
                dest_loc = dest_data.name
            except Exception:
                print("Could not find destination entry for", str(booking.id))
                dest_lat, dest_long = 0.0, 0.0
                dest_loc = ''

            # affiliate_mongo = affiliates_collection.find_one({
            #     "affiliate_id": int(affiliate_book_mongo.get("affiliate_id")),
            #     "trip_type.trip_type_name": affiliate_book_mongo.get("trip_name")
            # })
            # matching_trip_types = [
            #     t for t in affiliate_mongo.get("trip_type", [])
            #     if t.get("trip_type_name") == affiliate_book_mongo.get("trip_name")
            # ]
            # all_start_images = [t.get("startImages", []) for t in matching_trip_types]
            # all_stop_images = [t.get("stopImages", []) for t in matching_trip_types]

            if not affiliate_book_mongo:
                return jsonify({'success': -1, 'message': 'B2B data not found'})
            driver_trip = {
                "id": booking.id,
                "code": booking.code,
                "region": booking.region,
                "name": affiliate_book_mongo.get("rep_fullname", ""),
                "startdate": startdate_str,
                "starttime": starttime_str,
                "enddate": enddate_str,
                "endtime": endtime_str,
                "dur": booking.dur.strftime('%H:%M:%S'),
                "lat": lat,
                "long": long_val,
                "estimate": pricing.driver_base_ch + pricing.driver_night_ch,
                "car_type": car_type,
                "loc": loc,
                "mobile": affiliate_book_mongo.get("spoc_data", {}).get("source_spoc_contact", ""),
                "did_no": booking.did,
                "driver_type": driver_acc.perma,
                "veh_no" : affiliate_book_mongo.get("vehicle_no","") if affiliate_book_mongo else "",
                "veh_model" :  affiliate_book_mongo.get("vehicle_model","") if affiliate_book_mongo else "",
                "b2b_type": affiliate_book_mongo.get("trip_type"),
                "b2b_mapped_trip_type": affiliate_book_mongo.get("trip_name", ""),
                "dest_mob": affiliate_book_mongo.get("spoc_data", {}).get("dest_spoc_contact", ""),
                "affiliate_name": affiliate_book_mongo.get("client_name", ""),
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "appt_id": affiliate_book_mongo.get("appointment_id",""),
                "trip_id": booking.id,
                "elapsed": elapsed_dur,
                "trip_type": booking.type,
                "days": booking.days,
                "status": trip.status,
                "trip_start_images": affiliate_book_mongo.get("trip_start_images_structure", {}),
                "trip_stop_images": affiliate_book_mongo.get("trip_stop_images_structure", {}),
                "source_name": affiliate_book_mongo.get("spoc_data", {}).get("source_spoc_name", ""),
                "rep_mob":affiliate_book_mongo.get("rep_mobile", "") ,
            }
        else:
            # Default branch for other types
            driver_trip = {
                "trip_id": booking.id,
                "booking_id": booking.id,
                "code": booking.code,
                "region": booking.region,
                "dur": booking.dur.strftime('%H:%M:%S'),
                "starttime": starttime_str,
                "startdate": startdate_str,
                "enddate": enddate_str,
                "endtime": endtime_str,
                "lat": lat,
                "long": long_val,
                "estimate": price,
                "loc": loc,
                "mobile": mobile,
                "did_no": booking.did,
                "car_type": car_type,
                "name": name,
                "elapsed": elapsed_dur,
                "dest_lat": dest_lat,
                "dest_long": dest_long,
                "dest_loc": dest_loc,
                "trip_type": booking.type,
                "days": booking.days,
                "payment": booking.payment_type,
                "insurance": booking.insurance,
                "insurance_ch": booking.insurance_cost,
                "status": trip.status,
            }
    except Exception as e:
        return jsonify({'trip_id': -1, 'message': 'Server error', 'error': str(e)})

    return jsonpickle.encode(driver_trip), 200

@drivers.route('/api/driver/list_confirm', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/list_confirm.yml')
@jwt_required()
def list_confirm():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'User not authorized'}), 401

    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    driver_acc = Drivers.query.filter_by(user=driver_user).first()
    if not driver_acc:
        return jsonify({'success': -2, 'count': 0, 'message': 'Driver account not found'})

    driver = driver_acc.id
    driver_rate = driver_acc.rating
    cur_dt = datetime.datetime.utcnow() - datetime.timedelta(seconds=3600)
    curdate, curtime = cur_dt.strftime("%Y-%m-%d"), cur_dt.strftime("%H:%M:%S")

    results_b2c_round = db.session.query(Bookings, BookPending, BookPricing).filter(
        BookPending.driver == driver,
        BookPending.valid == 1,
        BookPending.book_id == Bookings.id,
        BookPricing.book_id == Bookings.id,
        Bookings.valid == 0,
        or_(
            Bookings.startdate > curdate,
            and_(Bookings.startdate == curdate, Bookings.starttime > curtime)
        )
    ).filter(Bookings.type == BookingParams.TYPE_ROUNDTRIP).order_by(Bookings.starttime.desc()).all()


    base_query = db.session.query(Bookings, BookPending, BookPricing, BookDest).outerjoin(
        BookDest, BookDest.book_id == Bookings.id).filter(
        BookPending.driver == driver,
        BookPending.valid == 1,
        BookPending.book_id == Bookings.id,
        BookPricing.book_id == Bookings.id,
        Bookings.valid == 0,
        or_(
            Bookings.startdate > curdate,
            and_(Bookings.startdate == curdate, Bookings.starttime > curtime)
        )
    )
    result = base_query.all()
    results_b2c_other = base_query.filter(Bookings.type > BookingParams.TYPE_ROUNDTRIP, Bookings.type < BookingParams.TYPE_C24).all()
    results_b2b = base_query.filter(Bookings.type == BookingParams.TYPE_B2B).all()





    results_c24 = db.session.query(Bookings, BookPending, BookPricing, C24Bookings, C24Rep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(C24Bookings.ref == Bookings.id).filter(C24Rep.id == C24Bookings.rep).filter(BookDest.book_id == Bookings.id). \
        order_by(Bookings.starttime.desc()).all()
    #print("Time", tm.time() - sttm)
    #sttm = tm.time()
    results_olx = db.session.query(Bookings, BookPending, BookPricing, OLXBookings, OLXRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_OLX). \
        filter(OLXBookings.ref == Bookings.id).filter(OLXRep.id == OLXBookings.rep).filter(BookDest.book_id == Bookings.id). \
        order_by(Bookings.starttime.desc()).all()
    #print("Time", tm.time() - sttm)
    #sttm = tm.time()
    results_cardekho = db.session.query(Bookings, BookPending, BookPricing, CardekhoBookings, CardekhoRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_CARDEKHO).filter(BookDest.book_id == Bookings.id). \
        filter(CardekhoBookings.ref == Bookings.id).filter(CardekhoRep.id == CardekhoBookings.rep). \
        order_by(Bookings.starttime.desc()).all()
    #print("Time", tm.time() - sttm)
    #sttm = tm.time()
    results_bhandari = db.session.query(Bookings, BookPending, BookPricing, BhandariBookings, BhandariRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_BHANDARI).filter(BookDest.book_id == Bookings.id). \
        filter(BhandariBookings.ref == Bookings.id).filter(BhandariRep.id == BhandariBookings.rep). \
        order_by(Bookings.starttime.desc()).all()
    #print("Time", tm.time() - sttm)
    #sttm = tm.time()
    results_zoomcar = db.session.query(Bookings, BookPending, BookPricing, ZoomcarBookings, ZoomcarRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_ZOOMCAR).filter(BookDest.book_id == Bookings.id). \
        filter(ZoomcarBookings.ref == Bookings.id).filter(ZoomcarRep.id == ZoomcarBookings.rep). \
        order_by(Bookings.starttime.desc()).all()
    results_mahindra = db.session.query(Bookings, BookPending, BookPricing, MahindraBookings, MahindraRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_MAHINDRA).filter(BookDest.book_id == Bookings.id). \
        filter(MahindraBookings.ref == Bookings.id).filter(MahindraRep.id == MahindraBookings.rep). \
        order_by(Bookings.starttime.desc()).all()
    results_revv_v2 = db.session.query(Bookings, BookPending, BookPricing, RevvV2Bookings, RevvV2Rep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_REVV_V2).filter(BookDest.book_id == Bookings.id). \
        filter(RevvV2Bookings.ref == Bookings.id).filter(RevvV2Rep.id == RevvV2Bookings.rep). \
        order_by(Bookings.starttime.desc()).all()

    results_spinny = db.session.query(Bookings, BookPending, BookPricing, SpinnyBookings, SpinnyRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_SPINNY).filter(BookDest.book_id == Bookings.id). \
        filter(SpinnyBookings.ref == Bookings.id).filter(SpinnyRep.id == SpinnyBookings.rep). \
        order_by(Bookings.starttime.desc()).all()
    results_pridehonda = db.session.query(Bookings, BookPending, BookPricing, PrideHondaBookings, PrideHondaRep, BookDest).filter(BookPending.driver == driver). \
        filter(BookPending.valid == 1). \
        filter(BookPending.book_id == Bookings.id).filter(BookPricing.book_id == Bookings.id). \
        filter(and_(or_(Bookings.startdate > curdate,
                                                          and_(Bookings.startdate == curdate,
                                                               Bookings.starttime > curtime)))). \
        filter(Bookings.valid == 0).filter(Bookings.type == BookingParams.TYPE_PRIDEHONDA).filter(BookDest.book_id == Bookings.id). \
        filter(PrideHondaBookings.ref == Bookings.id).filter(PrideHondaRep.id == PrideHondaBookings.rep). \
        order_by(Bookings.starttime.desc()).all()





    results = (results_b2c_round + results_b2c_other + results_c24 +
               results_olx + results_cardekho + results_bhandari +
               results_zoomcar + results_mahindra + results_revv_v2 + results_spinny
               + results_pridehonda + results_b2b)
    #results = results_b2c_round + results_b2c_other + results_b2b


    print("Lengths of b2b: ",len(results_b2b),flush=True)
    # if not driver_acc or not driver_acc.available:
    #     return jsonify({'success': -2, 'count': len(results), 'message': 'Driver not available'}), 200

    result_json = []
    print("Length: ",len(results),flush=True)

    b2b_refs = {int(result[0].id) for result in results if result[0].type == BookingParams.TYPE_B2B}
    affiliates_cursor = AffiliateCollections.affiliates_book.find({'book_ref': {'$in': list(b2b_refs)}})
    affiliates_data = {doc['book_ref']: doc for doc in affiliates_cursor}

    for result in results:
        try:

            if result[0].type < BookingParams.TYPE_C24 or result[0].type == BookingParams.TYPE_B2B:
                car_type = result[0].car_type
            else:
                car_type = 0
            if result[0].type < BookingParams.TYPE_C24:
                if result[0].type != BookingParams.TYPE_ROUNDTRIP and result[3]:
                    dest_data = result[3]
                else:
                    dest_data = None
            elif result[0].type == BookingParams.TYPE_B2B:
                dest_data = result[3]
            else:
                dest_data = result[5]
            try:
                dest_lat = dest_data.lat
                dest_long = dest_data.lng
                dest_loc = dest_data.name
            except Exception:
                dest_lat = dest_long = 0.0
                dest_loc = ''
            if result[0].type == BookingParams.TYPE_B2B:
                affiliate_mongo = affiliates_data.get(int(result[0].id))
                # b2b_id = affiliate_mongo.get("affiliate_id") if affiliate_mongo else ""
                b2b_mobile = affiliate_mongo.get("rep_mobile") if affiliate_mongo else ""
                b2b_name = affiliate_mongo.get("rep_fullname") if affiliate_mongo else ""
                b2b_type = affiliate_mongo.get("trip_type") if affiliate_mongo else ""
                b2b_mapped_trip_type =  affiliate_mongo.get("trip_name") if affiliate_mongo else ""
                b2b_dest_spoc_number = affiliate_mongo.get("spoc_data").get("dest_spoc_contact","") if affiliate_mongo else ""
                b2b_dest_spoc_name = affiliate_mongo.get("spoc_data").get("dest_spoc_name    ","") if affiliate_mongo else ""
                b2b_source_spoc_number = affiliate_mongo.get("spoc_data").get("source_spoc_contact","") if affiliate_mongo else ""
                b2b_source_spoc_name = affiliate_mongo.get("spoc_data").get("source_spoc_name","") if affiliate_mongo else ""
                b2b_veh_no = affiliate_mongo.get("vehicle_no","") if affiliate_mongo else ""
                b2b_veh_model =  affiliate_mongo.get("vehicle_model","") if affiliate_mongo else ""
                client_name = affiliate_mongo.get("client_name", "") if affiliate_mongo else ""
                b2b_appt_id = affiliate_mongo.get("appointment_id", "") if affiliate_mongo else ""
                b2b_est = result[2].driver_base_ch + result[2].driver_night_ch

                res_data = {
                    "id": result[0].id,
                    "name": b2b_name,
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime('%H:%M:%S'),
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": b2b_est,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "mobile": b2b_source_spoc_number,
                    "trip_type": result[0].type,
                    "days": result[0].days,
                    "driver_type": driver_acc.perma,
                    "veh_no": b2b_veh_no,
                    "veh_model": b2b_veh_model,
                    "b2b_type": b2b_type,
                    "b2b_mapped_trip_type":b2b_mapped_trip_type,
                    "dest_mob": b2b_dest_spoc_number,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "appt_id": b2b_appt_id,
                    "trip_id": result[0].id,
                    "code": result[0].code,
                    "source_name": b2b_source_spoc_name,
                    "rep_mob": b2b_mobile,
                    "dest_name": b2b_dest_spoc_name,
                    "affiliate_name": client_name,
                    "region": result[0].region,
                    "insurance": result[0].insurance,
                    "insurance_ch": result[0].insurance_cost,
                }
            elif (result[0].type >= BookingParams.TYPE_C24):  # is b2b
                b2b_data = result[3]
                b2b_rep = result[4]
                b2b_id = b2b_data.id
                b2b_est = 0
                if b2b_rep:
                    b2b_mobile = b2b_rep.mobile
                    b2b_name = b2b_rep.name
                if b2b_data:
                    b2b_type = b2b_data.trip_type
                    b2b_dest_spoc = b2b_data.drop_mob
                    b2b_veh_no = b2b_data.veh_no
                    b2b_veh_model = b2b_data.veh_model
                    b2b_appt_id = b2b_data.appt
                else:
                    b2b_type = 0
                    b2b_dest_spoc = 0
                    b2b_veh_no = 0
                    b2b_veh_model = "UNKNOWN"
                    b2b_appt_id = -1
                if (result[0].type == BookingParams.TYPE_ZOOMCAR or
                    result[0].type == BookingParams.TYPE_CARDEKHO or
                    result[0].type == BookingParams.TYPE_BHANDARI or
                    result[0].type == BookingParams.TYPE_OLX or
                    result[0].type == BookingParams.TYPE_REVV_V2 or
                    result[0].type == BookingParams.TYPE_MAHINDRA or
                    result[0].type == BookingParams.TYPE_SPINNY or
                    result[0].type == BookingParams.TYPE_PRIDEHONDA):
                    b2b_est = result[0].estimate
                    res_data = {
                        "id": b2b_id,
                        "name": b2b_name,
                        "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                        "starttime": result[0].starttime.strftime("%H:%M:%S"),
                        "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                        "endtime": result[0].endtime.strftime("%H:%M:%S"),
                        "dur": result[0].dur.strftime("%H:%M:%S"),  # assuming this is already in the correct format
                        "lat": result[0].lat,
                        "long": result[0].long,
                        "estimate": b2b_est,
                        "car_type": car_type,
                        "loc": result[0].loc,
                        "mobile": b2b_mobile,
                        "trip_type": result[0].type,
                        "days": result[0].days,
                        "dest_lat": dest_lat,
                        "dest_long": dest_long,
                        "dest_loc": dest_loc,
                        "driver_type": driver_acc.perma,
                        "veh_no": b2b_veh_no,
                        "veh_model": b2b_veh_model,
                        "b2b_type": b2b_type,
                        "dest_mob": b2b_dest_spoc,
                        "appt_id": b2b_appt_id,
                        "trip_id": result[0].id,
                        "region": result[0].region,
                        "code": result[0].code,
                        "insurance": result[0].insurance,
                        "insurance_ch": result[0].insurance_cost,
                    }

            else:
                res_data = {
                    "id": result[0].id,
                    "name": "D4M Customer",
                    "startdate": result[0].startdate.strftime("%Y-%m-%d"),
                    "starttime": result[0].starttime.strftime("%H:%M:%S"),
                    "enddate": result[0].enddate.strftime("%Y-%m-%d"),
                    "endtime": result[0].endtime.strftime("%H:%M:%S"),
                    "dur": result[0].dur.strftime("%H:%M:%S"),
                    "days": result[0].days,
                    "lat": result[0].lat,
                    "long": result[0].long,
                    "estimate": result[2].estimate,
                    "car_type": car_type,
                    "loc": result[0].loc,
                    "dest_lat": dest_lat,
                    "dest_long": dest_long,
                    "dest_loc": dest_loc,
                    "mobile": "",
                    "trip_type": result[0].type,
                    "region": result[0].region,
                    "insurance": result[0].insurance,
                    "insurance_ch": result[0].insurance_cost,
                    "code": result[0].code
                }
        except Exception as e:
            print("error", e, flush=True)
            continue
        result_json.append(json.loads(jsonpickle.encode(res_data)))

    return jsonify(result_json if result_json else {'success': -1, 'message': 'No confirmed bookings found'})

