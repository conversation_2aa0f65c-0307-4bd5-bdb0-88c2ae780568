#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import re
import datetime
from flask_cors import cross_origin

from flask import Blueprint, request, jsonify
from flask_jwt_extended import set_access_cookies, set_refresh_cookies
from flasgger import swag_from
from pydantic import BaseModel,ValidationError,constr,field_validator
from sqlalchemy import exc
from flask_jwt_extended import jwt_required, get_jwt

from services.admin_dashboard.admin_login_service import generate_otp_service, validate_admin_otp_service, \
    login_admin_service,admin_refresh_service,validate_password_change_otp_service,password_change_service,delete_user_service   
from models.models import UserToken, db,Users
from utils.auth.admin_login_utils import check_token_revoked
from utils.response_utils import standard_response
from flask import current_app as app

admin_login = Blueprint('admin_login', __name__)

class GenerateOtpPayload(BaseModel):
    mobile: str

    @field_validator("mobile")
    @classmethod
    def validate_mobile(cls, v):
        if not re.fullmatch(r'\d{10}', v):
            raise ValueError("Invalid mobile number")
        return v
    
    
class ValidateAdminOtpPayload(BaseModel):
    mobile: str
    otp: str

    @field_validator("mobile")
    @classmethod
    def valid_mobile(cls, v):
        if not re.fullmatch(r'\d{10}', v):
            raise ValueError("Invalid mobile number")
        return v

    @field_validator("otp")
    @classmethod
    def valid_otp(cls, v):
        if not re.fullmatch(r'\d{4,6}', v):
            raise ValueError("Invalid OTP format")
        return v
    

@admin_login.route('/token/admin/otp/generate', methods=['POST'])
def generate_otp():
    try:
        try:
            payload = GenerateOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in generate_otp: {error_details}")
            return jsonify(standard_response(
                success=-1,
                status=400,
                message=ve.errors()[0]['msg'],
                response_status="error"
            )), 400

        success, message = generate_otp_service(payload)
        if success == -3:
            return jsonify(standard_response(
                success=-3,
                status=429,
                message=message,
                response_status="error"
            )), 429
        if success == -2:
            return jsonify(standard_response(
                success=-2,
                status=401,
                message=message,
                response_status="error"
            )), 401
        return jsonify(standard_response(
            success=1,
            status=200,
            message="OTP sent successfully",
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in generate_otp: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
    
@admin_login.route('/token/admin/otp/validate', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/validate_otp.yml')
def val_otp():
    try:
        try:
            payload = ValidateAdminOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in val_otp: {error_details}")
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        user_agent = request.headers.get('User-Agent')
        result = validate_admin_otp_service(payload, user_agent)

        if isinstance(result, tuple) and len(result) == 3 and isinstance(result[2], dict):
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code

        # result is (access_token, refresh_token, refresh_expiry, user_data)
        access_token, refresh_token, refresh_expiry, user_data = result

        expiry = datetime.datetime.now() + refresh_expiry
        token_entry = UserToken(
            user_data['id'],  # cur_user.id
            refresh_token,
            user_agent,
            expiry,
            login_from=UserToken.ADMIN_LOGIN
        )

        try:
            db.session.add(token_entry)
            db.session.commit()
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Invalid refresh token. Please login again.",
                response_status="error"
            )), 401
        # resp = jsonify(user_data)
        resp = jsonify(standard_response(
            success=1,
            status=200,
            message="OTP validated successfully",
            data=user_data,
            response_status="success"
        ))
        set_access_cookies(resp, access_token)
        set_refresh_cookies(resp, refresh_token)
        return resp

    except Exception as e:
        app.logger.exception(f"Unexpected error in val_otp: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500


class AdminLoginPayload(BaseModel):
    mobile: str
    pwd: str

    @field_validator("mobile", "pwd")
    @classmethod
    def not_empty(cls, v):
        if not v.strip():
            raise ValueError("Field cannot be empty")
        return v
    
@admin_login.route('/token/admin/login', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/login_for_admin.yml')
@cross_origin(supports_credentials=True)
def login_admin():
    try:
        try:
            payload = AdminLoginPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in login_admin: {error_details}")
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        user_agent = request.headers.get('User-Agent')
        result = login_admin_service(payload, user_agent)

        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code

        access_token, refresh_token, refresh_expiry, user_data = result
        expiry = datetime.datetime.now() + refresh_expiry

        token_entry = UserToken(
            user_data['id'],
            refresh_token,
            user_agent,
            expiry,
            login_from=UserToken.ADMIN_LOGIN
        )

        try:
            db.session.add(token_entry)
            db.session.commit()
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Invalid refresh token. Please login again.",
                response_status="error"
            )), 401

        # resp = jsonify(user_data)
        resp = jsonify(standard_response(
            success=1,
            status=200,
            message="Login successful",
            data=user_data,
            response_status="success"
        ))
        set_access_cookies(resp, access_token)
        set_refresh_cookies(resp, refresh_token)
        return resp

    except Exception as e:
        db.session.rollback()
        app.logger.exception(f"Unexpected error in login_admin: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
@admin_login.route('/api/admin/refresh', methods=['POST'])   
@swag_from('/app/swagger_docs/login_admin/refresh_admin_token.yml')
@jwt_required(refresh=True)
def admin_refresh():
    try:
        refresh_token = request.cookies.get("refresh_token_cookie")
        if not refresh_token:
            return jsonify(standard_response(
                success=0,
                status=401,
                message="Refresh token not found",
                response_status="error"
            )), 401
            # return jsonify({'success': 0, 'refresh': False}), 401

        result = admin_refresh_service(refresh_token)
        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code
            # return jsonify({'success': success, **data}), status_code

        access_token = result
        resp = jsonify({'success': 1, 'refresh': True})
        set_access_cookies(resp, access_token)
        # return jsonify(standard_response(
        #     success=1,
        #     status=200,
        #     message="Token refreshed successfully",
        #     data={'refresh': True},
        #     response_status="success"
        # )), 200
        return resp, 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_refresh: {e}")
        return jsonify(standard_response(
            success=-4,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': -4, 'error': str(e), 'refresh': False}), 500
    
    
class ValidatePasswordChangeOtpPayload(BaseModel):
    mobile: constr(min_length=10, max_length=10)
    otp: constr(min_length=1)
    
@admin_login.route('/token/admin/password_change/otp/validate', methods=['POST'])
def password_change_val_otp():
    try:
        try:
            payload = ValidatePasswordChangeOtpPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in password_change_val_otp: {error_details}")
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400
            # return jsonify({'success': 0}), 400

        result = validate_password_change_otp_service(payload)

        if isinstance(result, tuple) and len(result) == 3:
            success, status_code, data = result
            return jsonify(standard_response(
                success=success,
                status=status_code,
                message=data.get('message') or data.get('error', 'Failed'),
                data=data,
                response_status="error"
            )), status_code
            # return jsonify({'success': success, **data}), status_code
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Otp validated successfully",
            response_status="success"
        )), 200
        # return jsonify({'success': 1, "message": "Otp validated successfully"})

    except Exception as e:
        app.logger.exception(f"Unexpected error in password_change_val_otp: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': -5, 'error': str(e)}), 500
    
    
class AdminPasswordChangePayload(BaseModel):
    mobile: constr(min_length=10, max_length=10)
    new_pwd: constr(min_length=1)
    
@admin_login.route('/token/admin/password_change', methods=['POST'])
def password_change():
    try:
        try:
            payload = AdminPasswordChangePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in password_change: {error_details}")
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400
            # return jsonify({'success': 0}), 400

        success, status_code, data = password_change_service(payload)
        return jsonify(standard_response(
            success=success,
            status=status_code,
            message=data.get('message') or data.get('error', 'Failed'),
            data=data,
            response_status="error"
        )), status_code
        # return jsonify({'success': success, **data}), status_code

    except Exception as e:
        app.logger.exception(f"Unexpected error in password_change: {e}")
        return jsonify(standard_response(
            success=0,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({'success': 0, 'error': str(e)}), 500
    
class AdminUserDeletePayload(BaseModel):
    user_id: int
    
    
@admin_login.route('/token/admin/user/delete', methods=['POST'])
@swag_from('/app/swagger_docs/login_admin/delete_user.yml')
@jwt_required()
@check_token_revoked
def delete_user():
    claims = get_jwt()
    if claims.get('roles') != Users.ROLE_SUPERADMIN:
        return jsonify(standard_response(
            success=0,
            status=403,
            message="This is not valid api call",
            response_status="error"
        )), 403
        # return jsonify({"status": "error", "message": "This is not valid api call"}), 500

    try:
        try:
            payload = AdminUserDeletePayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            app.logger.warning(f"Validation error in delete_user: {error_details}")
            return jsonify(standard_response(
                success=0,
                status=400,
                message="User ID required",
                response_status="error"
            )), 400
            # return jsonify({"status": 400, "result": "FAILURE", "message": "User ID required"}), 400

        status_code, data = delete_user_service(payload)
        return jsonify(standard_response(
            success=1,
            status=status_code,
            message="User deleted and tokens invalidated",
            data=data,
            response_status="success"
        )), status_code
        # return jsonify({"status": status_code, **data}), status_code

    except Exception as e:
        app.logger.exception(f"Unexpected error in delete_user: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        # return jsonify({"status": 500, "result": "FAILURE", "message": {"message": str(e)}}), 500