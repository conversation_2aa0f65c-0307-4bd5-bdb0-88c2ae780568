#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>


# Built-in imports
from datetime import datetime, timedelta, date
import time
import calendar

# Third-party imports
from flask import Blueprint, request, jsonify
from sqlalchemy.orm import aliased
from sqlalchemy.sql import func, case, text, cast
from sqlalchemy.types import Numeric
from sqlalchemy import or_, and_, not_, distinct
from flasgger import swag_from
from pydantic import BaseModel,ValidationError,Field,field_validator
from typing import Optional, Literal ,List
from flask_jwt_extended import jwt_required
import traceback
from flask import current_app as app

# Internal application imports
from utils.bookings.booking_params import Regions
from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from  utils.time_utils import convert_to_utc
from utils.response_utils import standard_response
from services.admin_dashboard.analytics_service import admin_analytics_count_service,get_daily_sales_data, \
    get_analytics_graph_sales,get_analytics_daily_revenue,get_revenue_graph_data,get_daily_trips_graph_data, \
    get_trips_graph_data,get_total_ratings_data,get_customer_registration_counts,get_customer_source_counts, \
    get_cancellation_reason_counts,get_transaction_summary,get_customer_transaction_summary_admin, \
        get_driver_transaction_summary_admin,get_driver_inventory_count, booking_summary_admin_service, \
    fetch_locations,parse_regions,get_live_routes_data,delete_ongoing_route_doc


admin_analytics = Blueprint('admin_analytics', __name__)



class AnalyticsCountPayload(BaseModel):
    from_date: str
    to_date: str
    region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date(cls, value):
        try:
            datetime.strptime(value, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return value
    
@admin_analytics.route('/api/admin/analytics_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_count.yml')
def admin_analytics_count():
    try:
        try:
            payload = AnalyticsCountPayload(**request.form.to_dict())
        except ValidationError as ve:
            error_details = [
                f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
                for err in ve.errors()
            ]
            return jsonify(standard_response(
                success=-4,
                status=422,
                message=error_details,
                response_status="error"
            )), 422

        success, message, data = admin_analytics_count_service(payload)
        if success < 0:
            return jsonify(standard_response(
                success=success,
                status=400,
                message=message,
                response_status="error"
            )), 400
        return jsonify(standard_response(
            success=success,
            status=200,
            message=message,
            data=data,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_count: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
class AdminAnalyticsDailySalesPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
    
@admin_analytics.route('/api/admin/analytics_graph_daily_sales', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS,Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_sales.yml')
def admin_analytics_graph_daily_sales():
    try:
        try:
            payload = AdminAnalyticsDailySalesPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        response_data = get_daily_sales_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily sales analytics",
            data=response_data,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_sales: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
class AdminAnalyticsSalesPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
    
@admin_analytics.route('/api/admin/analytics_graph_sales', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_sales.yml')
def admin_analytics_graph_sales():
    try:
        try:
            payload = AdminAnalyticsSalesPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        result = get_analytics_graph_sales(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved sales analytics",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_sales: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class AdminAnalyticsDailyRevenuePayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    

@admin_analytics.route('/api/admin/analytics_graph_daily_revenue', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_revenue.yml')  
def admin_analytics_graph_daily_revenue():
    try:
        try:
            payload = AdminAnalyticsDailyRevenuePayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        result = get_analytics_daily_revenue(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily revenue analytics",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_revenue: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
class AnalyticsRevenuePayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
    
@admin_analytics.route('/api/admin/analytics_graph_revenue', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_revenue.yml')
def admin_analytics_graph_revenue():
    try:
        try:
            payload = AnalyticsRevenuePayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        result, status = get_revenue_graph_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved revenue analytics",
            data=result,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_revenue: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
        
class DailyTripsPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
    
@admin_analytics.route('/api/admin/analytics_graph_daily_trips', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_daily_trips.yml')
def admin_analytics_graph_daily_trips():
    try:
        try:
            payload = DailyTripsPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        response, status_code = get_daily_trips_graph_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved daily trips analytics",
            data=[response],
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_daily_trips: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
        
class TripsAnalyticsPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    

@admin_analytics.route('/api/admin/analytics_graph_trips', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_trips.yml')
def admin_analytics_graph_trips():
    try:
        try:
            payload = TripsAnalyticsPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Invalid input",
                response_status="error"
            )), 400

        response, status = get_trips_graph_data(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved trips analytics",
                data=[response],
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in admin_analytics_graph_trips: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
class RatingAnalyticsPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    search_region: str
    
    @field_validator('from_date', 'to_date', 'from_time', 'to_time', 'search_region')
    @classmethod
    def no_empty_string(cls, v):
        if v is None or str(v).strip() == '':
            raise ValueError("Required field is missing or empty")
        return v
    
@admin_analytics.route('/api/admin/total_ratings', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_user_rating_count.yml')
def total_counts_ratings():
    try:
        try:
            payload = RatingAnalyticsPayload(**request.form.to_dict())
        except ValidationError as ve:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Missing or invalid date values",
                response_status="error"
            )), 400

        response, status = get_total_ratings_data(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved total ratings and average rating data",
            data=response,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_ratings: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class CustomerRegistrationCountPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"

    @field_validator("from_date", "to_date")
    @classmethod
    def no_empty_dates(cls, v):
        if not v or not v.strip():
            raise ValueError("Date field is required")
        return v
    
@admin_analytics.route('/api/admin/customer_register_reg_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_customer_register_reg_count.yml')
def total_counts_reg_customer():
    try:
        try:
            payload = CustomerRegistrationCountPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Missing or invalid date values",
                response_status="error"
            )), 400

        response, status = get_customer_registration_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved total customer registration counts by city",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_reg_customer: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
class CustomerRegisterSourcePayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    search_region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Date is required")
        return v
    
@admin_analytics.route('/api/admin/customer_register_source_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/analytics_customer_register_source_count.yml')
def total_counts_source_customer():
    try:
        try:
            payload = CustomerRegisterSourcePayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Missing or invalid date values",
                response_status="error"
            )), 400

        response, status = get_customer_source_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved total customer registration counts by source",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in total_counts_source_customer: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class CancellationReasonCountPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    search_region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Missing or invalid date")
        return v
    
    
@admin_analytics.route('/api/admin/cancellation_reason_counts', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
@swag_from('/app/swagger_docs/analytics_admin/analytics_cancellation_reason_counts.yml')
def cancellation_reason_counts():
    try:
        try:
            payload = CancellationReasonCountPayload(**request.form.to_dict())
        except ValidationError:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Missing or invalid date values",
                response_status="error"
            )), 400

        response, status = get_cancellation_reason_counts(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved cancellation reason counts",
                data=response,
                response_status="success"
            )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in cancellation_reason_counts: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
        
    
class TransactionSummaryPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Literal["Drivers", "Customers"] = "Drivers"
    search_region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_dates(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return v
    
    
@admin_analytics.route('/api/admin/transaction_summary', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary.yml')
def transaction_summary():
    try:
        payload_dict = request.form.to_dict()
        payload = TransactionSummaryPayload(**payload_dict)
        result, status = get_transaction_summary(payload)
        if status == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved transaction summary",
                data=result,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=0,
                status=result['status'],
                message=result['message'],
                response_status="error"
            )), status
    except ValidationError as ve:
        return jsonify(standard_response(
            success=-1,
            status=422,
            message="Missing or invalid date values",
            response_status="error"
        )), 422
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class TransactionSummaryCustomerAdminValidator(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: str = Literal["Fine", "Gift", "Deduct", "Add", "Cancel"] 
    search_region: Optional[str] = None
    
@admin_analytics.route('/api/admin/transaction_summary_customer_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary_customer_admin.yml')
def transaction_summary_customer_admin():
    try:
        payload = TransactionSummaryCustomerAdminValidator(**request.form.to_dict())
    except Exception as e:
        return jsonify(standard_response(
            success=0,
            status=400,
            message="Validation Error: " + str(e),
            response_status="error"
        )), 400
    try:
        result = get_customer_transaction_summary_admin(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved transaction summary",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary_customer_admin: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500


class TransactionSummaryDriverAdminValidator(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Optional[str] = "Fine"
    search_region: Optional[str] = "-1"
    @field_validator("from_date", "to_date")
    @classmethod
    def validate_dates(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return v
    
@admin_analytics.route('/api/admin/transaction_summary_driver_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/transaction_summary_driver_admin.yml')
def transaction_summary_driver_admin():
    try:
        payload = TransactionSummaryDriverAdminValidator(**request.form.to_dict())
    except Exception as e:
        return jsonify(standard_response(
            success=0,
            status=400,
            message="Validation Error: " + str(e),
            response_status="error"
        )), 400

    try:
        result = get_driver_transaction_summary_admin(payload)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved transaction summary",
            data=result,
            response_status="success"
        )), 200
    except Exception as e:
        app.logger.exception(f"Unexpected error in transaction_summary_driver_admin: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class DriverInventoryCountValidator(BaseModel):
    from_date: str = Field(..., description="Start date in YYYY-MM-DD")
    to_date: str = Field(..., description="End date in YYYY-MM-DD")
    from_time: str = Field(default="00:00:00")
    to_time: str = Field(default="23:59:59")
    data_type: str = Field(default="Fine")
    search_region: Optional[str] = Field(default="-1")

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date(cls, value):
        try:
            datetime.strptime(value, "%Y-%m-%d")
            return value
        except Exception:
            raise ValueError("Date must be in YYYY-MM-DD format")
        
        
@admin_analytics.route('/api/admin/driver_inventory_count', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
@swag_from('/app/swagger_docs/analytics_admin/driver_inventory_count.yml')
def driver_inventory_count():
    try:
        payload = DriverInventoryCountValidator(**request.form.to_dict())
        result = get_driver_inventory_count(payload)
        return jsonify(standard_response(success=1, status=200, data=result, message="Success")), 200

    except ValueError as ve:
        return jsonify(standard_response(success=0, status=400, message=str(ve))), 400

    except Exception as e:
        app.logger.exception(f"Unexpected error in driver_inventory_count: {e}")
        return jsonify(standard_response(success=-5, status=500, message="Internal Server Error")), 500
    
    
class BookingSummaryAdminPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Optional[str] = "Allocation"
    sort: Optional[int] = 1
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    search_region: Optional[str] = None

    @property
    def aff_ids(self) -> Optional[List[int]]:
        if self.affiliatefilter:
            return [int(x) for x in self.affiliatefilter.split(',')]
        return None

    @property
    def regions(self) -> List[int]:
        if self.search_region and self.search_region != Regions.ALL_REGIONS_ACCESS:
            return [int(x) for x in self.search_region.split(',')]
        return []
    

@admin_analytics.route('/api/admin/booking_summary_admin', methods=['POST'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS, Tabs.ANALYTICS_B2B])
def booking_summary_admin():
    try:
        payload = BookingSummaryAdminPayload(**request.form.to_dict())
        result, status_code = booking_summary_admin_service(payload)
        if status_code == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully retrieved booking summary",
                data=result,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=0,
                status=status_code,
                message=result['message'],
                response_status="error"
            )), status_code
    except ValidationError:
        return jsonify(standard_response(
            success=-1,
            status=400,
            message="Missing or invalid parameters",
            response_status="error"
        )), 400
    except Exception as e:
        app.logger.exception(f"Unexpected error in booking_summary_admin: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
    
class AnalyticsDataValidator(BaseModel):
    from_date: str
    to_date: str
    data_type: str
    from_date_previous: Optional[str] = None
    to_date_previous: Optional[str] = None
    region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Invalid date format, expected YYYY-MM-DD")
        return v
    
    
@admin_analytics.route('/api/admin/analytics_data', methods=['GET'])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def analytics_data():
    try:
        payload = AnalyticsDataValidator(**request.args)
        regions = parse_regions(payload.region)

        # Convert current period to datetime
        from_date_str, from_time_str = convert_to_utc(payload.from_date, "00:00:00")
        to_date_str, to_time_str = convert_to_utc(payload.to_date, "23:59:59")
        from_date = datetime.strptime(f"{from_date_str} {from_time_str}", '%Y-%m-%d %H:%M:%S')
        to_date = datetime.strptime(f"{to_date_str} {to_time_str}", '%Y-%m-%d %H:%M:%S')

        # Optional previous period
        from_date2 = to_date2 = None
        if payload.from_date_previous and payload.to_date_previous:
            fd2_str, ft2_str = convert_to_utc(payload.from_date_previous, "00:00:00")
            td2_str, tt2_str = convert_to_utc(payload.to_date_previous, "23:59:59")
            from_date2 = datetime.strptime(f"{fd2_str} {ft2_str}", '%Y-%m-%d %H:%M:%S')
            to_date2 = datetime.strptime(f"{td2_str} {tt2_str}", '%Y-%m-%d %H:%M:%S')

            if from_date2 > from_date or to_date2 > to_date:
                return jsonify(standard_response(
                    success=-4,
                    status=400,
                    message="Previous dates cannot be greater than current dates",
                    response_status="error"
                )), 400

        # Fetch data
        locations1 = fetch_locations(payload.data_type, from_date, to_date, regions)
        locations2 = fetch_locations(payload.data_type, from_date2, to_date2, regions) if from_date2 and to_date2 else []

        if locations1 is None or (from_date2 and to_date2 and locations2 is None):
            return jsonify(standard_response(
                success=-2,
                status=400,
                message="Invalid data type",
                response_status="error"
            )), 400
        return jsonify(standard_response(
            success=1,
            status=200,
            message="Successfully retrieved analytics data",
            data=[locations1, locations2],
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in analytics_data: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
class LiveRoutesValidator(BaseModel):
    status_min: int = Field(..., description="Minimum status to filter trips")
    
@admin_analytics.route("/api/admin/analytics/live_routes/<int:status_min>", methods=["GET"])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def route_docs_with_status_filter(status_min):
    try:
        # Validate path param
        payload = LiveRoutesValidator(status_min=status_min)
    except Exception as e:
        return jsonify(standard_response(
            success=-1,
            status=400,
            message="Validation Error: " + str(e),
            response_status="error"
        )), 400

    try:
        data= get_live_routes_data(payload.status_min)
        return jsonify(standard_response(
            success=1,
            status=200,
            message="No active trips found" if not data else "Successfully retrieved live routes",
            data=data,
            response_status="success"
        )), 200

    except Exception as e:
        app.logger.exception(f"Unexpected error in route_docs_with_status_filter: {e}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500
    
class DeleteRouteDocValidator(BaseModel):
    book_id: int = Field(..., description="Booking ID")
    driver_id: int = Field(..., description="Driver ID")
    
    
    
@admin_analytics.route("/api/admin/analytics/delete_route", methods=["DELETE"])
@jwt_required()
@check_access(tab_required=[Tabs.ANALYTICS])
def delete_booking_route_doc():
    try:
        data = request.get_json()
        if not data:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Request JSON missing",
                response_status="error"
            )), 400

        try:
            validated = DeleteRouteDocValidator(**data)
        except Exception as e:
            return jsonify(standard_response(
                success=0,
                status=400,
                message="Validation error: " + str(e),
                response_status="error"
            )), 400

        response, status_code = delete_ongoing_route_doc(
            validated.book_id,
            validated.driver_id
        )
        if status_code == 200:
            return jsonify(standard_response(
                success=1,
                status=200,
                message="Successfully deleted route document",
                data=response,
                response_status="success"
            )), 200
        else:
            return jsonify(standard_response(
                success=response['success'],
                status=status_code,
                message=response['message'],
                response_status="error"
            )), status_code

    except Exception:
        error_details = traceback.format_exc()
        app.logger.exception(f"Unexpected error in delete_booking_route_doc: {error_details}")
        return jsonify(standard_response(
            success=-5,
            status=500,
            message="An unexpected error occurred. Please try again later.",
            response_status="error"
        )), 500