from .models import DriverSearch, DriverSearchHistory, db

class DriverSearchUnified:
    @staticmethod
    def get_by_id(skey):
        return DriverSearch.query.get(skey) or DriverSearchHistory.query.get(skey)

    @staticmethod
    def get_by_column(**kwargs):
        record = DriverSearch.query.filter_by(**kwargs).first()
        if record:
            record._source_table = 'main'
            return record
        record = DriverSearchHistory.query.filter_by(**kwargs).first()
        if record:
            record._source_table = 'history'
        return record

    @staticmethod
    def filter_by(**kwargs):
        # combine both main and history results
        main_results = DriverSearch.query.filter_by(**kwargs).all()
        history_results = DriverSearchHistory.query.filter_by(**kwargs).all()
        return main_results + history_results

    @staticmethod
    def delete_by_column(**kwargs):
        record = DriverSearch.query.filter_by(**kwargs).first()
        if record:
            db.session.delete(record)
            db.session.commit()
            return 'main'
        record = DriverSearchHistory.query.filter_by(**kwargs).first()
        if record:
            db.session.delete(record)
            db.session.commit()
            return 'history'
        return None

    @staticmethod
    def update_by_column(filters: dict, updates: dict):
        record = DriverSearch.query.filter_by(**filters).first()
        source = 'main'
        if not record:
            record = DriverSearchHistory.query.filter_by(**filters).first()
            source = 'history'
        if not record:
            return None
        for key, value in updates.items():
            if hasattr(record, key):
                setattr(record, key, value)
        db.session.commit()
        return source, record
