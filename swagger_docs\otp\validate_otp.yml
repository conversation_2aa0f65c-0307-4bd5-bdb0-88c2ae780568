tags:
  - Authentication
summary: Validate OTP for user login or registration
description: |
  Validates the OTP sent to the user's mobile number for login or registration. 
  Applies rate limiting for failed attempts and locks the user out after too many invalid tries.
consumes:
  - multipart/form-data
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: otp
    in: formData
    type: string
    required: true
    description: OTP received by the user
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")
responses:
  201:
    description: OTP validated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "OTP validated successfully."
        data:
          type: object
          properties:
            token_and_secret:
              type: object
              example: {"token": "abc123", "secret": "xyz456"}
  400:
    description: General OTP verification failure or unexpected error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "OTP verification failed: Invalid credentials."
  401:
    description: OTP expired or incorrect
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -6
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "<PERSON><PERSON> expired. Please request a new one."
  423:
    description: Too many failed OTP attempts; account temporarily locked
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Account locked due to multiple failed attempts. Try again in 600 seconds."
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
