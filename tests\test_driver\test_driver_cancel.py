import pytest
from flask import Flask
from models.models import Users, Drivers, db,AdminAccess,UserToken,DriverVerify,Bookings,BookDest,Trip,UserTrans,DriverLoc,DriverDetails,BookPending,BookPricing,DriverSearch
from utils.security_utils import get_pwd,get_salt
import random
import hashlib
import j<PERSON>pickle
import json
import datetime
from unittest.mock import patch, MagicMock
from sqlalchemy import exc
from utils.bookings.booking_params import BookingParams
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
from flask import current_app as app
import redis_config

#  API - /api/decline/driver/charge

from conftest import create_user_and_driver,unique_user_data,driver_bookings,driver_trip,driver_details
def access_token_create(user_data):
    expires_access = datetime.timedelta(days=365)
    identity_with_claims = {
        'id': user_data.id,
        'roles': user_data.role,
        'region': user_data.region,
        'fname': user_data.fname,
        'lname': user_data.lname,
    }
    with app.app_context():
        access_token = create_access_token(identity=identity_with_claims, additional_claims=identity_with_claims, expires_delta=expires_access)
    return access_token

def test_cancel_driver_charge_not_driver(client, driver_login):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    access_token=access_token_create(user)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id': 1,
    }
    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_cancel_driver_charge_incomplete_booking(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id': None,
    }
    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_cancel_driver_charge_account_disabled(client,driver_login):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.enabled = False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    access_token=access_token_create(user)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':'1'
    }

    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)
    print(response)
    assert response.status_code == 401
    assert response.json['success'] == -1

def test_cancel_driver_charge_booking_notfound(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':'1',
    }
    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == -3

def test_cancel_driver_charge_trip_started(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    trip_id=driver_trip(booking_id,2)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':booking_id,
    }
    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == -2

def test_cancel_driver_charge_success(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':booking_id,
        "reason":'1'
    }
    response = client.post('/api/decline/driver/charge', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == 1
    assert response.json['cancel_charge'] == [0,99]

# ---------------------------------

#  API - /api/decline/driver
def test_cancel_driver_already_cancel(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    try:
        booking=db.session.query(Bookings).filter(Bookings.id==booking_id).first()
        booking.valid=-1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':booking_id,
        "reason":'1'
    }
    response = client.post('/api/decline/driver', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == 0

def test_cancel_driver_success(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    driver_details_id=driver_details(driver_id)
    redis_config.redis_available=True
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':booking_id,
        "reason":'1'
    }
    response = client.post('/api/decline/driver', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == 1

def test_cancel_driver_integrity_error(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    booking_id=driver_bookings(user_id,driver_id)
    driver_details_id=driver_details(driver_id)
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'booking_id':booking_id,
        "reason":'1'
    }
    redis_config.redis_available=True
    with patch('models.db.session.commit') as mock_commit:
        mock_commit.side_effect = exc.IntegrityError(None, None, None)

        response = client.post('/api/decline/driver', headers=headers, data=form_data)
        assert response.status_code == 401
        assert response.json['success'] == -1

# ---------------------------------