tags:
  - Login_admin
summary: Admin Login
description: >
  This endpoint allows admins to log in by providing their mobile number and password. The response includes JWT tokens and user details on successful authentication.
parameters:
  - in: formData
    name: mobile
    type: string
    required: true
    description: <PERSON><PERSON>'s mobile number for login.
    example: "9876543210"
  - in: formData
    name: pwd
    type: string
    required: true
    description: <PERSON><PERSON>'s password for login.
    example: "adminpassword"
responses:
  200:
    description: Successfully logged in. Returns access and refresh tokens along with user details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Login successful
        data:
          type: object
          properties:
            user_fname:
              type: string
              example: "<PERSON>"
            user_mobile:
              type: string
              example: "9876543210"
            user_email:
              type: string
              example: "<EMAIL>"
            user_lname:
              type: string
              example: "Doe"
            user_region:
              type: string
              example: "Region 1"
            tabs:
              type: string
              example: "0,1,2,3,4"
            regions:
              type: string
              example: "0,1,2,3"
            notification:
              type: string
              example: "0,1,2"
            id:
              type: integer
              example: 1001
            code:
              type: string
              example: "eyJhbGciOiJIUzI1NiIsInR..."
            role:
              type: integer
              example: 1
  400:
    description: Bad request due to missing or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Invalid mobile or password
  401:
    description: Unauthorized access or invalid credentials or refresh token issue.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Invalid login credentials or session expired
  403:
    description: Forbidden access for users without admin rights.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        error:
          type: string
          example: Forbidden access for this role
  422:
    description: Validation error for input data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid input
        data:
          type: object
          properties:
            error:
              type: array
              items:
                type: string
              example: ["mobile: field required", "pwd: field required"]
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -99
        message:
          type: string
          example: An unexpected error occurred. Please try again later.
