from models.models import db, Users, DriverInfo, Drivers, DriverDetails, DriverBank, DriverGLIDDocDetails
from models.models import DriverGLBankDetails, DriverGLDrivLicDetails, Bookings, Trip, BookDest, DriverApprovalLog, \
    DriverTrans
from faker import Faker
import random
from datetime import date, time, datetime, timedelta
from sqlalchemy import exc
import unittest
from unittest import mock
from unittest.mock import patch
import requests
from flask import current_app as app

fake = Faker()

def create_user_and_driver_data(rating=4.5, region=1, approved=1, available=1, bank_verified=1):
    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    try:
        db.session.add(user)
        db.session.commit()

        driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
        db.session.add(driver)
        db.session.commit()
        driver_info = DriverInfo(driver.id, "WB-100000", datetime(2025, 12, 31), datetime(1990, 10, 20), 'Newtown',
                    'Main Street', '<PERSON>', '**********', 'Friend', 28.7041, 77.1025,
                    'path/to/driver_id_front.jpg', 'path/to/driver_id_back.jpg',
                    'path/to/license_front.jpg', 'path/to/license_back.jpg',
                    'path/to/driver_picture.jpg', 'ID987654',
                    'Experienced driver with excellent road skills',
                    id_verified= DriverInfo.DOC_VERIFIED,
                    license_verified= DriverInfo.DOC_VERIFIED)
        driver_details = DriverDetails(driver.id,
                                    ride=random.randint(1, 100),
                                    hour=random.randint(1, 50),
                                    rating=rating,withdrawable=1000)

        driver_bank = DriverBank(driver.id, 'ACCN0001234', 'AC000123', 'HHPP2819', 'acc_doc_path', bank_verified=bank_verified)

        db.session.add(driver_info)
        db.session.add(driver_details)
        db.session.add(driver_bank)
        db.session.commit()
        return user, driver
    except exc.IntegrityError:
        db.session.rollback()
        return None, None

def create_driver_grid_doc_details(driver_id):
    driver_grid_doc_details = DriverGLIDDocDetails(
        driver_id=driver_id,
        id_type=DriverGLIDDocDetails.TYPE_VOTERID, id_card_no="ABCD1234XYZ",
        name="John Doe", gender="Male", driver_pic="path/to/picture.jpg",
        dob=datetime(1990, 1, 1), house="1234", street="Main Street",
        district="Sample District", sub_district="Sample Sub-District",
        landmark="Near Landmark", state="Sample State", pincode="123456",
        country="Sample Country", vtc_name="Sample VTC", name_status=True,
        photo_status=True, photo_score=95.0, dob_status=True,
        json_dump={"additional_info": "Dummy data for testing"}
    )

    try:
        db.session.add(driver_grid_doc_details)
        db.session.commit()
    except Exception as e:
        db.session.rollback()

def create_driver_gl_bank_details(driver_id):
    driver_gl_bank_details = DriverGLBankDetails(
        driver_id=driver_id,
        name="John Doe",
        acc_no="**********",
        ifsc="ABCD0123456",
        bank_name="Test Bank",
        district="Test District",
        bank_branch="Test Branch",
        name_status=False,
        json_dump={
            "account_holder": "John Doe",
            "bank_name": "Test Bank",
            "branch_name": "Test Branch",
            "ifsc_code": "ABCD0123456",
            "account_number": "**********",
            "district": "Test District"
        }
    )

    db.session.add(driver_gl_bank_details)
    db.session.commit()

    return driver_gl_bank_details

def create_driver_gl_driver_lic_details(driver_id):

    driver_gl_driver_lic_details = DriverGLDrivLicDetails(
        driver_id=driver_id,
        dl_no="DL123456789",
        name="John Doe",
        driver_pic="path/to/driver_pic.jpg",
        dob=datetime(1990, 1, 1),
        addr="123 Main St, City, State",
        pincode="123456",
        license_exp=datetime(2025, 1, 1),
        state="California",
        license_issue=datetime(2020, 1, 1),
        name_status=True,
        photo_status=True,
        photo_score=95.0,
        dob_status=True,
        lic_exp_status=True,
        json_dump=None
    )
    db.session.add(driver_gl_driver_lic_details)
    db.session.commit()

    return driver_gl_driver_lic_details


""" Test cases for api: /api/admin/dash/search """
# Test case for successful search
def test_successful_search(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'starting_from': '0',
        'no_of_logs': '10',
        'region': '0'
    }

    create_user_and_driver_data()

    response = client.post('/api/admin/dash/search', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert 'data' in res_data

# Test case for missing required parameters
def test_missing_required_parameters(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0'
    }

    response = client.post('/api/admin/dash/search', data=form_data, headers=auth_headers)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert 'error' in res_data['status']

# # Test case for invalid starting_from and no_of_logs values
def test_invalid_starting_and_no_of_logs(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'starting_from': 'invalid',
        'no_of_logs': 'invalid',
        'region': '0'
    }

    response = client.post('/api/admin/dash/search', data=form_data, headers=auth_headers)
    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1

# Test case for filtering by region
def test_filter_by_region(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'starting_from': '0',
        'no_of_logs': '10',
        'region': '0'
    }

    create_user_and_driver_data()

    response = client.post('/api/admin/dash/search', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1


""" Test cases for api: /api/admin/driver_trips/logs """
# Test Case: Missing Driver ID
def test_driver_trips_missing_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': 0
    }

    response = client.post('/api/admin/driver_trips/logs', data=form_data, headers=auth_headers)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Invalid input'

# Test Case: Successful Retrieval of Driver Trips
def test_driver_trips_successful_data(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()


    current_date = date.today()

    # Add 2 days to current date
    startdate = current_date + timedelta(days=2)
    enddate = current_date + timedelta(days=2)

    starttime = time(10, 30)
    endtime = time(12, 30)

    # booking instance
    booking = Bookings(1, 'sk_1', driver.id, 22.1, 88.1, starttime, startdate, '01:30:00', endtime, enddate, 200.0, 0, 'Kolkata', 3)
    db.session.add(booking)
    db.session.commit()

    trip = Trip(booking.id)
    db.session.add(trip)
    trip.endtime = datetime.combine(enddate, endtime)

    db.session.commit()

    form_data = {
        'region': '0',
        'driver_id': driver.id
    }

    response = client.post('/api/admin/driver_trips/logs', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200

    assert res_data['success'] == 1


""" Test cases for api: /api/admin/driver_approval_log/all """
# Test Case: Missing Driver ID
def test_driver_approval_logs_missing_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': 0
    }

    response = client.post('/api/admin/driver_approval_log/all', data=form_data, headers=auth_headers)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Invalid input'


# Test Case: Successful Retrieval of Driver Approval Logs
def test_driver_approval_logs_successful_data(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    logs1 = DriverApprovalLog(driver.id, changes="Photo Change", approval=1, editedby='Admin', change_from='old_image_path',
                        change_to='new_immage_path', remark="Updated")

    db.session.add(logs1)

    db.session.commit()

    form_data = {
        'region': '0',
        'driver_id': driver.id,
        'fromdate': date.today().strftime('%Y-%m-%d'),
        'todate': date.today().strftime('%Y-%m-%d')
    }

    response = client.post('/api/admin/driver_approval_log/all', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200
    assert res_data['success'] == 1
    assert len(res_data['data']) > 0


""" Test cases for api: /api/admin/driver/details """
# Test Case: Missing Driver ID
def test_driver_details_missing_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': 0
    }

    response = client.get('/api/admin/driver/details', query_string=form_data, headers=auth_headers)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['message'] == 'Invalid input'


# Test Case: Successful Retrieval of Driver Approval Logs
def test_driver_details_successful_data(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_gl_bank_details(driver.id)
    create_driver_gl_driver_lic_details(driver.id)
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'region': '0',
        'driver_id': driver.id
    }

    response = client.get('/api/admin/driver/details', query_string=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200
    assert res_data['success'] == 1


""" Test cases for api: /api/admin/driver_due """
# Unauthorized Access (User Lacks Required Admin Privileges)
def test_add_driver_due_unauthorized_access(client, customer_login):
    auth_headers, non_admin = customer_login()


    form_data = {
        'driver_id': 1,
        'amount': 1000,
        'remark': 'Test remark',
        'due_type': 'Fine',
        'payment_mode': 'Cash',
        'region': '0'
    }

    response = client.post('/api/admin/driver_due', data=form_data, headers=auth_headers)
    res_data = response.get_json()

    assert response.status_code == 403

# Successful Addition of a Driver Due (Various Due Types)
def test_add_driver_due_success_fine(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    form_data = {
        'driver_id': driver.id,
        'amount': 1000,
        'remark': 'Test remark',
        'due_type': 'Fine',
        'payment_mode': 'Cash',
        'region': 0
    }

    response = client.post('/api/admin/driver_due', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200
    assert res_data['success'] == 1

# Due Type: Withdraw
def test_add_driver_due_success_withdraw(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    form_data = {
        'driver_id': driver.id,
        'amount': 500,
        'remark': 'Withdraw remark',
        'due_type': 'Withdraw',
        'payment_mode': 'Bank Transfer',
        'region': '0'
    }


    response = client.post('/api/admin/driver_due', data=form_data, headers=auth_headers)
    res_data = response.get_json()

    assert response.status_code == 200
    assert res_data['success'] == 1

# Missing Required Fields in the Request
def test_add_driver_due_missing_fields(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'driver_id': 1,
        # 'amount' is missing
        'remark': 'Test remark',
        'due_type': 'Fine',
        'payment_mode': 'Cash',
        'region': '0'
    }

    response = client.post('/api/admin/driver_due', data=form_data, headers=auth_headers)
    res_data = response.get_json()

    assert response.status_code == 422
    assert res_data['success'] == -1

""" Test cases for api: /api/admin/driver_dues_log """
# Missing Required Fields
def test_driver_due_log_missing_fields(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'region': '0'
    }
    response = client.post('/api/admin/driver_dues_log', data=form_data, headers=auth_headers)
    res_data = response.get_json()

    assert response.status_code == 422
    assert res_data['success'] == -1

# User Not Found
def test_driver_due_log_user_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'mobile': '**********',
        'region': '0'
    }

    response = client.post('/api/admin/driver_dues_log', data=form_data, headers=auth_headers)
    res_data = response.get_json()

    assert response.status_code == 404
    assert res_data['success'] == -2

# Driver Exists (Old Format)
def test_driver_due_log_driver_exists_old_format(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    # Create the first DriverTrans object
    driver_trans1 = DriverTrans(
        did=driver.id,  # Driver ID
        amt=1000.0,  # Amount in currency units
        wall_a=12000,  # Wallet balance after the transaction
        wall_b=11000,  # Wallet balance before the transaction
        with_a=5000,  # Withdrawable balance after the transaction
        with_b=4500,  # Withdrawable balance before the transaction
        method="Admin Panel",  # Method used for the transaction
        status=DriverTrans.COMPLETED,  # Transaction status
        cash=0,  # Cash component of the transaction
        trans_payment_id="TRANS12345",  # Payment ID
        stop=True,  # Set stop timestamp
        admin_name="AdminUser1",  # Admin who initiated the transaction
        remarks="Monthly bonus",  # Remarks for the transaction
        description="Bonus payout for September"  # Description
    )

    # Create the second DriverTrans object
    driver_trans2 = DriverTrans(
        did=driver.id,  # Driver ID
        amt=2000.0,  # Amount in currency units
        wall_a=15000,  # Wallet balance after the transaction
        wall_b=13000,  # Wallet balance before the transaction
        with_a=6000,  # Withdrawable balance after the transaction
        with_b=5500,  # Withdrawable balance before the transaction
        method="Bank Transfer",  # Method used for the transaction
        status=DriverTrans.INITIATED,  # Transaction status
        cash=500,  # Cash component of the transaction
        trans_payment_id="TRANS67890",  # Payment ID
        stop=False,  # No stop timestamp
        admin_name="AdminUser2",  # Admin who initiated the transaction
        remarks="Advance payment",  # Remarks for the transaction
        description="Advance for upcoming trip"  # Description
    )

    db.session.add(driver_trans1)
    db.session.add(driver_trans2)
    db.session.commit()

    form_data = {
        'mobile': user.mobile,
        'region': '0'
    }

    response = client.post('/api/admin/driver_dues_log', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200


""" Test cases for api: /api/admin/getlocality """
# Test Case: Missing Latitude and Longitude
def test_get_locality_missing_lat_long(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0'
    }

    response = client.post('/api/admin/getlocality', headers=auth_headers, data=form_data)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1

# Test Case: Successful Retrieval of Locality
def test_get_locality_success(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'lat': '28.7041',
        'long': '77.1025',
        'region': '0'
    }

    app.config["MAP_MY_INDIA"] = '0f3006866555ca5960585b3e6c5363b6'

    # Mocking the MapMyIndia API response
    mock_response = {
        'results': [
            {'locality': 'Connaught Place', 'formatted_address': 'Connaught Place, New Delhi'}
        ]
    }

    # Manually patching requests.get for mocking
    with unittest.mock.patch('requests.get') as mock_get:
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = mock_response

        # Make the GET request to the endpoint
        response = client.post('/api/admin/getlocality', data=form_data, headers=auth_headers)

    res_data = response.get_json()
    print(res_data)

    # Assertions to verify the expected behavior
    assert response.status_code == 200
    assert res_data['success'] == 1
    assert res_data['data'] == 'Connaught Place'

# Test Case: No Results from MapMyIndia API
def test_get_locality_no_results(client, admin_login):
    # Setting up the configuration for MAP_MY_INDIA
    app.config["MAP_MY_INDIA"] = '0f3006866555ca5960585b3e6c5363b6'

    auth_headers, admin = admin_login
    query_params = {
        'lat': '28.7041',
        'long': '77.1025',
        'region': '0'
    }

    # Mocking the MapMyIndia API response to simulate no results
    mock_response = {'results': []}

    with unittest.mock.patch('requests.get') as mock_get:
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = mock_response

        response = client.post('/api/admin/getlocality', data=query_params, headers=auth_headers)
        res_data = response.get_json()

    # Assertions to verify the expected behavior
    print(res_data)
    assert response.status_code == 200
    assert res_data['success'] == 0
    assert res_data['message'] == 'No locality found'


""" Test cases for api: /api/admin/register_driver_list """
# Test case: Invalid timestamp
def test_search_drivers_invalid_timestamp(client, admin_login):

    auth_headers, admin = admin_login
    form_data = {
        'timestamp_gt': 'invalid_date_format',
        'timestamp_lt': '2024-01-01 00:00:00',
        'regions': '0'
    }

    response = client.post('/api/admin/register_driver_list', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 422
    assert res_data['success'] == -1



""" Test cases for api: /api/admin/driver/dl_verify """
# Test case for missing required parameters
def test_driver_dl_verify_missing_parameters(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'region': '0'
        # driver_id is missing
    }

    response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 422
    res_data = response.get_json()
    assert res_data['success'] == -1

# Test case for driver not found
def test_driver_dl_verify_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': '999',  # Non-existent driver ID
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['message'] == 'Driver not found'


# Test case for driver info not found
def test_driver_dl_verify_driver_info_not_found(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Remove driver info to simulate not found
    db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver.id).delete()
    db.session.commit()

    response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 404
    res_data = response.get_json()
    print(res_data)
    assert res_data['success'] == -9
    assert res_data['message'] == "Driver Previous Details Not Found"

# Test case for driving license already fetched
def test_driver_dl_verify_license_already_fetched(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)
    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == -7
    assert res_data['message'] == "Driving Licence Already Fetched"

# Test case for successful verification of driving license
def test_driver_dl_verify_success(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post and upload_pic_base64 functions
    with unittest.mock.patch('requests.post') as mocked_post, \
        unittest.mock.patch('utils.s3_utils.upload_pic_base64') as mocked_upload_pic:

        # Mock the response from requests.post
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'driving_license_data': {
                    'document_id': 'DL123456',
                    'name': 'John Doe',
                    'date_of_birth': '1990-01-01',
                    'validity': {
                        'non_transport': {
                            'expiry_date': '2030-01-01',
                            'issue_date': '2020-01-01'
                        }
                    },
                    'photo_base64': 'base64image==',  # Ensure proper padding
                    'address': '123 Main St',
                    'pincode': '123456',
                    'rto_details': {
                        'state': 'Some State'
                    }
                }
            }
        }
        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(1, {"name_match": True, "lic_exp_match": True, 'photo_match': True, 'dob_match': True, 'face_match_score': 0.8})):

            mocked_upload_pic.return_value = 'fake_image_path/upload.png'

            response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    res_data = response.get_json()

    assert response.status_code == 200
    assert res_data['success'] == 1
    assert res_data['message'] == "Driving Licence Verified"

# Test case for dispute driving license
def test_driver_dl_dispute(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post and upload_pic_base64 functions
    with unittest.mock.patch('requests.post') as mocked_post, \
        unittest.mock.patch('utils.s3_utils.upload_pic_base64') as mocked_upload_pic:

        # Mock the response from requests.post
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'driving_license_data': {
                    'document_id': 'DL123456',
                    'name': 'John Doe',
                    'date_of_birth': '1990-01-01',
                    'validity': {
                        'non_transport': {
                            'expiry_date': '2030-01-01',
                            'issue_date': '2020-01-01'
                        }
                    },
                    'photo_base64': 'base64image==',  # Ensure proper padding
                    'address': '123 Main St',
                    'pincode': '123456',
                    'rto_details': {
                        'state': 'Some State'
                    }
                }
            }
        }

        # Mock the match_details function to simulate a dispute scenario
        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(0, {"name_match": False, "lic_exp_match": False, 'photo_match': False, 'dob_match': False, 'face_match_score': 0.7})):
            # Mock the upload_pic_base64 to return a fake filename
            mocked_upload_pic.return_value = 'fake_image_path/upload.png'

            response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    res_data = response.get_json()

    assert response.status_code == 200
    assert res_data['success'] == 0
    assert res_data['message'] == "Driving Licence Verified but Disputed"

# Test case for Gridlines API returning code "1001" indicating that the driving license does not exist
def test_driver_dl_verify_license_not_exists(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post and upload_pic_base64 functions
    with unittest.mock.patch('requests.post') as mocked_post:
        # Mock the response from requests.post to simulate the "1001" code
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1001'
            }
        }

        # Send the POST request to the API endpoint
        response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    res_data = response.get_json()

    # Assert that the API returns the expected status and message
    assert response.status_code == 200
    assert res_data['success'] == -3
    assert res_data['message'] == "Driving Licence Not Exists, Please Check DOB"

# Test case for Gridlines API returning a 400 error
def test_driver_dl_verify_gridlines_api_error(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post to simulate a 400 error from the API
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 400
        mocked_post.return_value.json.return_value = {
            'error': {'code': 'INVALID_DRIVING_LICENSE'}
        }

        # Perform the API request
        response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    # Get the response data
    res_data = response.get_json()

    # Assert the response status code and data
    assert response.status_code == 400
    assert res_data['success'] == -1
    assert res_data['message'] == "Invalid Driving Licence Number"

# Test case for Gridlines API returning an unexpected error
def test_driver_dl_verify_gridlines_api_unexpected_error(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post to simulate a 500 error from the API
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 500
        mocked_post.return_value.text = 'Internal Server Error'

        # Perform the API request
        response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    res_data = response.get_json()

    # Assert the response status code and check if an error is present
    assert response.status_code == 500
    assert res_data['success'] == -5

# Test case for driving license fetched but failed to verify
def test_driver_dl_verify_failed_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'dl_no': 'DL123456',
        'dob': '1990-01-01',
        'driver_id': driver.id,
        'region': '0'
    }

    # Mock the requests.post and upload_pic_base64 functions
    with unittest.mock.patch('requests.post') as mocked_post, \
        unittest.mock.patch('utils.s3_utils.upload_pic_base64') as mocked_upload_pic:

        # Mock the response from requests.post to simulate code "1000" but verification failure
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'driving_license_data': {
                    'document_id': 'DL123456',
                    'name': 'John Doe',
                    'date_of_birth': '1990-01-01',
                    'validity': {
                        'non_transport': {
                            'expiry_date': '2030-01-01',
                            'issue_date': '2020-01-01'
                        }
                    },
                    'photo_base64': 'base64image==',  # Ensure proper padding
                    'address': '123 Main St',
                    'pincode': '123456',
                    'rto_details': {
                        'state': 'Some State'
                    }
                }
            }
        }

        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(-1, {})):

            mocked_upload_pic.return_value = 'fake_image_path/upload.png'

            response = client.post('/api/admin/driver/dl_verify', data=form_data, headers=auth_headers)

    res_data = response.get_json()


    assert response.status_code == 200
    assert res_data['success'] == 10
    assert res_data['message'] == "Driving Licence Fetched but failed to Verify"


""" Test cases for api: /api/admin/driver/dl_reverify """
# Test Case: Missing 'driver_id' Parameter
def test_driver_dl_reverify_missing_driver_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'force_verify': '1',
        'remarks': 'Force verification test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 422

# Test Case: Driver Not Found
def test_driver_dl_reverify_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '99999',  # Non-existent driver ID
        'force_verify': '1',
        'remarks': 'Driver not found test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 404

# Test Case: Driver Info Not Found
def test_driver_dl_reverify_driver_info_not_found(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    db.session.add(user)
    db.session.commit()

    driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
    db.session.add(driver)
    db.session.commit()

    form_data = {
        'driver_id': driver.id,
        'force_verify': driver.id,
        'remarks': 'Driver info not found test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 404

# Test Case: Force Verify Driving License
def test_driver_dl_reverify_force_verify(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)  # Function to create driver license details

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Force verification of driving license',
        'region': '0'
    }

    response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 3
    assert res_data['message'] == 'Driving Licence Forced Verified'

def test_driver_dl_reverify_successful_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverify driving license',
        'region': '0'
    }

    with mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (1, {"name_match": True, "lic_exp_match": True, 'photo_match': True, 'dob_match': True, 'face_match_score': 0.8})

        response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)

    res_data = response.get_json()

    assert response.status_code == 200
    assert res_data['success'] == 1
    assert res_data['message'] == 'Driving Licence Verified'
    assert res_data['data']['details'] == {
        'dob_match': True,
        'face_match_score': 0.8,
        'lic_exp_match': True,
        'name_match': True,
        'photo_match': True
    }

# Test Case: Driving License Still Disputed
def test_driver_dl_reverify_details_disputed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverify disputed driving license',
        'region': '0'
    }

    with mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (0, {
            "name_match": False,
            "lic_exp_match": False,
            'photo_match': True,
            'dob_match': True,
            'face_match_score': 0.8
        })

        response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    assert res_data['success'] == 0
    assert res_data['message'] == 'Driving Licence Still Disputed'
    assert res_data['data']['details'] == {
        'dob_match': True,
        'face_match_score': 0.8,
        'lic_exp_match': False,
        'name_match': False,
        'photo_match': True
    }

# Test Case: Driving License Verification Failed
def test_driver_dl_reverify_verification_failed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverification failure',
        'region': '0'
    }

    with mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (-1, {})

        response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Driving Licence Failed to Reverify'

# Test Case: Database Commit Failure
def test_driver_dl_reverify_db_commit_failure(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_driver_lic_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Database commit failure',
        'region': '0'
    }

    with mock.patch('api.admin_dashboard.admin_driver_routes.db.session.commit', side_effect=Exception('Commit failed')):
        response = client.post('/api/admin/driver/dl_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['success'] == -99



""" Test cases for api: /api/admin/driver/bankdoc_verify """
# Test Case: Missing 'acc_no', 'ifsc', or 'driver_id' Parameter
def test_bankdoc_verify_missing_parameters(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'acc_no': '**********',
        # Missing 'ifsc' and 'driver_id',
        'region': '0'
    }

    response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)
    assert response.status_code == 422

# Test Case: Driver Not Found
def test_bankdoc_verify_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': '99999',  # Non-existent driver ID
        'region': '0'
    }

    response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    assert response.status_code == 404
    assert res_data['message'] == 'Driver not found'

# Test Case: Bank Information Not Found
def test_bankdoc_verify_bank_info_not_found(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    db.session.add(user)
    db.session.commit()

    driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
    db.session.add(driver)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,
        'region': '0'
    }

    response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    assert response.status_code == 404
    assert res_data["message"] == "Driver PrevDetails Not Found"

# Test Case: Driver Already Fetched Bank Details
def test_bankdoc_verify_driver_already_fetched(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_bank_details(driver.id)

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,  # Assuming a valid driver ID
        'region': '0'
    }

    response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    assert response.status_code == 200
    assert res_data["message"] == "Bank details already fetched"

# Test Case: Successful Bank Verification
def test_bankdoc_verify_successful_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,
        'region': '0'
    }

    mock_response = {
        'data': {
            'code': '1000',
            'bank_account_data': {
                'name': 'John Doe',
                'bank_name': 'Bank Name',
                'city': 'City Name',
                'branch': 'Branch Name'
            }
        }
    }

    # Mocking the match_details function to return a successful match
    with patch('services.admin_dashboard.driver_verify_service.match_details') as mock_match:
        mock_match.return_value = (1, {"name_match": True})  # Mocking a successful name match

        with patch('requests.post') as mocked_post:
            mocked_post.return_value.status_code = 200
            mocked_post.return_value.json.return_value = mock_response

            # Sending the POST request to the '/api/admin/driver/bankdoc_verify' endpoint
            response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)

        assert response.status_code == 200
        res_data = response.get_json()

        # Validating the response content
        assert res_data['success'] == 1  # Status 1 indicates successful verification
        assert res_data['message'] == "Bank Details Verified"

# Test Case: Invalid Account Number
def test_bankdoc_verify_invalid_account_number(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,
        'region': '0'
    }

    mock_response = {
        'data': {
            'code': '1001'
        }
    }

    with patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = mock_response

        # Sending the POST request to the '/api/admin/driver/bankdoc_verify' endpoint
        response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    # Validating the response content
    assert res_data['success'] == 2
    assert res_data['message'] == "Provided invalid Account Number."

# Test Case: Invalid IFSC
def test_bankdoc_verify_invalid_ifsc(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'acc_no': '**********',
        'ifsc': 'INVALIDIFSC',
        'driver_id': driver.id,
        'region': '0'
    }

    mock_response = {
        'data': {
            'code': '1002'
        }
    }

    with patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = mock_response

        # Sending the POST request to the '/api/admin/driver/bankdoc_verify' endpoint
        response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    # Validating the response content
    assert res_data['success'] == 2
    assert res_data['message'] == "Provided invalid IFSC."

# Test Case: Account is Blocked
def test_bankdoc_verify_account_blocked(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,
        'region': '0'
    }

    mock_response = {
        'data': {
            'code': '1003'
        }
    }

    with patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = mock_response

        # Sending the POST request to the '/api/admin/driver/bankdoc_verify' endpoint
        response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    # Validating the response content
    assert res_data['success'] == 2
    assert res_data['message'] == "Account is blocked."

# Test Case: Account is Closed
def test_bankdoc_verify_account_closed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'acc_no': '**********',
        'ifsc': 'IFSC0001',
        'driver_id': driver.id,
        'region': '0'
    }

    mock_response = {
        'data': {
            'code': '1004'
        }
    }

    with patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = mock_response

        # Sending the POST request to the '/api/admin/driver/bankdoc_verify' endpoint
        response = client.post('/api/admin/driver/bankdoc_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    # Validating the response content
    assert res_data['success'] == 2
    assert res_data['message'] == "Account is closed."



""" Test cases for api: /api/admin/driver/bank_reverify """
# Test Case: Missing 'driver_id' Parameter
def test_bank_reverify_missing_driver_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'force_verify': '1',
        'remarks': 'Force verification test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'driver_id parameter is required'}

# Test Case: Driver Not Found
def test_bank_reverify_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '99999',  # Non-existent driver ID
        'force_verify': '1',
        'remarks': 'Driver not found test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.json == {'error': 'Driver not found'}

# Test Case: Bank Information Not Found
def test_bank_reverify_bank_info_not_found(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    db.session.add(user)
    db.session.commit()

    driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
    db.session.add(driver)
    try:
        db.session.commit()
    except Exception as e:
        print(e)

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Bank info not found test',
        'region': '0'
    }

    response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.json == {'error': 'Driver bank not found'}

# Test Case: Force Verify Bank Details
def test_bank_reverify_force_verify(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_bank_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Force verification of bank details',
        'region': '0'
    }

    response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 3
    assert res_data['message'] == 'Bank Details Forced Verified'

# Test Case: Bank Details Verified Successfully
def test_bank_reverify_successful_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_gl_bank_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverify bank details',
        'region': '0'
    }

    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (1, {"name_match": True})

        response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 1
    assert res_data['message'] == 'Bank Details Verified'
    assert res_data['details'] == {"name_match": True}

# Test Case: Bank Details Still Disputed
def test_bank_reverify_details_disputed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_bank_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverify disputed bank details',
        'region': '0'
    }

    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (0, {"name_match": False})

        response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 0
    assert res_data['message'] == 'Bank Details Still Disputed'
    assert res_data['details'] == {"name_match": False}

# Test Case: Bank Details Verification Failed
def test_bank_reverify_verification_failed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_bank_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Reverification failure',
        'region': '0'
    }

    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details') as mocked_match_details:
        mocked_match_details.return_value = (-1, {})

        response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -1
    assert res_data['message'] == 'Bank Details Failed to Reverify'

# Test Case: Database Commit Failure
def test_bank_reverify_db_commit_failure(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_gl_bank_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Database commit failure',
        'region': '0'
    }

    with unittest.mock.patch('api.admin_dashboard.admin_driver_routes.db.session.commit', side_effect=Exception('Commit failed')):
        response = client.post('/api/admin/driver/bank_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'Database commit failed.'
    assert 'error' in res_data


""" Test cases for api: /api/admin/driver/generate_iddoc_otp """
# Test case for missing 'id_no' parameter
def test_generate_iddoc_otp_missing_id_no(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '1',
        'region': '0'
    }

    response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'id parameter is required'}

# Test case for missing 'driver_id' parameter
def test_generate_iddoc_otp_missing_driver_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'id_no': '**********',
        'region': '0'
    }

    response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'driver_id parameter is required'}

# Test case for driver not found
def test_generate_iddoc_otp_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'id_no': '**********',
        'driver_id': '999', # Assuming this driver ID does not exist
        'region': '0'
    }

    response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.json == {'error': 'Driver not found'}

# Test case for driver info not found
def test_generate_iddoc_otp_driver_info_not_found(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    db.session.add(user)
    db.session.commit()

    driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
    db.session.add(driver)
    db.session.commit()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }

    response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.json == {'error': 'Driver Info not found'}

# Test case for Aadhaar number does not have a mobile number registered (code 1008)
def test_generate_iddoc_otp_aadhaar_no_mobile(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {'code': '1008'}
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 2
    assert res_data['message'] == 'Aadhaar number does not have a mobile number registered with it.'

# Test case for exceeding maximum OTP generation limit (code 1011)
def test_generate_iddoc_otp_exceed_limit(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {'code': '1011'}
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 3
    assert res_data['message'] == 'Exceeded Maximum OTP generation Limit. Please try again in some time.'

# Test case for Aadhaar number does not exist (code 1012)
def test_generate_iddoc_otp_aadhaar_does_not_exist(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {'code': '1012'}
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 4
    assert res_data['message'] == 'Aadhaar number does not exist.'

# Test case for invalid Aadhaar number (error code INVALID_AADHAAR)
def test_generate_iddoc_otp_invalid_aadhaar(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': 'invalid_aadhaar',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 400
        mocked_post.return_value.json.return_value = {
            'error': {'code': 'INVALID_AADHAAR'}
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -1
    assert res_data['message'] == 'Invalid Aadhaar Number.'

# Test case for OTP already sent (error code OTP_ALREADY_SENT)
def test_generate_iddoc_otp_already_sent(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 400
        mocked_post.return_value.json.return_value = {
            'error': {'code': 'OTP_ALREADY_SENT'}
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'OTP already sent. Please try after 60 seconds.'

# Test case for successful OTP generation
def test_generate_iddoc_otp_success(client, admin_login):
    auth_headers, admin = admin_login
    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1001',
                'transaction_id': 'trans123'
            }
        }

        response = client.post('/api/admin/driver/generate_iddoc_otp', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 1
    assert res_data['transaction_id'] == 'trans123'
    assert res_data['message'] == 'OTP sent to your Registered Mobile number. Check your mobile.'


""" Test cases for api: /api/admin/driver/iddoc_a_verify """
# Test case for missing 'id_no' parameter
def test_missing_iddoc_a_verify_id_no(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'driver_id': '1',
        'otp': '123456',
        'trans_id': 'trans123'
    }

    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'id parameter is required'}

# Test case for missing 'driver_id' parameter
def test_missing_iddoc_a_verify_driver_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'id_no': '1234',
        'otp': '123456',
        'trans_id': 'trans123'
    }

    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'driver_id parameter is required'}

# Test case for missing 'trans_id' parameter
def test_missing_iddoc_a_verify_trans_id(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'id_no': '1234',
        'driver_id': '1',
        'otp': '123456'
    }

    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json == {'error': 'trans_id parameter is required'}

# Test case for driver not found
def test_iddoc_a_verify_driver_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0',
        'id_no': '1234',
        'driver_id': '0',
        'otp': '123456',
        'trans_id': 'trans123'
    }

    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    assert response.json == {'error': 'Driver not found'}

# Test Case: Driver Details Not Found
def test_iddoc_a_verify_driver_details_not_found(client, admin_login):
    auth_headers, admin = admin_login
    mobile = f"{random.randint(**********, **********)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_DRIVER)
    db.session.add(user)
    db.session.commit()

    driver = Drivers(user, 'WB-100000',' doc_path', 'pic_path', perma=True, approved=1)
    db.session.add(driver)
    db.session.commit()

    form_data = {
        'id_no': '1234',
        'driver_id': driver.id,
        'otp': '123456',
        'trans_id': 'trans123',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['status'] == -9
    assert res_data['message'] == 'Driver PrevDetails Not Found'

# Test Case: Driving ID Already Fetched
def test_iddoc_a_verify_driving_id_already_fetched(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)  # Simulate existing record
    form_data = {
        'id_no': '1234',
        'driver_id': driver.id,
        'otp': '123456',
        'trans_id': 'trans123',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == -7
    assert res_data['message'] == 'Driving ID Already Fetched'

# Test Case: Session Expired (Code 1003)
def test_aadhaar_session_expired(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'otp': '123456',
        'trans_id': 'trans123',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1003'
            }
        }

        response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'Session Expired. Please start the process again.'

# Test Case: OTP Attempts Exceeded (Code 1005)
def test_aadhaar_otp_attempts_exceeded(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'otp': '123456',
        'trans_id': 'trans123',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1005'
            }
        }

        response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'OTP attempts exceeded. Please start the process again.'

# Test Case: Invalid OTP or Aadhaar Number
def test_invalid_otp_or_aadhaar(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'id_no': '**********',
        'driver_id': driver.id,
        'otp': 'invalid_otp',
        'trans_id': 'trans123',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 400
        mocked_post.return_value.json.return_value = {
            'error': {'code': 'INVALID_OTP'}
        }

        response = client.post('/api/admin/driver/iddoc_a_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -1
    assert res_data['message'] == 'Invalid Aadhaar Number or OTP.'


""" Test cases for api: /api/admin/driver/id_a_reverify """
# Test Case: Missing driver_id Parameter
def test_missing_driver_id_a_verify(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'remarks': 'Reverification request',
        'region': '0'
    }
    response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['error'] == 'driver_id parameter is required'

# Test Case: Force Verify Aadhar Details
def test_force_verify_aadhar_details(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'force_verify': '1',
        'remarks': 'Force verification test',
        'region': '0'
    }
    response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 3
    assert res_data['message'] == 'Aaadhar Details Forced Verified'

# Test Case: Aadhar Details Successfully Verified
def test_successful_aadhar_details_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Aadhar verification',
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(1, {
        'name_match': True, 'photo_match': True, 'face_match_score': 0.85, 'dob_match': True
    })):
        response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 1
    assert res_data['message'] == 'Aaadhar Details Verified'

# Test Case: Aadhar Details Disputed
def test_aadhar_details_disputed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Dispute check',
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(0, {
        'name_match': False, 'photo_match': False, 'face_match_score': 0.60, 'dob_match': False
    })):
        response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 0
    assert res_data['message'] == 'Aaadhar Details Still Disputed'

# Test Case: Aadhar Details Verification Failure
def test_aadhar_details_verification_failure(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Verification failure test',
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(-1, {})):
        response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -1
    assert res_data['message'] == 'Aaadhar Details Failed to Reverify'

# Test Case: Database Commit Failure
def test_database_commit_failure(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Commit failure test',
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(1, {
        'name_match': True, 'photo_match': True, 'face_match_score': 0.85, 'dob_match': True
    })):
        with unittest.mock.patch('api.admin_dashboard.admin_driver_routes.db.session.commit', side_effect=Exception("DB Error")):
            response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'Database commit failed.'
    assert 'DB Error' in res_data['error']

# Test Case: Unexpected Internal Server Error
def test_unexpected_internal_server_error(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'remarks': 'Unexpected error test',
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', side_effect=Exception("Unexpected Error")):
        response = client.post('/api/admin/driver/id_a_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['success'] == -3


""" Test cases for api: /api/admin/driver/iddoc_v_verify """
# Test Case: Missing driver_id Parameter
def test_missing_driver_id_parameter(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'id_no': '**********',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['error'] == 'driver_id parameter is required'

# Test Case: Missing id_no Parameter
def test_missing_id_no_parameter(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '1',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['error'] == 'id parameter is required'

# Test Case: Driver Details Not Found
def test_driver_details_not_found(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '9999',  # Non-existing driver ID
        'id_no': '**********',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['status'] == -9
    assert res_data['message'] == 'Driver Not Found'

# Test Case: Driving ID Already Fetched
def test_driving_id_already_fetched(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()
    create_driver_grid_doc_details(driver.id)  # Simulate existing record

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == -7
    assert res_data['message'] == 'Driving ID Already Fetched'

# Test Case: Voter ID Does Not Exist
def test_voter_id_does_not_exist(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('api.admin_dashboard.admin_driver_routes.requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1007'
            }
        }

        response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == -2
    assert res_data['message'] == 'Voter Id does not exist.'

# Test Case: Voter Details Fetched but Failed to Verify
def test_voter_details_fetched_but_failed_to_verify(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'voter_data': {'name': 'John Doe', 'gender': 'Male', 'state': 'State1', 'district': 'District1'}
            }
        }
        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(-1, {'name_match': False})):
            response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 10
    assert res_data['message'] == 'Voter Details Fetched but failed to Verify'

# Test Case: Successful Voter Details Verification
def test_successful_voter_details_verification(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'voter_data': {'name': 'John Doe', 'gender': 'Male', 'state': 'State1', 'district': 'District1'}
            }
        }
        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(1, {'name_match': True})):
            response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 1
    assert res_data['message'] == 'Voter Details Verified'

# Test Case: Invalid Voter ID
def test_invalid_voter_id(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': 'invalid_id',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 400
        mocked_post.return_value.json.return_value = {
            'error': {'code': 'INVALID_VOTER_ID'}
        }

        response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['status'] == -1
    assert res_data['message'] == 'Invalid voter id.'

# Test Case: Voter Details Verified but Disputed
def test_voter_details_verified_but_disputed(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('requests.post') as mocked_post:
        mocked_post.return_value.status_code = 200
        mocked_post.return_value.json.return_value = {
            'data': {
                'code': '1000',
                'voter_data': {'name': 'Jane Doe', 'gender': 'Female', 'state': 'State2', 'district': 'District2'}
            }
        }
        with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(0, {'name_match': False})):
            response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['status'] == 0
    assert res_data['message'] == 'Voter Details Verified but Disputed'

# Test Case: Unexpected API Response Code
def test_unexpected_api_response_code(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('api.admin_dashboard.admin_driver_routes.requests.post') as mocked_post:
        mocked_post.return_value.status_code = 500
        mocked_post.return_value.text = "Internal Server Error"

        response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['status'] == -5
    assert res_data['message'] == 'Unexpected error'
    assert 'Internal Server Error' in res_data['response']

# Test Case: External API Request Exception
def test_external_api_request_exception(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': driver.id,
        'id_no': '**********',
        'region': '0'
    }
    with unittest.mock.patch('api.admin_dashboard.admin_driver_routes.requests.post', side_effect=requests.RequestException("Connection Error")):
        response = client.post('/api/admin/driver/iddoc_v_verify', data=form_data, headers=auth_headers)

    assert response.status_code == 500
    res_data = response.get_json()
    assert res_data['error'] == 'Connection Error'


""" Test cases for api: /api/admin/driver/id_v_reverify """

# Test Case: Missing driver_id Parameter
def test_missing_v_reverify(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0'
    }
    response = client.post('/api/admin/driver/id_v_reverify', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['error'] == 'driver_id parameter is required'

# Test Case: Valid Force Verify
def test_force_verify_success(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'force_verify': 1,
        'remarks': 'Force verification for testing',
        'region': '0'
    }
    response = client.post('/api/admin/driver/id_v_reverify', headers=auth_headers, data=form_data)

    assert response.status_code == 200
    assert response.json['status'] == 3
    assert response.json['message'] == 'Voter Details Forced Verified'

# Test Case: Matching Voter Details
def test_match_voter_details_success(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(1, {'name_match': True})):
        response = client.post('/api/admin/driver/id_v_reverify', headers=auth_headers, data=form_data)
    assert response.status_code == 200
    assert response.json['status'] == 1
    assert response.json['message'] == 'Voter Details Verified'

# Test Case: Disputed Voter Details
def test_disputed_voter_details(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_grid_doc_details(driver.id)

    form_data = {
        'region': '0',
        'driver_id': driver.id
    }

    response = client.post('/api/admin/driver/id_v_reverify', headers=auth_headers, data=form_data)
    assert response.status_code == 200
    assert response.json['status'] == 0
    assert response.json['message'] == 'Voter Details Still Disputed'

# Test Case: Verification Failure
def test_verification_failure(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    create_driver_grid_doc_details(driver.id)

    form_data = {
        'driver_id': driver.id,
        'region': '0'
    }
    with unittest.mock.patch('services.admin_dashboard.driver_verify_service.match_details', return_value=(-1, {'name_match': False})):
        response = client.post('/api/admin/driver/id_v_reverify', headers=auth_headers, data=form_data)
    assert response.status_code == 400
    assert response.json['status'] == -1
    assert response.json['message'] == 'Voter Details Failed to Reverify'


""" Test cases for api: /api/admin/driver/updateandlog """

# Test case for missing driver_id parameter
def test_update_and_log_missing_driver_id(client, admin_login):

    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    response = client.post('/api/admin/driver/updateandlog', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['error'] == 'driver_id parameter is required'

# Test case for non-existing driver
def test_update_and_log_non_existing_driver(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'driver_id': '99999',
        'region': '0'
    }

    response = client.post('/api/admin/driver/updateandlog', data=form_data, headers=auth_headers)
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['error'] == 'Driver not found'

# Test case for updating driver data successfully
def test_update_and_log_success(client, admin_login):
    auth_headers, admin = admin_login

    user, driver = create_user_and_driver_data()

    form_data = {
        'driver_id': str(driver.id),
        'fname': 'UpdatedFirstName',
        'lname': 'UpdatedLastName',
        'license': 'NEW-WB-200000',
        'dob': '1991-01-01',
        'approval': '1',
        'editedby': 'Admin',
        'remark': 'Test update',
        'region': '0'
    }
    response = client.post('/api/admin/driver/updateandlog', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['message'] == 'Driver updated successfully'


