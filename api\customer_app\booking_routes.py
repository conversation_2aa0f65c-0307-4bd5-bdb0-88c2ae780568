#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  booking_routes.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Aniket <PERSON>

# === Standard Library ===
from typing import Optional

# === Third-Party Libraries ===
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity
from pydantic import BaseModel, ValidationError

# === Application (Internal) Imports ===
from utils.validation_utils import account_enabled
from utils.time_utils import convert_datetime_to_utc
from utils.response_utils import standard_response
from utils.bookings.booking_params import BookingParams
from models.models import Users
from services.customer_app.booking_service import search_booking, confirm_booking_service
from flasgger import swag_from

customer_booking = Blueprint('customer_app_booking', __name__)


class SearchPayload(BaseModel): 
    pickup_lat: float
    pickup_long: float
    pickup_loc: str
    car_type: int
    gear_type: int
    booking_type: int
    duration: str
    pickup_time: str
    is_immediate: bool
    region: int = 0
    coupon_code: str | None = None
    drop_lat: float | None = None
    drop_long: float | None = None
    drop_loc: str | None = None
    source: str | None = None
    ninsurance: int | None = 0
    
class ConfirmBookingPayload(BaseModel):
    search_id: str
    payment_type: Optional[int] = 1
    is_immediate: bool = False
    
@customer_booking.route('/api/booking/search', methods=['POST'])
@swag_from('/app/swagger_docs/customer_app/booking/register_search.yml')
@jwt_required()
def register_search():
    try:
        user_id = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify(standard_response(success=-1, status=401, message='User restricted or token invalid')), 401
    print(user_id)
    if not account_enabled(user_id):
        return jsonify(standard_response(success=-2, status=403, message='Account access denied or user flagged')), 403
    try:
        payload = SearchPayload(**request.form.to_dict())
    except ValidationError as ve:
        print(ve.errors(),flush=True)
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(success=-3, status=422, message=error_details)), 422

    # Convert pickup_time to UTC datetime
    try:
        pickup_dt = convert_datetime_to_utc(
            payload.pickup_time,
            tz=request.headers.get('X-Timezone', 'Asia/Kolkata')
        )
    except ValueError as e:
        return jsonify(standard_response(success=-4, status=400, message='Invalid datetime format. Expected: YYYY-MM-DD HH:MM:SS')), 400

    # Delegate all business logic to service
    data,success,message = search_booking(
        user_id=user_id,
        search_id=None,       # service can generate ID
        book_type=payload.booking_type,  # adjust mapping
        car_type=payload.car_type,
        gear_type=payload.gear_type,
        pickup_time=pickup_dt,
        duration=payload.duration,
        pickup_lat=payload.pickup_lat,
        pickup_long=payload.pickup_long,
        pickup_loc=payload.pickup_loc,
        drop_lat=payload.drop_lat,
        drop_long=payload.drop_long,
        drop_loc=payload.drop_loc,
        is_immediate=payload.is_immediate,
        region=payload.region,
        source=payload.source,
        ninsurance=payload.ninsurance,
        coupon_code=payload.coupon_code,
    )
    if success < 0 and success != -7:
        return jsonify(standard_response(success=success, status=400, message=message)), 400
    if success == -7:
        return jsonify(standard_response(success=success, status=500, message=message)),500

    return jsonify(standard_response(success=success, status=200, message=message, data=data)), 200


@customer_booking.route('/api/booking/confirm', methods=['POST'])
@jwt_required()
@swag_from('/app/swagger_docs/customer_app/booking/confirm_booking.yml')
def confirm_booking():
    try:
        user = get_jwt_identity()
    except Exception:
        return jsonify(standard_response(success=-1, status=401, message='Failed to get identity')), 401
    claims = get_jwt()
    if claims['roles'] == Users.ROLE_DRIVER:
        return jsonify(standard_response(success=-2, status=401, message='Invalid role - Driver')), 401
    if not account_enabled(user):
        return jsonify(standard_response(success=-3, status=401, message='User restricted')), 401
    if user in BookingParams.USER_BANNED_LIST:
        return jsonify(standard_response(success=-4, status=403, message='User banned to create booking')), 403
    try:
        payload = ConfirmBookingPayload(**request.form.to_dict())
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(success=-5, message=error_details), 422
    data,success,message = confirm_booking_service(user, claims, payload)
    if success < 0 and success != -12:
        return jsonify(standard_response(success=success, status=400, message=message)), 400
    if success == -12:
        return jsonify(standard_response(success=-12, status=500, message=message)), 500

    return jsonify(standard_response(success=success, status=200, message=message, data=data)), 200
    
