from main import app
import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime
from booking_params import Regions
from _email import send_mail
from models import DriverBank, Drivers, DriverDetails, Users, Trip, Bookings
from db_config import db
import pandas as pd

DUE_THRESHOLD = 100
RIDE_THRESHOLD = 0

def write_to_excel(write_list, sheet_name, filename):
    filename = D4M_UTIL_PATH + 'output/' + filename
    df = pd.DataFrame(write_list)
    writer = pd.ExcelWriter(filename, engine='xlsxwriter')
    df.to_excel(writer, sheet_name=sheet_name, index=False)
    writer._save()
    return filename

if __name__ == '__main__':
    with app.app_context():
        q = db.session.query(Drivers, DriverDetails, Users). \
                    filter(DriverDetails.withdrawable >= DUE_THRESHOLD). \
                    filter(Drivers.id == DriverDetails.driver_id). \
                    filter(Drivers.approved >= 0). \
                    filter(Drivers.perma == 0). \
                    filter(DriverDetails.ride_count > RIDE_THRESHOLD). \
                    filter(Drivers.user == Users.id). \
                    order_by(DriverDetails.owed)
        all_due_pending = q.all()
        due_pending_list = list()
        date_str = datetime.now().strftime("%d%m%Y")
        from_addr = "<EMAIL>"
        to_addr_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        filename = "due-pending-" + date_str + ".xlsx"

        for r in all_due_pending:
            due_e = dict()
            driver_id = r[0].id
            due_e["Driver ID"] = driver_id
            due_e["Driver Mobile"] = r[2].mobile
            due_e["City"] = Regions.to_string(r[2].region)
            ride_count = len(db.session.query(Trip, Bookings). \
                                        filter(Trip.book_id == Bookings.id). \
                                        filter(Bookings.driver == driver_id).all())
            due_e["Ride Count"] = ride_count
            driver_bank = db.session.query(DriverBank). \
                                    filter(DriverBank.driver_id == driver_id). \
                                    first()
            if not driver_bank:
                due_e["IFSC Code"] = due_e["A/C No"] = ""
            else:
                due_e["IFSC Code"] = driver_bank.ifsc
                due_e["A/C No"] = str(driver_bank.acc_no)
            due_e["Driver Name"] = r[2].get_name()
            due_e["Payable Amount"] = str(int( r[1].withdrawable - DUE_THRESHOLD)) + ".00"
            due_e["Wallet Amount"] = str(int(r[1].wallet)) + ".00"
            due_e["Remarks"] = "Due payment " + date_str
            due_pending_list.append(due_e)

        filepath = write_to_excel(due_pending_list, "due-pending", filename)
        subject = "Pending due payments - " + date_str
        content = "Please find Excel sheet containing pending dues to be paid. " \
                "Check the dues in the panel after payment."
        attachment = filepath
        send_mail(from_addr, to_addr_list, subject, content, attachment)
