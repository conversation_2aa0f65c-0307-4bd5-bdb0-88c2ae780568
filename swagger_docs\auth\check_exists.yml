tags:
  - Authentication
summary: Check if a mobile number is registered
description: |
  Checks whether a given mobile number is already registered in the system. 
  Differentiates between user self-registration and admin-registered users.
consumes:
  - multipart/form-data
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number to be checked
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")
responses:
  200:
    description: Mobile number is registered
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Mobile number is registered."
        data:
          type: object
          properties:
            registered_by:
              type: string
              example: "user"
  200_admin:
    description: Mobile number is registered by admin (re-registration required)
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 2
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Mobile number is registered by admin. Re-registration required."
        data:
          type: object
          properties:
            registered_by:
              type: string
              example: "admin"
  404:
    description: Mobile number not found
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Mobile number not found."
  400:
    description: Invalid request (e.g. validation error)
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid mobile number format"
  500:
    description: Server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
