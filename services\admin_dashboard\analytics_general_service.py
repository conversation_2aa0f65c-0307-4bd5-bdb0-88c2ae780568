#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

import time
from datetime import datetime, timedelta

from flask import request
from sqlalchemy.sql import func, case
from sqlalchemy.orm import aliased
from sqlalchemy import and_, or_, not_
from typing import Tuple, List, Dict

from models.models import db, Users, UserRegistrationDetails, Bookings, Trip, Drivers, \
    BookingCancelled, UserTrans, DriverTrans, DriverInfo, \
    BookingAlloc, AdminAccess, TripLog, TripPricing , DriverSetLoc, UserLoc, DriverSearch
from models.affiliate_models import AffBookingLogs, AffiliateCollections  
from utils.time_utils import convert_to_utc
from utils.bookings.booking_params import BookingParams, Regions
from utils.user_utils import get_name_by_id
from schemas.admin_dashboard.analytics_schemas import AnalyticsCountPayload, AdminAnalyticsDailySalesPayload, \
    AdminAnalyticsSalesPayload, AdminAnalyticsDailyRevenuePayload, AnalyticsRevenuePayload, DailyTripsPayload, \
        TripsAnalyticsPayload, RatingAnalyticsPayload, CustomerRegistrationCountPayload, CustomerRegisterSourcePayload, \
            CancellationReasonCountPayload

def admin_analytics_count_service(payload: AnalyticsCountPayload):
    """
    Computes analytics summary metrics for registered and active customers/drivers, trip statistics, and financials.

    Args:
        payload (AnalyticsCountPayload): Payload containing from_date, to_date, and region.

    Returns:
        tuple: A tuple containing:
            - int: Success code (1 for success, -3 for invalid date)
            - str: Status message
            - dict: Analytics data including counts and trends
    """
    from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
    to_date_utc = convert_to_utc(payload.to_date, "23:59:59")
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        return -3, "Missing or invalid date values", {}

    region = payload.region
    regions = [int(r) for r in region.split(',')] if region and region != '-1' else None

    customers = db.session.query(Users.id).outerjoin(UserRegistrationDetails, Users.id == UserRegistrationDetails.user_id) \
        .filter(Users.role == 0, Users.enabled == 1)
    if regions:
        customers = customers.filter(UserRegistrationDetails.region.in_(regions) | (UserRegistrationDetails.region.is_(None)))
    reg_customers = customers.filter(Users.reg >= from_date, Users.reg <= to_date)
    reg_customers_count = reg_customers.count()

    date_diff = (to_date - from_date).days + 1
    previous_from_date = from_date - timedelta(days=date_diff)
    previous_to_date = to_date - timedelta(days=date_diff)

    active_user = db.session.query(Users.id).join(Bookings, Users.id == Bookings.user) \
        .join(Trip, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24).distinct()
    if regions:
        active_user = active_user.filter(Bookings.region.in_(regions))
    active_user_count = active_user.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).count()
    active_user_count_previous = active_user.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).count()

    trend_active_user = (active_user_count - active_user_count_previous) if active_user_count_previous is not None else active_user_count

    trip_count = db.session.query(func.count(Trip.id)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        trip_count = trip_count.filter(Bookings.region.in_(regions))
    trip_count_current = trip_count.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar()

    avg_trips_per_user = trip_count_current / active_user_count if active_user_count > 0 else 0

    drivers = db.session.query(Users, Drivers).filter(Drivers.user == Users.id) \
        .filter(Users.role == 1, Users.enabled == 1)
    if regions:
        drivers = drivers.filter(Users.region.in_(regions))
    reg_drivers = drivers.filter(Users.reg >= from_date, Users.reg <= to_date)
    reg_drivers_count = reg_drivers.count()

    UsersAlias = aliased(Users)
    active_driver = db.session.query(Drivers.id).join(Bookings, Drivers.id == Bookings.driver) \
        .join(Trip, Bookings.id == Trip.book_id) \
        .join(UsersAlias, Drivers.user == UsersAlias.id) \
        .filter(Drivers.approved == 1, Bookings.type < BookingParams.TYPE_C24).distinct()
    if regions:
        active_driver = active_driver.filter(UsersAlias.region.in_(regions))
    active_driver_count = active_driver.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).count()
    active_driver_count_previous = active_driver.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).count()

    trend_active_driver = (active_driver_count - active_driver_count_previous) if active_driver_count_previous is not None else active_driver_count

    avg_trips_per_driver = trip_count_current / active_driver_count if active_driver_count > 0 else 0

    booking_count = db.session.query(func.count(Bookings.id)).filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        booking_count = booking_count.filter(Bookings.region.in_(regions))
    total_booking_count = booking_count.filter(Bookings.startdate >= from_date, Bookings.startdate <= to_date).scalar() or 0

    average_trips_per_day = trip_count_current / date_diff if date_diff > 0 else 0
    trip_count_prev = trip_count.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).scalar()
    trend_trips = (trip_count_current - trip_count_prev) if trip_count_prev is not None else trip_count_current

    total_sales = db.session.query(func.sum(Trip.price)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        total_sales = total_sales.filter(Bookings.region.in_(regions))
    total_sales_count = total_sales.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar() or 0

    total_revenue = db.session.query(func.sum(Trip.net_rev)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        total_revenue = total_revenue.filter(Bookings.region.in_(regions))
    total_revenue_count = total_revenue.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar() or 0

    prev_sales_count = total_sales.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).scalar() or 0
    trend_sales = (total_sales_count - prev_sales_count) if prev_sales_count is not None else total_sales_count
    average_sales_per_day = total_sales_count / date_diff if date_diff > 0 else 0

    return 1, "Success", {
        "registered_customer": reg_customers_count,
        "active_customers": active_user_count,
        "trend_user": trend_active_user,
        "average_trips_per_user": round(avg_trips_per_user, 1),
        "registered_drivers": reg_drivers_count,
        "active_drivers": active_driver_count,
        "trend_driver": trend_active_driver,
        "average_trips_per_driver": round(avg_trips_per_driver, 1),
        "total_bookings": total_booking_count,
        "total_trips_count": trip_count_current,
        "trend_trips": trend_trips,
        "average_trips_per_day": round(average_trips_per_day, 2),
        "total_sales": round(total_sales_count),
        "trend_sales": round(trend_sales),
        "total_revenue": round(total_revenue_count),
        "average_sales_per_day": round(average_sales_per_day)
    }
    
def get_daily_sales_data(payload: AdminAnalyticsDailySalesPayload):
    """
    Retrieves daily sales (Trip.price) for the last 60 days based on region and affiliate filters.

    Args:
        payload (AdminAnalyticsDailySalesPayload): Contains region, is_b2b, and affiliatefilter.

    Returns:
        dict: A list of day-wise sales data in reverse chronological order (oldest first).
    """
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter

    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    daily_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        daily_sales = daily_sales.filter(
            Bookings.type < BookingParams.TYPE_C24
        ).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))
        ).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
        )
    else:
        daily_sales = daily_sales.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        ).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))
        ).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
        )

    if region and region != '-1':
        regions = [int(value) for value in region.split(',')]
        daily_sales = daily_sales.filter(Bookings.region.in_(regions))

    if aff_ids:
        daily_sales = daily_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    results = daily_sales.limit(60).all()[::-1]

    day_json = []
    for row in results:
        if row.day is None:
            continue
        day_json.append({
            "day": row.day.strftime('%d-%m-%Y'),
            "sales": float(round(row.sale, 2)) if row.sale not in [None, ''] else 0.0
        })

    return {
            'day_data': day_json
    }
    
def get_analytics_graph_sales(payload: AdminAnalyticsSalesPayload):
    """
    Aggregates sales data (Trip.price) over year, month, and week for graph plotting.

    Args:
        payload (AdminAnalyticsSalesPayload): Payload containing region, is_b2b, and affiliatefilter.

    Returns:
        dict: A dictionary containing lists of yearly, monthly, and weekly sales data.
    """
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    # Monthly sales
    monthly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        monthly_sales = monthly_sales.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        monthly_sales = monthly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            monthly_sales = monthly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        regions = [int(r) for r in region.split(',')]
        monthly_sales = monthly_sales.filter(Bookings.region.in_(regions))

    monthly_sales = monthly_sales.group_by(
        func.month(func.addtime(Trip.starttime, '05:30:00')),
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # Yearly sales
    yearly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        yearly_sales = yearly_sales.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        yearly_sales = yearly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            yearly_sales = yearly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        yearly_sales = yearly_sales.filter(Bookings.region.in_(regions))

    yearly_sales = yearly_sales.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # Weekly sales
    weekly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        weekly_sales = weekly_sales.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        weekly_sales = weekly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            weekly_sales = weekly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        weekly_sales = weekly_sales.filter(Bookings.region.in_(regions))

    weekly_sales = weekly_sales.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12).all()[::-1]

    # Format output
    month_json = [{
        "month": row.month,
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in monthly_sales if row.month and row.year]

    year_json = [{
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in yearly_sales if row.year]

    week_json = [{
        "week": row.week,
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in weekly_sales if row.week]

    return {
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
    }
    
    
def get_analytics_daily_revenue(payload: AdminAnalyticsDailyRevenuePayload):
    """
    Retrieves daily net revenue (Trip.net_rev) for the last 60 days.

    Args:
        payload (AdminAnalyticsDailyRevenuePayload): Contains region, is_b2b, and affiliatefilter.

    Returns:
        dict: A list of day-wise net revenue data in reverse chronological order (oldest first).
    """
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    query = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        query = query.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        query = query.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            query = query.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        region_ids = [int(r) for r in region.split(',')]
        query = query.filter(Bookings.region.in_(region_ids))

    query = query.group_by(
        func.date(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
    ).limit(60)

    results = query.all()[::-1]  # reverse for chronological order

    day_data = []
    for row in results:
        if row.day is None:
            continue
        day_data.append({
            "day": row.day.strftime('%d-%m-%Y'),
            "revenues": float(row.revenue) if row.revenue not in [None, ''] else 0.0
        })

    return {
            "day_data": day_data
    }
    
    
    
def get_revenue_graph_data(payload: AnalyticsRevenuePayload):
    """
    Aggregates revenue (Trip.net_rev) over year, month, and week for plotting purposes.

    Args:
        payload (AnalyticsRevenuePayload): Contains region, is_b2b, and affiliatefilter.

    Returns:
        tuple: A tuple containing:
            - dict: Dictionary with year, month, and week revenue data
            - int: HTTP status code (200 for success, -1 if no data found)
    """
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    region_ids = [int(r) for r in region.split(',')] if region and region != '-1' else None

    # Yearly Revenue Query
    yearly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    # Monthly Revenue Query
    monthly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    # Weekly Revenue Query
    weekly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        yearly = yearly.filter(Bookings.type < BookingParams.TYPE_C24)
        monthly = monthly.filter(Bookings.type < BookingParams.TYPE_C24)
        weekly = weekly.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        yearly = yearly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        monthly = monthly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        weekly = weekly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            yearly = yearly.filter(AffBookingLogs.aff_id.in_(aff_ids))
            monthly = monthly.filter(AffBookingLogs.aff_id.in_(aff_ids))
            weekly = weekly.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        yearly = yearly.filter(Bookings.region.in_(region_ids))
        monthly = monthly.filter(Bookings.region.in_(region_ids))
        weekly = weekly.filter(Bookings.region.in_(region_ids))

    # Group + Order
    yearly = yearly.group_by(func.year(func.addtime(Trip.starttime, '05:30:00')))
    monthly = monthly.group_by(
        func.month(func.addtime(Trip.starttime, '05:30:00')),
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    )
    weekly = weekly.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12)

    # Run queries
    start_time = time.time()
    year_data = yearly.all()
    month_data = monthly.all()
    week_data = weekly.all()[::-1]
    query_time = time.time() - start_time

    # Transform results
    year_json = [
        {
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in year_data if row.year
    ]

    month_json = [
        {
            "month": row.month,
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in month_data if row.month and row.year
    ]

    week_json = [
        {
            "week": row.week,
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in week_data if row.week
    ]

    if not (year_json or month_json or week_json):
        return {'success': -1}, 200

    return {
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
    }, 200
    

def get_daily_trips_graph_data(payload: DailyTripsPayload):
    
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None
    region_ids = [int(x) for x in region.split(',')] if region and region != '-1' else None

    daily_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        daily_trips = daily_trips.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        daily_trips = daily_trips.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            daily_trips = daily_trips.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        daily_trips = daily_trips.filter(Bookings.region.in_(region_ids))

    daily_trips = daily_trips.group_by(
        func.date(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
    ).limit(60).all()[::-1]

    day_json = []
    for row in daily_trips:
        if row.day:
            day_json.append({
                "day": row.day.strftime('%d-%m-%Y'),
                "trips": int(row.trip) if row.trip else 0
            })

    return {
            "day_data": day_json
    }, 200
    
    
def get_trips_graph_data(payload: TripsAnalyticsPayload):
    """
    Retrieves the count of trips between the specified dates, filtered by region and affiliate.

    Args:
        from_date (datetime): Start date of the range.
        to_date (datetime): End date of the range.
        region (str): Region filter.
        affiliatefilter (list): List of affiliate IDs to filter on.

    Returns:
        int: Total number of completed trips in the given date range and filters.
    """
    region_ids = [int(x) for x in payload.region.split(',')] if payload.region and payload.region != '-1' else None
    aff_ids = [int(x) for x in payload.affiliatefilter.split(',')] if payload.affiliatefilter else None
    is_b2b = payload.is_b2b

    # --- Base Query ---
    trip_filter = [Bookings.id == Trip.book_id]

    if is_b2b == 0:
        trip_filter.append(Bookings.type < BookingParams.TYPE_C24)
    else:
        trip_filter.extend([
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        ])
        if aff_ids:
            trip_filter.append(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        trip_filter.append(Bookings.region.in_(region_ids))

    # --- Yearly ---
    yearly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # --- Monthly ---
    monthly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # --- Weekly ---
    weekly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12).all()[::-1]

    # --- Serialize Results ---
    year_json = [
        {"year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in yearly_trips if row.year is not None
    ]

    month_json = [
        {"month": row.month, "year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in monthly_trips if row.month is not None and row.year is not None
    ]

    week_json = [
        {"week": row.week, "year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in weekly_trips if row.week is not None
    ]

    if len(year_json) + len(month_json) + len(week_json) == 0:
        return {'success': -1}, 200

    return {
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
    }, 200
    
    
def get_total_ratings_data(payload: RatingAnalyticsPayload):
    """
    Calculates the average customer rating for trips completed in the specified time period and filters.

    Args:
        from_date (datetime): Start date of the range.
        to_date (datetime): End date of the range.
        region (str): Region filter.
        affiliatefilter (list): List of affiliate IDs to filter on.

    Returns:
        float: Average customer rating rounded to 2 decimal places, or 0 if no data exists.
    """
    try:
        from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
        to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

        from_date = datetime.strptime(from_date_utc[0], '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date_utc[0], '%Y-%m-%d').date()
        from_time = datetime.strptime(from_date_utc[1], '%H:%M:%S').time()
        to_time = datetime.strptime(to_date_utc[1], '%H:%M:%S').time()

        if from_date > to_date:
            return {'success': -3, 'error': 'Missing or invalid date values'}, 400

        if payload.search_region == Regions.ALL_REGIONS_ACCESS:
            region_filter = None
        else:
            region_filter = [int(r) for r in payload.search_region.split(',')]

        q = db.session.query(
            func.count(case((Bookings.user_rating == 5, 1))).label('count_5_star'),
            func.count(case((Bookings.user_rating == 4, 1))).label('count_4_star'),
            func.count(case((Bookings.user_rating == 3, 1))).label('count_3_star'),
            func.count(case((Bookings.user_rating == 2, 1))).label('count_2_star'),
            func.count(case((Bookings.user_rating == 1, 1))).label('count_1_star'),
            func.count(case((Bookings.user_rating == 0, 1))).label('count_unrated'),
            func.avg(case((Bookings.user_rating > 0, Bookings.user_rating))).label('average_rating'),
            func.count(Bookings.user_rating).label('total_ratings')
        ).join(Trip, Bookings.id == Trip.book_id).filter(
            Bookings.type < 50,
            Trip.status == Trip.TRIP_STOPPED,
            (
                (Bookings.startdate == from_date) & (Bookings.starttime >= from_time) |
                (Bookings.startdate > from_date) & (Bookings.startdate < to_date) |
                (Bookings.startdate == to_date) & (Bookings.starttime <= to_time)
            )
        )

        if region_filter:
            q = q.filter(Bookings.region.in_(region_filter))

        result = q.first()

        result_list = [
            result.count_5_star or 0,
            result.count_4_star or 0,
            result.count_3_star or 0,
            result.count_2_star or 0,
            result.count_1_star or 0,
            result.count_unrated or 0,
            float(round(result.average_rating, 2)) if result.average_rating else 0.0,
            result.total_ratings or 0
        ]
        return result_list, 200
    except Exception as e:
        raise e
    
def get_customer_registration_counts(payload: CustomerRegistrationCountPayload):
    """
    Counts the number of customers who registered within the specified date range and filters.

    Args:
        from_date (datetime): Start date of the range.
        to_date (datetime): End date of the range.
        region (str): Region filter.
        affiliatefilter (list): List of affiliate IDs to filter on.

    Returns:
        int: Number of newly registered customers.
    """
    try:
        from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
        to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

        from_datetime = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
        to_datetime = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

        if from_datetime > to_datetime:
            return {'success': -3, 'error': 'Missing or invalid date values'}, 400

        # Query city-wise registration
        city_q = db.session.query(
            UserRegistrationDetails.city,
            func.count(UserRegistrationDetails.id).label('registration_count')
        ).join(
            Users, UserRegistrationDetails.user_id == Users.id
        ).filter(
            Users.reg >= from_datetime,
            Users.reg <= to_datetime,
            UserRegistrationDetails.city.isnot(None),
            UserRegistrationDetails.address.isnot(None)
        ).group_by(UserRegistrationDetails.city).order_by(
            func.count(UserRegistrationDetails.id).desc()
        ).limit(10).all()

        # Query region-wise registration
        region_q = db.session.query(
            UserRegistrationDetails.region,
            func.count(UserRegistrationDetails.id).label('registration_count')
        ).join(
            Users, UserRegistrationDetails.user_id == Users.id
        ).filter(
            Users.reg >= from_datetime,
            Users.reg <= to_datetime,
            UserRegistrationDetails.region.isnot(None)
        ).group_by(UserRegistrationDetails.region).order_by(
            func.count(UserRegistrationDetails.id).desc()
        ).limit(10).all()

        city_list = [{'city': row.city, 'count': row.registration_count} for row in city_q]
        region_list = [{'region': Regions.to_string_reg(row.region), 'count': row.registration_count} for row in region_q]

        return {
                'city_registration': city_list,
                'region_registration': region_list
        }, 200
    except Exception as e:
        raise e
    
    
def get_customer_source_counts(payload: CustomerRegisterSourcePayload):
    """
    Retrieves the distribution of customer acquisition sources within a given date range.

    Args:
        from_date (datetime): Start date of the range.
        to_date (datetime): End date of the range.
        region (str): Region filter.
        affiliatefilter (list): List of affiliate IDs to filter on.

    Returns:
        list[dict]: A list of dictionaries, each containing:
            - 'source' (str): Source name (e.g., 'Referral', 'Organic').
            - 'count' (int): Number of customers from that source.
    """
    from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
    to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

    from_datetime = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_datetime = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_datetime > to_datetime:
        return {'success': -3, 'error': 'Missing or invalid date values'}, 400

    if payload.search_region is None or payload.search_region.strip() == '':
        return {'success': -4, 'error': 'Missing Region'}, 400

    regions = None
    if payload.search_region and payload.search_region != '-1':
        regions = [int(value) for value in payload.search_region.split(',')]

    query = db.session.query(
        case(
            (UserRegistrationDetails.source.in_(UserRegistrationDetails.predefined_sources), UserRegistrationDetails.source),
            else_="Others"
        ).label("source"),
        func.count(UserRegistrationDetails.id).label("registration_count")
    ).join(
        Users, UserRegistrationDetails.user_id == Users.id
    ).filter(
        Users.reg >= from_datetime,
        Users.reg <= to_datetime,
        UserRegistrationDetails.source.isnot(None)
    )

    if regions:
        query = query.filter(UserRegistrationDetails.region.in_(regions))

    result = query.group_by("source").order_by(
        func.count(UserRegistrationDetails.id).desc()
    ).all()

    result_list = [{'source': row.source, 'count': row.registration_count} for row in result]

    return result_list, 200


def get_cancellation_reason_counts(payload: CancellationReasonCountPayload):
    """
    Counts the number of cancelled bookings in the specified time period and filters.

    Args:
        from_date (datetime): Start date of the range.
        to_date (datetime): End date of the range.
        region (str): Region filter.
        affiliatefilter (list): List of affiliate IDs to filter on.

    Returns:
        int: Total number of cancelled bookings.
    """
    from_date_str, from_time_str = convert_to_utc(payload.from_date, payload.from_time)
    to_date_str, to_time_str = convert_to_utc(payload.to_date, payload.to_time)

    if from_date_str > to_date_str:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    aff_ids = [int(x) for x in payload.affiliatefilter.split(',')] if payload.affiliatefilter else None
    regions = [int(r) for r in payload.search_region.split(',')] if payload.search_region and payload.search_region != '-1' else None

    latest_cancellations = (
        db.session.query(
            BookingCancelled.booking.label("b_cancelled_book_id"),
            BookingCancelled.reason.label("b_cancelled_reason"),
            BookingCancelled.reason_detail.label("b_cancelled_reason_detail"),
            BookingCancelled.timestamp.label("b_cancelled_timestamp"),
            func.row_number().over(
                partition_by=BookingCancelled.booking,
                order_by=BookingCancelled.timestamp.desc()
            ).label("rn")
        )
        .filter(BookingCancelled.reason != BookingCancelled.RSN_REVERSE_CANCELLATION)
        .subquery()
    )

    query = (
        db.session.query(
            case(
                ((Bookings.driver == 1) & (Bookings.valid == Bookings.CANCELLED_USER), "Pre Cancel"),
                ((Bookings.driver != 1) & (Bookings.valid == Bookings.CANCELLED_USER), "Post Cancel"),
                (Bookings.valid == Bookings.CANCELLED_D4M, "Admin Cancel"),
                else_="Unknown",
            ).label("cancel_type"),
            func.count().label("count"),
            latest_cancellations.c.b_cancelled_reason.label("cancellation_reason"),
        )
        .join(latest_cancellations, Bookings.id == latest_cancellations.c.b_cancelled_book_id)
        .filter(Bookings.valid < 0)
        .filter(latest_cancellations.c.rn == 1)
        .filter(
            and_(
                or_(
                    and_(Bookings.startdate > from_date_str),
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str),
                ),
                or_(
                    and_(Bookings.startdate < to_date_str),
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str),
                ),
            )
        )
    )

    if payload.is_b2b == 0:
        query = query.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        query = query.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            query = query.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if regions:
        query = query.filter(Bookings.region.in_(regions))

    query = query.group_by(
        latest_cancellations.c.b_cancelled_reason, "cancel_type"
    ).order_by(func.count().desc())

    results = query.all()

    result_dict = {}
    for row in results:
        stage = row.cancel_type
        if stage not in result_dict:
            result_dict[stage] = []
        result_dict[stage].append({'reason': row.cancellation_reason, 'count': row.count})

    formatted_result = [
        {'cancel_stage': stage, 'reasons': reasons[:10]}
        for stage, reasons in result_dict.items()
    ]

    return formatted_result, 200