tags:
  - Login_admin
summary: Generate OTP for Admin
description: >
  This endpoint allows admins to request the generation of a One-Time Password (OTP) for login. Admins can request an OTP via their mobile number. There is a limit to how frequently OTPs can be requested.
parameters:
  - in: formData
    name: mobile
    type: string
    required: true
    description: <PERSON><PERSON>'s mobile number for which the OTP should be generated.
    example: "9876543210"
responses:
  200:
    description: OTP successfully generated and sent to the provided mobile number.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
  400:
    description: Bad request due to missing mobile number.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failure).
          example: -1
  401:
    description: Unauthorized request, invalid mobile number, or the account is disabled.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for failure).
          example: -2
  422:
    description: Validation error in input data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid input
        data:
          type: object
          properties:
            error:
              type: array
              items:
                type: string
  429:
    description: Too many OTP requests in a short period.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for failure).
          example: -3
        message:
          type: string
          description: Error message indicating that OTP requests are too frequent.
          example: "OTP request too frequent"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-99 for failure).
          example: -99
        error:
          type: string
          description: Error message detailing the exception that occurred.
          example: "Internal server error"
