from redis_config import execute_with_fallback
from flask_socketio import emit, join_room, leave_room, disconnect
import time
from flask import Blueprint, jsonify, request
import jwt
from db_config import db
from models.affiliate_models import Affiliate, AffiliateRep,AffiliateCollections
from bson.objectid import ObjectId
from socketio_config import socketio
import json
from flask import current_app as app

notify = Blueprint('socketio_b2b', __name__)

notification_expiry = {
    1: 86400,  # 24 hours
    2: 86400,  # 24 hours
    3: 86400,  # 24 hours
    4: 86400,  # 24 hours
    5: 86400,  # 24 hours
    6: 86400,
    7: 86400
}

def get_b2b_notification_type(noti_type: str) -> int:
    notification_mapping = {
        "recharge": 1,  # 4
        "transfer": 2, # 4
        "booking": 3, # 1
        "cancelled by admin": 4, # 3
        "cancelled by driver": 5, # 3
        "cancelled by affiliate": 6, # 3
        "allocate": 7 # 2
    }

    return notification_mapping.get(noti_type.lower(), None)

def get_recipient_affiliates(src_aff_id, dest_aff_id):
    recipient_affiliates = set()
    current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == dest_aff_id).first()
    visited_affiliates = set()
    while current_affiliate and current_affiliate.master != -1:
        if current_affiliate.master not in visited_affiliates:
            if current_affiliate.master != src_aff_id:
                recipient_affiliates.add(current_affiliate.master)
            visited_affiliates.add(current_affiliate.master)
        current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == current_affiliate.master).first()

    return recipient_affiliates

def create_recharge_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id):
    recipient_reps = set()
    messages = {}

    src_aff_reps = db.session.query(AffiliateRep).filter(AffiliateRep.affiliate_id == src_aff_id).all()
    recipient_reps |= {rep.id for rep in src_aff_reps if rep.id != src_rep}

    recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id)

    if src_aff_id != dest_aff_id:
        recipient_affiliates.add(dest_aff_id)
        messages.update({
            'receipt_aff_msg': f"₹ {notification_data['amount']} added to your affiliate account - ({notification_data['client_name']})",
            'src_rep_msg': f"₹ {notification_data['amount']} credited to {notification_data['dest_client_name']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"₹ {notification_data['amount']} credited to {notification_data['dest_client_name']} - ({notification_data['client_name']})"
        })
    else:
        messages.update({
            'src_rep_msg': f"₹ {notification_data['amount']} added to your affiliate account - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"₹ {notification_data['amount']} credited to {notification_data['dest_client_name']} - {notification_data['client_name']}",
            'receipt_aff_msg': f"₹ {notification_data['amount']} credited to {notification_data['dest_client_name']} - {notification_data['client_name']}"
        })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'recharge.gif',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'source_affiliate': src_aff_id,
            'source_aff_rep': src_rep,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None

def create_transfer_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id, sender_aff_id):
    recipient_reps = set()
    messages = {}

    src_aff_reps = db.session.query(AffiliateRep).filter(AffiliateRep.affiliate_id == src_aff_id).all()
    recipient_reps |= {rep.id for rep in src_aff_reps if rep.id != src_rep}

    if src_aff_id != sender_aff_id and src_aff_id != dest_aff_id:
        recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id) | \
                            get_recipient_affiliates(src_aff_id, sender_aff_id) | \
                                {dest_aff_id} | {sender_aff_id}
        messages.update({
            'receipt_aff_msg': f"₹ {notification_data['amount']} is transfer to your affiliate account - ({notification_data['client_name']})",
            'src_rep_msg': f"₹ {notification_data['amount']} is transfer from {notification_data['sender_client_name']} to {notification_data['receiver_client_name']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"₹ {notification_data['amount']} is transfer from {notification_data['sender_client_name']} to {notification_data['receiver_client_name']} - ({notification_data['client_name']})"
        })

    elif src_aff_id != dest_aff_id:
        recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id) | {dest_aff_id}
        messages.update({
            'receipt_aff_msg': f"₹ {notification_data['amount']} is transfer to your affiliate account - ({notification_data['client_name']})",
            'src_rep_msg': f"₹ {notification_data['amount']} is transfer from your affiliate account to {notification_data['receiver_client_name']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"₹ {notification_data['amount']} is transfer from {notification_data['sender_client_name']} to {notification_data['receiver_client_name']} - ({notification_data['client_name']})"
        })
    else:
        recipient_affiliates = get_recipient_affiliates(src_aff_id, sender_aff_id) | {sender_aff_id}
        messages.update({
            'receipt_aff_msg': f"₹ {notification_data['amount']} is transfer to your affiliate account from {notification_data['sender_client_name']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'src_rep_msg': f"₹ {notification_data['amount']} is transfer to your affiliate account from {notification_data['sender_client_name']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"₹ {notification_data['amount']} is transfer from {notification_data['sender_client_name']} to {notification_data['receiver_client_name']} - ({notification_data['client_name']})"
        })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'transfer.gif',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'source_affiliate': src_aff_id,
            'source_aff_rep': src_rep,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None


def create_booking_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id):
    recipient_reps = set()
    messages = {}

    src_aff_reps = db.session.query(AffiliateRep).filter(AffiliateRep.affiliate_id == src_aff_id).all()
    recipient_reps |= {rep.id for rep in src_aff_reps if rep.id != src_rep}

    recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id)

    if src_aff_id != dest_aff_id:
        recipient_affiliates.add(dest_aff_id)
        messages.update({
            'receipt_aff_msg': f"New Booking is created for your affiliate. Code: {notification_data['book_code']} - ({notification_data['client_name']})",
            'src_rep_msg': f"New Booking is created for {notification_data['dest_client_name']}. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"New Booking is created for {notification_data['dest_client_name']}. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })
    else:
        messages.update({
            'src_rep_msg': f"New Booking is created. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"New Booking is created. Code: {notification_data['book_code']} - ({notification_data['client_name']})",
            'receipt_aff_msg': f"New Booking is created. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'booking.gif',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'source_affiliate': src_aff_id,
            'source_aff_rep': src_rep,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None

def create_affiliate_cancelled_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id):
    recipient_reps = set()
    messages = {}

    src_aff_reps = db.session.query(AffiliateRep).filter(AffiliateRep.affiliate_id == src_aff_id).all()
    recipient_reps |= {rep.id for rep in src_aff_reps if rep.id != src_rep}

    recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id)

    if src_aff_id != dest_aff_id:
        recipient_affiliates.add(dest_aff_id)
        messages.update({
            'receipt_aff_msg': f"Your booking is cancelled. Code: {notification_data['book_code']} - ({notification_data['client_name']})",
            'src_rep_msg': f"Booking is cancelled for {notification_data['dest_client_name']}. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"Booking is cancelled for {notification_data['dest_client_name']}. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })
    else:
        messages.update({
            'receipt_aff_msg': f"Booking is cancelled. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'src_rep_msg': f"Booking is cancelled. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"Booking is cancelled for {notification_data['dest_client_name']}. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'affiliate_cancel.svg',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'source_affiliate': src_aff_id,
            'source_aff_rep': src_rep,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None


def create_admin_cancelled_notification(notification_data, notification_type, dest_aff_id):
    recipient_reps = set()
    messages = {}

    recipient_affiliates = get_recipient_affiliates(0, dest_aff_id)
    recipient_affiliates.add(dest_aff_id)

    messages.update({
        'receipt_aff_msg': f"Booking is cancelled by Admin. Code: {notification_data['book_code']}",
        'other_aff_msg': f"Booking is cancelled by Admin. Code: {notification_data['book_code']}"
    })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'admin_cancel.svg',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None


def create_driver_cancelled_notification(notification_data, notification_type, dest_aff_id):
    recipient_reps = set()
    messages = {}

    recipient_affiliates = get_recipient_affiliates(0, dest_aff_id)
    recipient_affiliates.add(dest_aff_id)

    messages.update({
        'receipt_aff_msg': f"Booking is cancelled by Driver. Code: {notification_data['book_code']}",
        'other_aff_msg': f"Booking is cancelled by Driver. Code: {notification_data['book_code']}"
    })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'driver_cancel.svg',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None


def create_driver_allocate_notification(notification_data, notification_type, dest_aff_id):
    recipient_reps = set()
    messages = {}

    recipient_affiliates = get_recipient_affiliates(0, dest_aff_id)

    recipient_affiliates.add(dest_aff_id)

    recipient_affiliates.add(dest_aff_id)
    messages.update({
        'receipt_aff_msg': f"Driver is allocated to booking. Code: {notification_data['book_code']}",
        'other_aff_msg': f"Driver is allocated to booking. Code: {notification_data['book_code']}"
    })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'allocate.svg',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None

def create_affiliate_driver_allocate_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id):
    recipient_reps = set()
    messages = {}

    src_aff_reps = db.session.query(AffiliateRep).filter(AffiliateRep.affiliate_id == src_aff_id).all()
    recipient_reps |= {rep.id for rep in src_aff_reps if rep.id != src_rep}

    recipient_affiliates = get_recipient_affiliates(src_aff_id, dest_aff_id)

    if src_aff_id != dest_aff_id:
        recipient_affiliates.add(dest_aff_id)
        messages.update({
            'receipt_aff_msg': f"Driver is allocated. Code: {notification_data['book_code']} - ({notification_data['client_name']})",
            'src_rep_msg': f"Driver is allocated. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"Driver is allocated. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })
    else:
        messages.update({
            'receipt_aff_msg': f"Driver is allocated. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'src_rep_msg': f"Driver is allocated. Code: {notification_data['book_code']} - {notification_data['rep_name']} ({notification_data['client_name']})",
            'other_aff_msg': f"Driver is allocated. Code: {notification_data['book_code']} - ({notification_data['client_name']})"
        })

    try:
        current_timestamp = int(time.time() * 1000)
        expiry = notification_expiry.get(notification_type, 3600)  # Default 1 hour expiry
        expiration_timestamp = current_timestamp + (expiry * 1000)

        notification_data.update({
            'imageUrl': 'allocate.svg',
            'expiry': expiration_timestamp,
            'created_at': current_timestamp,
            'source_affiliate': src_aff_id,
            'source_aff_rep': src_rep,
            'dest_affiliate': dest_aff_id,
            'messages': messages,
            'recipient_affiliates': list(recipient_affiliates),
            'recipient_reps': list(recipient_reps),
            'read_by': []
        })

        doc_id = AffiliateCollections.affiliates_notifications.insert_one(notification_data).inserted_id
        return str(doc_id)
    except Exception as e:
        print(f"Error storing notification: {e}", flush=True)
        return None

def send_notification_to_affiliate(notification_data, type, dest_aff_id, src_rep=-1, src_aff_id=-1, sender_aff_id=-1):
    notification_type = get_b2b_notification_type(type)

    if notification_type is None:
        return {"status": "Invalid notification type"}

    doc_id = ""
    if notification_type == 1:
        doc_id = create_recharge_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id)
    elif notification_type == 2:
        doc_id = create_transfer_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id, sender_aff_id)
    elif notification_type == 3:
        doc_id = create_booking_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id)
    elif notification_type == 4:
        doc_id = create_admin_cancelled_notification(notification_data, notification_type, dest_aff_id)
    elif notification_type == 5:
        doc_id = create_driver_cancelled_notification(notification_data, notification_type, dest_aff_id)
    elif notification_type == 6:
        doc_id = create_affiliate_cancelled_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id)
    elif notification_type == 7:
        if src_rep != -1:
            doc_id = create_affiliate_driver_allocate_notification(notification_data, notification_type, src_rep, src_aff_id, dest_aff_id)
        else:
            doc_id = create_driver_allocate_notification(notification_data, notification_type, dest_aff_id)

    if not doc_id:
        return {"status": "Failed to send notification"}

    data = {
        'id': str(doc_id),
        'image': notification_data.get('imageUrl'),
        'type': notification_data.get('type'),
        'created_at': notification_data.get('created_at'),
    }

    # Send to recipient affiliates
    for aff in notification_data.get('recipient_affiliates', []):
        data['content'] = notification_data['messages'].get(
            'receipt_aff_msg' if aff == dest_aff_id else 'other_aff_msg',
            "New notification"
        )
        socketio.emit('affiliate', data, room=f"affiliate_{aff}")

    # Send to reps in src_affiliate, excluding the sender
    if src_aff_id != -1 and src_rep != -1:
        src_aff_room = f"room_reps:affiliate_{src_aff_id}"
        sessions = execute_with_fallback("hgetall", src_aff_room) or {}

        filtered_sessions = [sid for sid, rep in sessions.items() if int(rep) != src_rep]
        # print("Sessions", filtered_sessions, flush=True)
        for sid in filtered_sessions:
            data['content'] = notification_data['messages'].get('src_rep_msg', "New notification")
            socketio.emit('affiliate', data, to=sid)

    return {"status": "Notification sent successfully", "notification_id": doc_id}

def add_to_room(room_name, session_id, rep_id, aff_id):
    existing_affiliate = execute_with_fallback("get", f"rep_affiliate:{rep_id}")
    if existing_affiliate and existing_affiliate != aff_id:
        print(f"Rep {rep_id} moved from {existing_affiliate} to {aff_id}", flush=True)
        old_room_key = f"room_reps:affiliate_{existing_affiliate}"
        old_sessions = execute_with_fallback("hgetall", old_room_key)
        for sid in old_sessions:
            if old_sessions[sid] == str(rep_id):
                execute_with_fallback("hdel", old_room_key, sid)

    execute_with_fallback("set", f"rep_affiliate:{rep_id}", aff_id)
    join_room(room_name)
    execute_with_fallback("hset", f"room_reps:{room_name}", session_id, rep_id)
    execute_with_fallback("set", f"session_rooms:{session_id}", room_name)

    print(f"User {session_id} (Rep {rep_id}) joined room {room_name}", flush=True)

def get_unexpired_notifications(affiliate_id, rep_id):
    current_timestamp = int(time.time() * 1000)
    try:
        unread_notifications = AffiliateCollections.affiliates_notifications.find({
                "$or": [
                    {"recipient_reps": rep_id, "read_by": {"$nin": [rep_id]}},
                    {"recipient_affiliates": affiliate_id, "read_by": {"$nin": [rep_id]}}
                ],
                "expiry": {"$gt": current_timestamp}
            })


        filtered_notifications = []

        for notification in unread_notifications:
            message = ""
            if rep_id in notification.get("recipient_reps", []):
                message = notification["messages"].get("src_rep_msg", "")
            elif affiliate_id == notification.get("dest_affiliate"):
                message = notification["messages"].get("receipt_aff_msg", "")
            else:
                message = notification["messages"].get("other_aff_msg", "")

            filtered_notifications.append({
                "id": str(notification['_id']),
                "image": notification.get("imageUrl"),
                "type": notification.get("type"),
                "created_at": notification.get("created_at"),
                "content": message
            })
        return filtered_notifications

    except Exception as e:
        print(f"Error fetching unread notifications: {e}", flush=True)
        return []

@socketio.on('read_by_rep')
def handle_read_notification(data):

    session_id = request.sid
    rep_id = data.get('rep_id')
    notification_id = data.get('notification_id')

    if not rep_id or not notification_id:
        emit('error', {'message': 'rep_id and notification_id are required'}, to=session_id)
        return

    try:

        if not notification:
            emit('error', {'message': 'Notification not found'}, to=session_id)
            return

        result = AffiliateCollections.affiliates_notifications.update_one(
            {"_id": ObjectId(notification_id)},
            {"$addToSet": {"read_by": rep_id}}
        )

        emit('notification_read', {'rep_id': rep_id, 'notification_id': notification_id}, broadcast=True)

    except Exception as e:
        print(f"Error updating read status: {e}", flush=True)
        emit('error', {'message': 'Internal server error'}, to=session_id)

"""
# testing api to generate notification
@notify.route('/api/test/notification', methods=['POST'])
def notification():
    type = request.form['notification_type']
    src_rep = int(request.form['src_rep'])
    src_aff_id = int(request.form['src_aff_id'])
    dest_aff_id = int(request.form['dest_aff_id'])
    sender_aff = int(request.form['sender_aff_id'])

    if type == "Recharge":
        notification = {
            "amount": 5000,
            "rep_name": "John Doe",
            "client_name": "ABC Travels",
            "dest_client_name": "XYZ Enterprises",
            "type": type
        }
    if type == "Transfer":
        notification = {
                "amount": 500,
                "rep_name": "John Doe",
                "client_name": "ABC Travels",
                "sender_client_name": "PQR Limited",
                "receiver_client_name": "XYZ Enterprises",
                "type": type
            }
    if type == "Booking":
        notification = {
                "book_code": "QSD9DJ",
                "rep_name": "John Doe",
                "client_name": "ABC Travels",
                "dest_client_name": "XYZ Enterprises",
                "type": type
        }

    response = send_notification_to_affiliate(notification, type, dest_aff_id, src_rep=src_rep, src_aff_id=src_aff_id, sender_aff_id=sender_aff)
    return jsonify(response)
"""

# Live update of affiliate
# Booking Table, Booking Details, Wallet

# live_update_type
# 1: booking table, 2: details,

def create_live_affiliate_key(affiliate_id, rep_id):
    """Create a standardized Redis key for live affiliate data."""
    return f"live_affiliate_{affiliate_id}_{rep_id}"

@socketio.on('join_live_room')
def join_live_room(data):
    sid = request.sid
    current_aff_id = data.get('current_aff_id')
    rep_id = data.get('rep_id')

    if not current_aff_id or not rep_id:
        return {'status': 'error', 'message': 'Missing required data'}

    room_name = f"live_affiliate_{current_aff_id}"
    join_room(room_name)

    redis_key = create_live_affiliate_key(current_aff_id, rep_id)

    execute_with_fallback("sadd", redis_key, sid)
    execute_with_fallback("sadd", f"session_affiliate_sets:{sid}", redis_key)

    return {
        'status': 'success',
        'message': f'Joined room {room_name}',
        'room': room_name,
        'session_id': sid
    }

@socketio.on('join_multiple_live_room')
def join_multiple_live_room(data):
    sid = request.sid
    try:
        current_aff_ids = json.loads(data.get('current_aff_id', '[]'))
        if not isinstance(current_aff_ids, list):
            return {'status': 'error', 'message': 'current_aff_id must be a JSON array'}
        rep_id = data.get('rep_id')
    except json.JSONDecodeError:
        return {'status': 'error', 'message': 'Invalid JSON format for current_aff_id'}

    if not current_aff_ids or not rep_id:
        return {'status': 'error', 'message': 'Missing required data'}
    listrooms = []
    for current_aff_id in current_aff_ids:
        room_name = f"live_affiliate_{current_aff_id}"
        join_room(room_name)
        listrooms.append(room_name)

        redis_key = create_live_affiliate_key(current_aff_id, rep_id)

        execute_with_fallback("sadd", redis_key, sid)
        execute_with_fallback("sadd", f"session_affiliate_sets:{sid}", redis_key)

    return {
        'status': 'success',
        'message': f'Joined rooms',
        'room': json.dumps(listrooms),
        'session_id': sid
    }

@socketio.on('leave_live_room')
def leave_live_room(data):
    sid = request.sid
    current_aff_id = data.get('current_aff_id')
    rep_id = data.get('rep_id')

    if not current_aff_id or not rep_id:
        return {'status': 'error', 'message': 'Missing required data'}

    room_name = f"live_affiliate_{current_aff_id}"
    leave_room(room_name)

    # Remove only the specific sid
    redis_key = create_live_affiliate_key(current_aff_id, rep_id)
    execute_with_fallback("srem", redis_key, sid)
    execute_with_fallback("srem", f"session_affiliate_sets:{sid}", redis_key)

    # Check if any SIDs are left in the set
    remaining_sids = execute_with_fallback("scard", redis_key)
    if remaining_sids == 0:
        execute_with_fallback("delete", redis_key)

    return {
        'status': 'success',
        'message': f'Left room {room_name}',
        'room': room_name,
        'session_id': sid
    }

def send_live_update_to_affiliate(data, type, dest_aff_id, region):
    visited_affiliates = set()
    recipient_affiliates = set()
    recipient_affiliates.add(dest_aff_id)
    current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == dest_aff_id).first()

    while current_affiliate and current_affiliate.master != -1:
        current_affiliate = db.session.query(Affiliate).filter(Affiliate.id == current_affiliate.master).first()
        if current_affiliate.id not in visited_affiliates:
            recipient_affiliates.add(current_affiliate.id)
            visited_affiliates.add(current_affiliate.id)

    for aff_id in recipient_affiliates:
        room_name = f"live_affiliate_{aff_id}"
        socketio.emit('affiliate_live', {'info': data, 'type': type, 'region': region}, room=room_name)
    return {"status": f"Live update sent to channel {room_name} subscribers"}




