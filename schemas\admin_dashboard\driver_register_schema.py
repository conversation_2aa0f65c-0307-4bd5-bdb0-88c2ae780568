from pydantic import BaseModel, field_validator,ValidationError,root_validator
from typing import Optional
from datetime import datetime

class RegisterDriverListPayload(BaseModel):
    timestamp_gt: Optional[str]
    timestamp_lt: Optional[str]
    regions: Optional[str]
    @field_validator('timestamp_gt', 'timestamp_lt')
    @classmethod     
    def validate_datetime_format(cls, v):
        if v is None:
            return v
        try:
            datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise ValueError("Invalid datetime format, expected '%Y-%m-%d %H:%M:%S'")
        return v