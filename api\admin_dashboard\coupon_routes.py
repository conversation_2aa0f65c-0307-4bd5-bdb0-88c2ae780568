#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON>

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from flasgger import swag_from
from pydantic import ValidationError

from utils.auth.admin_login_utils import check_access
from utils.admin_utils import Tabs
from utils.response_utils import standard_response
from utils.validation_utils import complete
from services.admin_dashboard.coupon_service import (
        new_coupon_add, fetch_coupons, change_coupon_state,
        fetch_coupon_details, update_coupon
    )
from schemas.admin_dashboard.coupon_schemas import (
        NewCouponPayload, CouponStateChangePayload, CouponUpdatePayload, CouponListPayload
    )

admin_coupon = Blueprint('coupon_route', __name__)

@admin_coupon.route('/api/admin/add_coupon', methods=['POST'])
@swag_from('/app/swagger_docs/coupon_admin/add_coupon.yml')
# @jwt_required()
# @check_access(tab_required=[Tabs.COUPONS])
def add_new_coupon():
    # if not complete(request.form, ['coupon_type', 'coupon_code','start_from', 'valid_till', 'min_trip','max_trip', 'min_price','trip_type_allowed']):
    #     return jsonify(standard_response(-2, 404, 'Incomplete coupon details')), 404
    
    try:
        form_data = request.form.to_dict()
        payload = NewCouponPayload(**form_data)
        response = new_coupon_add(payload)
        return jsonify(response), response['status_code']
    except ValidationError as ve:   
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(success=-1, status=422, message="Validation Error", data={'error': error_details}, response_status="error")), 422
    except Exception as e:
        return jsonify(standard_response(success=-99, status=500, message="Internal Server Error", response_status="error", data={'error': str(e)})), 500


@admin_coupon.route('/api/admin/coupon_list', methods=['POST'])
@swag_from('/app/swagger_docs/coupon_admin/get_active_coupons.yml')
# @jwt_required()
# @check_access(tab_required=[Tabs.COUPONS])
def coupon_list():
    form_data = request.form.to_dict()
    tz = request.headers.get('X-Timezone', 'Asia/Kolkata')
    payload = CouponListPayload(**form_data)
    response = fetch_coupons(payload,tz)
    return jsonify(response), response['status_code']



@admin_coupon.route('/api/admin/change_coupon_state', methods=['POST'])
@swag_from('/app/swagger_docs/coupon_admin/change_coupon_state.yml')
# @jwt_required()
# @check_access(tab_required=[Tabs.COUPONS])
def coupon_state_change():
    if not complete(request.form, ['coupon_id']):
        return jsonify(standard_response(-2, 404, 'Incomplete coupon id')), 404

    try:
        form_data = request.form.to_dict()
        payload = CouponStateChangePayload(**form_data)
        response = change_coupon_state(payload.coupon_id)
        print(response,flush=True)
        print("working 4",flush=True)
        return jsonify(response), response['status_code']
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(success=-1, status=422, message="Validation Error", data={'error': error_details}, response_status="error")), 422



@admin_coupon.route('/api/admin/update_coupon', methods=['POST'])
@swag_from('/app/swagger_docs/coupon_admin/update_coupon.yml')
# @jwt_required()
# @check_access(tab_required=[Tabs.COUPONS])
def coupon_update():
    if not complete(request.form, ['coupon_id']):
        return jsonify(standard_response(success=-2, status=404, message='Incomplete coupon id', response_status="error")), 404
    try:
        form_data = request.form.to_dict()
        payload = CouponUpdatePayload(**form_data)
        response = update_coupon(payload)
        return jsonify(response), response['status_code']
    except ValidationError as ve:
        error_details = [
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}"
            for err in ve.errors()
        ]
        return jsonify(standard_response(success=-1, status=422, message="Validation Error", data={'error': error_details}, response_status="error")), 422



