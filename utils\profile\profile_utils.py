#Standard Libraries
from numpy import base_repr
from flask import current_app as app

# === Application (Internal) Imports ===
from db_config import db
from models.models import Users, Drivers
from utils.bookings.booking_params import Regions

SITE_URL = 'https://driver4me-data.s3.amazonaws.com/'

OFFICE_ADDRESSES_AND_PHONE_NUMBERS = {
    "REGIONS": {
        Regions.REGN_KOLKATA: {
            "address": "123 Park Street, Kolkata, WB",
            "phone": "+************"
        },
        Regions.REGN_HYDERABAD: {
            "address": "45 Banjara Hills, Hyderabad, TS",
            "phone": "+************"
        },
        Regions.REGN_GUWAHATI: {
            "address": "67 GS Road, Guwahati, AS",
            "phone": "+************"
        },
        Regions.REGN_NAGPUR: {
            "address": "89 Civil Lines, Nagpur, MH",
            "phone": "+************"
        },
        Regions.REGN_PUNE: {
            "address": "56 Koregaon Park, Pune, MH",
            "phone": "+************"
        },
        Regions.REGN_MUMBAI: {
            "address": "101 Marine Drive, Mumbai, MH",
            "phone": "+************"
        },
        Regions.REGN_DELHI: {
            "address": "32 Connaught Place, Delhi",
            "phone": "+************"
        },
        Regions.REGN_CHENNAI: {
            "address": "78 Mount Road, Chennai, TN",
            "phone": "+************"
        },
        Regions.REGN_BANGALORE: {
            "address": "123 MG Road, Bangalore, KA",
            "phone": "+************"
        },
        Regions.REGN_AHMEDABAD: {
            "address": "45 CG Road, Ahmedabad, GJ",
            "phone": "+************"
        },
        Regions.REGN_SILIGURI: {
            "address": "78 Hill Cart Road, Siliguri, WB",
            "phone": "+************"
        },
        Regions.REGN_BHUBANESWAR: {
            "address": "56 Janpath, Bhubaneswar, OD",
            "phone": "+************"
        },
        Regions.REGN_PATNA: {
            "address": "34 Gandhi Maidan, Patna, BR",
            "phone": "+************"
        },
        Regions.REGN_RANCHI: {
            "address": "12 Kanke Road, Ranchi, JH",
            "phone": "+************"
        },
        Regions.REGN_GURGAON: {
            "address": "67 DLF Phase 2, Gurgaon, HR",
            "phone": "+************"
        },
        Regions.REGN_NOIDA: {
            "address": "89 Sector 18, Noida, UP",
            "phone": "+************"
        },
        Regions.REGN_JAIPUR: {
            "address": "45 MI Road, Jaipur, RJ",
            "phone": "+************"
        },
        Regions.REGN_LUCKNOW: {
            "address": "67 Hazratganj, Lucknow, UP",
            "phone": "+************"
        },
        Regions.REGN_CHANDIGARH: {
            "address": "101 Sector 17, Chandigarh, CH",
            "phone": "+************"
        },
        Regions.REGN_ALAMPUR: {
            "address": "123 Old Market, Alampur, TS",
            "phone": "+************"
        }
    }
}

def account_enabled(user):
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry:
        return user_entry.enabled
    else:
        return False

def validate_role(user, role, valid_roles=[]):
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry and user_entry.role == role:
        if role in valid_roles or len(valid_roles) == 0:
            return True
        else:
            return False
    else:
        return False

def get_driver_user_id(driver_id):
    if driver_id == 1:
        return -1
    driver_e = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    if not driver_e:
        return ""
    else:
        return driver_e.user


def get_driver_name_from_id(driver_id):
    if driver_id == 1:
        return "N/A"
    driver_e = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                    filter(Users.id == Drivers.user).first()
    if not driver_e:
        return ""
    else:
        return driver_e[1].get_name()


def get_ref_code(user_id, mobile):
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    return base_repr(user_id + int(int(mobile)%100 * 10e6), 36)

def get_user_name_from_id(user_id):
    u = db.session.query(Users).filter(Users.id == user_id).first()
    if not u:
        return ""
    else:
        return u.get_name()

def set_user_ref_code(user_id, user_mobile):
    ref_code = get_ref_code(user_id, user_mobile)
    db.session.query(Users).filter(Users.id == user_id).update({Users.ref_code: ref_code})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        ref_code = ""
    return ref_code


def get_user_ref_code(user_details):
    if not user_details.ref_code:
        ref_code = set_user_ref_code(user_details.id, user_details.mobile)
    else:
        ref_code = user_details.ref_code
    return ref_code

def get_pic_url(filename):
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if  not use_s3:
        return SITE_URL + app.config['UPLOAD_FOLDER'] + filename
    else:
        return "https://storage.drivers4me.com/" + app.config['UPLOAD_FOLDER'] + filename    

def get_region_info(region_name):
    regions = OFFICE_ADDRESSES_AND_PHONE_NUMBERS.get("REGIONS", {})
    region_info = regions.get(region_name)
    
    if not region_info:
        return "Not serviceable in this area"
    
    address = region_info.get("address", "Address not available")
    phone = region_info.get("phone", "Phone number not available")
    return address,phone
