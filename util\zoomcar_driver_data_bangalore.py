import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str_2 = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathSP = D4M_UTIL_PATH + 'output/hyderabad-spinny-driver.csv'
subjectSP = "Spinny Driver Payment - Bangalore - " + date_str_2
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
send_mail(from_addr, to_addr_list, subjectSP, content, filepathSP)