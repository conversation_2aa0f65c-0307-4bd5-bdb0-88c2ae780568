#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  redis_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import json

from redis_config import execute_with_fallback, is_redis_available


def read_redis_data(key):
    if is_redis_available():
        data = execute_with_fallback('get', key)
        if data is not None:
            return json.loads(data), True
    return {}, False

def write_redis_data(key, data):
    execute_with_fallback('set', key, json.dumps(data)) 
    
def update_redis_data(key, data):
    write_redis_data(key, data)