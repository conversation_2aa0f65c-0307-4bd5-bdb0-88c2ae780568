#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  booking_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import math
from numpy import base_repr
from datetime import datetime, timedelta


EARTH_RADIUS = 6373  # Earth radius in kilometers

def distance_on_earth(lat1, long1, lat2, long2):
    # Convert latitude and longitude to spherical coordinates in radians.
    degrees_to_radians = math.pi / 180.0

    # phi = 90 - latitude
    phi1 = (90.0 - lat1) * degrees_to_radians
    phi2 = (90.0 - lat2) * degrees_to_radians

    # theta = longitude
    theta1 = long1 * degrees_to_radians
    theta2 = long2 * degrees_to_radians

    # Compute spherical distance from spherical coordinates.
    # For two locations in spherical coordinates (1, theta, phi) and (1, theta', phi')
    # cosine(arc length) = sin(phi) * sin(phi') * cos(theta - theta') + cos(phi) * cos(phi')
    cos_value = (math.sin(phi1) * math.sin(phi2) * math.cos(theta1 - theta2) +
                 math.cos(phi1) * math.cos(phi2))

    # Clamp the cos_value to the valid range of [-1, 1] to avoid math domain errors
    cos_value = max(-1, min(1, cos_value))

    # Calculate the arc length
    arc = math.acos(cos_value)

    # Multiply the arc by the radius of the Earth to get the distance
    return arc * EARTH_RADIUS

def get_book_code(booking_id):
    from models.models import Bookings,db
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    code = base_repr(booking_id + int(int(booking_id)%100 * 10e6), 36)
    code = str(code).rjust(6, '0')
    code_saved = db.session.query(Bookings).filter(Bookings.id == booking_id)
    if not code_saved.first() or not code_saved.first().code:
        try:
            code_saved.update({Bookings.code: code})
            db.session.flush()
        except Exception as excp:
            print(excp)
            db.session.rollback()
    else:
        if code_saved.first().code != code:
            try:
                code_saved.update({Bookings.code: code})
                db.session.flush()
            except Exception as excp:
                print(excp)
                db.session.rollback()
    return code

def get_b2b_book_code(booking_id):
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    code = base_repr(booking_id + int(int(booking_id)%100 * 10e6), 36)
    code = str(code).rjust(6, '0')

    return code

def is_vip_or_faced_issue(label_value: int) -> bool:
    def check_bit(value, position):
        return (value >> position) & 1

    is_vip = check_bit(label_value, 0) == 1
    faced_issue = check_bit(label_value, 2) == 1

    return is_vip or faced_issue

def check_ongoing_time(booking):
    from utils.bookings.booking_params import BookingParams
    booking_start_datetime = datetime.combine(booking.startdate, booking.starttime)
    booking_end_datetime = datetime.combine(booking.enddate, booking.endtime)

    booking_start_minus_n_minutes = booking_start_datetime - timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)
    booking_end_plus_n_minutes = booking_end_datetime + timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)

    return booking_start_minus_n_minutes, booking_end_plus_n_minutes