#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  app.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from gevent import monkey
monkey.patch_all()

import grpc.experimental.gevent as grpc_gevent
grpc_gevent.init_gevent()

from __init__ import create_app
from config import ProdConfig
import sys, traceback, time
from flask import render_template, request, url_for, g, jsonify
from flasgger import Swagger
# from _ops_message import SERVER_DEV, send_slack_msg_new
from socketio_config import socketio
from services.socket.socketio_app import live_update_to_channel
from services.socket.live_update_booking import send_live_update_of_booking
from services.socket.live_update_aff_book import send_live_aff_booking_table
from gevent import spawn, sleep
# from socketio_b2b import notify
from models.affiliate_models import init_aff_loc

app, socketio = create_app(ProdConfig)
init_aff_loc(app)

swagger_config = {
    "swagger_ui": not app.config['MAIN_SERVER'],
    "specs": [
        {
            "endpoint": "apispec_1",
            "route": "/apispec_1.json",
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ] if not app.config['MAIN_SERVER'] else [],
    "headers": [],
    "static_url_path": "/flasgger_static",
    "specs_route": "/docs" if not app.config['MAIN_SERVER'] else None,
}

swagger_template = {
    'securityDefinitions': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header',
            'description': 'JWT token with Bearer scheme'
        },
        'X-CSRF-Token': {
            'type': 'apiKey',
            'name': 'x-csrf-token',
            'in': 'header',
            'description': 'CSRF token for secure requests'
        }
    },
    'security': [
        {
            'Bearer': [],
            'X-CSRF-Token': []
        }
    ],
    'info': {
        'version': '1.0',
        'title': 'Drivers4me API Documentation 📌',
        'description': 'Detailed Documentation and Testing of APIs'
    }
}

swagger = Swagger(app, config=swagger_config, template=swagger_template)
'''
@app.errorhandler(500)
def handle_500(e):
    page_name =  request.url
    s1 = traceback.format_exc().splitlines()
    send_slack_msg_new(SERVER_DEV, "Received 500 in: " + str(page_name) + \
                       ". Caught exception: " + str(e) + \
                       ". For more details, open " + \
                       "https://www.pythonanywhere.com/user/drivers4me/files/" + \
                       "var/log/www.drivers4me.com.error.log")
    #for l in s1:
    #    send_slack_msg_new(SERVER_DEV, l)
    return render_template('404.html'), 500


@app.errorhandler(404)
def handle_404(e):
    return render_template('404.html'), 404

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify(error="Rate limit exceeded"), 429

@app.before_request
def before_request():
    request_url = request.url.replace("https://www.drivers4me.com", "")
    if "static/" in request_url:
        print("static call")
        return
    try:
        print("call", request_url, ":", request.form, ":", dict(request.headers))
    except Exception:
        pass
    return

@app.after_request
def after_request(response):
    request_url = request.url.replace("https://www.drivers4me.com", "")
    if "static/" in request_url:
        return response
    try:
        print("send", request_url, ":", str(response.data.decode("utf-8").replace("\n", "")[:200]).strip(), ":", dict(request.headers))
    except Exception:
        pass
    return response
'''
"""
@app.after_request
def after_request(response):
    request_url = request.url.replace("https://www.drivers4me.com", "")
    if "static/" in request_url:
        return response
    today = datetime.today().strftime('%Y-%m-%d.%H')
    now = datetime.today().strftime('%H:%M:%S')
    delta = time.time() - g.start
    user_id = get_jwt_identity()
    if not user_id:
        user_id = -1
    with open("/home/<USER>/log/time.log." + today, "a+") as of:
        of.write("%s,%d,%s,%.2f\n" % (now, user_id,
                                      request_url, delta))
    return response


profiler = Profiler()  # You can have this in another module
profiler.init_app(app)
"""
# if __name__ == '__main__':
#     app.run(host=Server.SERVER_URL, port=Server.SERVER_PORT, threaded=True, debug=False)

if __name__ == '__main__':
    try:
        socketio.run(app, host=Server.SERVER_URL, port=Server.SERVER_PORT, debug=False, log_output= True)
    except KeyboardInterrupt:
        print("Server stopped with keyboard c c.")
    except Exception as e:
        print(f"Error is: {e}")
