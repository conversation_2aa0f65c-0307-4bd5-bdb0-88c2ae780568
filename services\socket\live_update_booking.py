
from datetime import datetime,timedelta
from db_config import mdb
import json,math
import time
from flask import request, jsonify
from flask_jwt_extended import jwt_required
from sqlalchemy.sql import func, or_,desc,asc,and_,exists,case
from sqlalchemy import exc
from models.models import Users, db
from models.models import  Drivers, DriverDetails,BookPricing
from models.models import Trip, Bookings, BookDest, TripPricing, BookingAlloc, BookPending, AdminLog, TripStartPic, TripEndPic, BookingCancelled, TripLog,UserCancelled,UserTrans
from models.affiliate_models import Affiliate,AffBookingLogs,AffiliateCollections
from utils.time_utils import combine_and_convert_to_local, split_date_time,convert_to_local_time
from utils.bookings.booking_params import  BookingParams
import pytz
from services.socket.socketio_app import live_update_to_channel
from gevent import spawn
from sqlalchemy.orm import aliased,joinedload
from pymongo.errors import PyMongoError
from flask import current_app as app



def touch_booking(booking_id: int, region: str):
    """
    – If the doc exists: set timestamp=now and seq_no++ in one go  
    – If it doesn’t exist: insert it with seq_no starting at 1
    """
    try:
        result = AffiliateCollections.booking_live_events.update_one(
            { "booking_id": booking_id },
            [
                {
                    "$set": {
                        "booking_region": { "$ifNull": ["$booking_region", region] },
                        "seq_no":         { "$add": [ { "$ifNull": ["$seq_no", 0] }, 1 ] },
                        "timestamp":      "$$NOW"
                    }
                }
            ],
            upsert=True
        )

        return result

    except PyMongoError as e:
        # log a detailed error message
        print(f"MongoDB update_one failed for booking_id={booking_id}, region={region}: {e!r}",flush=True)
        # optionally, wrap or re-raise
        raise

    except Exception as e:
        # catch anything unexpected
        print(f"Unexpected error in touch_booking(booking_id={booking_id}, region={region})",flush=True)
        # re-raise to bubble up
        raise

# def determine_booking_type(booking_type):
#     if booking_type is not None:
#         booking_type = booking_type[0]  # Extract the single value from the tuple
#         return 1 if booking_type < BookingParams.TYPE_C24 else 0 if booking_type == BookingParams.TYPE_B2B else -1
#     else:
#         raise ValueError('Booking type not found')

#this is for testing purpose
"""
@app.route('/api/testing/booking_list/single_row', methods=['POST'])
@jwt_required()
def live_update_single_row_testing_function():
    try:
        bookid = request.form.get('bookid', type=int)
        booking_region = request.form.get('booking_region', 0, type=int)
        send_live_update_of_booking(bookid, booking_region)
        return jsonify({'success': 1, 'data': 'api call completed'}), 200
    except  Exception as e:
        return jsonify({'success': -1, 'error': f'failed {str(e)}'}), 400
"""

def send_live_update_of_booking(bookid, booking_region, channel= 'bookingupdate',tz='Asia/Kolkata'):  
    try:
        spawn(parallel_booking_update_send, bookid, booking_region,tz, channel,app._get_current_object())
    except  Exception as e:
        print('Exception send_live_update_of_booking...', flush=True)
        live_update_to_channel({'id' : bookid}, room_name='bookings', type='bookings', region=booking_region, channel= 'fallback_booking_update')
    return jsonify({'success': 1, 'data': 'api call completed'}), 200

def parallel_booking_update_send(bookid, booking_region,tz, channel= 'bookingupdate',app=None):
    try:
        with app.app_context():
            booking_data = get_booking_list_single_row(bookid,tz)
        touch_booking(bookid, booking_region)
        live_update_to_channel(booking_data, room_name='bookings', type='bookings', region=booking_region, channel= channel)
    except Exception as e:
        print(e,flush=True)
        live_update_to_channel({'id' : bookid}, room_name='bookings', type='bookings', region=booking_region, channel= 'fallback_booking_update')

def get_booking_list_single_row(bookid,tz):
    try:
        driver_user = aliased(Users, name="driver_user")
        booking = db.session.query(Bookings).filter(Bookings.id == bookid).first()
        try:
            is_b2c = BookingParams.determine_booking_type(booking.type)
        except ValueError as e:
            return jsonify({'error': str(e)}), 404

        columns = [
            Bookings.id.label("book_id"),
            Bookings.code.label("book_code"),
            Bookings.created_at.label("book_timestamp"),
            Bookings.lat.label("book_lat"),
            Bookings.long.label("book_long"),
            Bookings.dur.label("book_dur"),
            Bookings.user_rating.label("book_user_rating"),
            Bookings.estimate.label("book_estimate_price"),
            Bookings.estimate_pre_tax.label("book_estimate_pre_tax"),
            Bookings.loc.label("book_loc_name"),
            Bookings.type.label("book_trip_type"),
            Bookings.car_type.label("book_car_type"),
            Bookings.days.label("book_dur_days"),
            Bookings.comment.label("book_comment"),
            Bookings.payment_type.label("book_payment_type"),
            Bookings.insurance_num.label("book_insurance_num"),
            Bookings.insurance_cost.label("book_insurance_cost"),
            Bookings.valid.label("book_valid"),
            Trip.status.label("trip_status"),
            Trip.starttime.label("trip_starttime"),
            Trip.due.label("trip_due"),
            Trip.endtime.label("trip_endtime"),
            Trip.price.label("trip_price"),
            Trip.timestamp.label("alttimestamp"),
            Bookings.user.label("user_id"),
            Bookings.driver.label("driver_id"),
            Bookings.startdate.label("startdate"),
            Bookings.starttime.label("starttime"),
            Bookings.endtime.label("book_endtime"),
            Bookings.enddate.label("book_enddate"),
            Bookings.region.label("book_region"),
            BookDest.name.label("book_dest_name"),
            BookDest.lat.label("book_dest_lat"),
            BookDest.lng.label("book_dest_long"),
            BookPricing.booking_ch.label('booking_ch'),
            BookPricing.driver_base_ch.label('driver_base_ch'),
            BookPricing.driver_night_ch.label('driver_night_ch'),
            BookPricing.estimate.label('estimate'),
            driver_user.mobile.label('driver_mobile'),
            driver_user.label_bv.label('driver_label'),
            driver_user.label_bv.label('driver_user'),
            DriverDetails.ride_count.label('ride_count'),
            DriverDetails.b2b_ride_count.label('b2b_ride_count'),
            func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
        ]

        if is_b2c == 0:
            columns += [
                TripPricing.driver_base_ch.label('t_driver_base_ch'),
                TripPricing.driver_night_ch.label('t_driver_night_ch'),
            ]

        query = db.session.query(
            *columns
        ).filter(Bookings.id == bookid).filter(Bookings.type < BookingParams.TYPE_C24 if is_b2c else Bookings.type==BookingParams.TYPE_B2B)


        query = query.outerjoin(Trip, Bookings.id == Trip.book_id) \
            .outerjoin(BookDest, Bookings.id == BookDest.book_id) \
            .join(Drivers, Bookings.driver == Drivers.id) \
            .join(driver_user, Drivers.user == driver_user.id) \
            .join(DriverDetails, Drivers.id == DriverDetails.driver_id) \
            .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)

        if is_b2c:
            query = query.join(Users, Bookings.user == Users.id) \
                .add_columns(
                    Users.mobile.label('customer_mobile'),
                    Users.bookcount.label('bookcount'),
                    Users.label_bv.label('customer_label'),
                    func.concat(Users.fname, ' ', Users.lname).label('customer_name'),
                )
        else:
            query = query.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).outerjoin(TripPricing, Bookings.id == TripPricing.book_id)

        results = query.all()
        response = []

        veh_no = "N/A"
        veh_model = "N/A"
        affiliate_name = ""
        trip_name = "N/A"
        trip_type="N/A"


        for result in results:
            var_price = result.trip_price if result.trip_price else result.estimate
            var_due = result.booking_ch
            highlight = 0
            dateb, timeb = split_date_time(combine_and_convert_to_local(result.startdate, result.starttime,tz))

            if not is_b2c:
                affiliate_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': result.book_id})
                if affiliate_mongo:
                    custom_data = affiliate_mongo.get('custom_data', {})
                    veh_no = affiliate_mongo.get('vehicle_no', None)  # Will return None if 'veh_no' is not in custom_data
                    veh_model = affiliate_mongo.get('vehicle_model', None)  # Will return None if 'veh_model' is not in custom_data
                    trip_name = affiliate_mongo.get('trip_name',None)
                    trip_type = BookingParams.get_b2b_trip_type(affiliate_mongo.get('trip_type', None))
                    affiliate_name_row = db.session.query(Affiliate.client_name).filter(Affiliate.id == affiliate_mongo['affiliate_id']).first()
                    affiliate_name = affiliate_name_row.client_name if affiliate_name_row else None
                    priority = affiliate_mongo.get("priority", 0)
                    pending_state = affiliate_mongo.get("pending_state", BookPending.BROADCAST)
                else:
                    print("Vehicle Number not found.")

            response.append({
                'booking_id': result.book_id,
                'driver_id': result.driver_id,
                'user_id': result.user_id,
                "booking_code":result.book_code,
                'booking_valid': result.book_valid,
                'booking_timestamp': convert_to_local_time(result.book_timestamp,tz).strftime('%Y-%m-%d %H:%M:%S'),
                'booking_startdate': dateb.strftime('%Y-%m-%d'),
                'booking_starttime': timeb.strftime('%H:%M:%S'),
                'booking_dur': result.book_dur.strftime('%H:%M:%S'),
                'booking_days': result.book_dur_days,
                'booking_trip_type': result.book_trip_type,
                'booking_region': result.book_region,
                "booking_insurance":1 if result.book_insurance_num > 0 else 0,
                'booking_start_name': result.book_loc_name,
                'booking_start_lat': result.book_lat,
                'booking_start_long': result.book_long,
                'booking_payment_type': result.book_payment_type,
                'booking_estimate': result.book_estimate_price,
                'booking_remark': result.book_comment,
                'booking_dest_name': result.book_dest_name,
                'booking_dest_lat': result.book_dest_lat,
                'booking_dest_long': result.book_dest_long,
                'trip_status': result.trip_status,
                'trip_starttime':convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_starttime else '',
                'trip_endtime':convert_to_local_time(result.trip_endtime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_endtime else '',
                'alttimestamp':convert_to_local_time(result.alttimestamp,tz).strftime('%Y-%m-%d %H:%M:%S') if result.alttimestamp else '',
                'driver_user': result.driver_user,
                'driver_mobile': result.driver_mobile,
                'driver_name': result.driver_name,
                'driver_label': result.driver_label,
                'booking_due': f"{round(float(var_due), 2):.2f}" if var_due else "",
                # 'booking_price': f"{round(float(var_price), 2):.2f}" if var_price else "",
                'booking_price': ((result.t_driver_night_ch + result.t_driver_base_ch) if result.trip_status and int(result.trip_status) == 0 else (result.driver_night_ch + result.driver_base_ch) ) if not is_b2c else (f"{round(float(var_price), 2):.2f}" if var_price else ""),
                'booking_car_type': result.book_car_type,
                'user_booking_count': 0 if (is_b2c == 0) else result.bookcount,
                'driver_booking_count': result.ride_count if result.driver_id else None,
                'driver_b2b_booking_count': result.b2b_ride_count if result.driver_id else None,
                'driver_b2C_booking_count': (result.ride_count - result.b2b_ride_count) if result.driver_id else None,
                'highlight': highlight,
            })
            if not is_b2c :
                response[-1]['vehicle_no'] = veh_no  # Access the last dictionary in the list
                response[-1]['vehicle_model'] = veh_model
                response[-1]['affiliate_name'] = affiliate_name
                response[-1]['trip_name'] = trip_name
                response[-1]['trip_type'] = trip_type
                # 0 = ALLOCATED, 1 = BROADCAST, 2 = SUPPRESSED
                response[-1]['book_pending_state'] = pending_state
                response[-1]['priority'] = priority
            else:
                response[-1]['customer_mobile'] = result.customer_mobile
                response[-1]['customer_name'] = result.customer_name
                response[-1]['customer_label'] = result.customer_label
            print('response[0]', response[0], flush=True)
            return response[0] if response and response[0] else {}
    except ValueError as e:
        print("'error",e,flush=True)
        return {}
    except Exception as e:
        print("'error",e,flush=True)
        return {}

def get_booking_list_multiple_rows(bookids,is_b2c,tz='Asia/Kolkata'):
    with app.app_context():
        try:
            driver_user = aliased(Users, name="driver_user")
            #booking = db.session.query(Bookings).filter(Bookings.id == bookid).first()

            
            columns = [
                Bookings.id.label("book_id"),
                Bookings.code.label("book_code"),
                Bookings.created_at.label("book_timestamp"),
                Bookings.lat.label("book_lat"),
                Bookings.long.label("book_long"),
                Bookings.dur.label("book_dur"),
                Bookings.user_rating.label("book_user_rating"),
                Bookings.estimate.label("book_estimate_price"),
                Bookings.estimate_pre_tax.label("book_estimate_pre_tax"),
                Bookings.loc.label("book_loc_name"),
                Bookings.type.label("book_trip_type"),
                Bookings.car_type.label("book_car_type"),
                Bookings.days.label("book_dur_days"),
                Bookings.comment.label("book_comment"),
                Bookings.payment_type.label("book_payment_type"),
                Bookings.insurance_num.label("book_insurance_num"),
                Bookings.insurance_cost.label("book_insurance_cost"),
                Bookings.valid.label("book_valid"),
                Trip.status.label("trip_status"),
                Trip.starttime.label("trip_starttime"),
                Trip.due.label("trip_due"),
                Trip.endtime.label("trip_endtime"),
                Trip.price.label("trip_price"),
                Trip.timestamp.label("alttimestamp"),
                Bookings.user.label("user_id"),
                Bookings.driver.label("driver_id"),
                Bookings.startdate.label("startdate"),
                Bookings.starttime.label("starttime"),
                Bookings.endtime.label("book_endtime"),
                Bookings.enddate.label("book_enddate"),
                Bookings.region.label("book_region"),
                BookDest.name.label("book_dest_name"),
                BookDest.lat.label("book_dest_lat"),
                BookDest.lng.label("book_dest_long"),
                BookPricing.booking_ch.label('booking_ch'),
                BookPricing.driver_base_ch.label('driver_base_ch'),
                BookPricing.driver_night_ch.label('driver_night_ch'),
                BookPricing.estimate.label('estimate'),
                driver_user.mobile.label('driver_mobile'),
                driver_user.label_bv.label('driver_label'),
                driver_user.label_bv.label('driver_user'),
                DriverDetails.ride_count.label('ride_count'),
                DriverDetails.b2b_ride_count.label('b2b_ride_count'),
                func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
            ]

            if is_b2c == 0:
                columns += [
                    TripPricing.driver_base_ch.label('t_driver_base_ch'),
                    TripPricing.driver_night_ch.label('t_driver_night_ch'),
                ]
                
            query = db.session.query(
                *columns
            ).filter(Bookings.id.in_(bookids)).filter(Bookings.type < BookingParams.TYPE_C24 if is_b2c else Bookings.type==BookingParams.TYPE_B2B)
            

            query = query.outerjoin(Trip, Bookings.id == Trip.book_id) \
                .outerjoin(BookDest, Bookings.id == BookDest.book_id) \
                .join(Drivers, Bookings.driver == Drivers.id) \
                .join(driver_user, Drivers.user == driver_user.id) \
                .join(DriverDetails, Drivers.id == DriverDetails.driver_id) \
                .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)

            if is_b2c:
                query = query.join(Users, Bookings.user == Users.id) \
                    .add_columns(
                        Users.mobile.label('customer_mobile'),
                        Users.bookcount.label('bookcount'),
                        Users.label_bv.label('customer_label'),
                        func.concat(Users.fname, ' ', Users.lname).label('customer_name'),
                    )
            else:
                query = query.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).outerjoin(TripPricing, Bookings.id == TripPricing.book_id)

            results = query.all()
            response = []
            
            veh_no = "N/A"
            veh_model = "N/A" 
            affiliate_name = ""
            trip_name = "N/A"
            trip_type="N/A"

            print('results', results, flush=True)
            for result in results:
                var_price = result.trip_price if result.trip_price else result.estimate
                var_due = result.booking_ch
                highlight = 0
                dateb, timeb = split_date_time(combine_and_convert_to_local(result.startdate, result.starttime,tz))
                
                if not is_b2c:
                    affiliate_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': result.book_id})
                    if affiliate_mongo:
                        custom_data = affiliate_mongo.get('custom_data', {})
                        veh_no = affiliate_mongo.get('vehicle_no', None)  # Will return None if 'veh_no' is not in custom_data
                        veh_model = affiliate_mongo.get('vehicle_model', None)  # Will return None if 'veh_model' is not in custom_data
                        trip_name = affiliate_mongo.get('trip_name',None)
                        trip_type = BookingParams.get_b2b_trip_type(affiliate_mongo.get('trip_type', None))
                        affiliate_name_row = db.session.query(Affiliate.client_name).filter(Affiliate.id == affiliate_mongo['affiliate_id']).first()
                        affiliate_name = affiliate_name_row.client_name if affiliate_name_row else None 
                        priority = affiliate_mongo.get("priority", 0)
                        pending_state = affiliate_mongo.get("pending_state", BookPending.BROADCAST)
                    else:
                        print("Vehicle Number not found.")
                        
                response.append({
                    'booking_id': result.book_id,
                    'driver_id': result.driver_id,
                    'user_id': result.user_id,
                    "booking_code":result.book_code,
                    'booking_valid': result.book_valid,
                    'booking_timestamp': convert_to_local_time(result.book_timestamp,tz).strftime('%Y-%m-%d %H:%M:%S'),
                    'booking_startdate': dateb.strftime('%Y-%m-%d'),
                    'booking_starttime': timeb.strftime('%H:%M:%S'),
                    'booking_dur': result.book_dur.strftime('%H:%M:%S'),
                    'booking_days': result.book_dur_days,
                    'booking_trip_type': result.book_trip_type,
                    'booking_region': result.book_region,
                    "booking_insurance":1 if result.book_insurance_num > 0 else 0,
                    'booking_start_name': result.book_loc_name,
                    'booking_start_lat': result.book_lat,
                    'booking_start_long': result.book_long,
                    'booking_payment_type': result.book_payment_type,
                    'booking_estimate': result.book_estimate_price,
                    'booking_remark': result.book_comment,
                    'booking_dest_name': result.book_dest_name,
                    'booking_dest_lat': result.book_dest_lat,
                    'booking_dest_long': result.book_dest_long,
                    'trip_status': result.trip_status,
                    'trip_starttime':convert_to_local_time(result.trip_starttime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_starttime else '',
                    'trip_endtime':convert_to_local_time(result.trip_endtime,tz).strftime('%Y-%m-%d %H:%M:%S') if result.trip_endtime else '',
                    'alttimestamp':convert_to_local_time(result.alttimestamp,tz).strftime('%Y-%m-%d %H:%M:%S') if result.alttimestamp else '',
                    'driver_user': result.driver_user,
                    'driver_mobile': result.driver_mobile,
                    'driver_name': result.driver_name,
                    'driver_label': result.driver_label,
                    'booking_due': f"{round(float(var_due), 2):.2f}" if var_due else "", 
                    # 'booking_price': f"{round(float(var_price), 2):.2f}" if var_price else "",
                    'booking_price': ((result.t_driver_night_ch + result.t_driver_base_ch) if result.trip_status and int(result.trip_status) == 0 else (result.driver_night_ch + result.driver_base_ch) ) if not is_b2c else (f"{round(float(var_price), 2):.2f}" if var_price else ""),
                    'booking_car_type': result.book_car_type,
                    'user_booking_count': 0 if (is_b2c == 0) else result.bookcount,
                    'driver_booking_count': result.ride_count if result.driver_id else None,
                    'driver_b2b_booking_count': result.b2b_ride_count if result.driver_id else None,
                    'driver_b2C_booking_count': (result.ride_count - result.b2b_ride_count) if result.driver_id else None,
                    'highlight': highlight,
                })
                if not is_b2c :
                    response[-1]['vehicle_no'] = veh_no  # Access the last dictionary in the list
                    response[-1]['vehicle_model'] = veh_model
                    response[-1]['affiliate_name'] = affiliate_name
                    response[-1]['trip_name'] = trip_name
                    response[-1]['trip_type'] = trip_type
                    # 0 = ALLOCATED, 1 = BROADCAST, 2 = SUPPRESSED
                    response[-1]['book_pending_state'] = pending_state
                    response[-1]['priority'] = priority
                else:
                    response[-1]['customer_mobile'] = result.customer_mobile
                    response[-1]['customer_name'] = result.customer_name
                    response[-1]['customer_label'] = result.customer_label
            print('response[0]', response[0], flush=True)
            return response if response and response[0] else {}
        except ValueError as e:
            print("'error",e,flush=True)
            return {}
        except Exception as e:
            print("'error",e,flush=True)
            return {}