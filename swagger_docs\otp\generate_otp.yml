tags:
  - Authentication
summary: Generate OTP for user login or registration
description: |
  Sends an OTP to the user's mobile number for login or registration purposes. 
  Enforces rate limiting to prevent abuse.
consumes:
  - multipart/form-data
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: countrycode
    in: formData
    type: string
    required: true
    description: Country code of the mobile number (e.g., "+91")
responses:
  201:
    description: OTP sent successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "OTP has been successfully sent."
  400:
    description: Validation error in request data
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid mobile number format"
  423:
    description: Too many OTP requests from this user
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Too many OTP requests. Please try again later in 10 mins."
  500:
    description: Internal server error or OTP provider failure
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Unable to process O<PERSON> request. Please try again later."
