#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _utils.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON>hatos Mitra


#Standard Libraries
import hashlib
import hmac
import uuid
import time
import re
import os
from datetime import datetime,timedelta
from random import randint
from flask import current_app as app
import pytz

# === Application (Internal) Imports ===
from redis_config import execute_with_fallback,get_redis_client

# === Constants ===
SITE_URL = 'https://driver4me-data.s3.amazonaws.com/'
USER_SOFT_BANNED_LIST = [68]  # A Ghosh
USER_BANNED_LIST = []  # 534]

MIN_LATITUDE_WORLD = -90
MAX_LATITUDE_WORLD = 90
MIN_LONGITUDE_WORLD = -180
MAX_LONGITUDE_WORLD = 180

MIN_LATITUDE_INDIA = 6.55
MAX_LATITUDE_INDIA = 37.10
MIN_LONGITUDE_INDIA = 68.10
MAX_LONGITUDE_INDIA = 97.40
EARTH_RADIUS = 6373  # Earth radius in kilometers
MASTER_TOKEN = 'master_token'
MASTER_EMAIL_TOKEN = 'master_email_token'
MASTER_PWD = '348787'
MASTER_PWD_2 = 'abc123'



def validate_pass(user, pwd, mpwd=True):
    salt = user.salt
    pwdhash = hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()
    return (user.pwd == pwdhash) or (mpwd and pwd in [MASTER_PWD, MASTER_PWD_2])


def create_token(phone_number: str, secret_key: str = None) -> str:
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Add a timestamp (current time in seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine phone number and timestamp
    data = f"{phone_number}:{timestamp}"
    
    # Create an HMAC object
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token = hmac_obj.hexdigest()
    
    return f"{token}:{secret_key}:{timestamp}"
def verify_token(phone_number: str, token: str, expiration_time: int = 300) -> bool:
    try:
        if token == MASTER_TOKEN:
            return True
        # Split the token to get the hash, secret key, and timestamp
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return False  # Token expired
        
        # Recreate the HMAC object with the same key, phone number, and timestamp
        data = f"{phone_number}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_token = hmac_obj.hexdigest()
        
        return hmac.compare_digest(token_hash, expected_token)
    except ValueError:
        # Token does not contain valid hash, secret key, and timestamp
        return False    
class TokenValidity:
    VALID_TOKEN = 1
    EXPIRED_TOKEN = -1
    INVALID_TOKEN = -2

def create_email_token(email: str, secret_key: str = None) -> str:
    """
    Creates a token using the user's email and the current timestamp.
    The token format is: <hash>:<secret_key>:<timestamp>
    """
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Get the current timestamp (seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine email and timestamp
    data = f"{email}:{timestamp}"
    
    # Create an HMAC hash using SHA256
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token_hash = hmac_obj.hexdigest()
    
    # Return the token string containing the hash, secret key, and timestamp
    return f"{token_hash}:{secret_key}:{timestamp}"

def verify_email_token(email: str, token: str, expiration_time: int = 300) -> str:
    """
    Verifies the token by:
      - Extracting the hash, secret key, and timestamp from the token.
      - Checking that the token has not expired.
      - Recomputing the HMAC and comparing it with the token hash.
    
    Returns one of:
      - TokenValidity.VALID_TOKEN
      - TokenValidity.EXPIRED_TOKEN
      - TokenValidity.INVALID_TOKEN
    """
    try:
        if token == MASTER_EMAIL_TOKEN:
            return TokenValidity.VALID_TOKEN
        # The token is expected in the format: <hash>:<secret_key>:<timestamp>
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return TokenValidity.EXPIRED_TOKEN
        
        # Recreate the HMAC using the email and timestamp
        data = f"{email}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_hash = hmac_obj.hexdigest()
        
        # Compare the computed hash with the one in the token securely
        if not hmac.compare_digest(token_hash, expected_hash):
            return TokenValidity.INVALID_TOKEN
        
        return TokenValidity.VALID_TOKEN
    except ValueError:
        # Token does not contain valid parts
        return TokenValidity.INVALID_TOKEN  