#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _utils.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON>hat<PERSON> Mitra


#Standard Libraries
import hashlib
import hmac
import uuid
import time
import re
import os
from datetime import datetime,timedelta
from random import randint
from flask import current_app as app
import pytz

# === Application (Internal) Imports ===
from redis_config import execute_with_fallback,get_redis_client

# === Constants ===
SITE_URL = 'https://driver4me-data.s3.amazonaws.com/'
USER_SOFT_BANNED_LIST = [68]  # A Ghosh
USER_BANNED_LIST = []  # 534]

MIN_LATITUDE_WORLD = -90
MAX_LATITUDE_WORLD = 90
MIN_LONGITUDE_WORLD = -180
MAX_LONGITUDE_WORLD = 180

MIN_LATITUDE_INDIA = 6.55
MAX_LATITUDE_INDIA = 37.10
MIN_LONGITUDE_INDIA = 68.10
MAX_LONGITUDE_INDIA = 97.40
EARTH_RADIUS = 6373  # Earth radius in kilometers
MASTER_TOKEN = 'master_token'
MASTER_EMAIL_TOKEN = 'master_email_token'
MASTER_PWD = '348787'
MASTER_PWD_2 = 'abc123'

def get_salt(): 
    """
    Generates a unique salt string for password hashing.

    Returns:
        str: A 32-character hexadecimal string used as salt.
    """
    return uuid.uuid4().hex

def get_pwd(pwd, salt):
    """
    Generates a SHA-512 hash of the given password concatenated with a salt.

    Args:
        pwd (str): Plain text password.
        salt (str): Salt to mix with the password.

    Returns:
        str: Hexadecimal SHA-512 hash of the salted password.
    """
    return hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()


def validate_pass(user, pwd, mpwd=True):
    """
    Validates a user's password against stored hash, including optional master password bypass.

    Args:
        user (object): User object with attributes `pwd` (hashed password) and `salt`.
        pwd (str): Input password to validate.
        mpwd (bool): If True, allows master password override.

    Returns:
        bool: True if password matches or is a valid master password; otherwise False.
    """
    salt = user.salt
    pwdhash = hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()
    return (user.pwd == pwdhash) or (mpwd and pwd in [MASTER_PWD, MASTER_PWD_2])

def to_camel_case(s: str) -> str:
    """
    Converts a string with spaces, underscores, or hyphens to camelCase.

    Args:
        s (str): Input string.

    Returns:
        str: String formatted in camelCase.
    
    Example:
        'Business Category' -> 'businessCategory'
    """
    s = s.strip()
    
    # Check if already camelCase
    if (
        s and 
        re.match(r'^[a-z][a-zA-Z0-9]*$', s) and 
        not re.search(r'[\s_-]', s)
    ):
        return s

    parts = re.split(r'[\s_-]+', s)
    if not parts:
        return ''
    head, *tail = parts
    return head.lower() + ''.join(w.capitalize() for w in tail)


def ist_to_gmt(ist_time: str) -> str:
    """
    Converts IST time string to GMT time string.

    Args:
        ist_time (str): Time in 'YYYY-MM-DD HH:MM:SS' format (IST timezone).

    Returns:
        str: Corresponding time in GMT in the same format.
    """
    dt = pytz.timezone("Asia/Kolkata").localize(datetime.strptime(ist_time, "%Y-%m-%d %H:%M:%S"))
    return dt.astimezone(pytz.timezone("GMT")).strftime("%Y-%m-%d %H:%M:%S")

#convert timestamp to ist string
def convert_utc_to_ist_str(utc_dt):
    """
    Converts a UTC datetime object to an IST formatted string.

    Args:
        utc_dt (datetime): UTC datetime object.

    Returns:
        str or None: IST time formatted as 'DD/MM/YYYY hh:mm AM/PM' or None if input is invalid.
    """
    if not utc_dt:
        return None
    
    # Ensure utc_dt is timezone-aware in UTC
    if utc_dt.tzinfo is None:
        utc = pytz.timezone('UTC')
        utc_dt = utc.localize(utc_dt)
    
    # Convert to IST
    ist = pytz.timezone('Asia/Kolkata')
    ist_dt = utc_dt.astimezone(ist)

    # Format the datetime string
    return ist_dt.strftime('%d/%m/%Y %I:%M %p')

def create_token(phone_number: str, secret_key: str = None) -> str:
    """
    Generates a secure HMAC-based token using phone number and current timestamp.

    Args:
        phone_number (str): User's phone number.
        secret_key (str, optional): Secret key for signing; if not provided, one is generated.

    Returns:
        str: Token in format <hash>:<secret_key>:<timestamp>
    """
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Add a timestamp (current time in seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine phone number and timestamp
    data = f"{phone_number}:{timestamp}"
    
    # Create an HMAC object
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token = hmac_obj.hexdigest()
    
    return f"{token}:{secret_key}:{timestamp}"
def verify_token(phone_number: str, token: str, expiration_time: int = 300) -> bool:
    """
    Verifies a token generated from phone number and secret key.

    Args:
        phone_number (str): Phone number associated with the token.
        token (str): Token in the format <hash>:<secret_key>:<timestamp>.
        expiration_time (int): Allowed time in seconds before token expiry.

    Returns:
        bool: True if token is valid and not expired, otherwise False.
    """

    try:
        if token == MASTER_TOKEN:
            return True
        # Split the token to get the hash, secret key, and timestamp
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return False  # Token expired
        
        # Recreate the HMAC object with the same key, phone number, and timestamp
        data = f"{phone_number}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_token = hmac_obj.hexdigest()
        
        return hmac.compare_digest(token_hash, expected_token)
    except ValueError:
        # Token does not contain valid hash, secret key, and timestamp
        return False    
class TokenValidity:
    """
    Constants for token verification results.

    Attributes:
        VALID_TOKEN (int): Token is valid.
        EXPIRED_TOKEN (int): Token has expired.
        INVALID_TOKEN (int): Token is invalid.
    """
    VALID_TOKEN = 1
    EXPIRED_TOKEN = -1
    INVALID_TOKEN = -2

def create_email_token(email: str, secret_key: str = None) -> str:
    """
    Generates a token for email validation using HMAC and current timestamp.

    Args:
        email (str): Email address for which the token is generated.
        secret_key (str, optional): Secret key to sign the token. Auto-generated if not provided.

    Returns:
        str: Token string in format <hash>:<secret_key>:<timestamp>
    """
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Get the current timestamp (seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine email and timestamp
    data = f"{email}:{timestamp}"
    
    # Create an HMAC hash using SHA256
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token_hash = hmac_obj.hexdigest()
    
    # Return the token string containing the hash, secret key, and timestamp
    return f"{token_hash}:{secret_key}:{timestamp}"

def verify_email_token(email: str, token: str, expiration_time: int = 300) -> str:
    """
    Verifies the token by:
      - Extracting the hash, secret key, and timestamp from the token.
      - Checking that the token has not expired.
      - Recomputing the HMAC and comparing it with the token hash.
    Args:
        email (str): Email address to validate against.
        token (str): Token in format <hash>:<secret_key>:<timestamp>.
        expiration_time (int): Validity window for the token in seconds.
    Returns one of:
      - TokenValidity.VALID_TOKEN
      - TokenValidity.EXPIRED_TOKEN
      - TokenValidity.INVALID_TOKEN
    """
    try:
        if token == MASTER_EMAIL_TOKEN:
            return TokenValidity.VALID_TOKEN
        # The token is expected in the format: <hash>:<secret_key>:<timestamp>
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return TokenValidity.EXPIRED_TOKEN
        
        # Recreate the HMAC using the email and timestamp
        data = f"{email}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_hash = hmac_obj.hexdigest()
        
        # Compare the computed hash with the one in the token securely
        if not hmac.compare_digest(token_hash, expected_hash):
            return TokenValidity.INVALID_TOKEN
        
        return TokenValidity.VALID_TOKEN
    except ValueError:
        # Token does not contain valid parts
        return TokenValidity.INVALID_TOKEN  