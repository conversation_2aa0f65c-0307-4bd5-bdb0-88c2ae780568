#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  validation_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachat<PERSON> Mi<PERSON>

"""
Utility functions for validation.
"""

from datetime import date
from sqlalchemy import func

from db_config import db
from models.models import Users, Drivers, Coupons, AppliedCoupon


def account_enabled(user):
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry:
        return user_entry.enabled
    else:
        return False
    
def validate_coupon(coupon_code, user):
    if not coupon_code:
        return None, "No coupon code provided."

    coupon = Coupons.query.filter_by(code=coupon_code).first()

    if not coupon:
        return None, "Invalid coupon code."

    if coupon.state != Coupons.LABEL_ACTIVE:
        return None, "Coupon is inactive."

    if coupon.valid_till and coupon.valid_till < date.today():
        return None, "Coupon has expired."

    # Count how many times user has used the coupon
    if coupon.mobile!=0:
        usage_count = db.session.query(func.count()).filter(
        AppliedCoupon.code == id,
        AppliedCoupon.user == user
        ).scalar()

        if coupon.user_redeem_limit is not None and usage_count >= coupon.user_redeem_limit:
            return None, "Coupon usage limit exceeded for this user."

    return coupon, None