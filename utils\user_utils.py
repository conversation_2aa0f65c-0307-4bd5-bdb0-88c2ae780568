#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  user_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>


from models.models import Users
from models.models import db

def get_name_by_id(user_id):
    name=db.session.query(Users).filter(Users.id == user_id).first()
    if not name:
        return None
    return name.get_name()

def isadmin(current_user):
    checkadmin =[*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]
    user = db.session.query(Users).filter(Users.id == current_user).first()
    if not user or user.role not in checkadmin:
        return False
    return True
