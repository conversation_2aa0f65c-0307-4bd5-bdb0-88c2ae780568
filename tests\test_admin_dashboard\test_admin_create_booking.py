import random
from faker import Faker
from models.models import Users, db

fake = Faker()

""" Test cases for api: /api/admin/register_cust_soft """
# Test Case : Missing JWT Token
def test_cb_misssing_jwt_token(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'mobile': '9876543210',
        'region': 0
    }

    response = client.post('/api/admin/register_cust_soft', data=form_data)
    assert response.status_code == 401

    res_data = response.get_json()
    assert res_data['result'] == 'FAILURE'

# Test Case : Unauthorized Admin Role
def test_cb_unauthorized_admin_role(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter_by(id=user).first()
    form_data = {
        'mobile': user.mobile,
        'region': '0'
    }
    response = client.post('/api/admin/register_cust_soft', data=form_data, headers=auth_headers)

    assert response.status_code == 403

# Test Case : Incomplete Mobile Number
def test_cb_incomplete_mobile_number(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'region': '0'
    }
    response = client.post('/api/admin/register_cust_soft', data=form_data, headers=auth_headers)

    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -2
    assert res_data['msg'] == 'Incomplete mobile number'

# Test Case : User Already Exists
def test_cb_user_already_exists(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(7000000000, 9999999999)}"
    user = Users(fake.name(), "Doe", mobile, fake.email(), "password", Users.ROLE_USER)
    db.session.add(user)
    db.session.commit() 

    form_data = {
        'mobile': user.mobile,
        'region': '0'
    }
    response = client.post('/api/admin/register_cust_soft', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['msg'] == 'User already exists!'

# Test Case: Successful User Registration
def test_cb_successful_user_registration(client, admin_login):
    auth_headers, admin = admin_login

    mobile = f"{random.randint(7000000000, 9999999999)}"

    form_data = {
        'mobile': mobile,
        'email': '<EMAIL>',
        'region': '0'
    }
    response = client.post('/api/admin/register_cust_soft', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 2
    assert 'new_user_id' in res_data
    assert res_data['msg'] == 'User created successfully'
