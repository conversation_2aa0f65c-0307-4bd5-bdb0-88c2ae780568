from main import app
from booking_params import BookingParams
import os
import time
from datetime import datetime, date, timedelta
import requests
from pytz import timezone
from models import db, Users, Bookings

# --- Gupshup API Config ---
API_URL       = "https://api.gupshup.io/wa/api/v1/template/msg"
API_KEY       = "vk9i60br4md5x6mc5m8b5jbdamqwhmqg"
SOURCE_NUMBER = "918882012345"
SOURCE_NAME   = "Drivers4Me"
TEMPLATE_ID   = "49174673-f30d-47fe-ae11-8c58228fa960"

# --- Helpers ---
def is_valid_10digit(phone):
    return phone and phone.isdigit() and len(phone) == 10

def get_name(name):
    return name.strip() if name and name.strip() else "Customer"

def send_whatsapp_message(name, phone):
    headers = {
        "accept": "application/json",
        "apikey": API_KEY,
        "content-type": "application/x-www-form-urlencoded"
    }

    payload = {
        "template": f'{{"params":["{name}"],"id":"{TEMPLATE_ID}"}}',
        "channel": "whatsapp",
        "source": SOURCE_NUMBER,
        "src.name": SOURCE_NAME,
        "destination": f"91{phone}"
    }

    try:
        response = requests.post(API_URL, headers=headers, data=payload)
        print(f"📤 Sending to 91-{phone} | Status: {response.status_code}", flush=True)
        print(f"↪️  Response: {response.text}", flush=True)

        if response.status_code in [200, 202]:
            return response.json().get("status") == "submitted"
        return False
    except Exception as e:
        print(f"❌ Exception during API call to {phone}: {e}", flush=True)
        return False

# --- Main reminder processing ---
def process_30d_reminders():
    with app.app_context():
        today = date.today()
        target = today - timedelta(days=30)

        # Subquery: get each user's last booking date
        last_booking = (
            db.session.query(
                Bookings.user,
                db.func.max(Bookings.startdate).label("last_date")
            )
            .filter(
                Bookings.valid == 1,
                Bookings.type < BookingParams.TYPE_C24 #to prevent sending to old affiliates
            )
            .group_by(Bookings.user)
            .subquery()
        )

        # Join Users with their last booking, filter those who booked 30 days ago
        query = (
            db.session.query(Users, last_booking.c.last_date)
            .join(last_booking, Users.id == last_booking.c.user)
            .filter(
                last_booking.c.last_date == target,
            )
        )

        IST = timezone("Asia/Kolkata")
        now_ist = datetime.now(IST).strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n⏱️ {now_ist} IST — Processing 30-day reminders...", flush=True)

        count = 0
        for user, last_date in query:
            if not is_valid_10digit(user.mobile):
                print(f"⚠️ Skipping user {user.id} — invalid phone: {user.mobile}", flush=True)
                continue

            name = get_name(user.fname)
            print("name: ", name,"mobile:", user.mobile,"id:", user.id,flush=True)

            if send_whatsapp_message(name, user.mobile):
                print(f"✅ Reminder sent to {name} (91{user.mobile})", flush=True)
                count += 1
            else:
                print(f"❌ Failed to send reminder to {name} (91{user.mobile})", flush=True)

        db.session.commit()
        print(f"🗂️  {count} reminder(s) committed to DB.\n", flush=True)

# --- Daily loop ---
if __name__ == "__main__":
    process_30d_reminders()
