#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  admin_login_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from pydantic import BaseModel, field_validator, <PERSON>
from typing import Optional, Literal, List
from datetime import datetime
from utils.bookings.booking_params import Regions

class AnalyticsCountPayload(BaseModel):
    from_date: str
    to_date: str
    region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date(cls, value):
        try:
            datetime.strptime(value, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return value
    
class AdminAnalyticsDailySalesPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
class AdminAnalyticsSalesPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
class AdminAnalyticsDailyRevenuePayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
class AnalyticsRevenuePayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
class DailyTripsPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    

class TripsAnalyticsPayload(BaseModel):
    region: Optional[str] = None
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    
class RatingAnalyticsPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    search_region: str
    
    @field_validator('from_date', 'to_date', 'from_time', 'to_time', 'search_region')
    @classmethod
    def no_empty_string(cls, v):
        if v is None or str(v).strip() == '':
            raise ValueError("Required field is missing or empty")
        return v
    
class CustomerRegistrationCountPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"

    @field_validator('from_date', 'to_date')
    def no_empty_dates(cls, v):
        if not v or not v.strip():
            raise ValueError("Date field is required")
        return v
    
class CustomerRegisterSourcePayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    search_region: Optional[str] = None

    @field_validator('from_date', 'to_date')
    @classmethod
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Date is required")
        return v
    
class CancellationReasonCountPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    search_region: Optional[str] = None

    @field_validator('from_date', 'to_date')
    @classmethod
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Missing or invalid date")
        return v
    
class TransactionSummaryPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Literal["Drivers", "Customers"] = "Drivers"
    search_region: Optional[str] = None

    @field_validator('from_date', 'to_date')
    @classmethod
    def validate_dates(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return v
    
class TransactionSummaryCustomerAdminValidator(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: str = Literal["Fine", "Gift", "Deduct", "Add", "Cancel"] 
    search_region: Optional[str] = None
    
class TransactionSummaryDriverAdminValidator(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Optional[str] = "Fine"
    search_region: Optional[str] = "-1"
    @field_validator('from_date', 'to_date')
    @classmethod
    def validate_dates(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")
        return v
    
class DriverInventoryCountValidator(BaseModel):
    from_date: str = Field(..., description="Start date in YYYY-MM-DD")
    to_date: str = Field(..., description="End date in YYYY-MM-DD")
    from_time: str = Field(default="00:00:00")
    to_time: str = Field(default="23:59:59")
    data_type: str = Field(default="Fine")
    search_region: Optional[str] = Field(default="-1")

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date(cls, value):
        try:
            datetime.strptime(value, "%Y-%m-%d")
            return value
        except Exception:
            raise ValueError("Date must be in YYYY-MM-DD format")

class BookingSummaryAdminPayload(BaseModel):
    from_date: str
    to_date: str
    from_time: Optional[str] = "00:00:00"
    to_time: Optional[str] = "23:59:59"
    data_type: Optional[str] = "Allocation"
    sort: Optional[int] = 1
    is_b2b: Optional[int] = 0
    affiliatefilter: Optional[str] = None
    search_region: Optional[str] = None

    @property
    def aff_ids(self) -> Optional[List[int]]:
        if self.affiliatefilter:
            return [int(x) for x in self.affiliatefilter.split(',')]
        return None

    @property
    def regions(self) -> List[int]:
        if self.search_region and self.search_region != Regions.ALL_REGIONS_ACCESS:
            return [int(x) for x in self.search_region.split(',')]
        return []
    
class AnalyticsDataValidator(BaseModel):
    from_date: str
    to_date: str
    data_type: str
    from_date_previous: Optional[str] = None
    to_date_previous: Optional[str] = None
    region: Optional[str] = None

    @field_validator("from_date", "to_date")
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Invalid date format, expected YYYY-MM-DD")
        return v
    
class LiveRoutesValidator(BaseModel):
    status_min: int = Field(..., description="Minimum status to filter trips")
    
class DeleteRouteDocValidator(BaseModel):
    book_id: int = Field(..., description="Booking ID")
    driver_id: int = Field(..., description="Driver ID")
    
    