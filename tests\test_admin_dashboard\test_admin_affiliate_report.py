import json
from models.models import ScheduledReport
import io
import os
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

def test_create_scheduled_report_success(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'report_type': 1,  # ScheduledReport.REPORT_BOOKING
        'frequency': 1,    # ScheduledReport.FREQUENCY_DAILY
        'duration': 1,     # ScheduledReport.DURATION_PREVIOUS_DAY
        'email_list': json.dumps(['<EMAIL>', '<EMAIL>']),
        'city': json.dumps([1, 2]),
        'affiliate_filter': json.dumps([10, 20]),
        'subject': 'Daily Report',
        'message': 'Here is your daily report.',
        'draft': 0
    }

    response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    res = response.get_json()
    assert res['success'] == 1
    assert res['message'] == 'Scheduled report created successfully'
    assert 'id' in res
def test_create_scheduled_report_invalid_report_type(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'report_type': 99,  # Invalid
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([]),
        'affiliate_filter': json.dumps([]),
    }

    response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Invalid report type'
def test_create_scheduled_report_invalid_email_list_format(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': 'not-a-json',  # invalid JSON
        'city': json.dumps([]),
        'affiliate_filter': json.dumps([]),
    }

    response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    assert response.status_code == 500
    res = response.get_json()
    assert res['success'] == 0
    assert 'Error creating report' in res['message']

def test_create_scheduled_report_missing_required_fields(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([]),
        'affiliate_filter': json.dumps([]),
    }

    response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert 'Invalid' in res['message']
def test_create_scheduled_report_as_draft(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([]),
        'affiliate_filter': json.dumps([]),
        'draft': 1
    }

    response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    res = response.get_json()
    assert res['success'] == 1
    assert 'id' in res

def test_update_scheduled_report_status_success(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a report to update its status
    form_data = {
        'report_type': '1',  # Convert to string since form data is typically strings
        'frequency': '1',    
        'duration': '1',     
        'email_list': '["<EMAIL>"]',  # Already JSON string
        'city': '[1, 2]',    # Already JSON string
        'affiliate_filter': '[10, 20]',  # Already JSON string
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': '0',
        'region': '-1'
    }
    
    create_response = client.post(
        '/api/admin/affiliate/create_report_schedule', 
        data=form_data, 
        headers=auth_headers
    )
    report_id = create_response.get_json()['id']

def test_update_scheduled_report_status_already_set(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create an active report (not draft)
    form_data = {
        'report_type': '1',
        'frequency': '1',
        'duration': '1',
        'email_list': '["<EMAIL>"]',
        'city': '[1, 2]',
        'affiliate_filter': '[10, 20]',
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': '0',  # Not a draft
        'region': '-1'
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', 
                                data=form_data, 
                                headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Try to activate an already active report
    status_data = {
        'action': str(ScheduledReport.STATUS_ACTIVE),
         'region':-1
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                          data=status_data, 
                          headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0  # Note: Using 0 instead of False
    assert res['message'] == 'Schedule is already Active'

def test_update_scheduled_report_status_missing_action(client, admin_login):
    auth_headers, _ = admin_login
    
    # Create a report
    form_data = {
        'report_type': '1',
        'frequency': '1',
        'duration': '1',
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': '0',
        'region': '-1'
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', 
                                data=form_data, 
                                headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Send request without action
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                          data={ 'region':-1},
                          headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Action is required'

def test_update_draft_report_status(client, admin_login):
    auth_headers, _ = admin_login
    
    # Create a draft report
    form_data = {
        'report_type': '1',
        'frequency': '1',
        'duration': '1',
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': '1',  # This is a draft
        'region': '-1'
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', 
                                data=form_data, 
                                headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Try to activate a draft report
    status_data = {
        'action': str(ScheduledReport.STATUS_ACTIVE),
        'region':-1
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                          data=status_data, 
                          headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'This report is still in draft. Please go to edit and complete all required details and submit it.'
def test_update_scheduled_report_status_invalid_action_format(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a report
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': 0,
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Send invalid action format
    status_data = {
        'action': 'not-a-number',
        'region':-1
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                           data=status_data, 
                           headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Invalid action format'

def test_update_scheduled_report_status_invalid_action_value(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a report
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': 0,
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Send invalid action value
    status_data = {
        'action': 999  ,
           'region':-1
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                           data=status_data, 
                           headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Invalid action. Must be activate or cancel'

def test_update_scheduled_report_status_report_not_found(client, admin_login):
    auth_headers, _ = admin_login
    
    # Use a non-existent report ID
    non_existent_id = 99999
    
    status_data = {
        'action': ScheduledReport.STATUS_ACTIVE,
           'region':-1
        
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{non_existent_id}/status', 
                           data=status_data, 
                           headers=auth_headers)
    
    assert response.status_code == 404
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Scheduled report not found'

def test_update_scheduled_report_status_draft_report(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a draft report
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Draft Report',
        'message': 'Draft message',
        'draft': 1 , # This is a draft
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Try to activate a draft report
    status_data = {
        'action': ScheduledReport.STATUS_ACTIVE,
        'region':-1
    }
    
    response = client.patch(f'/api/admin/affiliate/scheduled_reports/{report_id}/status', 
                           data=status_data, 
                           headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == False
    assert res['message'] == 'This report is still in draft. Please go to edit and complete all required details and submit it.'

def test_edit_scheduled_report_success(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a report to edit
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Original Report',
        'message': 'Original message',
        'draft': 0,
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Now edit the report
    edit_data = {
        'report_type': ScheduledReport.REPORT_CANCELLATION,  # Changed from booking to cancellation
        'frequency': ScheduledReport.FREQUENCY_WEEKLY,       # Changed from daily to weekly
        'duration': ScheduledReport.DURATION_THIS_WEEK,      # Changed duration
        'email_list': json.dumps(['<EMAIL>', '<EMAIL>']),
        'city': json.dumps([2, 3]),
        'affiliate_filter': json.dumps([20, 30]),
        'subject': 'Updated Report',
        'message': 'Updated message',
        'draft': 0,
        'region':-1
    }
    
    response = client.put(f'/api/admin/affiliate/scheduled_reports/{report_id}', 
                         data=edit_data, 
                         headers=auth_headers)
    
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert res['message'] == 'Scheduled report updated successfully'

def test_edit_scheduled_report_to_draft(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create an active report
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Active Report',
        'message': 'Active message',
        'draft': 0  ,
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Now edit the report and make it a draft
    edit_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Draft Report',
        'message': 'Draft message',
        'draft': 1  ,
        'region':-1
    }
    
    response = client.put(f'/api/admin/affiliate/scheduled_reports/{report_id}', 
                         data=edit_data, 
                         headers=auth_headers)
    
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    assert res['message'] == 'Scheduled report updated successfully'

def test_edit_scheduled_report_invalid_report_type(client, admin_login):
    auth_headers, _ = admin_login
    
    # First create a report
    form_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': 0,
        'region':-1
    }
    
    create_response = client.post('/api/admin/affiliate/create_report_schedule', data=form_data, headers=auth_headers)
    report_id = create_response.get_json()['id']
    
    # Try to update with invalid report type
    edit_data = {
        'report_type': 999,  # Invalid report type
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': 0,
        'region':-1
    }
    
    response = client.put(f'/api/admin/affiliate/scheduled_reports/{report_id}', 
                         data=edit_data, 
                         headers=auth_headers)
    
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Invalid report type'

def test_edit_scheduled_report_report_not_found(client, admin_login):
    auth_headers, _ = admin_login
    
    # Use a non-existent report ID
    non_existent_id = 99999
    
    edit_data = {
        'report_type': 1,
        'frequency': 1,
        'duration': 1,
        'email_list': json.dumps(['<EMAIL>']),
        'city': json.dumps([1]),
        'affiliate_filter': json.dumps([10]),
        'subject': 'Test Report',
        'message': 'Test message',
        'draft': 0,
        'region':-1
    }
    
    response = client.put(f'/api/admin/affiliate/scheduled_reports/{non_existent_id}', 
                         data=edit_data, 
                         headers=auth_headers)
    
    assert response.status_code == 404
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Scheduled report not found'





def test_send_report_success(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-31 23:59:59',
        'trans_type': 'Booking',
        'selected_affiliates': '1,2',
        'selected_cities': '3',
        'email_list': '<EMAIL>',
        'subject': 'Monthly Report',
        'message': 'Here is the report.',
        'regions': -1
    }

    with patch('adminnew.affiliate.affiliate_admin.build_affiliate_report_query') as mock_query, \
         patch('adminnew.affiliate.affiliate_admin.build_affiliate_report_data', return_value=[{'sample': 'data'}]), \
         patch('adminnew.affiliate.affiliate_admin.create_excel_file', return_value='/tmp/sample.xlsx'), \
         patch('adminnew.affiliate.affiliate_admin.send_mail', return_value=True), \
         patch('os.remove'):

        mock_query.return_value.all.return_value = []

        response = client.post('/api/admin/affiliate/send_report', data=form_data, headers=auth_headers)

        assert response.status_code == 200

def test_send_report_missing_email_list(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-31 23:59:59',
        'trans_type': 'Booking',
        'selected_affiliates': '1',
        'selected_cities': '2',
        'subject': 'Report',
        'message': 'No emails.',
        'regions':-1
    }

    response = client.post('/api/admin/affiliate/send_report', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'No email list provided'


def test_send_report_invalid_date_format(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'start_date': 'invalid-date',
        'end_date': '2024-01-31 23:59:59',
        'trans_type': 'Booking',
        'selected_affiliates': '1',
        'selected_cities': '2',
        'email_list': '<EMAIL>',
        'subject': 'Invalid Date Test',
        'message': 'Invalid start date format.',
        'regions':-1
    }

    response = client.post('/api/admin/affiliate/send_report', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res = response.get_json()
    assert res['success'] == 0
    assert res['message'] == 'Invalid date format, use YYYY-MM-DD HH:mm:ss'


def test_send_report_email_failure(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-31 23:59:59',
        'trans_type': 'Booking',
        'selected_affiliates': '1',
        'selected_cities': '2',
        'email_list': '<EMAIL>',
        'subject': 'Should Fail',
        'message': 'Simulating email failure',
        'regions':-1
    }

    with patch('adminnew.affiliate.affiliate_admin.build_affiliate_report_query') as mock_query, \
        patch('adminnew.affiliate.affiliate_admin.build_affiliate_report_data', return_value=[{'sample': 'data'}]), \
         patch('adminnew.affiliate.affiliate_admin.create_excel_file', return_value='/tmp/sample.xlsx'), \
         patch('adminnew.affiliate.affiliate_admin.send_mail', side_effect=Exception('error sending mail')), \
         patch('os.remove'):
        mock_query.return_value.all.return_value = []

        response = client.post('/api/admin/affiliate/send_report', data=form_data, headers=auth_headers)
        assert response.status_code == 500
        res = response.get_json()
        assert res['success'] == 0
        assert 'Email sending failed' in res['message']




def test_affiliate_report_success(client, admin_login):
    auth_headers, _ = admin_login
    params = {
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-31 23:59:59',
        'trans_type': 'Booking',
        'selected_affiliates': '1,2',
        'selected_cities': '3',
        'regions': -1,
    }

    # Mock query result
    mock_booking = MagicMock()
    mock_booking.id = 101
    mock_booking.code = "BOOK123"
    mock_booking.startdate = datetime(2024, 1, 5).date()
    mock_booking.starttime = datetime(2024, 1, 5, 10, 0).time()
    mock_booking.created_at = datetime(2024, 1, 4, 9, 0)
    mock_booking.region = 3
    mock_booking.estimate = 500
    mock_booking.loc = "Start Point"
    mock_booking.price = 1000
    mock_booking.comment = "Test Comment"
    mock_booking.client_name = "Test Client"
    mock_booking.destination = "End Point"
    mock_booking.dist = 15
    mock_booking.insurance_ch = 50
    mock_booking.driver_name = "John Doe"
    mock_booking.driver_contact = "9999999999"
    mock_booking.trip_start = datetime(2024, 1, 5, 10, 10)
    mock_booking.trip_end = datetime(2024, 1, 5, 11, 0)
    mock_booking.driver_base_ch = 200
    mock_booking.driver_night_ch = 0
    mock_booking.driver_ot_ch = 50
    mock_booking.insurance_charge = 75

    with patch('adminnew.affiliate.affiliate_admin.build_affiliate_report_query') as mock_query_fn, \
         patch('adminnew.affiliate.affiliate_admin.AffiliateCollections.affiliates_book.find', return_value=[]), \
         patch('adminnew.affiliate.affiliate_admin.db.session.query') as mock_alloc_query:

        mock_query = MagicMock()
        mock_query.all.return_value = [mock_booking]
        mock_query_fn.return_value = mock_query

        mock_alloc_query.return_value.filter.return_value.group_by.return_value.all.return_value = [(101, datetime(2024, 1, 4, 9, 30))]

        response = client.get('/api/admin/affiliate/admin_report', query_string=params, headers=auth_headers)
        assert response.status_code == 200

        json_data = response.get_json()
        assert json_data['success'] == 1
        assert isinstance(json_data['data'], list)
        assert len(json_data['data']) == 1
        assert json_data['data'][0]['book_code'] == 'BOOK123'
