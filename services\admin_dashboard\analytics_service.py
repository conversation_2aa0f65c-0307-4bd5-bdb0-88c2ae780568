#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  analytics_service.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

import time
from datetime import datetime, timed<PERSON>ta

from flask import request
from sqlalchemy.sql import func, case
from sqlalchemy.orm import aliased
from sqlalchemy import and_, or_, not_
from typing import Tuple, List, Dict

from models.models import db, Users, UserRegistrationDetails, Bookings, Trip, Drivers, \
    BookingCancelled, UserTrans, DriverTrans, DriverInfo, \
    BookingAlloc, AdminAccess, TripLog, TripPricing , DriverSetLoc, UserLoc, DriverSearch
from models.affiliate_models import AffBookingLogs, AffiliateCollections  
from utils.time_utils import convert_to_utc
from utils.bookings.booking_params import BookingParams, Regions
from utils.user_utils import get_name_by_id

def admin_analytics_count_service(payload):
    from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
    to_date_utc = convert_to_utc(payload.to_date, "23:59:59")
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        return -3, "Missing or invalid date values", {}

    region = payload.region
    regions = [int(r) for r in region.split(',')] if region and region != '-1' else None

    customers = db.session.query(Users.id).outerjoin(UserRegistrationDetails, Users.id == UserRegistrationDetails.user_id) \
        .filter(Users.role == 0, Users.enabled == 1)
    if regions:
        customers = customers.filter(UserRegistrationDetails.region.in_(regions) | (UserRegistrationDetails.region.is_(None)))
    reg_customers = customers.filter(Users.reg >= from_date, Users.reg <= to_date)
    reg_customers_count = reg_customers.count()

    date_diff = (to_date - from_date).days + 1
    previous_from_date = from_date - timedelta(days=date_diff)
    previous_to_date = to_date - timedelta(days=date_diff)

    active_user = db.session.query(Users.id).join(Bookings, Users.id == Bookings.user) \
        .join(Trip, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24).distinct()
    if regions:
        active_user = active_user.filter(Bookings.region.in_(regions))
    active_user_count = active_user.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).count()
    active_user_count_previous = active_user.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).count()

    trend_active_user = (active_user_count - active_user_count_previous) if active_user_count_previous is not None else active_user_count

    trip_count = db.session.query(func.count(Trip.id)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        trip_count = trip_count.filter(Bookings.region.in_(regions))
    trip_count_current = trip_count.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar()

    avg_trips_per_user = trip_count_current / active_user_count if active_user_count > 0 else 0

    drivers = db.session.query(Users, Drivers).filter(Drivers.user == Users.id) \
        .filter(Users.role == 1, Users.enabled == 1)
    if regions:
        drivers = drivers.filter(Users.region.in_(regions))
    reg_drivers = drivers.filter(Users.reg >= from_date, Users.reg <= to_date)
    reg_drivers_count = reg_drivers.count()

    UsersAlias = aliased(Users)
    active_driver = db.session.query(Drivers.id).join(Bookings, Drivers.id == Bookings.driver) \
        .join(Trip, Bookings.id == Trip.book_id) \
        .join(UsersAlias, Drivers.user == UsersAlias.id) \
        .filter(Drivers.approved == 1, Bookings.type < BookingParams.TYPE_C24).distinct()
    if regions:
        active_driver = active_driver.filter(UsersAlias.region.in_(regions))
    active_driver_count = active_driver.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).count()
    active_driver_count_previous = active_driver.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).count()

    trend_active_driver = (active_driver_count - active_driver_count_previous) if active_driver_count_previous is not None else active_driver_count

    avg_trips_per_driver = trip_count_current / active_driver_count if active_driver_count > 0 else 0

    booking_count = db.session.query(func.count(Bookings.id)).filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        booking_count = booking_count.filter(Bookings.region.in_(regions))
    total_booking_count = booking_count.filter(Bookings.startdate >= from_date, Bookings.startdate <= to_date).scalar() or 0

    average_trips_per_day = trip_count_current / date_diff if date_diff > 0 else 0
    trip_count_prev = trip_count.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).scalar()
    trend_trips = (trip_count_current - trip_count_prev) if trip_count_prev is not None else trip_count_current

    total_sales = db.session.query(func.sum(Trip.price)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        total_sales = total_sales.filter(Bookings.region.in_(regions))
    total_sales_count = total_sales.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar() or 0

    total_revenue = db.session.query(func.sum(Trip.net_rev)).join(Bookings, Bookings.id == Trip.book_id) \
        .filter(Bookings.type < BookingParams.TYPE_C24)
    if regions:
        total_revenue = total_revenue.filter(Bookings.region.in_(regions))
    total_revenue_count = total_revenue.filter(Trip.starttime >= from_date, Trip.starttime <= to_date).scalar() or 0

    prev_sales_count = total_sales.filter(Trip.starttime >= previous_from_date, Trip.starttime <= previous_to_date).scalar() or 0
    trend_sales = (total_sales_count - prev_sales_count) if prev_sales_count is not None else total_sales_count
    average_sales_per_day = total_sales_count / date_diff if date_diff > 0 else 0

    return 1, "Success", {
        "registered_customer": reg_customers_count,
        "active_customers": active_user_count,
        "trend_user": trend_active_user,
        "average_trips_per_user": round(avg_trips_per_user, 1),
        "registered_drivers": reg_drivers_count,
        "active_drivers": active_driver_count,
        "trend_driver": trend_active_driver,
        "average_trips_per_driver": round(avg_trips_per_driver, 1),
        "total_bookings": total_booking_count,
        "total_trips_count": trip_count_current,
        "trend_trips": trend_trips,
        "average_trips_per_day": round(average_trips_per_day, 2),
        "total_sales": round(total_sales_count),
        "trend_sales": round(trend_sales),
        "total_revenue": round(total_revenue_count),
        "average_sales_per_day": round(average_sales_per_day)
    }
    
def get_daily_sales_data(payload):
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter

    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    daily_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        daily_sales = daily_sales.filter(
            Bookings.type < BookingParams.TYPE_C24
        ).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))
        ).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
        )
    else:
        daily_sales = daily_sales.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        ).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))
        ).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
        )

    if region and region != '-1':
        regions = [int(value) for value in region.split(',')]
        daily_sales = daily_sales.filter(Bookings.region.in_(regions))

    if aff_ids:
        daily_sales = daily_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    results = daily_sales.limit(60).all()[::-1]

    day_json = []
    for row in results:
        if row.day is None:
            continue
        day_json.append({
            "day": row.day.strftime('%d-%m-%Y'),
            "sales": float(round(row.sale, 2)) if row.sale not in [None, ''] else 0.0
        })

    return {
            [{'day_data': day_json}]
    }
    
def get_analytics_graph_sales(payload):
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    # Monthly sales
    monthly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        monthly_sales = monthly_sales.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        monthly_sales = monthly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            monthly_sales = monthly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        regions = [int(r) for r in region.split(',')]
        monthly_sales = monthly_sales.filter(Bookings.region.in_(regions))

    monthly_sales = monthly_sales.group_by(
        func.month(func.addtime(Trip.starttime, '05:30:00')),
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # Yearly sales
    yearly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        yearly_sales = yearly_sales.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        yearly_sales = yearly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            yearly_sales = yearly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        yearly_sales = yearly_sales.filter(Bookings.region.in_(regions))

    yearly_sales = yearly_sales.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # Weekly sales
    weekly_sales = db.session.query(
        func.sum(Trip.price).label('sale'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        weekly_sales = weekly_sales.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        weekly_sales = weekly_sales.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            weekly_sales = weekly_sales.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        weekly_sales = weekly_sales.filter(Bookings.region.in_(regions))

    weekly_sales = weekly_sales.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12).all()[::-1]

    # Format output
    month_json = [{
        "month": row.month,
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in monthly_sales if row.month and row.year]

    year_json = [{
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in yearly_sales if row.year]

    week_json = [{
        "week": row.week,
        "year": row.year,
        "sales": float(round(row.sale, 2)) if row.sale else 0.0
    } for row in weekly_sales if row.week]

    return {
        [{
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
        }]
    }
    
    
def get_analytics_daily_revenue(payload):
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    query = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        query = query.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        query = query.filter(
            Bookings.type == BookingParams.TYPE_B2B,
            AffBookingLogs.book_id == Bookings.id
        )
        if aff_ids:
            query = query.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region and region != '-1':
        region_ids = [int(r) for r in region.split(',')]
        query = query.filter(Bookings.region.in_(region_ids))

    query = query.group_by(
        func.date(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
    ).limit(60)

    results = query.all()[::-1]  # reverse for chronological order

    day_data = []
    for row in results:
        if row.day is None:
            continue
        day_data.append({
            "day": row.day.strftime('%d-%m-%Y'),
            "revenues": float(row.revenue) if row.revenue not in [None, ''] else 0.0
        })

    return {
         [{
            "day_data": day_data
        } ]
    }
    
    
    
def get_revenue_graph_data(payload):
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None

    region_ids = [int(r) for r in region.split(',')] if region and region != '-1' else None

    # Yearly Revenue Query
    yearly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    # Monthly Revenue Query
    monthly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(Bookings.id == Trip.book_id)

    # Weekly Revenue Query
    weekly = db.session.query(
        func.sum(Trip.net_rev).label('revenue'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        yearly = yearly.filter(Bookings.type < BookingParams.TYPE_C24)
        monthly = monthly.filter(Bookings.type < BookingParams.TYPE_C24)
        weekly = weekly.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        yearly = yearly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        monthly = monthly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        weekly = weekly.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            yearly = yearly.filter(AffBookingLogs.aff_id.in_(aff_ids))
            monthly = monthly.filter(AffBookingLogs.aff_id.in_(aff_ids))
            weekly = weekly.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        yearly = yearly.filter(Bookings.region.in_(region_ids))
        monthly = monthly.filter(Bookings.region.in_(region_ids))
        weekly = weekly.filter(Bookings.region.in_(region_ids))

    # Group + Order
    yearly = yearly.group_by(func.year(func.addtime(Trip.starttime, '05:30:00')))
    monthly = monthly.group_by(
        func.month(func.addtime(Trip.starttime, '05:30:00')),
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    )
    weekly = weekly.group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12)

    # Run queries
    start_time = time.time()
    year_data = yearly.all()
    month_data = monthly.all()
    week_data = weekly.all()[::-1]
    query_time = time.time() - start_time

    # Transform results
    year_json = [
        {
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in year_data if row.year
    ]

    month_json = [
        {
            "month": row.month,
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in month_data if row.month and row.year
    ]

    week_json = [
        {
            "week": row.week,
            "year": row.year,
            "revenues": float(row.revenue) if row.revenue else 0.0
        } for row in week_data if row.week
    ]

    if not (year_json or month_json or week_json):
        return {'success': -1}, 200

    return {
 [{
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
        }]
    }, 200
    

def get_daily_trips_graph_data(payload):
    region = payload.region
    is_b2b = payload.is_b2b
    aff_filter = payload.affiliatefilter
    aff_ids = [int(x) for x in aff_filter.split(',')] if aff_filter else None
    region_ids = [int(x) for x in region.split(',')] if region and region != '-1' else None

    daily_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.date(func.addtime(Trip.starttime, '05:30:00')).label('day')
    ).filter(Bookings.id == Trip.book_id)

    if is_b2b == 0:
        daily_trips = daily_trips.filter(
            Bookings.type < BookingParams.TYPE_C24
        )
    else:
        daily_trips = daily_trips.filter(
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            daily_trips = daily_trips.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        daily_trips = daily_trips.filter(Bookings.region.in_(region_ids))

    daily_trips = daily_trips.group_by(
        func.date(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.date(func.addtime(Trip.starttime, '05:30:00')).desc()
    ).limit(60).all()[::-1]

    day_json = []
    for row in daily_trips:
        if row.day:
            day_json.append({
                "day": row.day.strftime('%d-%m-%Y'),
                "trips": int(row.trip) if row.trip else 0
            })

    return {
            "day_data": day_json
    }, 200
    
    
def get_trips_graph_data(payload):
    region_ids = [int(x) for x in payload.region.split(',')] if payload.region and payload.region != '-1' else None
    aff_ids = [int(x) for x in payload.affiliatefilter.split(',')] if payload.affiliatefilter else None
    is_b2b = payload.is_b2b

    # --- Base Query ---
    trip_filter = [Bookings.id == Trip.book_id]

    if is_b2b == 0:
        trip_filter.append(Bookings.type < BookingParams.TYPE_C24)
    else:
        trip_filter.extend([
            AffBookingLogs.book_id == Bookings.id,
            Bookings.type == BookingParams.TYPE_B2B
        ])
        if aff_ids:
            trip_filter.append(AffBookingLogs.aff_id.in_(aff_ids))

    if region_ids:
        trip_filter.append(Bookings.region.in_(region_ids))

    # --- Yearly ---
    yearly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # --- Monthly ---
    monthly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.month(func.addtime(Trip.starttime, '05:30:00'))
    ).all()

    # --- Weekly ---
    weekly_trips = db.session.query(
        func.count(Trip.id).label('trip'),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
        func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
    ).filter(*trip_filter).group_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
    ).order_by(
        func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
        func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
    ).limit(12).all()[::-1]

    # --- Serialize Results ---
    year_json = [
        {"year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in yearly_trips if row.year is not None
    ]

    month_json = [
        {"month": row.month, "year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in monthly_trips if row.month is not None and row.year is not None
    ]

    week_json = [
        {"week": row.week, "year": row.year, "trips": int(row.trip) if row.trip else 0}
        for row in weekly_trips if row.week is not None
    ]

    if len(year_json) + len(month_json) + len(week_json) == 0:
        return {'success': -1}, 200

    return {
            'year_data': year_json,
            'month_data': month_json,
            'week_data': week_json
    }, 200
    
    
def get_total_ratings_data(payload):
    try:
        from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
        to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

        from_date = datetime.strptime(from_date_utc[0], '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date_utc[0], '%Y-%m-%d').date()
        from_time = datetime.strptime(from_date_utc[1], '%H:%M:%S').time()
        to_time = datetime.strptime(to_date_utc[1], '%H:%M:%S').time()

        if from_date > to_date:
            return {'success': -3, 'error': 'Missing or invalid date values'}, 400

        if payload.search_region == Regions.ALL_REGIONS_ACCESS:
            region_filter = None
        else:
            region_filter = [int(r) for r in payload.search_region.split(',')]

        q = db.session.query(
            func.count(case((Bookings.user_rating == 5, 1))).label('count_5_star'),
            func.count(case((Bookings.user_rating == 4, 1))).label('count_4_star'),
            func.count(case((Bookings.user_rating == 3, 1))).label('count_3_star'),
            func.count(case((Bookings.user_rating == 2, 1))).label('count_2_star'),
            func.count(case((Bookings.user_rating == 1, 1))).label('count_1_star'),
            func.count(case((Bookings.user_rating == 0, 1))).label('count_unrated'),
            func.avg(case((Bookings.user_rating > 0, Bookings.user_rating))).label('average_rating'),
            func.count(Bookings.user_rating).label('total_ratings')
        ).join(Trip, Bookings.id == Trip.book_id).filter(
            Bookings.type < 50,
            Trip.status == Trip.TRIP_STOPPED,
            (
                (Bookings.startdate == from_date) & (Bookings.starttime >= from_time) |
                (Bookings.startdate > from_date) & (Bookings.startdate < to_date) |
                (Bookings.startdate == to_date) & (Bookings.starttime <= to_time)
            )
        )

        if region_filter:
            q = q.filter(Bookings.region.in_(region_filter))

        result = q.first()

        result_list = [
            result.count_5_star or 0,
            result.count_4_star or 0,
            result.count_3_star or 0,
            result.count_2_star or 0,
            result.count_1_star or 0,
            result.count_unrated or 0,
            float(round(result.average_rating, 2)) if result.average_rating else 0.0,
            result.total_ratings or 0
        ]
        return result_list, 200
    except Exception as e:
        raise e
    
def get_customer_registration_counts(payload):
    try:
        from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
        to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

        from_datetime = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
        to_datetime = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

        if from_datetime > to_datetime:
            return {'success': -3, 'error': 'Missing or invalid date values'}, 400

        # Query city-wise registration
        city_q = db.session.query(
            UserRegistrationDetails.city,
            func.count(UserRegistrationDetails.id).label('registration_count')
        ).join(
            Users, UserRegistrationDetails.user_id == Users.id
        ).filter(
            Users.reg >= from_datetime,
            Users.reg <= to_datetime,
            UserRegistrationDetails.city.isnot(None),
            UserRegistrationDetails.address.isnot(None)
        ).group_by(UserRegistrationDetails.city).order_by(
            func.count(UserRegistrationDetails.id).desc()
        ).limit(10).all()

        # Query region-wise registration
        region_q = db.session.query(
            UserRegistrationDetails.region,
            func.count(UserRegistrationDetails.id).label('registration_count')
        ).join(
            Users, UserRegistrationDetails.user_id == Users.id
        ).filter(
            Users.reg >= from_datetime,
            Users.reg <= to_datetime,
            UserRegistrationDetails.region.isnot(None)
        ).group_by(UserRegistrationDetails.region).order_by(
            func.count(UserRegistrationDetails.id).desc()
        ).limit(10).all()

        city_list = [{'city': row.city, 'count': row.registration_count} for row in city_q]
        region_list = [{'region': Regions.to_string_reg(row.region), 'count': row.registration_count} for row in region_q]

        return {
                'city_registration': city_list,
                'region_registration': region_list
        }, 200
    except Exception as e:
        raise e
    
    
def get_customer_source_counts(payload):
    from_date_utc = convert_to_utc(payload.from_date, "00:00:00")
    to_date_utc = convert_to_utc(payload.to_date, "23:59:59")

    from_datetime = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_datetime = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_datetime > to_datetime:
        return {'success': -3, 'error': 'Missing or invalid date values'}, 400

    if payload.search_region is None or payload.search_region.strip() == '':
        return {'success': -4, 'error': 'Missing Region'}, 400

    regions = None
    if payload.search_region and payload.search_region != '-1':
        regions = [int(value) for value in payload.search_region.split(',')]

    query = db.session.query(
        case(
            (UserRegistrationDetails.source.in_(UserRegistrationDetails.predefined_sources), UserRegistrationDetails.source),
            else_="Others"
        ).label("source"),
        func.count(UserRegistrationDetails.id).label("registration_count")
    ).join(
        Users, UserRegistrationDetails.user_id == Users.id
    ).filter(
        Users.reg >= from_datetime,
        Users.reg <= to_datetime,
        UserRegistrationDetails.source.isnot(None)
    )

    if regions:
        query = query.filter(UserRegistrationDetails.region.in_(regions))

    result = query.group_by("source").order_by(
        func.count(UserRegistrationDetails.id).desc()
    ).all()

    result_list = [{'source': row.source, 'count': row.registration_count} for row in result]

    return result_list, 200


def get_cancellation_reason_counts(payload):
    from_date_str, from_time_str = convert_to_utc(payload.from_date, payload.from_time)
    to_date_str, to_time_str = convert_to_utc(payload.to_date, payload.to_time)

    if from_date_str > to_date_str:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    aff_ids = [int(x) for x in payload.affiliatefilter.split(',')] if payload.affiliatefilter else None
    regions = [int(r) for r in payload.search_region.split(',')] if payload.search_region and payload.search_region != '-1' else None

    latest_cancellations = (
        db.session.query(
            BookingCancelled.booking.label("b_cancelled_book_id"),
            BookingCancelled.reason.label("b_cancelled_reason"),
            BookingCancelled.reason_detail.label("b_cancelled_reason_detail"),
            BookingCancelled.timestamp.label("b_cancelled_timestamp"),
            func.row_number().over(
                partition_by=BookingCancelled.booking,
                order_by=BookingCancelled.timestamp.desc()
            ).label("rn")
        )
        .filter(BookingCancelled.reason != BookingCancelled.RSN_REVERSE_CANCELLATION)
        .subquery()
    )

    query = (
        db.session.query(
            case(
                ((Bookings.driver == 1) & (Bookings.valid == Bookings.CANCELLED_USER), "Pre Cancel"),
                ((Bookings.driver != 1) & (Bookings.valid == Bookings.CANCELLED_USER), "Post Cancel"),
                (Bookings.valid == Bookings.CANCELLED_D4M, "Admin Cancel"),
                else_="Unknown",
            ).label("cancel_type"),
            func.count().label("count"),
            latest_cancellations.c.b_cancelled_reason.label("cancellation_reason"),
        )
        .join(latest_cancellations, Bookings.id == latest_cancellations.c.b_cancelled_book_id)
        .filter(Bookings.valid < 0)
        .filter(latest_cancellations.c.rn == 1)
        .filter(
            and_(
                or_(
                    and_(Bookings.startdate > from_date_str),
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str),
                ),
                or_(
                    and_(Bookings.startdate < to_date_str),
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str),
                ),
            )
        )
    )

    if payload.is_b2b == 0:
        query = query.filter(Bookings.type < BookingParams.TYPE_C24)
    else:
        query = query.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(
            Bookings.type == BookingParams.TYPE_B2B
        )
        if aff_ids:
            query = query.filter(AffBookingLogs.aff_id.in_(aff_ids))

    if regions:
        query = query.filter(Bookings.region.in_(regions))

    query = query.group_by(
        latest_cancellations.c.b_cancelled_reason, "cancel_type"
    ).order_by(func.count().desc())

    results = query.all()

    result_dict = {}
    for row in results:
        stage = row.cancel_type
        if stage not in result_dict:
            result_dict[stage] = []
        result_dict[stage].append({'reason': row.cancellation_reason, 'count': row.count})

    formatted_result = [
        {'cancel_stage': stage, 'reasons': reasons[:10]}
        for stage, reasons in result_dict.items()
    ]

    return formatted_result, 200



def get_customer_transaction_summary(from_date, to_date, regions):
    try:
        referral_query = (
                db.session.query(
                    Bookings.region.label('region'),
                    (-func.sum(case((Bookings.user.is_(None), 0), else_=UserTrans.amount / 100))).label('total_referral')
                )
                .outerjoin(Bookings, Bookings.user == UserTrans.user_id)
                .join(Users, UserTrans.user_id == Users.id)
                .filter(
                UserTrans.method.like("Referral use%"),
                    ~Users.mobile.between('7000000000', '7000000050'),
                    Bookings.type < 50,
                    UserTrans.timestamp.between(from_date, to_date)
                )
            )
        if regions:
            referral_query = referral_query.filter(Bookings.region.in_(regions))
            referral_results = referral_query.group_by(Bookings.region).all()


            referral_redem_query = (
                db.session.query(
                    Bookings.region.label('region'),
                    (-func.sum(case((Bookings.user.is_(None), 0), else_=UserTrans.amount / 100))).label('total_referral')
                )
                .outerjoin(Bookings, Bookings.user == UserTrans.user_id)
                .join(Users, UserTrans.user_id == Users.id)
                .filter(
                    UserTrans.method.like("Referral redemption%"),
                    ~Users.mobile.between('7000000000', '7000000050'),
                    Bookings.type < 50,
                    UserTrans.timestamp.between(from_date, to_date)
                )
            )
            if regions:
                referral_redem_query = referral_redem_query.filter(Bookings.region.in_(regions))
            referral_redem_results = referral_redem_query.group_by(Bookings.region).all()
            total_amount_gift = db.session.query(
                (-func.sum(UserTrans.amount / 100)).label('total_amount')
            ).select_from(Users).join(
                UserTrans, UserTrans.user_id == Users.id
            ).filter(
                UserTrans.method == 'Gift',
                UserTrans.timestamp >= from_date,
                UserTrans.timestamp <= to_date,
                Users.id.notin_(UserTrans.excluded_user_ids),
                UserTrans.amount > 0,
                # Filter by the latest region of the user
                func.coalesce(
                    db.session.query(Bookings.region)
                    .filter(Bookings.user == Users.id)
                    .order_by(Bookings.created_at.desc())
                    .limit(1)
                    .scalar_subquery(),
                    0  # Default to 0 if no booking exists
                ).in_(regions) if regions else True
            ).scalar()  # Use scalar() to get a single total value


            total_amount_fine = db.session.query(
                (-func.sum(UserTrans.amount / 100)).label('total_amount')
            ).select_from(Users).join(
                UserTrans, UserTrans.user_id == Users.id
            ).filter(
                UserTrans.method == 'Fine',
                UserTrans.timestamp >= from_date,
                UserTrans.timestamp <= to_date,
                Users.id.notin_(UserTrans.excluded_user_ids),
                UserTrans.amount < 0,
                # Filter by the latest region of the user
                func.coalesce(
                    db.session.query(Bookings.region)
                    .filter(Bookings.user == Users.id)
                    .order_by(Bookings.created_at.desc())
                    .limit(1)
                    .scalar_subquery(),
                    0  # Default to 0 if no booking exists
                ).in_(regions) if regions else True
            ).scalar()  # Use scalar() to get a single total value



            total_amount_cancel = db.session.query(
                -func.sum(UserTrans.amount / 100).label('total_amount')
            ).select_from(Users).join(
                UserTrans, UserTrans.user_id == Users.id
            ).filter(
                UserTrans.method.like('Cancellation charges for%'),
                UserTrans.timestamp >= from_date,
                UserTrans.timestamp <= to_date,
                Users.id.notin_(UserTrans.excluded_user_ids),
                # Filter by the latest region of the user
                func.coalesce(
                    db.session.query(Bookings.region)
                    .filter(Bookings.user == Users.id)
                    .order_by(Bookings.created_at.desc())
                    .limit(1)
                    .scalar_subquery(),
                    0  # Default to 0 if no booking exists
                ).in_(regions) if regions else True
            ).scalar()  # Use scalar() to get a single total value

            total_amount_reversal = db.session.query(
                (-func.sum(UserTrans.amount / 100)).label('total_amount')
            ).select_from(Users).join(
                UserTrans, UserTrans.user_id == Users.id
            ).join(
                BookingCancelled,UserTrans.id == BookingCancelled.utransid
            ).filter(
                UserTrans.method.like('Cancellation reversal for%'),
                UserTrans.timestamp >= from_date,
                UserTrans.timestamp <= to_date,
                BookingCancelled.cancel_source==BookingCancelled.SRC_ADMIN,
                Users.id.notin_(UserTrans.excluded_user_ids),
                # Filter by the latest region of the user
                func.coalesce(
                    db.session.query(Bookings.region)
                    .filter(Bookings.user == Users.id)
                    .order_by(Bookings.created_at.desc())
                    .limit(1)
                    .scalar_subquery(),
                    0  # Default to 0 if no booking exists
                ).in_(regions) if regions else True
            ).scalar()  # Use scalar() to get a single total value



            all_amounts = {
            'referral_given_amount':sum(r.total_referral for r in referral_results if r.total_referral),
            'referral_redem_amount':sum(r.total_referral for r in referral_redem_results if r.total_referral),
            'gift_amount': total_amount_gift if total_amount_gift else 0,
            'fine_amount': total_amount_fine if total_amount_fine else 0,
            'cancel_amount': total_amount_cancel if total_amount_cancel else 0,
            'reverse_amount': total_amount_reversal if total_amount_reversal else 0
                }
            response_data = {k: v for k, v in all_amounts.items() if v != 0}
            sorted_items = sorted(response_data.items(), key=lambda item: abs(item[1]), reverse=True)
            return sorted_items, 200
    except Exception as e:
        return {'success': 0, 'error': str(e)}, 500


def get_driver_transaction_summary(from_date, to_date, regions):
    try:
        total_amount_gift = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Gift',
                DriverTrans.amount > 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_automatic_inc = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                or_(
                    DriverTrans.method == 'Daily bonus',
                    DriverTrans.method == 'Weekly bonus'
                ),
                DriverTrans.amount > 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_fine = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Admin Fine',
                DriverTrans.amount < 0,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_tshirt = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'T-Shirt',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_bag = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Bag',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_registration = db.session.query(
                (-func.sum(DriverTrans.amount / 100)).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Registration',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_due_deduct = db.session.query(
                func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Due Deduction',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions)  if regions else True
            ).scalar()

        total_amount_withdraw = db.session.query(
                func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                 or_(DriverTrans.method == 'Withdraw',
                DriverTrans.method == 'Withdraw Amount'),
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_reactivation = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method == 'Reactivation',
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_cancel = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).filter(
                DriverTrans.method.like('Cancellation: Booking%'),
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        total_amount_reverse = db.session.query(
                -func.sum(DriverTrans.amount / 100).label('total_amount')
            ).select_from(Drivers).join(Users, Users.id == Drivers.user).join(
                DriverTrans, DriverTrans.driver_id == Drivers.id
            ).join(
                BookingCancelled,DriverTrans.id == BookingCancelled.dtransid
            ).filter(
                DriverTrans.method.like('Reversal: Booking%'),
                BookingCancelled.cancel_source==BookingCancelled.SRC_ADMIN,
                DriverTrans.timestamp >= from_date,
                DriverTrans.timestamp <= to_date,
                Drivers.id.notin_(DriverTrans.excluded_driver_ids),
                Users.region.in_(regions) if regions else True
            ).scalar()

        all_amounts  = {
                'gift_amount': total_amount_gift if total_amount_gift else 0 ,
                'tshirt_amount': total_amount_tshirt if total_amount_tshirt else 0,
                'bag_amount':total_amount_bag if total_amount_bag else 0,
                'fine_amount': total_amount_fine if total_amount_fine else 0,
                'due_deduct_amout':total_amount_due_deduct if total_amount_due_deduct else 0,
                'withdraw_amount':total_amount_withdraw if total_amount_withdraw else 0,
                'autom_incent_amount': total_amount_automatic_inc if total_amount_automatic_inc else 0,
                'registration_amount': total_amount_registration if total_amount_registration else 0,
                'reactivation_amount': total_amount_reactivation if total_amount_reactivation else 0,
                'cancel_amount': total_amount_cancel if total_amount_cancel else 0,
                'cancel_reverse_amount': total_amount_reverse if total_amount_reverse else 0,
            }
        response_data = {k: v for k, v in all_amounts.items() if v != 0}
        sorted_items = sorted(response_data.items(), key=lambda item: abs(item[1]), reverse=True)
        return sorted_items, 200
    except Exception as e:
        return {'success': 0, 'error': str(e)}, 500



def get_transaction_summary(data):
    from_date_utc = convert_to_utc(data.from_date, data.from_time)
    to_date_utc = convert_to_utc(data.to_date, data.to_time)

    from_dt = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_dt = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_dt > to_dt:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    regions = []
    if data.search_region and data.search_region != Regions.ALL_REGIONS_ACCESS:
        regions = [int(r) for r in data.search_region.split(",")]

    if data.data_type.lower() == 'customers':
        return get_customer_transaction_summary(from_dt, to_dt, regions)
    else:
        return get_driver_transaction_summary(from_dt, to_dt, regions)
    
    
def get_customer_transaction_summary_admin(payload):
    from_date_utc = convert_to_utc(payload.from_date, payload.from_time)
    to_date_utc = convert_to_utc(payload.to_date, payload.to_time)
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    # Parse regions
    regions = []
    if payload.search_region and payload.search_region != "-1":
        regions = [int(r) for r in payload.search_region.split(",")]

    # Cancel handling block
    if payload.data_type == "Cancel":
        cancel_query = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.sum(
                case(
                    (UserTrans.method.like('Cancellation charges for%'), UserTrans.amount / 100),
                    else_=0
                )
            ).label("cancellation_charges_count"),
            func.sum(
                case(
                    (UserTrans.method.like('Cancellation reversal for%'), UserTrans.amount / 100),
                    else_=0
                )
            ).label("cancellation_reversals_count")
        ).join(
            BookingCancelled, BookingCancelled.utransid == UserTrans.id
        ).join(
            Users, UserTrans.user_id == Users.id
        ).filter(
            UserTrans.timestamp.between(from_date, to_date),
            Users.id.notin_(UserTrans.excluded_user_ids),
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            or_(
                UserTrans.method.like('Cancellation charges for%'),
                UserTrans.method.like('Cancellation reversal for%')
            )
        )

        if regions:
            cancel_query = cancel_query.join(Bookings, Bookings.id == BookingCancelled.booking)\
                                       .filter(Bookings.region.in_(regions))

        results = cancel_query.group_by(BookingCancelled.user)\
                              .order_by(func.sum(func.abs(UserTrans.amount) / 100).desc())\
                              .limit(10).all()

        return [
            {
                "admin_name": get_name_by_id(row.admin_id),
                "cancellation_charges_count": abs(row.cancellation_charges_count),
                "cancellation_reversals_count": abs(row.cancellation_reversals_count)
            }
            for row in results
            if row.cancellation_charges_count is not None and row.cancellation_reversals_count is not None
        ]

    # Default flow for Fine, Gift, Add, Deduct
    query = db.session.query(
        UserTrans.admin_id,
        func.sum(func.abs(UserTrans.amount) / 100).label("total_amount")
    ).join(
        Users, UserTrans.user_id == Users.id
    ).filter(
        UserTrans.timestamp.between(from_date, to_date),
        Users.id.notin_(UserTrans.excluded_user_ids),
    )

    if payload.data_type == "Fine":
        query = query.filter(
            or_(
                UserTrans.method == 'Fine',
                UserTrans.method == 'Admin panel'
            ),
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount < 0
        )
    elif payload.data_type == "Gift":
        query = query.filter(
            or_(
                UserTrans.method == 'Gift',
                UserTrans.method == 'Admin panel'
            ),
            not_(
                or_(
                    UserTrans.remark.contains('RMA'),
                    UserTrans.remark.contains('PMA')
                )
            ),
            UserTrans.amount > 0
        )
    elif payload.data_type == "Deduct":
        query = query.filter(UserTrans.method == 'Deduct', UserTrans.amount < 0)
    elif payload.data_type == "Add":
        query = query.filter(UserTrans.method == 'Add', UserTrans.amount > 0)

    if regions:
        query = query.filter(
            func.coalesce(
                db.session.query(Bookings.region)
                .filter(Bookings.user == Users.id)
                .order_by(Bookings.created_at.desc())
                .limit(1)
                .scalar_subquery(),
                0
            ).in_(regions)
        )

    results = query.group_by(UserTrans.admin_id)\
                   .order_by(func.sum(func.abs(UserTrans.amount) / 100).desc())\
                   .limit(10).all()

    return [
        {
            "admin_name": get_name_by_id(row[0]),
            "total_amount": float(row[1])
        }
        for row in results
    ]
    
    
def get_driver_transaction_summary_admin(payload):
    from_date_utc = convert_to_utc(payload.from_date, payload.from_time)
    to_date_utc = convert_to_utc(payload.to_date, payload.to_time)
    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    regions = []
    if payload.search_region and payload.search_region != "-1":
        regions = [int(r) for r in payload.search_region.split(",")]

    if payload.data_type == "Cancel":
        cancel_query = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.sum(case(
                (DriverTrans.method.like('Cancellation: Booking%'), DriverTrans.amount / 100),
                else_=0
            )).label("cancellation_charges_count"),
            func.sum(case(
                (DriverTrans.method.like('Reversal: Booking%'), DriverTrans.amount / 100),
                else_=0
            )).label("cancellation_reversals_count")
        ).join(
            Drivers, BookingCancelled.driver == Drivers.id
        ).join(
            DriverTrans, DriverTrans.id == BookingCancelled.dtransid
        ).join(
            Users, Users.id == Drivers.user
        ).filter(
            DriverTrans.timestamp.between(from_date, to_date),
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            Drivers.id.notin_(DriverTrans.excluded_driver_ids),
            or_(
                DriverTrans.method.like('Cancellation: Booking%'),
                DriverTrans.method.like('Reversal: Booking%')
            )
        )

        if regions:
            cancel_query = cancel_query.filter(Users.region.in_(regions))

        results = cancel_query.group_by(BookingCancelled.user)\
                              .order_by(func.sum(func.abs(DriverTrans.amount) / 100).desc())\
                              .limit(10).all()

        return [
            {
                "admin_name": get_name_by_id(row.admin_id),
                "cancellation_charges_count": abs(row.cancellation_charges_count),
                "cancellation_reversals_count": abs(row.cancellation_reversals_count)
            }
            for row in results
            if row.cancellation_charges_count is not None and row.cancellation_reversals_count is not None
        ]

    # Non-cancel transaction types
    query = db.session.query(
        DriverTrans.admin_name,
        func.sum(func.abs(DriverTrans.amount) / 100).label('total_amount')
    ).select_from(Drivers).join(
        Users, Users.id == Drivers.user
    ).join(
        DriverTrans, DriverTrans.driver_id == Drivers.id
    ).filter(
        DriverTrans.timestamp.between(from_date, to_date),
        Drivers.id.notin_(DriverTrans.excluded_driver_ids)
    )

    if regions:
        query = query.filter(Users.region.in_(regions))

    method_map = {
        "Fine": ("Admin Fine", "<"),
        "Gift": ("Gift", ">"),
        "Due Deduct": ("Due Deduction", ">"),
        "Withdraw": ("Withdraw", "<"),
        "T-Shirt": ("T-Shirt", None),
        "Registration": ("Registration", None),
        "Bag": ("Bag", None),
    }

    if payload.data_type in method_map:
        method, sign = method_map[payload.data_type]
        query = query.filter(DriverTrans.method == method)
        if sign == "<":
            query = query.filter(DriverTrans.amount < 0)
        elif sign == ">":
            query = query.filter(DriverTrans.amount > 0)

    results = query.group_by(DriverTrans.admin_name)\
                   .order_by(func.sum(func.abs(DriverTrans.amount) / 100).desc())\
                   .limit(10).all()

    return [
        {
            "admin_name": row[0],
            "total_amount": float(row[1])
        }
        for row in results if row[0]
    ]
    
    
def get_driver_inventory_count(payload):
    from_date_str = payload.from_date
    to_date_str = payload.to_date
    from_time_str = payload.from_time
    to_time_str = payload.to_time

    from_date_utc = convert_to_utc(from_date_str, from_time_str)
    to_date_utc = convert_to_utc(to_date_str, to_time_str)

    from_date = datetime.strptime(f"{from_date_utc[0]} {from_date_utc[1]}", '%Y-%m-%d %H:%M:%S')
    to_date = datetime.strptime(f"{to_date_utc[0]} {to_date_utc[1]}", '%Y-%m-%d %H:%M:%S')

    if from_date > to_date:
        raise ValueError("Invalid date range")

    regions = []
    if payload.search_region and payload.search_region != '-1':
        regions = [int(r) for r in payload.search_region.split(',')]

    inventory = {
        'S': 0, 'M': 0, 'L': 0, 'XL': 0, 'XXL': 0, 'XXXL': 0, 'Bags': 0,
    }

    transactions = (
        db.session.query(DriverTrans)
        .select_from(Drivers)
        .join(Users, Users.id == Drivers.user)
        .join(DriverTrans, DriverTrans.driver_id == Drivers.id)
        .filter(
            DriverTrans.method.in_(['T-Shirt', 'Registration', 'Bag']),
            DriverTrans.timestamp >= from_date,
            DriverTrans.timestamp <= to_date,
            Drivers.id.notin_(DriverTrans.excluded_driver_ids),
        )
    )
    if regions:
        transactions = transactions.filter(Users.region.in_(regions))

    transactions = transactions.all()

    for trans in transactions:
        desc = trans.description.strip() if trans.description else ""
        method = trans.method

        if method == 'T-Shirt' and '-' in desc:
            size, count_str = desc.split('-', 1)
            try:
                count = int(count_str)
            except ValueError:
                count = 0
            if size.upper() in inventory:
                inventory[size.upper()] += count

        elif method == 'Registration':
            tokens = [t.strip() for t in desc.split(',') if t.strip()]
            for token in tokens:
                if '-' in token:
                    code, count_str = token.split('-', 1)
                    try:
                        count = int(count_str)
                    except ValueError:
                        count = 0
                    if code.upper() == 'B':
                        inventory['Bags'] += count
                    elif code.upper() in inventory:
                        inventory[code.upper()] += count

        elif method == 'Bag':
            try:
                count = int(desc)
            except ValueError:
                count = 0
            inventory['Bags'] += count

    return {k: v for k, v in inventory.items() if v > 0}


def booking_summary_admin_service(payload):
    from_date_str, from_time_str = convert_to_utc(payload.from_date, payload.from_time)
    to_date_str, to_time_str = convert_to_utc(payload.to_date, payload.to_time)

    if from_date_str > to_date_str:
        return {'success': -3, 'error': 'Invalid date range'}, 400

    is_b2b = payload.is_b2b
    aff_ids = payload.aff_ids
    regions = payload.regions
    sort_asc = (payload.sort == 0)

    if payload.data_type == "Allocation":
        q = db.session.query(
            BookingAlloc.alloc_id.label("admin_id"),
            func.count(BookingAlloc.id).label("alloc_count")
        ).join(
            Bookings, BookingAlloc.booking_id == Bookings.id
        ).join(
            AdminAccess, AdminAccess.admin_user_id == BookingAlloc.alloc_id
        )

        if is_b2b == 0:
            q = q.filter(Bookings.type < BookingParams.TYPE_C24)
        else:
            q = q.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(Bookings.type == Bookings.TYPE_B2B)
            if aff_ids:
                q = q.filter(AffBookingLogs.aff_id.in_(aff_ids))

        q = q.filter(
            and_(
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            )
        )

        if regions:
            q = q.filter(Bookings.region.in_(regions))

        order_clause = func.count(BookingAlloc.id).asc() if sort_asc else func.count(BookingAlloc.id).desc()

        rows = q.group_by(BookingAlloc.alloc_id).order_by(order_clause).limit(10).all()
        data = [{"admin_name": get_name_by_id(r.admin_id), "counts": int(r.alloc_count)} for r in rows]
        return data, 200

    elif payload.data_type == "Cancellation":
        q = db.session.query(
            BookingCancelled.user.label("admin_id"),
            func.count(BookingCancelled.id).label("cancel_count")
        ).join(Bookings, BookingCancelled.booking == Bookings.id)

        if is_b2b == 0:
            q = q.filter(Bookings.type < BookingParams.TYPE_C24)
        else:
            q = q.join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id).filter(Bookings.type == Bookings.TYPE_B2B)
            if aff_ids:
                q = q.filter(AffBookingLogs.aff_id.in_(aff_ids))

        q = q.filter(
            Bookings.valid < 0,
            BookingCancelled.reason != BookingCancelled.RSN_REVERSE_CANCELLATION,
            BookingCancelled.cancel_source == BookingCancelled.SRC_ADMIN,
            and_(
                or_(
                    Bookings.startdate > from_date_str,
                    and_(Bookings.startdate == from_date_str, Bookings.starttime >= from_time_str)
                ),
                or_(
                    Bookings.startdate < to_date_str,
                    and_(Bookings.startdate == to_date_str, Bookings.starttime <= to_time_str)
                )
            )
        )

        if regions:
            q = q.filter(Bookings.region.in_(regions))

        order_clause = func.count(BookingCancelled.id).asc() if sort_asc else func.count(BookingCancelled.id).desc()

        rows = q.group_by(BookingCancelled.user).order_by(order_clause).limit(10).all()
        data = [{"admin_name": get_name_by_id(r.admin_id), "counts": int(r.cancel_count)} for r in rows]
        return  data, 200

    return {'success': -4, 'error': f'Unknown data_type={payload.data_type}'}, 400


def parse_regions(region_str):
    if region_str and region_str != Regions.ALL_REGIONS_ACCESS:
        return [int(r) for r in region_str.split(',')]
    return []


def fetch_locations(data_type, from_date, to_date, regions):
    # Original logic preserved, only extracted
    query = None

    if data_type == "Completed Bookings":
        query = db.session.query(Bookings.lat, Bookings.long).filter(
            Bookings.startdate >= from_date.date(),
            Bookings.startdate <= to_date.date(),
            Bookings.type < BookingParams.TYPE_C24,
            Bookings.valid.notin_([Bookings.CANCELLED_USER, Bookings.CANCELLED_DRIVER, Bookings.CANCELLED_D4M]),
            Bookings.region.in_(regions) if regions else True
        )

    elif data_type == "Cancelled Bookings":
        query = db.session.query(Bookings.lat, Bookings.long).filter(
            Bookings.startdate >= from_date.date(),
            Bookings.startdate <= to_date.date(),
            Bookings.type < BookingParams.TYPE_C24,
            Bookings.valid.in_([Bookings.CANCELLED_USER, Bookings.CANCELLED_DRIVER, Bookings.CANCELLED_D4M]),
            Bookings.region.in_(regions) if regions else True
        )

    elif data_type == "Maximum Searches":
        query = db.session.query(DriverSearch.reflat, DriverSearch.reflong).filter(
            DriverSearch.date >= from_date.date(),
            DriverSearch.date <= to_date.date(),
            DriverSearch.type < BookingParams.TYPE_C24,
            DriverSearch.region.in_(regions) if regions else True
        )

    elif data_type == "Active Drivers":
        subquery = db.session.query(
            DriverSetLoc.driver_id,
            func.date(DriverSetLoc.timestamp).label('date'),
            func.max(DriverSetLoc.timestamp).label('max_timestamp')
        ).filter(
            DriverSetLoc.timestamp >= from_date,
            DriverSetLoc.timestamp <= to_date
        ).group_by(
            DriverSetLoc.driver_id,
            func.date(DriverSetLoc.timestamp)
        ).subquery()

        query = db.session.query(
            DriverSetLoc.lat, DriverSetLoc.lng
        ).join(
            subquery,
            and_(
                DriverSetLoc.driver_id == subquery.c.driver_id,
                func.date(DriverSetLoc.timestamp) == subquery.c.date,
                DriverSetLoc.timestamp == subquery.c.max_timestamp
            )
        )

    elif data_type == "Home Drivers":
        query = db.session.query(
            DriverInfo.pres_addr_lat, DriverInfo.pres_addr_lng
        ).join(Drivers, DriverInfo.driver_id == Drivers.id) \
        .join(Users, Drivers.user == Users.id) \
        .filter(
            DriverInfo.timestamp >= from_date,
            DriverInfo.timestamp <= to_date,
            Users.region.in_(regions) if regions else True
        )

    elif data_type == "Reg Customers":
        query = db.session.query(
            UserRegistrationDetails.lat, UserRegistrationDetails.lng
        ).filter(
            UserRegistrationDetails.timestamp >= from_date,
            UserRegistrationDetails.timestamp <= to_date,
            UserRegistrationDetails.region.in_(regions) if regions else True
        )

    elif data_type == "Reg Drivers":
        query = db.session.query(
            DriverInfo.reg_lat, DriverInfo.reg_lng
        ).join(Drivers, DriverInfo.driver_id == Drivers.id) \
        .join(Users, Drivers.user == Users.id) \
        .filter(
            DriverInfo.timestamp >= from_date,
            DriverInfo.timestamp <= to_date,
            Users.region.in_(regions) if regions else True
        )

    elif data_type == "Active Customers":
        subquery = db.session.query(
            UserLoc.user_id,
            func.date(UserLoc.timestamp).label('date'),
            func.max(UserLoc.timestamp).label('max_timestamp')
        ).filter(
            UserLoc.timestamp >= from_date,
            UserLoc.timestamp <= to_date
        ).group_by(
            UserLoc.user_id,
            func.date(UserLoc.timestamp)
        ).subquery()

        query = db.session.query(
            UserLoc.lat, UserLoc.lng
        ).join(
            subquery,
            and_(
                UserLoc.user_id == subquery.c.user_id,
                func.date(UserLoc.timestamp) == subquery.c.date,
                UserLoc.timestamp == subquery.c.max_timestamp
            )
        )

    if not query:
        return None

    locations = query.all()
    return [
        {'coordinates': [lng, lat]}
        for lat, lng in locations
        if lat is not None and lng is not None and
        -90 <= lat <= 90 and -180 <= lng <= 180 and
        (not regions or Regions.find_region(lat, lng) in regions if data_type in ["Active Customers", "Active Drivers"] else True)
    ]
    
    
def get_live_routes_data(status_min: int) -> Tuple[List[dict], Dict[str, int]]:
    try:
        sql_records = db.session.query(Bookings.id, Bookings.driver, Trip.status)\
            .join(Trip, Bookings.id == Trip.book_id)\
            .filter(Bookings.valid == 1)\
            .filter(Trip.status > status_min)\
            .all()

        if not sql_records:
            return [], {}

        book_id_list = []
        book_meta = {}
        status_counts = {4: 0, 5: 0, 6: 0}

        for book_id, driver_id, status in sql_records:
            book_id_list.append(book_id)
            book_meta[book_id] = {
                "driver_id": driver_id,
                "trip_status": status
            }
            if status in status_counts:
                status_counts[status] += 1

        mongo_docs = list(AffiliateCollections.ongoing_booking_routes.find({
            "book_id": {"$in": book_id_list}
        }))

        final_data = []
        for doc in mongo_docs:
            book_id = doc.get("book_id")
            driver_id = doc.get("driver_id")
            trip_started = doc.get("trip_started", 0)
            history = doc.get("history", [])
            deviations = doc.get("deviations", [])
            last_known = doc.get("last_known", [])
            last_known_ts = doc.get("last_known_timestamp")

            final_data.append({
                "book_id": book_id,
                "driver_id": driver_id,
                "trip_status": book_meta.get(book_id, {}).get("trip_status", "Unknown"),
                "trip_started": trip_started,
                "history_count": len(history),
                "deviations_count": len(deviations),
                "last_known": last_known,
                "last_known_timestamp": last_known_ts.isoformat() if last_known_ts else None,
            })

        summary = {
            "status_4": status_counts[4],
            "status_5": status_counts[5],
            "status_6": status_counts[6],
            "total_documents": len(final_data)
        }

        return {"data":final_data,
                "summary": summary
                }
    except Exception:
        raise
    
def delete_ongoing_route_doc(book_id: int, driver_id: int) -> dict:
    result = AffiliateCollections.ongoing_booking_routes.delete_one({
        "book_id": book_id,
        "driver_id": driver_id
    })

    if result.deleted_count == 0:
        return {"success": 0, "message": "No document found to delete"}, 404

    return {"success": 1, "message": "Document deleted successfully"}, 200