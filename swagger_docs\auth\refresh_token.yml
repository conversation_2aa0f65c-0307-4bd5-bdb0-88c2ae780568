tags:
  - Authentication
summary: Refresh access token
description: |
  Refreshes the access token using the provided refresh token in the Authorization header. 
  Ensures token validity, user status, and token expiry before issuing a new access token.
consumes:
  - application/json
produces:
  - application/json
parameters:
  - name: Authorization
    in: header
    required: true
    type: string
    description: |
      Bearer token for refresh. Format: `Bearer <refresh_token>`
  - name: User-Agent
    in: header
    required: false
    type: string
    description: User-Agent string of the client making the request (optional, used for logging/tracking)
responses:
  200:
    description: Access token refreshed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Access token refreshed successfully."
        data:
          type: object
          properties:
            access_token:
              type: string
              example: "newly_generated_access_token"
            refresh_expiry:
              type: string
              example: "2025-08-15T10:30:00.000000"
  400:
    description: Missing or invalid token format
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid token format. Use 'Bearer <token>'."
  401:
    description: Invalid or expired token, user disabled, or not found
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Invalid refresh token. Please login again."
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "An unexpected error occurred. Please try again later."
