from datetime import datetime,timedelta   
from models.models import Users,Drivers,Bookings,Trip,db,BookPricing,BookingCancelled,UserCancelled,UserTrans,DriverCancelled,DriverTrans,DriverDetails,BookingAlloc
from conftest import unique_user_data,driver_bookings,driver_trip,create_user_and_driver,driver_details,create_user
from unittest.mock import patch
from sqlalchemy import exc
     
def compute_driver_wallet(driver_details, total_owed):
    driver_withdrawable, driver_wallet = driver_details.withdrawable, driver_details.wallet
    sum_left = driver_wallet + driver_withdrawable - total_owed
    if total_owed == 0:
        return driver_wallet, driver_withdrawable
    if total_owed < 0 and driver_wallet > 0:
        return driver_wallet, driver_withdrawable - total_owed
    if total_owed > 0 and total_owed < driver_wallet:
        return driver_wallet - total_owed, driver_withdrawable
    if sum_left > 0:
        return 0, sum_left
    return sum_left, 0    

#  API - /api/admin/change_cancellation_reason/new

def test_change_cancel_reason_incomplete_booking(client, admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '-1',
    }  
    response = client.post('/api/admin/change_cancellation_reason/new',data=form_data,headers=auth_headers)
    response.status_code==400
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['message'] == 'form details incomplete'
    
def test_change_cancel_reason_already_reversed(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id) 
    bc = BookingCancelled(
                user=1, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid=1,
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add(bc)
    bc.cancel_reversed=True
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'book_cancel_id':bc.id,
        'new_reason':'1',
        'new_reason_detail':""
        
    }   
    response = client.post('/api/admin/change_cancellation_reason/new',data=form_data,headers=auth_headers)
    response.status_code==400
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['message'] == 'Booking already reversed.'
    
def test_change_cancel_reason_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    data2 = unique_user_data()
    user=create_user(data2)
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings(user.id,driver.id) 
    uc = UserCancelled(user.id, driver_booking, 99)
    db.session.add(uc)
    ut = UserTrans(uid=user.id, amt=(100*99)*(-1), method="Cancellation charges for #" + str(driver_booking),
                            status=UserTrans.COMPLETED)
    dc = DriverCancelled(driver.id, driver_booking, 99)
    db.session.add(ut)
    db.session.add(dc)
    details_id=driver_details(driver.id)
    details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver.id)
    driver_detail = details.first()
    wallet, withdrawable = compute_driver_wallet(driver_detail, 99)
    dt = DriverTrans(driver.id, -99*100,
                            wall_a=wallet, wall_b=driver_detail.wallet,
                            with_a=withdrawable, with_b=driver_detail.withdrawable,
                                method="Cancellation: Booking %s" % str('DE56HUD'),
                            status=DriverTrans.COMPLETED, stop=True
                            )
    bc = BookingCancelled(
                user=1, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid=user.id,
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add(bc)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'book_cancel_id':bc.id,
        'new_reason':'0',
        'new_reason_detail':""
    }   
    response = client.post('/api/admin/change_cancellation_reason/new',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    print(json_data)
    assert json_data['success'] == 1
    assert json_data['message'] == 'Changed Cancel Reason Successfully'
 
# ---------------------------------     

#  API - /api/admin/change_unallocate_reason/new

def test_change_unalloc_reason_not_unalloc_booking(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id) 
    bc = BookingCancelled(
                user=1, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid=1,
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add(bc)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'book_cancel_id':bc.id,
        'new_reason':'1',
        'new_reason_detail':""
        
    } 
    response = client.post('/api/admin/change_unallocate_reason/new',data=form_data,headers=auth_headers)
    response.status_code==400
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['message'] == 'Booking is not of Unallocation.'
    
def test_change_unalloc_reason_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    data2 = unique_user_data()
    user=create_user(data2)
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings(user.id,driver.id) 
    uc = UserCancelled(user.id, driver_booking, 99)
    db.session.add(uc)
    ut = UserTrans(uid=user.id, amt=(100*99)*(-1), method="Cancellation charges for #" + str(driver_booking),
                            status=UserTrans.COMPLETED)
    dc = DriverCancelled(driver.id, driver_booking, 99)
    db.session.add(ut)
    db.session.add(dc)
    details_id=driver_details(driver.id)
    details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver.id)
    driver_detail = details.first()
    wallet, withdrawable = compute_driver_wallet(driver_detail, 99)
    dt = DriverTrans(driver.id, -99*100,
                            wall_a=wallet, wall_b=driver_detail.wallet,
                            with_a=withdrawable, with_b=driver_detail.withdrawable,
                                method="Cancellation: Booking %s" % str('DE56HUD'),
                            status=DriverTrans.COMPLETED, stop=True
                            )
    bc = BookingCancelled(
                user=1, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid=user.id,
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add(bc)
    bc.cancel_type=1
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'book_cancel_id':bc.id,
        'new_reason':'0',
        'new_reason_detail':""
    }   
    response = client.post('/api/admin/change_unallocate_reason/new',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert json_data['message'] == 'Changed Unallocate Reason Successfully'

# ---------------------------------     

#  API - /api/admin/cancel/charge_new
  
def test_cancel_charge_waiver_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1',driver.id) 
    ba = BookingAlloc(driver_booking, driver.id, '1')
    db.session.add(ba)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'reason':'2',
    }  
    response = client.post('/api/admin/cancel/charge_new',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert json_data['charge'] == [0,0] 
    
def test_cancel_charge_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1','2') 
    ba = BookingAlloc(driver_booking, driver.id, '1')
    db.session.add(ba)
    current_time = datetime.now()
    one_hour_before = current_time - timedelta(hours=1)
    ba.timestamp = one_hour_before
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'reason':'12',
    }  
    response = client.post('/api/admin/cancel/charge_new',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert json_data['charge'] == [99,99] 

# ---------------------------------     

#  API - /api/admin/cancel_update/charge
    
def test_cancel_update_charge_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    driver_user,driver=create_user_and_driver(data)
    driver_booking=driver_bookings('1','2') 
    ba = BookingAlloc(driver_booking, driver.id, '1')
    db.session.add(ba)
    bc = BookingCancelled(
                user=1, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                booking=driver_booking, 
                uid='1',
                did=driver.id, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add(bc)
    current_time = datetime.now()
    one_hour_before = current_time - timedelta(hours=1)
    ba.timestamp = one_hour_before
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    form_data = {
        'region': '-1',
        'cancel_id':bc.id,
        'new_reason':'8',
    }  
    response = client.post('/api/admin/cancel_update/charge',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    print(json_data)
    assert json_data['success'] == 1
    assert json_data['updatedcharge'] == [0,120] or [0,100]

# ---------------------------------     

#  API - /api/admin/cancel/new
    
    
def test_cancel_booking_success(client, admin_login):
    auth_headers, admin = admin_login   
    data = unique_user_data()
    data2 = unique_user_data()
    data3 = unique_user_data()
    created_user = create_user(data3)
    driver_user,driver=create_user_and_driver(data)
    driver_user2,driver2=create_user_and_driver(data2)
    driver_booking=driver_bookings(created_user.id,driver2.id) 
    booking=db.session.query(Bookings).filter(Bookings.id==driver_booking).first()
    booking.valid=1
    details_id=driver_details(driver2.id)
    ba = BookingAlloc(driver_booking, driver2.id, '1')
    db.session.add(ba)
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()  
    form_data = {
        'region': '-1',
        'booking_id':driver_booking,
        'reason':'1',
    } 
    response = client.post('/api/admin/cancel/new',data=form_data,headers=auth_headers)
    response.status_code==200
    json_data = response.get_json()
    print(json_data)
    assert json_data['success'] == 1
    assert json_data['message'] == 'Booking Cancelled Successfully'
    
# --------------------------------- 