-- MySQL dump 10.13  Distrib 8.0.37, for Linux (x86_64)
--
-- Host: drivers4me-prod-mysql8.cvlxcvdi769a.ap-south-1.rds.amazonaws.com    Database: driversprod
-- ------------------------------------------------------
-- Server version	8.0.32

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Current Database: `driversprod`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `driversprod` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `driversprod`;

--
-- Table structure for table `admin_log`
--

DROP TABLE IF EXISTS `admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_log` (
  `admin_log_id` int NOT NULL AUTO_INCREMENT,
  `admin_user_id` int NOT NULL,
  `admin_action` varchar(50) NOT NULL,
  `admin_content` longtext,
  `admin_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_log_id`),
  KEY `admin_log_ibfk_1` (`admin_user_id`),
  CONSTRAINT `admin_log_ibfk_1` FOREIGN KEY (`admin_user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=404422 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bhandari_bookings`
--

DROP TABLE IF EXISTS `bhandari_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bhandari_bookings` (
  `bd_book_id` int NOT NULL AUTO_INCREMENT,
  `bd_book_ref` int NOT NULL,
  `bd_appt_id` varchar(100) NOT NULL,
  `bd_veh_reg` varchar(100) NOT NULL,
  `bd_veh_mdl` varchar(500) NOT NULL,
  `bd_trip_type` int NOT NULL DEFAULT '0',
  `bd_drop_no` varchar(20) DEFAULT NULL,
  `bd_pc_rep` int NOT NULL,
  `bd_b_region` int NOT NULL DEFAULT '0',
  `bd_comment` varchar(10000) DEFAULT NULL,
  `bd_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`bd_book_id`),
  KEY `fk_bd_rep` (`bd_pc_rep`),
  KEY `fk_ctr_book` (`bd_book_ref`),
  CONSTRAINT `fk_bd_book` FOREIGN KEY (`bd_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_bd_rep` FOREIGN KEY (`bd_pc_rep`) REFERENCES `bhandari_rep` (`bd_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=22881 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bhandari_pic`
--

DROP TABLE IF EXISTS `bhandari_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bhandari_pic` (
  `bd_pic_id` int NOT NULL AUTO_INCREMENT,
  `bd_pic_book` int NOT NULL,
  `bd_pic_index` int NOT NULL,
  `bd_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`bd_pic_id`),
  KEY `bd_pic_ibfk_1` (`bd_pic_book`),
  CONSTRAINT `bd_pic_ibfk_1` FOREIGN KEY (`bd_pic_book`) REFERENCES `bhandari_bookings` (`bd_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2535 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bhandari_rep`
--

DROP TABLE IF EXISTS `bhandari_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bhandari_rep` (
  `bd_rep_id` int NOT NULL AUTO_INCREMENT,
  `bd_rep_cr` int NOT NULL,
  `bd_rep_mob` varchar(15) DEFAULT NULL,
  `bd_region` int NOT NULL DEFAULT '0',
  `bd_cr_timestamp` timestamp NOT NULL,
  `bd_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`bd_rep_id`),
  KEY `fk_ctr_cr` (`bd_rep_cr`),
  CONSTRAINT `fk_bd_cr` FOREIGN KEY (`bd_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bhandari_util`
--

DROP TABLE IF EXISTS `bhandari_util`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bhandari_util` (
  `bd_util_id` int NOT NULL AUTO_INCREMENT,
  `bd_util_book` int NOT NULL,
  `bd_util_index` int NOT NULL,
  `bd_util_url` varchar(1000) NOT NULL,
  `bd_util_amt` int NOT NULL,
  PRIMARY KEY (`bd_util_id`),
  KEY `bd_util_ibfk_1` (`bd_util_book`),
  CONSTRAINT `bd_util_ibfk_1` FOREIGN KEY (`bd_util_book`) REFERENCES `bhandari_bookings` (`bd_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `book_dest`
--

DROP TABLE IF EXISTS `book_dest`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_dest` (
  `dest_book_id` int NOT NULL,
  `dest_book_lat` float NOT NULL,
  `dest_book_long` float NOT NULL,
  `dest_book_name` varchar(200) NOT NULL,
  PRIMARY KEY (`dest_book_id`),
  CONSTRAINT `book_dest_ibfk_1` FOREIGN KEY (`dest_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `book_pending_v2`
--

DROP TABLE IF EXISTS `book_pending_v2`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_pending_v2` (
  `pending_book_id` int NOT NULL,
  `pending_driver_id` int NOT NULL,
  `pending_valid` int NOT NULL,
  `pending_phase` int NOT NULL DEFAULT '0',
  `pending_score` float DEFAULT '-99',
  UNIQUE KEY `pending_book_id` (`pending_book_id`,`pending_driver_id`),
  KEY `pending_driver_id` (`pending_driver_id`),
  CONSTRAINT `book_pending_v2_ibfk_1` FOREIGN KEY (`pending_book_id`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `book_pending_v2_ibfk_2` FOREIGN KEY (`pending_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `book_pricing`
--

DROP TABLE IF EXISTS `book_pricing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_pricing` (
  `book_pricing_book_id` int NOT NULL,
  `book_pricing_estimate` float NOT NULL,
  `book_pricing_base_ch` float NOT NULL,
  `book_pricing_cartype_ch` float NOT NULL,
  `book_pricing_night_ch` float NOT NULL,
  `book_pricing_food_ch` float NOT NULL,
  `book_pricing_booking_ch` float NOT NULL,
  `book_pricing_dist_ch` float NOT NULL,
  `book_pricing_estimate_pre_tax` int NOT NULL DEFAULT '0',
  `book_pricing_cgst` int NOT NULL DEFAULT '0',
  `book_pricing_sgst` int NOT NULL DEFAULT '0',
  `book_pricing_insurance_ch` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`book_pricing_book_id`),
  CONSTRAINT `book_book_pricing_ibfk_1` FOREIGN KEY (`book_pricing_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--  <NAME_EMAIL>
Alter table book_pricing Add Column book_pricing_driver_base_ch float not null;

Alter table book_pricing Add column book_pricing_driver_night_ch float not null;

/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `booking_alloc`
--

DROP TABLE IF EXISTS `booking_alloc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `booking_alloc` (
  `ba_id` int NOT NULL AUTO_INCREMENT,
  `ba_driver_id` int NOT NULL,
  `ba_book_id` int NOT NULL,
  `ba_alloc_id` int NOT NULL,
  `ba_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ba_action` int NOT NULL DEFAULT '0',
  `ba_pending_phase` int NOT NULL DEFAULT '-1',
  `ba_score` float DEFAULT '-99',
  PRIMARY KEY (`ba_id`),
  KEY `booking_alloc_ibfk_1` (`ba_driver_id`),
  KEY `booking_alloc_ibfk_2` (`ba_book_id`),
  KEY `booking_alloc_ibfk_3` (`ba_alloc_id`),
  CONSTRAINT `booking_alloc_ibfk_1` FOREIGN KEY (`ba_driver_id`) REFERENCES `drivers` (`driver_id`),
  CONSTRAINT `booking_alloc_ibfk_2` FOREIGN KEY (`ba_book_id`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `booking_alloc_ibfk_3` FOREIGN KEY (`ba_alloc_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=464992 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `booking_cancelled`
--

DROP TABLE IF EXISTS `booking_cancelled`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `booking_cancelled` (
  `b_cancelled_id` int NOT NULL AUTO_INCREMENT,
  `b_cancelled_uid` int DEFAULT NULL,
  `b_cancelled_source` int DEFAULT NULL,
  `b_cancelled_book_id` int DEFAULT NULL,
  `b_cancelled_penalty_user` int DEFAULT NULL,
  `b_cancelled_penalty_driver` int DEFAULT NULL,
  `b_cancelled_reason` int DEFAULT NULL,
  `b_cancelled_reason_detail` varchar(255) DEFAULT NULL,
  `b_cancelled_timestamp` datetime DEFAULT NULL,
  `b_cancelled_book_uid` int DEFAULT NULL,
  `b_cancelled_book_did` int DEFAULT NULL,
  `b_cancel_reversed` tinyint(1) DEFAULT NULL,
  `b_cancelled_utrans_id` varchar(40) DEFAULT NULL,
  `b_cancelled_dtrans_id` varchar(40) DEFAULT NULL,
  PRIMARY KEY (`b_cancelled_id`),
  KEY `b_cancelled_book_uid` (`b_cancelled_book_uid`),
  KEY `b_cancelled_book_did` (`b_cancelled_book_did`),
  KEY `fk_booking_cancelled_utrans` (`b_cancelled_utrans_id`),
  KEY `fk_booking_cancelled_dtrans` (`b_cancelled_dtrans_id`),
  CONSTRAINT `booking_cancelled_ibfk_1` FOREIGN KEY (`b_cancelled_book_did`) REFERENCES `drivers` (`driver_id`),
  CONSTRAINT `booking_cancelled_ibfk_2` FOREIGN KEY (`b_cancelled_book_uid`) REFERENCES `users` (`user_id`),
  CONSTRAINT `booking_cancelled_ibfk_3` FOREIGN KEY (`b_cancelled_book_uid`) REFERENCES `users` (`user_id`),
  CONSTRAINT `booking_cancelled_ibfk_4` FOREIGN KEY (`b_cancelled_book_did`) REFERENCES `drivers` (`driver_id`),
  CONSTRAINT `fk_booking_cancelled_dtrans` FOREIGN KEY (`b_cancelled_dtrans_id`) REFERENCES `driver_trans` (`driver_trans_id`),
  CONSTRAINT `fk_booking_cancelled_utrans` FOREIGN KEY (`b_cancelled_utrans_id`) REFERENCES `user_trans` (`user_trans_id`)
) ENGINE=InnoDB AUTO_INCREMENT=58159 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `booking_feedback`
--

DROP TABLE IF EXISTS `booking_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `booking_feedback` (
  `bf_book_id` int NOT NULL,
  `bf_book_fb` int NOT NULL DEFAULT '0',
  `bf_admin_id` int NOT NULL,
  PRIMARY KEY (`bf_book_id`),
  KEY `bf_admin_id` (`bf_admin_id`),
  CONSTRAINT `booking_feedback_ibfk_1` FOREIGN KEY (`bf_book_id`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `booking_feedback_ibfk_2` FOREIGN KEY (`bf_admin_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bookings`
--

DROP TABLE IF EXISTS `bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bookings` (
  `book_ref` int NOT NULL AUTO_INCREMENT,
  `book_user` int NOT NULL,
  `book_driver` int NOT NULL,
  `book_valid` int NOT NULL DEFAULT '1',
  `book_lat` float NOT NULL,
  `book_long` float NOT NULL,
  `book_starttime` time NOT NULL,
  `book_dur` time NOT NULL,
  `book_startdate` date NOT NULL,
  `book_endtime` time NOT NULL,
  `book_enddate` date NOT NULL,
  `book_estimate_price` float NOT NULL,
  `book_real_price` float NOT NULL,
  `book_user_rating` tinyint NOT NULL,
  `book_driver_rating` tinyint NOT NULL,
  `book_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `book_search_id` varchar(40) CHARACTER SET utf16 COLLATE utf16_bin DEFAULT NULL,
  `book_loc_name` text CHARACTER SET utf16 COLLATE utf16_bin,
  `book_type` int NOT NULL DEFAULT '1',
  `book_dur_days` int NOT NULL DEFAULT '0',
  `book_comment` varchar(1000) CHARACTER SET utf16 COLLATE utf16_bin DEFAULT NULL,
  `book_payment_type` int NOT NULL DEFAULT '0',
  `book_region` int NOT NULL DEFAULT '0',
  `book_estimate_pre_tax` int NOT NULL DEFAULT '0',
  `book_insurance` tinyint(1) NOT NULL DEFAULT '0',
  `book_code` varchar(50) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `book_insurance_cost` int NOT NULL DEFAULT '0',
  `book_cancelled_timestamp` timestamp NULL DEFAULT NULL,
  `book_insurance_num` int NOT NULL DEFAULT '1',
  `book_otp` varchar(10) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL DEFAULT '999999',
  `book_did` varchar(30) NOT NULL,
  `book_did_release` tinyint(1) NOT NULL,
  PRIMARY KEY (`book_ref`),
  UNIQUE KEY `book_search_id` (`book_search_id`),
  KEY `book_user` (`book_user`),
  KEY `book_driver` (`book_driver`),
  KEY `bookings_ibfk_3` (`book_search_id`),
  KEY `book_code_idx` (`book_code`),
  KEY `book_region_idx` (`book_region`),
  KEY `book_startdate_idx` (`book_startdate`),
  KEY `book_enddate_idx` (`book_enddate`),
  KEY `book_type_idx` (`book_type`),
  KEY `book_valid_idx` (`book_valid`),
  CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`book_user`) REFERENCES `users` (`user_id`),
  CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`book_driver`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=484272 DEFAULT CHARSET=utf16 COLLATE=utf16_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `c24_bookings`
--

DROP TABLE IF EXISTS `c24_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `c24_bookings` (
  `ctf_book_id` int NOT NULL AUTO_INCREMENT,
  `ctf_book_ref` int NOT NULL,
  `ctf_appt_id` varchar(100) NOT NULL,
  `ctf_veh_reg` varchar(100) NOT NULL,
  `ctf_veh_mdl` varchar(500) NOT NULL,
  `ctf_trip_type` int NOT NULL DEFAULT '0',
  `ctf_drop_no` varchar(20) DEFAULT NULL,
  `ctf_pc_rep` int NOT NULL,
  `ctf_b_region` int NOT NULL DEFAULT '0',
  `ctf_comment` varchar(10000) DEFAULT NULL,
  PRIMARY KEY (`ctf_book_id`),
  KEY `fk_ctf_rep` (`ctf_pc_rep`),
  KEY `fk_ctr_book` (`ctf_book_ref`),
  CONSTRAINT `fk_ctf_rep` FOREIGN KEY (`ctf_pc_rep`) REFERENCES `c24_rep` (`ctf_rep_id`),
  CONSTRAINT `fk_ctr_book` FOREIGN KEY (`ctf_book_ref`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=5007 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `c24_pic`
--

DROP TABLE IF EXISTS `c24_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `c24_pic` (
  `ctf_pic_id` int NOT NULL AUTO_INCREMENT,
  `ctf_pic_book` int NOT NULL,
  `ctf_pic_index` int NOT NULL,
  `ctf_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`ctf_pic_id`),
  KEY `c24_pic_ibfk_1` (`ctf_pic_book`),
  CONSTRAINT `c24_pic_ibfk_1` FOREIGN KEY (`ctf_pic_book`) REFERENCES `c24_bookings` (`ctf_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26417 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `c24_rep`
--

DROP TABLE IF EXISTS `c24_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `c24_rep` (
  `ctf_rep_id` int NOT NULL AUTO_INCREMENT,
  `ctf_rep_cr` int NOT NULL,
  `ctf_rep_mob` varchar(15) DEFAULT NULL,
  `ctf_region` int NOT NULL DEFAULT '0',
  `ctf_cr_timestamp` timestamp NOT NULL,
  `ctf_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`ctf_rep_id`),
  KEY `fk_ctr_cr` (`ctf_rep_cr`),
  CONSTRAINT `fk_ctr_cr` FOREIGN KEY (`ctf_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `c24_util`
--

DROP TABLE IF EXISTS `c24_util`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `c24_util` (
  `ctf_util_id` int NOT NULL AUTO_INCREMENT,
  `ctf_util_book` int NOT NULL,
  `ctf_util_index` int NOT NULL,
  `ctf_util_url` varchar(1000) NOT NULL,
  `ctf_util_amt` int NOT NULL,
  PRIMARY KEY (`ctf_util_id`),
  KEY `c24_util_ibfk_1` (`ctf_util_book`),
  CONSTRAINT `c24_util_ibfk_1` FOREIGN KEY (`ctf_util_book`) REFERENCES `c24_bookings` (`ctf_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `my_operator_log`
--

DROP TABLE IF EXISTS `my_operator_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `my_operator_log` (
  `cl_id` int NOT NULL AUTO_INCREMENT,
  `cl_uid` varchar(50) NOT NULL,
  `cl_node_id` varchar(50) NOT NULL,
  `cl_timestamp` datetime NOT NULL,
  `cl_caller_id` varchar(15) NOT NULL,
  `cl_invalid_count` tinyint NOT NULL,
  `cl_call_success` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`cl_id`),
  UNIQUE KEY `cl_uid` (`cl_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=24239 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `call_req_log`
--

DROP TABLE IF EXISTS `call_req_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `call_req_log` (
  `crl_id` int NOT NULL AUTO_INCREMENT,
  `crl_book_code` varchar(15) NOT NULL,
  `crl_from_mobile` varchar(15) NOT NULL,
  `crl_to_mobile` varchar(15) NOT NULL,
  `crl_timestamp` datetime NOT NULL,
  PRIMARY KEY (`crl_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19283 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `caller_input`
--

DROP TABLE IF EXISTS `caller_input`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `caller_input` (
  `ci_id` int NOT NULL AUTO_INCREMENT,
  `ci_uid` varchar(50) NOT NULL,
  `ci_call_log_id` int NOT NULL,
  `ci_key_press` tinyint NOT NULL,
  `ci_book_code` varchar(15) NOT NULL,
  `ci_mobile` varchar(15) NOT NULL,
  PRIMARY KEY (`ci_id`),
  KEY `ci_call_log_id` (`ci_call_log_id`),
  CONSTRAINT `caller_input_ibfk_1` FOREIGN KEY (`ci_call_log_id`) REFERENCES `call_log` (`cl_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1257 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cardekho_bookings`
--

DROP TABLE IF EXISTS `cardekho_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cardekho_bookings` (
  `cd_book_id` int NOT NULL AUTO_INCREMENT,
  `cd_book_ref` int NOT NULL,
  `cd_appt_id` varchar(100) NOT NULL,
  `cd_veh_reg` varchar(100) NOT NULL,
  `cd_veh_mdl` varchar(500) NOT NULL,
  `cd_trip_type` int NOT NULL DEFAULT '0',
  `cd_drop_no` varchar(20) DEFAULT NULL,
  `cd_pc_rep` int NOT NULL,
  `cd_b_region` int NOT NULL DEFAULT '0',
  `cd_comment` varchar(10000) DEFAULT NULL,
  `cd_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`cd_book_id`),
  KEY `fk_cd_rep` (`cd_pc_rep`),
  KEY `fk_ctr_book` (`cd_book_ref`),
  CONSTRAINT `fk_cd_book` FOREIGN KEY (`cd_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_cd_rep` FOREIGN KEY (`cd_pc_rep`) REFERENCES `cardekho_rep` (`cd_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8140 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cardekho_pic`
--

DROP TABLE IF EXISTS `cardekho_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cardekho_pic` (
  `cd_pic_id` int NOT NULL AUTO_INCREMENT,
  `cd_pic_book` int NOT NULL,
  `cd_pic_index` int NOT NULL,
  `cd_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`cd_pic_id`),
  KEY `cd_pic_ibfk_1` (`cd_pic_book`),
  CONSTRAINT `cd_pic_ibfk_1` FOREIGN KEY (`cd_pic_book`) REFERENCES `cardekho_bookings` (`cd_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9141 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cardekho_rep`
--

DROP TABLE IF EXISTS `cardekho_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cardekho_rep` (
  `cd_rep_id` int NOT NULL AUTO_INCREMENT,
  `cd_rep_cr` int NOT NULL,
  `cd_rep_mob` varchar(15) DEFAULT NULL,
  `cd_region` int NOT NULL DEFAULT '0',
  `cd_cr_timestamp` timestamp NOT NULL,
  `cd_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`cd_rep_id`),
  KEY `fk_ctr_cr` (`cd_rep_cr`),
  CONSTRAINT `fk_cd_cr` FOREIGN KEY (`cd_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cardekho_util`
--

DROP TABLE IF EXISTS `cardekho_util`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cardekho_util` (
  `cd_util_id` int NOT NULL AUTO_INCREMENT,
  `cd_util_book` int NOT NULL,
  `cd_util_index` int NOT NULL,
  `cd_util_url` varchar(1000) NOT NULL,
  `cd_util_amt` int NOT NULL,
  PRIMARY KEY (`cd_util_id`),
  KEY `cd_util_ibfk_1` (`cd_util_book`),
  CONSTRAINT `cd_util_ibfk_1` FOREIGN KEY (`cd_util_book`) REFERENCES `cardekho_bookings` (`cd_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `deleted_user`
--

DROP TABLE IF EXISTS `deleted_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `deleted_user` (
  `deleted_id` int NOT NULL AUTO_INCREMENT,
  `deleted_user_id` int DEFAULT NULL,
  `deleted_user_reason` int DEFAULT NULL,
  `deleted_user_mobile` varchar(15) DEFAULT NULL,
  `deleted_user_newmobile` varchar(15) DEFAULT NULL,
  `deleted_user_timestamp` datetime DEFAULT NULL,
  PRIMARY KEY (`deleted_id`),
  KEY `fk_user` (`deleted_user_id`),
  CONSTRAINT `fk_user` FOREIGN KEY (`deleted_user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `download_campaign`
--

DROP TABLE IF EXISTS `download_campaign`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `download_campaign` (
  `dc_id` int NOT NULL AUTO_INCREMENT,
  `dc_campaign_str` varchar(150) NOT NULL,
  `dc_campaign_info` varchar(1000) NOT NULL,
  `dc_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`dc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=435 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `download_req`
--

DROP TABLE IF EXISTS `download_req`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `download_req` (
  `dr_id` int NOT NULL AUTO_INCREMENT,
  `dr_campaign_id` int NOT NULL,
  `dr_agent` longtext,
  `dr_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dr_redirect_target` varchar(100) NOT NULL,
  PRIMARY KEY (`dr_id`),
  KEY `download_req_ibfk_1` (`dr_campaign_id`),
  CONSTRAINT `download_req_ibfk_1` FOREIGN KEY (`dr_campaign_id`) REFERENCES `download_campaign` (`dc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=75559 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_bank`
--

DROP TABLE IF EXISTS `driver_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_bank` (
  `db_id` int NOT NULL AUTO_INCREMENT,
  `db_driver_id` int NOT NULL,
  `db_acc_no` varchar(100) DEFAULT NULL,
  `db_ifsc` varchar(50) DEFAULT NULL,
  `db_pan_no` varchar(100) DEFAULT NULL,
  `db_acc_doc` tinytext,
  PRIMARY KEY (`db_id`),
  KEY `db_driver_id` (`db_driver_id`),
  CONSTRAINT `driver_bank_ibfk_1` FOREIGN KEY (`db_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=22119 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_base_loc`
--

DROP TABLE IF EXISTS `driver_base_loc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_base_loc` (
  `driver_loc_id` int NOT NULL,
  `driver_loc_lat` float DEFAULT NULL,
  `driver_loc_long` float DEFAULT NULL,
  `driver_loc_name` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`driver_loc_id`),
  CONSTRAINT `driver_base_loc_ibfk_1` FOREIGN KEY (`driver_loc_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_cancelled`
--

DROP TABLE IF EXISTS `driver_cancelled`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_cancelled` (
  `cancelled_id` int NOT NULL AUTO_INCREMENT,
  `cancelled_driver_id` int NOT NULL,
  `cancelled_book_id` int NOT NULL,
  `cancelled_penalty` int NOT NULL,
  `cancelled_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cancelled_id`),
  KEY `driver_cancelled_ibfk_1` (`cancelled_driver_id`,`cancelled_book_id`),
  KEY `driver_cancelled_ibfk_2` (`cancelled_book_id`),
  CONSTRAINT `driver_cancelled_ibfk_1` FOREIGN KEY (`cancelled_driver_id`) REFERENCES `drivers` (`driver_id`),
  CONSTRAINT `driver_cancelled_ibfk_2` FOREIGN KEY (`cancelled_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=66220 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_details`
--

DROP TABLE IF EXISTS `driver_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_details` (
  `details_driver_id` int NOT NULL,
  `details_driver_rides` int NOT NULL,
  `details_rating_rides` int DEFAULT NULL,
  `details_driver_hours` int NOT NULL,
  `details_driver_rating` int NOT NULL,
  `driver_details_earning` float NOT NULL,
  `driver_details_owed` float NOT NULL,
  `driver_details_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `driver_details_wallet` int NOT NULL DEFAULT '0',
  `driver_details_withdrawable` int NOT NULL DEFAULT '0',
  `driver_details_approval_ts` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`details_driver_id`),
  CONSTRAINT `driver_details_ibfk_1` FOREIGN KEY (`details_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_fcm`
--

DROP TABLE IF EXISTS `driver_fcm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_fcm` (
  `driver_fcm_id` int NOT NULL,
  `driver_fcm_token` varchar(200) NOT NULL DEFAULT '',
  `driver_fcm_device` varchar(15) NOT NULL DEFAULT 'android',
  PRIMARY KEY (`driver_fcm_id`,`driver_fcm_device`),
  CONSTRAINT `driver_fcm_ibfk_1` FOREIGN KEY (`driver_fcm_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_idle`
--

DROP TABLE IF EXISTS `driver_idle`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_idle` (
  `driver_idle_id` int NOT NULL AUTO_INCREMENT,
  `driver_idle_did` int NOT NULL,
  `driver_idle_date` date NOT NULL,
  PRIMARY KEY (`driver_idle_id`),
  KEY `driver_idle_ibfk_1` (`driver_idle_did`),
  CONSTRAINT `driver_idle_ibfk_1` FOREIGN KEY (`driver_idle_did`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_info`
--

DROP TABLE IF EXISTS `driver_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_info` (
  `di_id` int NOT NULL AUTO_INCREMENT,
  `di_driver_id` int NOT NULL,
  `di_dob` date DEFAULT NULL,
  `di_pres_addr` varchar(1000) DEFAULT NULL,
  `di_verf_name` varchar(1000) DEFAULT NULL,
  `di_verf_ph` varchar(100) DEFAULT NULL,
  `di_verf_rel` varchar(100) DEFAULT NULL,
  `di_behav` int DEFAULT NULL,
  `di_body` int DEFAULT NULL,
  `di_road` int DEFAULT NULL,
  `di_license` varchar(100) NOT NULL,
  `di_license_exp` date NOT NULL,
  `di_pres_addr_lat` float NOT NULL,
  `di_pres_addr_lng` float NOT NULL,
  `di_driver_id_doc_f` varchar(1000) NOT NULL,
  `di_driver_id_doc_b` varchar(1000) NOT NULL,
  `di_pres_region` varchar(100) NOT NULL,
  `di_pic` varchar(1000) NOT NULL,
  `di_driver_lic_doc_f` varchar(1000) NOT NULL,
  `di_driver_lic_doc_b` varchar(1000) NOT NULL,
  `di_id_no` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`di_id`),
  KEY `di_driver_id` (`di_driver_id`),
  CONSTRAINT `driver_info_ibfk_1` FOREIGN KEY (`di_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20002 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_loc`
--

DROP TABLE IF EXISTS `driver_loc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_loc` (
  `dl_id` int NOT NULL AUTO_INCREMENT,
  `dl_driver_id` int NOT NULL,
  `dl_lat` float NOT NULL,
  `dl_lng` float NOT NULL,
  `dl_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`dl_id`),
  KEY `dl_driver_id` (`dl_driver_id`),
  CONSTRAINT `driver_loc_ibfk_1` FOREIGN KEY (`dl_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12014 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_log`
--

DROP TABLE IF EXISTS `driver_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_log` (
  `driver_log_id` int NOT NULL AUTO_INCREMENT,
  `driver_did` int NOT NULL,
  `driver_action` varchar(50) NOT NULL,
  `driver_content` varchar(100) NOT NULL,
  `driver_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`driver_log_id`),
  KEY `driver_log_ibfk_1` (`driver_did`),
  CONSTRAINT `driver_log_ibfk_1` FOREIGN KEY (`driver_did`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1220209 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_paid`
--

DROP TABLE IF EXISTS `driver_paid`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_paid` (
  `driver_paid_id` int NOT NULL AUTO_INCREMENT,
  `driver_paid_did` int NOT NULL,
  `driver_paid_amt` float NOT NULL,
  `driver_paid_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `driver_paid_due` float DEFAULT NULL,
  `driver_paid_source` int NOT NULL DEFAULT '0',
  `driver_paid_source_type` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`driver_paid_id`),
  KEY `driver_paid_ibfk_1` (`driver_paid_did`),
  CONSTRAINT `driver_paid_ibfk_1` FOREIGN KEY (`driver_paid_did`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB AUTO_INCREMENT=164590 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_perma_info`
--

DROP TABLE IF EXISTS `driver_perma_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_perma_info` (
  `perm_driver_id` int NOT NULL,
  `perm_driver_alloc` int NOT NULL DEFAULT '0',
  `perm_driver_base` int NOT NULL DEFAULT '0',
  `perm_driver_ta` int NOT NULL DEFAULT '0',
  `perm_driver_ot` int NOT NULL DEFAULT '0',
  `perm_driver_hours` int NOT NULL DEFAULT '10',
  PRIMARY KEY (`perm_driver_id`),
  CONSTRAINT `driver_perma_info_ibfk_1` FOREIGN KEY (`perm_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_region`
--

DROP TABLE IF EXISTS `driver_region`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_region` (
  `driver_region_did` int NOT NULL,
  `driver_region_no` int NOT NULL,
  PRIMARY KEY (`driver_region_did`,`driver_region_no`),
  CONSTRAINT `driver_region_ibfk_1` FOREIGN KEY (`driver_region_did`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_search`
--

DROP TABLE IF EXISTS `driver_search`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_search` (
  `search_id` varchar(40) NOT NULL,
  `search_user_id` int NOT NULL,
  `search_reflat` float NOT NULL,
  `search_reflong` float NOT NULL,
  `search_cartype` int NOT NULL,
  `search_time` time NOT NULL,
  `search_date` date NOT NULL,
  `search_dur` time NOT NULL,
  `search_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `search_type` int NOT NULL DEFAULT '1',
  `search_dur_days` int NOT NULL DEFAULT '0',
  `search_dist` int NOT NULL DEFAULT '0',
  `search_insurance` tinyint(1) NOT NULL DEFAULT '0',
  `search_insurance_num` int NOT NULL DEFAULT '1',
  `search_region` int NOT NULL DEFAULT '0',
  `search_source` varchar(60) NOT NULL DEFAULT 'unknown',
  PRIMARY KEY (`search_id`),
  KEY `search_user_id` (`search_user_id`),
  CONSTRAINT `driver_search_ibfk_1` FOREIGN KEY (`search_user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_skill`
--

DROP TABLE IF EXISTS `driver_skill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_skill` (
  `ds_id` int NOT NULL AUTO_INCREMENT,
  `ds_driver_id` int NOT NULL,
  `ds_hb_m` int DEFAULT '0',
  `ds_sed_m` int DEFAULT '0',
  `ds_suv_m` int DEFAULT '0',
  `ds_lux_m` int DEFAULT '0',
  `ds_hb_a` int DEFAULT '0',
  `ds_sed_a` int DEFAULT '0',
  `ds_suv_a` int DEFAULT '0',
  `ds_lux_a` int DEFAULT '0',
  PRIMARY KEY (`ds_id`),
  KEY `ds_driver_id` (`ds_driver_id`),
  KEY `ds_driver_id_2` (`ds_driver_id`),
  CONSTRAINT `driver_skill_ibfk_1` FOREIGN KEY (`ds_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_trans`
--

DROP TABLE IF EXISTS `driver_trans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_trans` (
  `driver_trans_id` varchar(40) NOT NULL DEFAULT '',
  `driver_trans_uid` int NOT NULL,
  `driver_trans_amt` float NOT NULL,
  `driver_trans_method` varchar(60) DEFAULT NULL,
  `driver_trans_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `driver_trans_status` int NOT NULL DEFAULT '0',
  `driver_trans_starttime` timestamp NOT NULL,
  `driver_trans_stoptime` timestamp NULL DEFAULT NULL,
  `driver_trans_wallet_before` int NOT NULL DEFAULT '0',
  `driver_trans_wallet_after` int NOT NULL DEFAULT '0',
  `driver_trans_withdraw_before` int NOT NULL DEFAULT '0',
  `driver_trans_withdraw_after` int NOT NULL DEFAULT '0',
  `driver_trans_cash` int NOT NULL DEFAULT '0',
  `driver_trans_remarks` varchar(200) NOT NULL DEFAULT '',
  PRIMARY KEY (`driver_trans_id`),
  KEY `driver_trans_ibfk_1` (`driver_trans_uid`),
  CONSTRAINT `driver_trans_ibfk_1` FOREIGN KEY (`driver_trans_uid`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

DROP TABLE IF EXISTS `customer_credits_transaction_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;

CREATE TABLE `customer_credits_transaction_log` (
  `customer_credits_transaction_log_id` varchar(40) NOT NULL DEFAULT '',
  `customer_credits_transaction_log_uid` int NOT NULL,
  `customer_credits_transaction_log_amt` float NOT NULL,
  `customer_credits_transaction_log_transaction_type` varchar(60) DEFAULT '',
  `customer_credits_transaction_log_credit_type` varchar(60) DEFAULT NULL,
  `customer_credits_transaction_log_status` int NOT NULL DEFAULT '0',
  `customer_credits_transaction_log_starttime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `customer_credits_transaction_log_stoptime` timestamp NULL DEFAULT NULL,
  `customer_credits_transaction_log_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `customer_credits_transaction_log_remark` varchar(255) NOT NULL DEFAULT '',
  `customer_credits_transaction_log_name` varchar(255) DEFAULT NULL,
  `customer_credits_transaction_log_admin_id` int DEFAULT NULL,
  `customer_credits_transaction_log_tpid` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`customer_credits_transaction_log_id`),
  KEY `customer_credits_transaction_log_ibfk_1` (`customer_credits_transaction_log_uid`),
  CONSTRAINT `customer_credits_transaction_log_ibfk_1` FOREIGN KEY (`customer_credits_transaction_log_uid`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*!40101 SET character_set_client = @saved_cs_client */;


DROP TABLE IF EXISTS `driver_dues_transaction_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;

CREATE TABLE `driver_dues_transaction_log` (
  `driver_dues_transaction_log_id` varchar(40) NOT NULL DEFAULT '',
  `driver_dues_transaction_log_uid` int NOT NULL,
  `driver_dues_transaction_log_amt` float NOT NULL,
  `driver_dues_transaction_log_transaction_type` varchar(60) DEFAULT '',
  `driver_dues_transaction_log_due_type` varchar(60) DEFAULT NULL,
  `driver_dues_transaction_log_remarks` varchar(200) DEFAULT '',
  `driver_dues_transaction_log_status` int NOT NULL DEFAULT '0',
  `driver_dues_transaction_log_starttime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `driver_dues_transaction_log_stoptime` timestamp NULL DEFAULT NULL,
  `driver_dues_transaction_log_wallet_before` int NOT NULL DEFAULT '0',
  `driver_dues_transaction_log_wallet_after` int NOT NULL DEFAULT '0',
  `driver_dues_transaction_log_withdraw_before` int NOT NULL DEFAULT '0',
  `driver_dues_transaction_log_withdraw_after` int NOT NULL DEFAULT '0',
  `driver_dues_transaction_log_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `driver_dues_transaction_log_name` varchar(255) DEFAULT NULL,
  `driver_dues_transaction_log_tpid` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`driver_dues_transaction_log_id`),
  KEY `driver_dues_transaction_log_ibfk_1` (`driver_dues_transaction_log_uid`),
  CONSTRAINT `driver_dues_transaction_log_ibfk_1` FOREIGN KEY (`driver_dues_transaction_log_uid`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_verify`
--

DROP TABLE IF EXISTS `driver_verify`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `driver_verify` (
  `dv_id` int NOT NULL,
  `dv_ref` smallint NOT NULL DEFAULT '0',
  `dv_photo` smallint NOT NULL DEFAULT '0',
  `dv_lic` smallint NOT NULL DEFAULT '0',
  `dv_bank` smallint NOT NULL DEFAULT '0',
  `dv_id_card` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`dv_id`),
  CONSTRAINT `driver_verify_ibfk_1` FOREIGN KEY (`dv_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drivers`
--

DROP TABLE IF EXISTS `drivers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `drivers` (
  `driver_id` int NOT NULL AUTO_INCREMENT,
  `driver_user` int NOT NULL,
  `driver_license_no` varchar(50) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `driver_license_doc` tinytext CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `driver_pic` tinytext CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `driver_approved` tinyint(1) NOT NULL,
  `driver_available` tinyint(1) NOT NULL,
  `driver_rating` float NOT NULL,
  `driver_note` varchar(1000) CHARACTER SET utf16 COLLATE utf16_bin DEFAULT '',
  `driver_perma` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`driver_id`),
  UNIQUE KEY `driv_uid` (`driver_user`),
  UNIQUE KEY `driver_license_no` (`driver_license_no`),
  CONSTRAINT `drivers_ibfk_1` FOREIGN KEY (`driver_user`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26308 DEFAULT CHARSET=utf16 COLLATE=utf16_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gujral_bookings`
--

DROP TABLE IF EXISTS `gujral_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gujral_bookings` (
  `gujral_booking_id` int NOT NULL AUTO_INCREMENT,
  `gujral_book_ref` int NOT NULL,
  `gujral_booking_region` int NOT NULL DEFAULT '0',
  `gujral_booking_shift` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`gujral_booking_id`),
  KEY `gujral_book_ref` (`gujral_book_ref`),
  CONSTRAINT `gujral_bookings_ibfk_1` FOREIGN KEY (`gujral_book_ref`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=195 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mahindra_bookings`
--

DROP TABLE IF EXISTS `mahindra_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mahindra_bookings` (
  `mh_book_id` int NOT NULL AUTO_INCREMENT,
  `mh_book_ref` int NOT NULL,
  `mh_appt_id` varchar(100) NOT NULL,
  `mh_veh_reg` varchar(100) NOT NULL,
  `mh_veh_mdl` varchar(500) NOT NULL,
  `mh_trip_type` int NOT NULL DEFAULT '0',
  `mh_drop_no` varchar(20) DEFAULT NULL,
  `mh_pc_rep` int NOT NULL,
  `mh_b_region` int NOT NULL DEFAULT '0',
  `mh_comment` varchar(10000) DEFAULT NULL,
  `mh_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`mh_book_id`),
  KEY `fk_mh_rep` (`mh_pc_rep`),
  KEY `fk_ctr_book` (`mh_book_ref`),
  CONSTRAINT `fk_mh_book` FOREIGN KEY (`mh_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_mh_rep` FOREIGN KEY (`mh_pc_rep`) REFERENCES `mahindra_rep` (`mh_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6902 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mahindra_pic`
--

DROP TABLE IF EXISTS `mahindra_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mahindra_pic` (
  `mh_pic_id` int NOT NULL AUTO_INCREMENT,
  `mh_pic_book` int NOT NULL,
  `mh_pic_index` int NOT NULL,
  `mh_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`mh_pic_id`),
  KEY `mh_pic_ibfk_1` (`mh_pic_book`),
  CONSTRAINT `mh_pic_ibfk_1` FOREIGN KEY (`mh_pic_book`) REFERENCES `mahindra_bookings` (`mh_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mahindra_rep`
--

DROP TABLE IF EXISTS `mahindra_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mahindra_rep` (
  `mh_rep_id` int NOT NULL AUTO_INCREMENT,
  `mh_rep_cr` int NOT NULL,
  `mh_rep_mob` varchar(15) DEFAULT NULL,
  `mh_region` int NOT NULL DEFAULT '0',
  `mh_cr_timestamp` timestamp NOT NULL,
  `mh_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`mh_rep_id`),
  KEY `fk_ctr_cr` (`mh_rep_cr`),
  CONSTRAINT `fk_mh_cr` FOREIGN KEY (`mh_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mobile_change`
--

DROP TABLE IF EXISTS `mobile_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mobile_change` (
  `mobile_change_pid` int NOT NULL AUTO_INCREMENT,
  `mobile_change_id` int DEFAULT NULL,
  `mobile_change_mobile` varchar(15) DEFAULT NULL,
  `mobile_change_new_mobile` varchar(15) DEFAULT NULL,
  `mobile_change_timestamp` datetime DEFAULT NULL,
  PRIMARY KEY (`mobile_change_pid`),
  KEY `fk_mobile_change_id` (`mobile_change_id`),
  CONSTRAINT `fk_mobile_change_id` FOREIGN KEY (`mobile_change_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `olx_bookings`
--

DROP TABLE IF EXISTS `olx_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `olx_bookings` (
  `olx_book_id` int NOT NULL AUTO_INCREMENT,
  `olx_book_ref` int NOT NULL,
  `olx_appt_id` varchar(100) NOT NULL,
  `olx_veh_reg` varchar(100) NOT NULL,
  `olx_veh_mdl` varchar(500) NOT NULL,
  `olx_trip_type` int NOT NULL DEFAULT '0',
  `olx_drop_no` varchar(20) DEFAULT NULL,
  `olx_pc_rep` int NOT NULL,
  `olx_b_region` int NOT NULL DEFAULT '0',
  `olx_comment` varchar(10000) DEFAULT NULL,
  `olx_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`olx_book_id`),
  KEY `fk_olx_rep` (`olx_pc_rep`),
  KEY `fk_ctr_book` (`olx_book_ref`),
  CONSTRAINT `fk_olx_book` FOREIGN KEY (`olx_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_olx_rep` FOREIGN KEY (`olx_pc_rep`) REFERENCES `olx_rep` (`olx_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2928 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `olx_pic`
--

DROP TABLE IF EXISTS `olx_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `olx_pic` (
  `olx_pic_id` int NOT NULL AUTO_INCREMENT,
  `olx_pic_book` int NOT NULL,
  `olx_pic_index` int NOT NULL,
  `olx_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`olx_pic_id`),
  KEY `olx_pic_ibfk_1` (`olx_pic_book`),
  CONSTRAINT `olx_pic_ibfk_1` FOREIGN KEY (`olx_pic_book`) REFERENCES `olx_bookings` (`olx_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2383 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `olx_rep`
--

DROP TABLE IF EXISTS `olx_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `olx_rep` (
  `olx_rep_id` int NOT NULL AUTO_INCREMENT,
  `olx_rep_cr` int NOT NULL,
  `olx_rep_mob` varchar(15) DEFAULT NULL,
  `olx_region` int NOT NULL DEFAULT '0',
  `olx_cr_timestamp` timestamp NOT NULL,
  `olx_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`olx_rep_id`),
  KEY `fk_ctr_cr` (`olx_rep_cr`),
  CONSTRAINT `fk_olx_cr` FOREIGN KEY (`olx_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `olx_util`
--

DROP TABLE IF EXISTS `olx_util`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `olx_util` (
  `olx_util_id` int NOT NULL AUTO_INCREMENT,
  `olx_util_book` int NOT NULL,
  `olx_util_index` int NOT NULL,
  `olx_util_url` varchar(1000) NOT NULL,
  `olx_util_amt` int NOT NULL,
  PRIMARY KEY (`olx_util_id`),
  KEY `olx_util_ibfk_1` (`olx_util_book`),
  CONSTRAINT `olx_util_ibfk_1` FOREIGN KEY (`olx_util_book`) REFERENCES `olx_bookings` (`olx_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_data_pt`
--

DROP TABLE IF EXISTS `payment_data_pt`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_data_pt` (
  `pt_trans_id` varchar(40) NOT NULL,
  `pt_order_id` varchar(255) NOT NULL,
  `pt_payment_id` varchar(60) DEFAULT NULL,
  `pt_description` varchar(150) DEFAULT NULL,
  `pt_method` varchar(100) DEFAULT NULL,
  `pt_status` varchar(50) DEFAULT NULL,
  `pt_amount` int DEFAULT '0',
  `pt_timestamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`pt_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_data_rp`
--

DROP TABLE IF EXISTS `payment_data_rp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_data_rp` (
  `rp_trans_id` varchar(40) NOT NULL,
  `rp_order_id` varchar(40) NOT NULL,
  `rp_payment_id` varchar(60) DEFAULT NULL,
  `rp_description` varchar(150) DEFAULT NULL,
  `rp_method` varchar(100) DEFAULT NULL,
  `rp_status` varchar(50) DEFAULT NULL,
  `rp_amount` int DEFAULT '0',
  `rp_timestamp` timestamp NULL DEFAULT NULL,
  `rp_credit_start_ts` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rp_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pridehonda_bookings`
--

DROP TABLE IF EXISTS `pridehonda_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pridehonda_bookings` (
  `ph_book_id` int NOT NULL AUTO_INCREMENT,
  `ph_book_ref` int DEFAULT NULL,
  `ph_appt_id` varchar(255) DEFAULT NULL,
  `ph_veh_reg` varchar(255) DEFAULT NULL,
  `ph_veh_mdl` varchar(255) DEFAULT NULL,
  `ph_trip_type` int DEFAULT NULL,
  `ph_drop_no` bigint DEFAULT NULL,
  `ph_pc_rep` int DEFAULT NULL,
  `ph_b_region` int DEFAULT NULL,
  `ph_dist` int DEFAULT NULL,
  `ph_comment` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ph_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pridehonda_pic`
--

DROP TABLE IF EXISTS `pridehonda_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pridehonda_pic` (
  `ph_pic_id` int NOT NULL AUTO_INCREMENT,
  `ph_pic_book` int DEFAULT NULL,
  `ph_pic_index` int DEFAULT NULL,
  `ph_pic_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ph_pic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pridehonda_rep`
--

DROP TABLE IF EXISTS `pridehonda_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pridehonda_rep` (
  `ph_rep_id` int NOT NULL AUTO_INCREMENT,
  `ph_name` varchar(255) DEFAULT NULL,
  `ph_rep_cr` int DEFAULT NULL,
  `ph_rep_mob` bigint DEFAULT NULL,
  `ph_region` int DEFAULT NULL,
  `ph_cr_timestamp` datetime DEFAULT NULL,
  PRIMARY KEY (`ph_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `referral_use`
--

DROP TABLE IF EXISTS `referral_use`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `referral_use` (
  `ru_id` int NOT NULL AUTO_INCREMENT,
  `ru_src_uid` int NOT NULL,
  `ru_dest_uid` int NOT NULL,
  `ru_src_user_trans` varchar(40) DEFAULT NULL,
  `ru_dest_user_trans` varchar(40) DEFAULT NULL,
  `ru_src_driver_trans` varchar(40) DEFAULT NULL,
  `ru_dest_driver_trans` varchar(40) DEFAULT NULL,
  `ru_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ru_id`),
  KEY `ru_src_uid` (`ru_src_uid`),
  KEY `ru_dest_uid` (`ru_dest_uid`),
  KEY `ru_src_trans` (`ru_src_user_trans`),
  KEY `ru_dest_trans` (`ru_dest_user_trans`)
) ENGINE=InnoDB AUTO_INCREMENT=2556 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `revv_bookings`
--

DROP TABLE IF EXISTS `revv_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `revv_bookings` (
  `revv_booking_id` int NOT NULL AUTO_INCREMENT,
  `revv_book_ref` int NOT NULL,
  `revv_booking_region` int NOT NULL DEFAULT '0',
  `revv_booking_shift` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`revv_booking_id`),
  KEY `revv_book_ref` (`revv_book_ref`),
  CONSTRAINT `revv_bookings_ibfk_1` FOREIGN KEY (`revv_book_ref`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=2865 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `revv_v2_bookings`
--

DROP TABLE IF EXISTS `revv_v2_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `revv_v2_bookings` (
  `revv_v2_book_id` int NOT NULL AUTO_INCREMENT,
  `revv_v2_book_ref` int NOT NULL,
  `revv_v2_appt_id` varchar(100) NOT NULL,
  `revv_v2_veh_reg` varchar(100) NOT NULL,
  `revv_v2_veh_mdl` varchar(500) NOT NULL,
  `revv_v2_trip_type` int NOT NULL DEFAULT '0',
  `revv_v2_drop_no` varchar(20) DEFAULT NULL,
  `revv_v2_pc_rep` int NOT NULL,
  `revv_v2_b_region` int NOT NULL DEFAULT '0',
  `revv_v2_comment` varchar(10000) DEFAULT NULL,
  `revv_v2_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`revv_v2_book_id`),
  KEY `fk_revv_v2_rep` (`revv_v2_pc_rep`),
  KEY `fk_ctr_book` (`revv_v2_book_ref`),
  CONSTRAINT `fk_revv_v2_book` FOREIGN KEY (`revv_v2_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_revv_v2_rep` FOREIGN KEY (`revv_v2_pc_rep`) REFERENCES `revv_v2_rep` (`revv_v2_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=528 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `revv_v2_pic`
--

DROP TABLE IF EXISTS `revv_v2_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `revv_v2_pic` (
  `revv_v2_pic_id` int NOT NULL AUTO_INCREMENT,
  `revv_v2_pic_book` int NOT NULL,
  `revv_v2_pic_index` int NOT NULL,
  `revv_v2_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`revv_v2_pic_id`),
  KEY `revv_v2_pic_ibfk_1` (`revv_v2_pic_book`),
  CONSTRAINT `revv_v2_pic_ibfk_1` FOREIGN KEY (`revv_v2_pic_book`) REFERENCES `revv_v2_bookings` (`revv_v2_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `revv_v2_rep`
--

DROP TABLE IF EXISTS `revv_v2_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `revv_v2_rep` (
  `revv_v2_rep_id` int NOT NULL AUTO_INCREMENT,
  `revv_v2_rep_cr` int NOT NULL,
  `revv_v2_rep_mob` varchar(15) DEFAULT NULL,
  `revv_v2_region` int NOT NULL DEFAULT '0',
  `revv_v2_cr_timestamp` timestamp NOT NULL,
  `revv_v2_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`revv_v2_rep_id`),
  KEY `fk_ctr_cr` (`revv_v2_rep_cr`),
  CONSTRAINT `fk_revv_v2_cr` FOREIGN KEY (`revv_v2_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `search_decline`
--

DROP TABLE IF EXISTS `search_decline`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `search_decline` (
  `decline_search_id` varchar(40) NOT NULL,
  `decline_driver_id` int NOT NULL,
  PRIMARY KEY (`decline_search_id`,`decline_driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `search_dest`
--

DROP TABLE IF EXISTS `search_dest`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `search_dest` (
  `dest_search_id` varchar(40) NOT NULL,
  `dest_search_lat` float NOT NULL,
  `dest_search_long` float NOT NULL,
  `dest_search_name` varchar(200) NOT NULL,
  PRIMARY KEY (`dest_search_id`),
  CONSTRAINT `search_dest_ibfk_1` FOREIGN KEY (`dest_search_id`) REFERENCES `driver_search` (`search_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `search_hold`
--

DROP TABLE IF EXISTS `search_hold`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `search_hold` (
  `hold_search_id` varchar(40) NOT NULL,
  `hold_driver_id` int NOT NULL,
  `hold_price` float NOT NULL,
  `hold_active` tinyint(1) NOT NULL DEFAULT '1',
  `hold_startdate` date NOT NULL,
  `hold_starttime` time NOT NULL,
  `hold_enddate` date NOT NULL,
  `hold_endtime` time NOT NULL,
  `hold_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`hold_search_id`,`hold_driver_id`),
  KEY `hold_driver_id` (`hold_driver_id`),
  CONSTRAINT `search_hold_ibfk_1` FOREIGN KEY (`hold_search_id`) REFERENCES `driver_search` (`search_id`),
  CONSTRAINT `search_hold_ibfk_2` FOREIGN KEY (`hold_driver_id`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `spinny_bookings`
--

DROP TABLE IF EXISTS `spinny_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spinny_bookings` (
  `sp_book_id` int NOT NULL AUTO_INCREMENT,
  `sp_book_ref` int NOT NULL,
  `sp_appt_id` varchar(100) NOT NULL,
  `sp_veh_reg` varchar(100) NOT NULL,
  `sp_veh_mdl` varchar(500) NOT NULL,
  `sp_trip_type` int NOT NULL DEFAULT '0',
  `sp_trip_type_detail` varchar(500) NOT NULL,
  `sp_business_func` varchar(500) NOT NULL,
  `sp_business_cat` varchar(500) NOT NULL,
  `sp_drop_no` varchar(20) DEFAULT NULL,
  `sp_pc_rep` int NOT NULL,
  `sp_b_region` int NOT NULL DEFAULT '0',
  `sp_comment` varchar(10000) DEFAULT NULL,
  `sp_dist` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`sp_book_id`),
  KEY `fk_sp_rep` (`sp_pc_rep`),
  KEY `fk_ctr_book` (`sp_book_ref`),
  CONSTRAINT `fk_sp_rep` FOREIGN KEY (`sp_pc_rep`) REFERENCES `spinny_rep` (`sp_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13703 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `spinny_pic`
--

DROP TABLE IF EXISTS `spinny_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spinny_pic` (
  `sp_pic_id` int NOT NULL AUTO_INCREMENT,
  `sp_pic_book` int NOT NULL,
  `sp_pic_index` int NOT NULL,
  `sp_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`sp_pic_id`),
  KEY `sp_pic_ibfk_1` (`sp_pic_book`),
  CONSTRAINT `sp_pic_ibfk_1` FOREIGN KEY (`sp_pic_book`) REFERENCES `spinny_bookings` (`sp_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `spinny_rep`
--

DROP TABLE IF EXISTS `spinny_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spinny_rep` (
  `sp_rep_id` int NOT NULL AUTO_INCREMENT,
  `sp_rep_cr` int NOT NULL,
  `sp_rep_mob` varchar(15) DEFAULT NULL,
  `sp_region` int NOT NULL DEFAULT '0',
  `sp_cr_timestamp` timestamp NOT NULL,
  `sp_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`sp_rep_id`),
  UNIQUE KEY `unique_sp_rep_mob_region` (`sp_rep_mob`,`sp_region`),
  KEY `fk_ctr_cr` (`sp_rep_cr`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip`
--

DROP TABLE IF EXISTS `trip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trip` (
  `trip_id` int NOT NULL AUTO_INCREMENT,
  `trip_book` int NOT NULL,
  `trip_start` datetime DEFAULT NULL,
  `trip_stop` datetime DEFAULT NULL,
  `trip_price` float NOT NULL DEFAULT '0',
  `trip_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `trip_due` float NOT NULL DEFAULT '0',
  `trip_trans` varchar(80) DEFAULT NULL,
  `trip_comment` varchar(1000) DEFAULT NULL,
  `trip_sgst` int NOT NULL DEFAULT '0',
  `trip_cgst` int NOT NULL DEFAULT '0',
  `trip_price_pre_tax` int NOT NULL DEFAULT '0',
  `trip_start_lat` float NOT NULL DEFAULT '0',
  `trip_start_lng` float NOT NULL DEFAULT '0',
  `trip_stop_lng` float NOT NULL DEFAULT '0',
  `trip_stop_lat` float NOT NULL DEFAULT '0',
  `trip_status` int DEFAULT '0',
  `trip_otp` varchar(10) NOT NULL DEFAULT '999999',
  `trip_driver_trans` varchar(80) DEFAULT NULL,
  `trip_net_revenue` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`trip_id`),
  UNIQUE KEY `trip_book_2` (`trip_book`),
  KEY `trip_book` (`trip_book`),
  KEY `fk_trip_trans` (`trip_trans`),
  KEY `trip_status_idx` (`trip_status`),
  KEY `trip_stop_idx` (`trip_stop`),
  KEY `trip_start_idx` (`trip_start`),
  KEY `fk_trip_driver_trans` (`trip_driver_trans`),
  CONSTRAINT `fk_trip_driver_trans` FOREIGN KEY (`trip_driver_trans`) REFERENCES `driver_trans` (`driver_trans_id`),
  CONSTRAINT `fk_trip_trans` FOREIGN KEY (`trip_trans`) REFERENCES `user_trans` (`user_trans_id`),
  CONSTRAINT `trip_ibfk_1` FOREIGN KEY (`trip_book`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=268082 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_end_pic`
--

DROP TABLE IF EXISTS `trip_end_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trip_end_pic` (
  `te_id` int NOT NULL AUTO_INCREMENT,
  `te_book_id` int NOT NULL,
  `te_car_front` tinytext,
  `te_car_back` tinytext,
  `te_car_right` tinytext,
  `te_timestamp` timestamp NOT NULL,
  `te_car_left` tinytext,
  `te_extra1` tinytext,
  `te_extra2` tinytext,
  `te_extra3` tinytext,
  `te_extra4` tinytext,
  PRIMARY KEY (`te_id`),
  KEY `te_book_id` (`te_book_id`),
  CONSTRAINT `trip_end_ibfk_1` FOREIGN KEY (`te_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=203780 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_log`
--

DROP TABLE IF EXISTS `trip_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trip_log` (
  `tl_id` int NOT NULL AUTO_INCREMENT,
  `tl_driver_id` int NOT NULL,
  `tl_book_id` int NOT NULL,
  `tl_action_user` int NOT NULL,
  `tl_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tl_action` int NOT NULL DEFAULT '0',
  `tl_lng` float DEFAULT '-1',
  `tl_lat` float DEFAULT '-1',
  PRIMARY KEY (`tl_id`),
  KEY `trip_log_ibfk_1` (`tl_driver_id`),
  KEY `trip_log_ibfk_2` (`tl_book_id`),
  KEY `trip_log_ibfk_3` (`tl_action_user`),
  CONSTRAINT `trip_log_ibfk_1` FOREIGN KEY (`tl_driver_id`) REFERENCES `drivers` (`driver_user`),
  CONSTRAINT `trip_log_ibfk_2` FOREIGN KEY (`tl_book_id`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `trip_log_ibfk_3` FOREIGN KEY (`tl_action_user`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1409856 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_start_pic`
--

DROP TABLE IF EXISTS `trip_start_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trip_start_pic` (
  `ts_id` int NOT NULL AUTO_INCREMENT,
  `ts_book_id` int NOT NULL,
  `ts_car_front` tinytext,
  `ts_car_back` tinytext,
  `ts_car_left` tinytext,
  `ts_car_right` tinytext,
  `ts_selfie` tinytext,
  `ts_timestamp` timestamp NOT NULL,
  `ts_extra1` tinytext,
  `ts_extra2` tinytext,
  `ts_extra3` tinytext,
  `ts_extra4` tinytext,
  PRIMARY KEY (`ts_id`),
  KEY `ts_book_id` (`ts_book_id`),
  CONSTRAINT `trip_start_ibfk_1` FOREIGN KEY (`ts_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=195352 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_cancelled`
--

DROP TABLE IF EXISTS `user_cancelled`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_cancelled` (
  `u_cancelled_id` int NOT NULL AUTO_INCREMENT,
  `u_cancelled_uid` int NOT NULL,
  `u_cancelled_book_id` int NOT NULL,
  `u_cancelled_penalty` int NOT NULL,
  `u_cancelled_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`u_cancelled_id`),
  KEY `user_cancelled_ibfk_1` (`u_cancelled_uid`,`u_cancelled_book_id`),
  KEY `user_cancelled_ibfk_2` (`u_cancelled_book_id`),
  CONSTRAINT `user_cancelled_ibfk_1` FOREIGN KEY (`u_cancelled_uid`) REFERENCES `users` (`user_id`),
  CONSTRAINT `user_cancelled_ibfk_2` FOREIGN KEY (`u_cancelled_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB AUTO_INCREMENT=23160 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_fcm`
--

DROP TABLE IF EXISTS `user_fcm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_fcm` (
  `user_fcm_id` int NOT NULL,
  `user_fcm_token` varchar(200) NOT NULL DEFAULT '',
  `user_fcm_device` varchar(15) NOT NULL DEFAULT 'android',
  PRIMARY KEY (`user_fcm_id`,`user_fcm_device`),
  CONSTRAINT `user_fcm_ibfk_1` FOREIGN KEY (`user_fcm_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_loc`
--

DROP TABLE IF EXISTS `user_loc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_loc` (
  `ul_id` int NOT NULL AUTO_INCREMENT,
  `ul_user_id` int NOT NULL,
  `ul_lat` float NOT NULL,
  `ul_lng` float NOT NULL,
  `ul_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ul_id`),
  KEY `ul_user_id` (`ul_user_id`),
  CONSTRAINT `user_loc_ibfk_1` FOREIGN KEY (`ul_user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1942445 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_token`
--

DROP TABLE IF EXISTS `user_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_token` (
  `token_user_id` int NOT NULL,
  `token_refresh` varchar(200) NOT NULL,
  `token_expiry` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `token_agent` varchar(150) NOT NULL,
  `token_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`token_user_id`,`token_refresh`),
  CONSTRAINT `user_token_ibfk_1` FOREIGN KEY (`token_user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_trans`
--

DROP TABLE IF EXISTS `user_trans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_trans` (
  `user_trans_id` varchar(40) NOT NULL DEFAULT '',
  `user_trans_uid` int NOT NULL,
  `user_trans_amt` float NOT NULL,
  `user_trans_method` varchar(60) DEFAULT NULL,
  `user_trans_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_trans_status` int NOT NULL DEFAULT '0',
  `user_trans_starttime` timestamp NOT NULL,
  `user_trans_stoptime` timestamp NULL DEFAULT NULL,
  `user_trans_cash` int NOT NULL DEFAULT '0',
  `user_trans_remark` varchar(255) DEFAULT NULL,
  `user_trans_name` varchar(255) DEFAULT NULL,
  `user_trans_admin_id` int DEFAULT NULL,
  `user_trans_tpid` varchar(60) DEFAULT NOT NULL,
  PRIMARY KEY (`user_trans_id`),
  KEY `user_trans_ibfk_1` (`user_trans_uid`),
  KEY `fk_ut_admin_id` (`user_trans_admin_id`),
  CONSTRAINT `fk_ut_admin_id` FOREIGN KEY (`user_trans_admin_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `user_trans_ibfk_1` FOREIGN KEY (`user_trans_uid`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `username_map`
--

DROP TABLE IF EXISTS `username_map`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `username_map` (
  `um_uid` int NOT NULL,
  `um_uname` varchar(50) NOT NULL,
  PRIMARY KEY (`um_uid`),
  CONSTRAINT `username_map_ibfk_1` FOREIGN KEY (`um_uid`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `user_id` int NOT NULL AUTO_INCREMENT COMMENT 'Auto-generated ID for user',
  `user_mobile` varchar(15) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL COMMENT 'Mobile number - unique / secondary key',
  `user_fname` tinytext CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `user_lname` tinytext CHARACTER SET utf16 COLLATE utf16_bin NOT NULL,
  `user_email` varchar(50) CHARACTER SET utf16 COLLATE utf16_bin DEFAULT NULL COMMENT 'Email - not mandatory',
  `user_sex` tinytext CHARACTER SET utf16 COLLATE utf16_bin COMMENT 'Sex - M / F / O',
  `user_password` text CHARACTER SET utf16 COLLATE utf16_bin NOT NULL COMMENT 'Password',
  `user_pwd_salt` tinytext CHARACTER SET utf16 COLLATE utf16_bin NOT NULL COMMENT 'Password salt',
  `user_registration` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_role` tinyint NOT NULL DEFAULT '0',
  `user_acc_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `user_credit` float NOT NULL DEFAULT '0',
  `user_region` int NOT NULL DEFAULT '0',
  `user_restore_id` varchar(200) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL DEFAULT '',
  `user_ref_code` varchar(10) CHARACTER SET utf16 COLLATE utf16_bin NOT NULL DEFAULT '',
  `user_marked` tinyint(1) NOT NULL DEFAULT '0',
  `user_label_bv` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `mob_no` (`user_mobile`),
  UNIQUE KEY `user_email` (`user_email`)
) ENGINE=InnoDB AUTO_INCREMENT=216211 DEFAULT CHARSET=utf16 COLLATE=utf16_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zoomcar_bookings`
--

DROP TABLE IF EXISTS `zoomcar_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zoomcar_bookings` (
  `zc_book_id` int NOT NULL AUTO_INCREMENT,
  `zc_book_ref` int NOT NULL,
  `zc_appt_id` varchar(100) NOT NULL,
  `zc_veh_reg` varchar(100) NOT NULL,
  `zc_veh_mdl` varchar(500) NOT NULL,
  `zc_trip_type` int NOT NULL DEFAULT '0',
  `zc_drop_no` varchar(20) DEFAULT NULL,
  `zc_pc_rep` int NOT NULL,
  `zc_b_region` int NOT NULL DEFAULT '0',
  `zc_comment` varchar(10000) DEFAULT NULL,
  `zc_dist` int NOT NULL DEFAULT '0',
  `zc_state` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`zc_book_id`),
  KEY `fk_zc_rep` (`zc_pc_rep`),
  KEY `fk_ctr_book` (`zc_book_ref`),
  CONSTRAINT `fk_zc_book` FOREIGN KEY (`zc_book_ref`) REFERENCES `bookings` (`book_ref`),
  CONSTRAINT `fk_zc_rep` FOREIGN KEY (`zc_pc_rep`) REFERENCES `zoomcar_rep` (`zc_rep_id`)
) ENGINE=InnoDB AUTO_INCREMENT=174420 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zoomcar_pic`
--

DROP TABLE IF EXISTS `zoomcar_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zoomcar_pic` (
  `zc_pic_id` int NOT NULL AUTO_INCREMENT,
  `zc_pic_book` int NOT NULL,
  `zc_pic_index` int NOT NULL,
  `zc_pic_url` varchar(1000) NOT NULL,
  PRIMARY KEY (`zc_pic_id`),
  KEY `zc_pic_ibfk_1` (`zc_pic_book`),
  CONSTRAINT `zc_pic_ibfk_1` FOREIGN KEY (`zc_pic_book`) REFERENCES `zoomcar_bookings` (`zc_book_id`)
) ENGINE=InnoDB AUTO_INCREMENT=293988 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `call_log`
--

DROP TABLE IF EXISTS `call_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `call_log` (
  `chl_id` INT NOT NULL AUTO_INCREMENT,
  `chl_uuid` VARCHAR(40) NOT NULL,
  `chl_dest_num` VARCHAR(20) NULL,
  `chl_did_num` VARCHAR(20)  NULL,
  `chl_cust_num` VARCHAR(20) NULL,
  `chl_start_stamp` DATETIME NULL,
  `chl_answer_stamp` DATETIME NULL,
  `chl_end_stamp` DATETIME NULL,
  `chl_hangup_cause` VARCHAR(80) NULL,
  `chl_duration` INT NULL,
  `chl_call_status` VARCHAR(20) NULL,
  `chl_call_id` VARCHAR(40) NULL,
  `chl_call_connected` BOOLEAN  NULL,
  `chl_recording_url` VARCHAR(255) NULL,
  `chl_book_id` INT NOT NULL,
  PRIMARY KEY (`chl_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `zoomcar_rep`
--

DROP TABLE IF EXISTS `zoomcar_rep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zoomcar_rep` (
  `zc_rep_id` int NOT NULL AUTO_INCREMENT,
  `zc_rep_cr` int NOT NULL,
  `zc_rep_mob` varchar(15) DEFAULT NULL,
  `zc_region` int NOT NULL DEFAULT '0',
  `zc_cr_timestamp` timestamp NOT NULL,
  `zc_name` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`zc_rep_id`),
  KEY `fk_ctr_cr` (`zc_rep_cr`),
  CONSTRAINT `fk_zc_cr` FOREIGN KEY (`zc_rep_cr`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zoomcar_util`
--

DROP TABLE IF EXISTS `zoomcar_util`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zoomcar_util` (
  `zc_util_id` int NOT NULL AUTO_INCREMENT,
  `zc_util_book` int NOT NULL,
  `zc_util_index` int NOT NULL,
  `zc_util_url` varchar(1000) NOT NULL,
  `zc_util_amt` int NOT NULL,
  PRIMARY KEY (`zc_util_id`),
  KEY `zc_util_ibfk_1` (`zc_util_book`),
  CONSTRAINT `zc_util_ibfk_1` FOREIGN KEY (`zc_util_book`) REFERENCES `zoomcar_bookings` (`zc_book_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-07-03  3:01:11
--
--<NAME_EMAIL>
--


--
-- Table structure for table `d_approval_log`
--
CREATE TABLE `d_approval_log` (
 `d_approval_log_id` INT NOT NULL AUTO_INCREMENT,
 `d_approval_did` INT NOT NULL,
 `d_approval_changes` VARCHAR(50),
 `d_approval` INT,
 `d_approval_editedby` VARCHAR(50),
 `d_approval_from` VARCHAR(50),
 `d_approval_to` VARCHAR(50),
 `d_approval_remark` VARCHAR(100),
 `d_approval_timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
 PRIMARY KEY (`d_approval_log_id`),
 FOREIGN KEY (`d_approval_did`) REFERENCES `drivers` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


--
-- Inserting data into table `driver_skill` - Run only once in Prod
--


INSERT INTO `driver_skill` (`ds_driver_id`, `ds_hb_m`, `ds_sed_m`, `ds_suv_m`, `ds_lux_m`, `ds_hb_a`, `ds_sed_a`, `ds_suv_a`, `ds_lux_a`)
SELECT `driver_id`, 3, 3, 3, 3, 3, 3, 3, 3 FROM `drivers`;


--
-- Altering table `driver_info` to add a new column `di_description`
--


ALTER TABLE `driver_info`
ADD COLUMN `di_description` VARCHAR(1000);


--
-- Updating `di_description` in table `driver_info` - Run only once in Prod
--


UPDATE `driver_info`
SET `di_description` = 'He ensures a comfortable and secure ride for passengers.';


--
-- Altering table `users` to add a new column `user_alt_mobile`
--


ALTER TABLE `users`
ADD COLUMN `user_alt_mobile` VARCHAR(15) DEFAULT NULL;


--
-- Updating `user_alt_mobile` in table `users` - Run only once in Prod
--


UPDATE `users`
SET `user_alt_mobile` = `user_mobile`;


--
-- Altering table `driver_info` to add a new column `di_driver_trip_pref`
--


ALTER TABLE `driver_info`
ADD COLUMN `di_driver_trip_pref` INT NULL;


--
-- Updating `di_driver_trip_pref` in table `driver_info` - Run only once in Prod
--


UPDATE `driver_info`
SET `di_driver_trip_pref` = 3;

--
--<NAME_EMAIL>
--
/*booking cancellation new column for unallocation*/
ALTER TABLE booking_cancelled
ADD COLUMN b_cancelled_type int;


/*setting default all entries to 0 (Cancellation)*/
UPDATE booking_cancelled
SET b_cancelled_type = 0;


/*bookings new column for car_type*/
ALTER TABLE bookings
ADD COLUMN book_car_type int;


/*adding new column for customer remarks */
ALTER TABLE `users` ADD COLUMN `customer_remarks` VARCHAR(80) DEFAULT NULL;


/*updating it with car_type from driver_search for previous entries please check once before executing due to dependencies*/
UPDATE bookings b
JOIN driver_search ds ON b.book_search_id = ds.search_id
SET b.book_car_type = ds.search_cartype;


/*bookings new column for booking created at it doesn't update with any update in field as done in book_timestamp*/
ALTER TABLE bookings
ADD COLUMN book_created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP;


/*updating it with timestamp from driver_search for previous entries please check once before executing due to dependencies*/
UPDATE bookings b
JOIN driver_search ds ON b.book_search_id = ds.search_id
SET b.book_created_at = ds.search_timestamp;


/*created new table for customer_details_log */
CREATE TABLE c_details_log (
   c_details_log_id INT AUTO_INCREMENT PRIMARY KEY,
   c_details_uid INT NOT NULL,
   c_details_changes VARCHAR(50),
   c_details_editedby VARCHAR(50),
   c_details_from VARCHAR(50),
   c_details_to VARCHAR(50),
   c_details_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);


/*updating old entries of mobile change to customer_log table*/
INSERT INTO c_details_log (c_details_uid, c_details_changes, c_details_from, c_details_to, c_details_timestamp)
SELECT
   mobile_change_id AS c_details_uid,
   'Mobile' AS c_details_changes,
   mobile_change_mobile AS c_details_from,
   mobile_change_new_mobile AS c_details_to,
   mobile_change_timestamp AS c_details_timestamp
FROM
   mobile_change;


/*created new table for checking user id which are allowed to book multiple bookings at same time */
CREATE TABLE user_multiple_book (
   user_multiple_book_id INT AUTO_INCREMENT PRIMARY KEY,
   user_multiple_book_uid INT NOT NULL
);

--<NAME_EMAIL>

/*created new table for coupon to store general and personal coupon */
CREATE TABLE `coupons` (
	`coupon_id` INT NOT NULL AUTO_INCREMENT,
	`coupon_code` VARCHAR(60) NOT NULL,
	`coupon_mobile` VARCHAR(15) NOT NULL,
  `coupon_percent_off` FLOAT NOT NULL,
	`coupon_max_off` FLOAT NOT NULL,
	`coupon_flat_off` FLOAT NOT NULL,
  `coupon_min_trip_price` FLOAT NOT NULL,
	`coupon_min_trip` INT NOT NULL,
  `coupon_valid_till` DATE NOT NULL,
  `coupon_redeem_limit` INT NOT NULL,
	`coupon_state` TINYINT NOT NULL,
  `coupon_created_at` DATETIME NOT NULL,
	PRIMARY KEY (`coupon_id`)
);
 /*created new table for city in which coupon is applicable */
CREATE TABLE coupon_city_map (
	ccm_id INT AUTO_INCREMENT PRIMARY KEY,
	ccm_cid INT NOT NULL,
	ccm_coupon_city VARCHAR(50) NOT NULL,
	FOREIGN KEY (ccm_cid) REFERENCES coupons(coupon_id) ON DELETE CASCADE
);
ALTER TABLE `user_trans`  ADD COLUMN `user_trans_tpid` varchar(60) NOT NULL;
ALTER TABLE `user_trans`  ADD COLUMN `user_trans_description` varchar(200) NOT NULL;
ALTER TABLE `driver_trans`  ADD COLUMN `driver_trans_description` varchar(200) NOT NULL;

/*created new table for storing price in case of redis failure*/

CREATE TABLE pricing (
    price_id INT PRIMARY KEY AUTO_INCREMENT,
    price_city VARCHAR(30) NOT NULL,
    price_trip_type VARCHAR(30) NOT NULL,
    price_occasion JSON,
    price_night_fare JSON,
    price_basic_fare JSON NOT NULL,
    price_car_fare JSON NOT NULL,
    price_insurance_fare JSON,
    price_var_charges JSON,
    price_static_charges JSON
);

-- Create indexes for all columns
CREATE INDEX idx_price_city ON pricing (price_city);
CREATE INDEX idx_price_trip_type ON pricing (price_trip_type);
CREATE INDEX idx_price_occasion ON pricing ((CAST(price_occasion AS CHAR(255))));
CREATE INDEX idx_price_night_fare ON pricing ((CAST(price_night_fare AS CHAR(255))));
CREATE INDEX idx_price_basic_fare ON pricing ((CAST(price_basic_fare AS CHAR(255))));
CREATE INDEX idx_price_car_fare ON pricing ((CAST(price_car_fare AS CHAR(255))));
CREATE INDEX idx_price_insurance_fare ON pricing ((CAST(price_insurance_fare AS CHAR(255))));

/*created new table for storing admin call details*/

CREATE TABLE admin_call_log (
    acl_id INT NOT NULL AUTO_INCREMENT,
    acl_uuid VARCHAR(40) DEFAULT NULL,
    acl_dest_num VARCHAR(20) DEFAULT NULL,
    acl_did_num VARCHAR(20) DEFAULT NULL,
    acl_src_num VARCHAR(20) DEFAULT NULL,
    acl_start_stamp DATETIME DEFAULT NULL,
    acl_answer_stamp DATETIME DEFAULT NULL,
    acl_end_stamp DATETIME DEFAULT NULL,
    acl_hangup_cause VARCHAR(50) DEFAULT NULL,
    acl_duration INT DEFAULT NULL,
    acl_call_status VARCHAR(20) DEFAULT NULL,
    acl_call_id VARCHAR(40) DEFAULT NOT NULL,
    acl_call_connected TINYINT(1) DEFAULT NULL,
    acl_recording_url VARCHAR(255) DEFAULT NULL,
    acl_book_id INT NOT NULL,
    PRIMARY KEY (acl_id)
);


Additions from Ankan:

--
-- Table structure for table `admin_user_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_user_log` (
  `admin_user_log_id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `user_id` int NOT NULL,
  `admin_action` tinyint NOT NULL,
  `admin_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_user_log_id`),
  KEY `fk_admin_user_log_admin` (`admin_id`),
  KEY `fk_admin_user_log_user` (`user_id`),
  CONSTRAINT `fk_admin_user_log_admin` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_admin_user_log_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=404422 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_user_search`
--


/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_user_search` (
  `admin_user_search_id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `user_id` int NOT NULL,
  `admin_action` varchar(50) NOT NULL,
  `admin_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_user_search_id`),
  KEY `fk_admin_user_search_admin` (`admin_id`),
  KEY `fk_admin_user_search_user` (`user_id`),
  CONSTRAINT `fk_admin_user_search_admin` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_admin_user_search_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=404422 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_user_booking`
--


/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_user_booking` (
  `admin_user_booking_id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `user_id` int NOT NULL,
  `admin_action` varchar(50) NOT NULL,
  `admin_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_user_booking_id`),
  KEY `fk_admin_user_booking_admin` (`admin_id`),
  KEY `fk_admin_user_booking_user` (`user_id`),
  CONSTRAINT `fk_admin_user_booking_admin` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_admin_user_booking_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=404422 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
--<NAME_EMAIL>
--

--
-- Indexing for user_alt_mobile column `users table`
–


CREATE INDEX idx_user_alt_mobile ON users(user_alt_mobile);


--
-- Indexing for user_alt_mobile column `users table`
–


--
--<NAME_EMAIL>
--

CREATE UNIQUE INDEX idx_coupon_code ON coupons(coupon_code);


CREATE INDEX idx_coupon_mobile ON coupons(coupon_mobile);


CREATE INDEX idx_coupon_id ON coupons(coupon_id);

--<NAME_EMAIL>
CREATE INDEX idx_c_details_uid ON c_details_log (c_details_uid);


--
--<NAME_EMAIL>
--

CREATE TABLE estimate_remarks (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    remark VARCHAR(400) NOT NULL,
    who_is_writing VARCHAR(50) NOT NULL,
    created_at DATETIME NOT NULL,
    expiry_time DATETIME NOT NULL,
    display BOOLEAN NOT NULL,
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_expiry_time (expiry_time),
    INDEX idx_display (display)
);

ALTER TABLE driver_trans
ADD COLUMN driver_trans_name VARCHAR(255) DEFAULT NULL;


ALTER TABLE driver_trans
ADD COLUMN driver_trans_tpid VARCHAR(60) DEFAULT NULL;

--
--<NAME_EMAIL>
--


ALTER TABLE estimate_remarks
ADD COLUMN change_by_id INT NOT NULL;


ALTER TABLE estimate_remarks
CHANGE COLUMN who_is_writing change_by_name VARCHAR(50) NOT NULL;


create index idx_search_id on driver_search(search_id);
create index idx_search_user_id on driver_search(search_user_id);
create index idx_search_reflat on driver_search(search_reflat);
create index idx_search_reflong on driver_search(search_reflong);
create index idx_search_cartype on driver_search(search_cartype);
create index idx_search_time on driver_search(search_time);
create index idx_search_date on driver_search(search_date);
create index idx_search_dur on driver_search(search_dur);
create index idx_search_timestamp on driver_search(search_timestamp);
create index idx_search_type on driver_search(search_type);
create index idx_search_dur_days on driver_search(search_dur_days);
create index idx_search_dist on driver_search(search_dist);
create index idx_search_insurance on driver_search(search_insurance);
create index idx_search_insurance_num on driver_search(search_insurance_num);
create index idx_search_region on driver_search(search_region);
create index idx_search_source on driver_search(search_source);


ALTER TABLE d_approval_log MODIFY d_approval_from VARCHAR(1000);
ALTER TABLE d_approval_log MODIFY d_approval_to VARCHAR(1000);


CREATE TABLE booking_details_update (
    bd_id INT AUTO_INCREMENT PRIMARY KEY,
    bd_book_id INT NOT NULL,
    bd_changes VARCHAR(50) NOT NULL,
    bd_status INT,
    bd_edited_by INT NOT NULL,
    bd_editedby_name VARCHAR(50),
    bd_from VARCHAR(500),
    bd_to VARCHAR(500),
    bd_remark VARCHAR(500),
    bd_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

--<NAME_EMAIL>

/*driver_info new columns for id_verified and dl_verified setting default to -2 (Not Verified)*/
ALTER TABLE driver_info
ADD COLUMN di_id_verified INT DEFAULT -2,
ADD COLUMN di_lic_verified INT DEFAULT -2;


/*driver_bank new column for bank_verified setting default to -2 (Not Verified)*/
ALTER TABLE driver_bank
ADD COLUMN db_bank_verified INT DEFAULT -2,

/*created new table for adding driver id card details which are fetched from third party gridlines*/
CREATE TABLE grid_lines_iddoc (
   g_iddoc_id INT PRIMARY KEY AUTO_INCREMENT,
   g_iddoc_driver_id INT NOT NULL,
   g_driver_id_type INT NOT NULL DEFAULT 0,
   g_id_card_no VARCHAR(255) NOT NULL,
   g_driver_i_name TEXT NOT NULL,
   g_driver_i_gender TEXT,
   g_driver_i_pic VARCHAR(255),
   g_driver_i_dob DATE,
   g_driver_house VARCHAR(50),
   g_driver_street VARCHAR(255),
   g_driver_i_district VARCHAR(255),
   g_driver_sub_district VARCHAR(255),
   g_driver_landmark VARCHAR(255),
   g_driver_i_state VARCHAR(255),
   g_driver_i_pincode VARCHAR(10),
   g_driver_country VARCHAR(255),
   g_driver_vtc_name VARCHAR(255),
   g_driver_i_name_status BOOLEAN DEFAULT FALSE,
   g_driver_i_photo_status BOOLEAN DEFAULT FALSE,
   g_driver_i_photo_score FLOAT DEFAULT 0.0,
   g_driver_i_dob_status BOOLEAN DEFAULT FALSE,
   g_driver_i_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
   g_driver_i_json_dump JSON
);


/*created new table for adding driver bank details which are fetched from third party gridlines*/
CREATE TABLE grid_lines_bankdoc (
   g_bankdoc_id INT PRIMARY KEY AUTO_INCREMENT,
   g_bankdoc_driver_id INT NOT NULL,
   g_driver_b_name TEXT NOT NULL,
   g_driver_acc_no VARCHAR(255) NOT NULL,
   g_driver_ifsc VARCHAR(255) NOT NULL,
   g_driver_bank_name VARCHAR(255),
   g_driver_b_district VARCHAR(255),
   g_driver_bank_branch VARCHAR(255),
   g_driver_bank_name_status BOOLEAN DEFAULT FALSE,
   g_driver_bank_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
   g_driver_bank_json_dump JSON
);


/*created new table for adding driver license details which are fetched from third party gridlines*/
CREATE TABLE grid_lines_drivlicdoc (
   g_dl_id INT AUTO_INCREMENT PRIMARY KEY,
   g_dl_driver_id INT NOT NULL,
   g_dl_no VARCHAR(50) UNIQUE NOT NULL,
   g_driver_dl_name TEXT NOT NULL,
   g_driver_dl_pic VARCHAR(255),
   g_driver_dob DATE,
   g_driver_dl_addr VARCHAR(255),
   g_driver_dl_pincode VARCHAR(10),
   g_driver_dl_license_issue DATE,
   g_driver_dl_license_exp DATE,
   g_driver_dl_state VARCHAR(255),
   g_driver_dl_name_status BOOLEAN DEFAULT FALSE,
   g_driver_dl_photo_status BOOLEAN DEFAULT FALSE,
   g_driver_dl_photo_score FLOAT DEFAULT 0.0,
   g_driver_dl_dob_status BOOLEAN DEFAULT FALSE,
   g_driver_dl_licexp_status BOOLEAN DEFAULT FALSE,
   g_driver_dl_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
   g_driver_dl_json_dump JSON
);
--------
-- Adding indexes to the tables -- Index for driver_id in grid_lines_iddoc
CREATE INDEX idx_grid_lines_iddoc_driver_id ON grid_lines_iddoc
(g_iddoc_driver_id);


-- Index for driver_id in grid_lines_bankdoc
CREATE INDEX idx_grid_lines_bankdoc_driver_id ON grid_lines_bankdoc (g_bankdoc_driver_id);

-- Index for driver_id in grid_lines_drivlicdoc
CREATE INDEX idx_grid_lines_drivlicdoc_driver_id ON grid_lines_drivlicdoc (g_dl_driver_id);

--------

--
--<NAME_EMAIL>
--

CREATE TABLE admin_access (
   admin_id INT AUTO_INCREMENT PRIMARY KEY,
   admin_user_id INT NOT NULL,
   admin_tab_access BIGINT NOT NULL DEFAULT 0,
   admin_regions_access BIGINT NOT NULL DEFAULT 0,
   admin_notification_access BIGINT NOT NULL DEFAULT 0,
   calling_number VARCHAR(15),
   agent_id VARCHAR(50),
   created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
   recent_edited_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   FOREIGN KEY (admin_user_id) REFERENCES users(user_id)
);

CREATE TABLE admin_logs (
   log_id INT AUTO_INCREMENT PRIMARY KEY,
   admin_id INT NOT NULL,
   admin_user_id INT NOT NULL,
   action INT NOT NULL, 
   changed_by INT NOT NULL,
   changed_by_name VARCHAR(100) NOT NULL,
   created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
   oldvalue TEXT,
   newvalue TEXT,
   remark TEXT,
   change_type INT NOT NULL
);


ALTER TABLE admin_logs ADD CONSTRAINT fk_admin_id FOREIGN KEY (admin_id) REFERENCES admin_access(admin_id);
ALTER TABLE admin_logs ADD CONSTRAINT fk_admin_user_id FOREIGN KEY (admin_user_id) REFERENCES users(user_id);
ALTER TABLE admin_logs ADD CONSTRAINT fk_changed_by FOREIGN KEY (changed_by) REFERENCES users(user_id);


--<NAME_EMAIL>

--Added for pytest table creation please check once

ALTER TABLE driverstest.user_token MODIFY token_expiry TIMESTAMP NULL DEFAULT NULL;
--------

--
--<NAME_EMAIL>
–

INSERT INTO admin_access (admin_user_id, admin_tab_access, admin_regions_access, admin_notification_access, calling_number, agent_id, created_at, recent_edited_at)
VALUES
   (10, 0, 0, 0, NULL, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
   (2, 0, 0, 0, NULL, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
   (8, 0, 0, 0, NULL, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--
--<NAME_EMAIL>
–

alter table user_token add column token_login_from int default 0;
update user_token set token_login_from = 0;


--
--<NAME_EMAIL>
–
-- Table for customer register details
CREATE TABLE user_reg_details (
    u_r_id INT AUTO_INCREMENT PRIMARY KEY,
    u_r_user_id INT NOT NULL UNIQUE,
    u_r_lat FLOAT,
    u_r_lng FLOAT,
    u_r_city VARCHAR(255),
    u_r_address VARCHAR(255),
    u_r_source VARCHAR(50),
    u_r_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE c_details_log
MODIFY c_details_from VARCHAR(250),
MODIFY c_details_to VARCHAR(250);

-- Alter Driver info to add driver remark
ALTER TABLE driver_info 
ADD COLUMN di_remark VARCHAR(1000);

-- Alter User Reg to add region
ALTER TABLE user_reg_details ADD COLUMN u_r_region INTEGER;

--<NAME_EMAIL>
-- new table for affiliate rep logs
CREATE TABLE `affiliate` (
  `aff_id` INT NOT NULL AUTO_INCREMENT,
  `aff_name` TEXT NOT NULL UNIQUE,
  `aff_dname` TEXT,
  `aff_logo` TEXT,
  `aff_region` TEXT NOT NULL,
  `aff_slave` JSON,
  `aff_master` INT,
  `aff_admin` INT NOT NULL,
  `aff_notification` BIGINT,
  `aff_reg` DATETIME NOT NULL,
  `aff_wallet` FLOAT,
  `aff_wallet_th` FLOAT,
  `aff_enabled` BOOLEAN,
  PRIMARY KEY (`aff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE affiliate_rep_logs (
    aff_rep_log_id INT AUTO_INCREMENT PRIMARY KEY,
    aff_rep_log_rid INT NOT NULL,
    aff_rep_log_action INT NOT NULL, -- 0 for created, 1 for edited
    aff_rep_log_changed_by INT NOT NULL,
    aff_rep_log_changed_by_name VARCHAR(100) NOT NULL,
    aff_rep_log_changes_made TEXT NOT NULL,
    aff_rep_log_old_value TEXT NULL,
    aff_rep_log_new_value TEXT NULL,
    aff_rep_log_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE `aff_book_logs` (
  `a_b_log_id` int NOT NULL AUTO_INCREMENT,
  `a_b_rep_id` int DEFAULT NULL,
  `a_b_admin_id` int DEFAULT NULL,
  `a_b_aff_id` int NOT NULL,
  `a_b_book_id` int DEFAULT NULL,
  `a_b_alloc_id` int DEFAULT NULL,
  `a_b_cancel_id` int DEFAULT NULL,
  `a_b_log_timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `a_b_mapped_by` int DEFAULT NULL,
  `a_b_raised_by` int DEFAULT NULL,
  PRIMARY KEY (`a_b_log_id`),
  UNIQUE KEY `a_b_book_id` (`a_b_book_id`),
  UNIQUE KEY `a_b_alloc_id` (`a_b_alloc_id`),
  UNIQUE KEY `a_b_cancel_id` (`a_b_cancel_id`),
  KEY `idx_rep_id` (`a_b_rep_id`),
  KEY `idx_admin_id` (`a_b_admin_id`),
  KEY `idx_aff_id` (`a_b_aff_id`),
  KEY `idx_book_id` (`a_b_book_id`),
  KEY `idx_alloc_id` (`a_b_alloc_id`),
  KEY `idx_cancel_id` (`a_b_cancel_id`),
  KEY `idx_a_b_mapped_by` (`a_b_mapped_by`),
  KEY `idx_a_b_raised_by` (`a_b_raised_by`),
  CONSTRAINT `fk_alloc` FOREIGN KEY (`a_b_alloc_id`) REFERENCES `booking_alloc` (`ba_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_book` FOREIGN KEY (`a_b_book_id`) REFERENCES `bookings` (`book_ref`) ON DELETE CASCADE,
  CONSTRAINT `fk_mapped_by` FOREIGN KEY (`a_b_mapped_by`) REFERENCES `affiliate` (`aff_id`),
  CONSTRAINT `fk_raised_by` FOREIGN KEY (`a_b_raised_by`) REFERENCES `affiliate` (`aff_id`),
  CONSTRAINT `fk_cancel` FOREIGN KEY (`a_b_cancel_id`) REFERENCES `booking_cancelled` (`b_cancelled_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


--  <NAME_EMAIL>
-- table for new affiliate 
CREATE TABLE `affiliate_driver_search` (
  `search_id` VARCHAR(40) NOT NULL,
  `search_aff_id` INT NOT NULL,
  `search_rep_id` INT NOT NULL,
  `search_reflat` FLOAT NOT NULL,
  `search_reflong` FLOAT NOT NULL,
  `search_destlat` FLOAT NOT NULL,
  `search_destlong` FLOAT NOT NULL,
  `search_cartype` INT NOT NULL,
  `search_time` TIME NOT NULL,
  `search_date` DATE NOT NULL,
  `search_dur` TIME NOT NULL,
  `search_timestamp` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `search_type` INT NOT NULL DEFAULT 1,
  `search_trip_type` INT NOT NULL DEFAULT 1,
  `search_dur_days` INT NOT NULL DEFAULT 0,
  `search_dist` INT NOT NULL DEFAULT 0,
  `search_insurance` TINYINT(1) NOT NULL DEFAULT 0,
  `search_insurance_ch` FLOAT NOT NULL,
  `search_estimate` FLOAT NOT NULL,
  `search_cust_base_fare` FLOAT NOT NULL,
  `search_cust_night_fare` FLOAT NOT NULL,
  `search_driver_base_fare` FLOAT NOT NULL,
  `search_driver_night_fare` FLOAT NOT NULL,
  `search_region` INT NOT NULL DEFAULT 0,
  `search_source` VARCHAR(60) NOT NULL DEFAULT 'unknown',
  `search_price_id` INT NOT NULL
  PRIMARY KEY (`search_id`),
  KEY `search_aff_id` (`search_aff_id`),
  KEY `search_rep_id` (`search_rep_id`),
  KEY `idx_search_id` (`search_id`),
  KEY `idx_search_reflat` (`search_reflat`),
  KEY `idx_search_reflong` (`search_reflong`),
  KEY `idx_search_cartype` (`search_cartype`),
  KEY `idx_search_time` (`search_time`),
  KEY `idx_search_date` (`search_date`),
  KEY `idx_search_dur` (`search_dur`),
  KEY `idx_search_timestamp` (`search_timestamp`),
  KEY `idx_search_type` (`search_type`),
  KEY `idx_search_dur_days` (`search_dur_days`),
  KEY `idx_search_dist` (`search_dist`),
  KEY `idx_search_insurance` (`search_insurance`),
  KEY `idx_search_insurance_ch` (`search_insurance_ch`),
  KEY `idx_search_region` (`search_region`),
  KEY `idx_search_source` (`search_source`),
  CONSTRAINT `aff_driver_search_ibfk_1` FOREIGN KEY (`search_aff_id`) REFERENCES `affiliate` (`aff_id`),
  CONSTRAINT `aff_driver_search_ibfk_2` FOREIGN KEY (`search_rep_id`) REFERENCES `affiliate_rep` (`rep_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;




CREATE TABLE `affiliate_rep` (
  `rep_id` INT NOT NULL AUTO_INCREMENT,
  `rep_aff_id` INT NOT NULL,
  `rep_fullname` TEXT,
  `rep_username` TEXT UNIQUE NOT NULL,
  `rep_mobile` VARCHAR(15) NOT NULL,
  `rep_email` VARCHAR(40),
  `rep_password` TEXT NOT NULL,
  `rep_pwd_salt` TEXT NOT NULL,
  `rep_notification` BIGINT,
  `rep_tab` BIGINT,
  `rep_region` TEXT NOT NULL,
  `rep_admin` INT NOT NULL,
  `rep_first_login` BOOLEAN,
  `rep_enabled` BOOLEAN,
  `rep_registration` DATETIME NOT NULL,
  PRIMARY KEY (`rep_id`),
  KEY `idx_rep_aff_id` (`rep_aff_id`),
  KEY `idx_rep_username` (`rep_username`),
  KEY `idx_rep_mobile` (`rep_mobile`),
  KEY `idx_rep_email` (`rep_email`),
  CONSTRAINT `affiliate_rep_ibfk_1` FOREIGN KEY (`rep_aff_id`) REFERENCES `affiliate` (`aff_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `affiliate_token` (
  `token_rep_id` INT NOT NULL,
  `token_refresh` VARCHAR(200) NOT NULL,
  `token_expiry` DATETIME DEFAULT NULL,
  `token_agent` VARCHAR(150) NOT NULL,
  `token_timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`token_rep_id`, `token_refresh`),
  CONSTRAINT `affiliate_token_ibfk_1` FOREIGN KEY (`token_rep_id`) REFERENCES `affiliate_rep` (`rep_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `affiliate_spocs` (
  `spoc_id` INT NOT NULL AUTO_INCREMENT,
  `spoc_name` TEXT NOT NULL,
  `spoc_mobile` VARCHAR(15) NOT NULL,
  `spoc_aff_id` INT NOT NULL,
  `spoc_rep_id` INT NOT NULL,
  `spoc_type` INT NOT NULL,
  `spoc_reg` DATETIME NOT NULL,
  PRIMARY KEY (`spoc_id`),
  UNIQUE KEY `unique_spoc_mobile_rep_id` (`spoc_mobile`, `spoc_rep_id`, `spoc_type`),
  CONSTRAINT `affiliate_spocs_ibfk_1` FOREIGN KEY (`spoc_aff_id`) REFERENCES `affiliate` (`aff_id`) ON DELETE CASCADE,
  CONSTRAINT `affiliate_spocs_ibfk_2` FOREIGN KEY (`spoc_rep_id`) REFERENCES `affiliate_rep` (`rep_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `affiliate_addresses` (
  `aa_add_id` INT NOT NULL AUTO_INCREMENT,
  `aa_address` TEXT NOT NULL,
  `aa_nickname` VARCHAR(50) NOT NULL,
  `aa_latitude` FLOAT NOT NULL,
  `aa_longitude` FLOAT NOT NULL,
  `aa_add_type` INT NOT NULL,
  `aa_aff_id` INT NOT NULL,
  `aa_rep_id` INT NOT NULL,
  PRIMARY KEY (`aa_add_id`),
  UNIQUE KEY `unique_nickname_affid` (`aa_nickname`, `aa_rep_id`, `aa_add_type`),
  CONSTRAINT `affiliate_addresses_ibfk_1` FOREIGN KEY (`aa_aff_id`) REFERENCES `affiliate` (`aff_id`) ON DELETE CASCADE,
  CONSTRAINT `affiliate_addresses_ibfk_2` FOREIGN KEY (`aa_rep_id`) REFERENCES `affiliate_rep` (`rep_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `address_spocs` (
  `as_id` INT NOT NULL AUTO_INCREMENT,
  `as_spoc_id` INT NOT NULL,
  `as_spoc_name` TEXT NOT NULL,
  `as_address_id` INT NOT NULL,
  PRIMARY KEY (`as_id`),
  CONSTRAINT `address_spocs_ibfk_1` FOREIGN KEY (`as_spoc_id`) REFERENCES `affiliate_spocs` (`spoc_id`) ON DELETE CASCADE,
  CONSTRAINT `address_spocs_ibfk_2` FOREIGN KEY (`as_address_id`) REFERENCES `affiliate_addresses` (`aa_add_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE `affiliate_wallet_logs` (
  `awl_id` VARCHAR(80) NOT NULL,
  `awl_aff_id` INT DEFAULT NULL,
  `awl_rep_id` INT DEFAULT NULL,
  `awl_amount` FLOAT NOT NULL,
  `awl_method` VARCHAR(255) NOT NULL,
  `awl_status` INT NOT NULL,
  `awl_from_account` INT DEFAULT NULL,
  `awl_to_account` INT DEFAULT NULL,
  `awl_wallet_before` FLOAT NOT NULL,
  `awl_wallet_after` FLOAT NOT NULL,
  `awl_starttime` DATETIME DEFAULT NULL,
  `awl_stoptime` DATETIME DEFAULT NULL,
  `awl_order_id` VARCHAR(255) DEFAULT NULL,
  `awl_payment_id` VARCHAR(255) DEFAULT NULL,
  `awl_timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `awl_source` INT NOT NULL,
  `awl_admin` INT DEFAULT NULL,
  `awl_reason` VARCHAR(255) DEFAULT NULL,
  `awl_remark` TEXT DEFAULT NULL,
  PRIMARY KEY (`awl_id`),
  FOREIGN KEY (`awl_aff_id`) REFERENCES `affiliate` (`aff_id`),
  FOREIGN KEY (`awl_rep_id`) REFERENCES `affiliate_rep` (`rep_id`),
  FOREIGN KEY (`awl_admin`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- CREATE TABLE `affiliate_cancelled` (
--   `cancelled_id` INT NOT NULL AUTO_INCREMENT,
--   `cancelled_aff_id` INT NOT NULL,
--   `cancelled_rep_id` INT NOT NULL,
--   `cancelled_book_id` INT NOT NULL,
--   `cancelled_penalty` INT NOT NULL,
--   `cancelled_timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
--   PRIMARY KEY (`cancelled_id`),
--   CONSTRAINT `affiliate_cancelled_ibfk_1` FOREIGN KEY (`cancelled_aff_id`) REFERENCES `affiliate` (`aff_id`) ON DELETE CASCADE,
--   CONSTRAINT `affiliate_cancelled_ibfk_2` FOREIGN KEY (`cancelled_book_id`) REFERENCES `bookings` (`book_ref`) ON DELETE CASCADE
-- ) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE aff_tax_trans (
    tax_tid VARCHAR(80) PRIMARY KEY,
    tax_payer_gst VARCHAR(60),
    tax_receiver_gst VARCHAR(60) NOT NULL,
    tax_cgst_amt FLOAT NOT NULL DEFAULT 0,
    tax_sgst_amt FLOAT NOT NULL DEFAULT 0,
    tax_igst_amt FLOAT NOT NULL DEFAULT 0,
    tax_total_gst_amt FLOAT NOT NULL DEFAULT 0,
    tax_total_tds_amt FLOAT NOT NULL DEFAULT 0,
    CONSTRAINT `aff_tax_trans_ibfk_1` FOREIGN KEY (`tax_tid`) REFERENCES `affiliate_wallet_logs` (`awl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE client_gst_details (
    cgd_id INT PRIMARY KEY AUTO_INCREMENT,
    cgd_client_id INT NOT NULL,
    cgd_gst_no VARCHAR(60) NOT NULL,
    cgd_state_code INT NOT NULL,
    cgd_address VARCHAR(255),
    cgd_legal_name VARCHAR(255),
    cgd_trade_name VARCHAR(255),
    cgd_email VARCHAR(100),
    cgd_phone VARCHAR(20)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


-- add affiliate wallet logs in booking_cancelled to avoid breaking constraint
Alter table booking_cancelled add column b_cancelled_atrans_id varchar(80) after b_cancelled_utrans_id;
ALTER TABLE booking_cancelled ADD CONSTRAINT `fk_booking_cancelled_atrans` FOREIGN KEY (`b_cancelled_atrans_id`) REFERENCES `affiliate_wallet_logs` (`awl_id`);

-- add affiliate wallet logs in trip to avoid breaking constraint
Alter table trip add column trip_aff_trans varchar(80) after trip_trans;
	ALTER TABLE trip ADD CONSTRAINT `fk_trip_aff_trans` FOREIGN KEY (`trip_aff_trans`) REFERENCES `affiliate_wallet_logs` (`awl_id`);


-- remove not null from book_user in bookings
Alter table bookings drop foreign key bookings_ibfk_1;
	ALTER TABLE bookings MODIFY book_user INT NULL;

-- remove not null from ba_alloc_id from booking_alloc
ALTER TABLE booking_alloc DROP FOREIGN KEY booking_alloc_ibfk_3;
	ALTER TABLE booking_alloc MODIFY ba_alloc_id INT NULL;
	ALTER TABLE booking_alloc ADD CONSTRAINT `booking_alloc_ibfk_3` FOREIGN KEY (`ba_alloc_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;




-- Alter Driver Trans to add admin user id
ALTER TABLE driver_trans
ADD COLUMN driver_trans_admin_user_id INT NULL;

-- Alter Driver Info to add driver's registration latitude and longitude
ALTER TABLE driver_info 
ADD COLUMN driver_registration_lat FLOAT NULL,
ADD COLUMN driver_registration_lng FLOAT NULL;
ADD COLUMN driver_registration_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP;


-- Create table to store driver location every single time
CREATE TABLE driver_set_loc (
    dsl_id INT AUTO_INCREMENT PRIMARY KEY,
    dsl_driver_id INT NOT NULL,
    dsl_lat FLOAT NOT NULL,
    dsl_lng FLOAT NOT NULL,
    dl_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE driver_info ADD COLUMN di_driver_languages VARCHAR(100) DEFAULT '0';

ALTER TABLE driver_info ADD COLUMN di_r_source VARCHAR(50);


UPDATE driver_info
SET di_driver_trip_pref = CASE
  WHEN di_driver_trip_pref = 4 THEN 3
  WHEN di_driver_trip_pref = 2 THEN 1
  WHEN di_driver_trip_pref = 3 THEN 2
  ELSE di_driver_trip_pref
END;

ALTER TABLE driver_info
ADD COLUMN di_bike_status BOOLEAN DEFAULT FALSE;


--INDEXING
CREATE INDEX idx_userloc_timestamp ON user_loc (ul_timestamp);
CREATE INDEX idx_userloc_userid ON user_loc (ul_user_id);



--Update Timestamp
UPDATE driver_info di
JOIN drivers d ON di.driver_id = d.driver_id
JOIN users u ON d.driver_user = u.user_id
SET di.driver_registration_timestamp = u.user_registration;


CREATE TABLE aff_price_mapping (
    aff_price_map_id INT AUTO_INCREMENT PRIMARY KEY,
    aff_id INT,
    aff_mapped_region INT,
    aff_mapped_trip_type INT,
    aff_map_price_aff INT,
    aff_map_price_aff_name TEXT,
    FOREIGN KEY (aff_id) REFERENCES affiliate(aff_id),
    FOREIGN KEY (aff_map_price_aff) REFERENCES affiliate(aff_id)
);

CREATE INDEX idx_aff_id ON aff_price_mapping(aff_id);
CREATE INDEX idx_mapped_price_affiliate ON aff_price_mapping(aff_map_price_aff);


--
--<NAME_EMAIL>
--

ALTER TABLE users MODIFY COLUMN user_fname VARCHAR(255);
ALTER TABLE users MODIFY COLUMN user_lname VARCHAR(255);
CREATE FULLTEXT INDEX idx_users_fulltext ON users (user_fname, user_lname);
SHOW INDEX FROM users WHERE Index_type = 'FULLTEXT';

--if still showing issue debug by below and fix it

SHOW TABLE STATUS WHERE Name = 'users';

ALTER TABLE users ENGINE = InnoDB;

SELECT COLUMN_NAME, COLLATION_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 'users' 
AND COLUMN_NAME IN ('user_fname', 'user_lname');

--If they are not using utf8mb4_general_ci or utf8mb4_unicode_ci, change them:

ALTER TABLE users MODIFY COLUMN user_fname VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE users MODIFY COLUMN user_lname VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

--If you still face issues, let me know the output of:
SELECT COUNT(*) FROM users WHERE user_fname IS NULL OR user_lname IS NULL;
UPDATE users SET user_fname = '' WHERE user_fname IS NULL;
UPDATE users SET user_lname = '' WHERE user_lname IS NULL;

DROP INDEX idx_users_lname_lower ON users;
CREATE FULLTEXT INDEX idx_users_fulltext ON users (user_fname, user_lname);
CREATE INDEX idx_users_lname_lower ON users ((LOWER(user_lname)));


-- SpocLogs table definition
CREATE TABLE spoc_logs (
    spoc_id INT PRIMARY KEY AUTO_INCREMENT,
    rep_id INT,
    admin_id INT,
    add_source INT,  -- Mapping: 0 for admin, 1 for representative
    spoc_state INT,  -- Mapping: 0 for add, 1 for update, 2 for delete
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AddressLogs table definition
CREATE TABLE address_logs (
    address_id INT PRIMARY KEY AUTO_INCREMENT,
    rep_id INT,
    admin_id INT,
    add_source INT,  -- Mapping: 0 for admin, 1 for representative
    address_state INT,  -- Mapping: 0 for add, 1 for update, 2 for delete
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE `draftaffiliate` (
  `draft_id` int NOT NULL AUTO_INCREMENT,
  `draft_name` varchar(40) NOT NULL,
  `draft_dname` varchar(30) NOT NULL,
  `draft_logo` varchar(120) DEFAULT NULL,
  `draft_approved` int DEFAULT NULL,
  `draft_region` text,
  `draft_slave` json DEFAULT NULL,
  `draft_master` int DEFAULT NULL,
  `draft_admin` int NOT NULL,
  `draft_notification` bigint NOT NULL,
  `draft_reg` datetime DEFAULT NULL,
  `draft_wallet` float DEFAULT NULL,
  `is_favourite` tinyint DEFAULT '0',
  `draft_creator` varchar(40) DEFAULT NULL,
  `draft_title` varchar(40) DEFAULT NULL,
  `draft_wallet_th` float NOT NULL,
  PRIMARY KEY (`draft_id`),
  UNIQUE KEY `draft_name` (`draft_name`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci |


CREATE TABLE `affiliate_custom_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `aff_name` varchar(255) DEFAULT NULL,
  `category` varchar(60) DEFAULT NULL,
  `changed_from` json NOT NULL,
  `changed_to` json NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci |

CREATE TABLE `affiliate_logs` (
  `log_id` int NOT NULL AUTO_INCREMENT,
  `log_date_time` datetime NOT NULL,
  `changed_by` varchar(100) NOT NULL,
  `section` varchar(100) NOT NULL,
  `changes_made` text NOT NULL,
  `changed_from` varchar(255) DEFAULT NULL,
  `changed_to` varchar(255) DEFAULT NULL,
  `affiliate_foreign_log_id` int DEFAULT NULL,
  `aff_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=184 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci |

CREATE TABLE `affiliate_pricing_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `aff_name` text,
  `changed_from` json NOT NULL,
  `changed_to` json NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 

CREATE TABLE trip_pricing (
    trip_pricing_book_id INT PRIMARY KEY,
    trip_pricing_base_ch FLOAT NOT NULL,
    trip_pricing_cartype_ch FLOAT DEFAULT 0,
    trip_pricing_night_ch FLOAT NOT NULL,
    trip_pricing_ot_ch FLOAT NOT NULL,
    trip_pricing_booking_ch FLOAT NOT NULL,
    trip_pricing_dist_ch FLOAT DEFAULT 0,
    trip_pricing_cgst FLOAT DEFAULT 0,
    trip_pricing_sgst FLOAT DEFAULT 0,
    trip_pricing_insurance_ch FLOAT NOT NULL,
    trip_pricing_driver_base_ch FLOAT NOT NULL,
    trip_pricing_driver_night_ch FLOAT NOT NULL,
    trip_pricing_driver_ot_ch FLOAT NOT NULL,
    CONSTRAINT `trip_pricing_ibfk_1` FOREIGN KEY (`trip_pricing_book_id`) REFERENCES `bookings` (`book_ref`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

ALTER TABLE affiliate ADD COLUMN mapped_wallet_affiliate INT;
ALTER TABLE draftaffiliate ADD COLUMN draft_mapped_wallet_affiliate INT;


ALTER TABLE affiliate_driver_search DROP FOREIGN KEY aff_driver_search_ibfk_2;
ALTER TABLE affiliate_driver_search MODIFY search_rep_id INT NULL;

--
--<NAME_EMAIL>
--
-- add column for table `affiliate_book_logs`
--
ALTER TABLE affiliate_book_logs
ADD COLUMN a_b_comment VARCHAR(1000) NULL DEFAULT '';

CREATE TABLE scheduled_report(
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_type INT NOT NULL,
    frequency INT NOT NULL,
    duration INT NOT NULL,
    email_list JSON NOT NULL,
    regions JSON DEFAULT NULL,
    affiliates JSON DEFAULT NULL,
    subject VARCHAR(255) DEFAULT NULL,
    message TEXT DEFAULT NULL,
    status INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    last_sent_at DATETIME DEFAULT NULL,
    last_sent_status BOOLEAN DEFAULT NULL,
    last_sent_error TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)

ALTER TABLE affiliate_rep 
ADD COLUMN rep_cr_book_aff TEXT NULL;


-- query to update all existing rep to their affiliate and its slaves access

-- region wise filtering 

UPDATE affiliate_rep AS ar
JOIN (
  SELECT
    ar2.rep_id,
    CONCAT_WS(
      ',',
      ar2.rep_aff_id,
      GROUP_CONCAT(DISTINCT jt.slave_id ORDER BY jt.slave_id)
    ) AS access_list
  FROM affiliate_rep AS ar2

  -- split the rep’s regions CSV into rows
  LEFT JOIN JSON_TABLE(
    CONCAT('[', ar2.rep_region, ']'),
    '$[*]' COLUMNS(
      rep_region INT PATH '$'
    )
  ) AS rj ON TRUE

  -- join to the rep's own affiliate
  LEFT JOIN affiliate AS a
    ON a.aff_id = ar2.rep_aff_id

  -- split the affiliate's aff_slave JSON into rows (can be empty)
  LEFT JOIN JSON_TABLE(
    a.aff_slave,
    '$.slaves[*]' COLUMNS(
      slave_id INT PATH '$'
    )
  ) AS jt ON TRUE

  -- join each slave's affiliate data (could be NULL if no slaves)
  LEFT JOIN affiliate AS b
    ON b.aff_id = jt.slave_id

  -- split that slave's aff_region CSV and match with rep's region
  LEFT JOIN JSON_TABLE(
    CONCAT('[', IFNULL(b.aff_region, ''), ']'),
    '$[*]' COLUMNS(
      slave_region INT PATH '$'
    )
  ) AS bj
    ON bj.slave_region = rj.rep_region

  WHERE bj.slave_region IS NOT NULL OR jt.slave_id IS NULL  -- include rep's own aff_id if no valid slaves

  GROUP BY ar2.rep_id, ar2.rep_aff_id
) AS sub
  ON ar.rep_id = sub.rep_id

SET ar.rep_cr_book_aff = sub.access_list;


ALTER TABLE affiliate_rep
MODIFY COLUMN rep_email VARCHAR(100);

CREATE TABLE driver_strikes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    driver_id INT NOT NULL,
    reason INT NOT NULL,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    starts_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    book_id INT DEFAULT NULL,
    strike_by INT NOT NULL,
    resolved_by INT DEFAULT NULL,
    resolved_at DATETIME DEFAULT NULL,
    strike_weight INT NOT NULL,
    cool_down_period INT NOT NULL,
    fine FLOAT DEFAULT 0.0,

    FOREIGN KEY (reason) REFERENCES `strike_reasons` (`id`),
    FOREIGN KEY (book_id) REFERENCES `bookings` (`book_ref`),
    FOREIGN KEY (strike_by) REFERENCES `users` (`user_id`),
    FOREIGN KEY (resolved_by) REFERENCES `users` (`user_id`),
    FOREIGN KEY (driver_id) REFERENCES `drivers` (`driver_id`)
);

CREATE TABLE strike_reasons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    brief VARCHAR(100) NOT NULL,
    strike_weight INT NOT NULL DEFAULT 1,
    cool_down_period INT NOT NULL,
    fine FLOAT DEFAULT 0.0,
    created_by INT NOT NULL,
    FOREIGN KEY (created_by) REFERENCES `users` (`user_id`)
);

ALTER TABLE drivers ADD COLUMN driver_strike_count INT DEFAULT 0;
ALTER TABLE drivers ADD COLUMN driver_was_approved INT DEFAULT -1;


ALTER TABLE driver_details
ADD COLUMN driver_details_reactivation_ts TIMESTAMP DEFAULT NULL;

ALTER TABLE driver_details
ADD COLUMN driver_details_ride_activing_ts DATETIME DEFAULT NULL;

UPDATE driver_details dd
JOIN (
    SELECT 
        b.book_driver AS driver_id,
        MAX(t.trip_stop) AS last_trip_end
    FROM bookings b
    JOIN trip t ON t.trip_book = b.book_ref
    WHERE b.book_valid = 1
      AND t.trip_status = 0
    GROUP BY b.book_driver
) AS last_trips
ON dd.details_driver_id = last_trips.driver_id
SET dd.driver_details_ride_activing_ts = last_trips.last_trip_end;

DELIMITER //

CREATE TRIGGER trg_update_ride_activing_ts
BEFORE UPDATE ON driver_details
FOR EACH ROW
BEGIN
    IF NEW.details_driver_rides <> OLD.details_driver_rides THEN
        SET NEW.driver_details_ride_activing_ts = CURRENT_TIMESTAMP;
    END IF;
END;
//

DELIMITER ;

ALTER TABLE users ADD COLUMN user_bookcount INT DEFAULT 0;

ALTER TABLE driver_details ADD COLUMN driver_b2b_ride_count INT DEFAULT 0;

CREATE TABLE driver_selfies (
    ds_id INT AUTO_INCREMENT PRIMARY KEY,
    ds_driver_id INT NOT NULL,
    ds_labeled_by INT NOT NULL,
    ds_selfie VARCHAR(255) NOT NULL,
    ds_created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ds_book_id INT NOT NULL,
    ds_img_main_class INT NOT NULL,
    ds_img_color_class INT DEFAULT NULL,
    ds_fine_waived BOOLEAN DEFAULT FALSE,
    ds_strike_id INT DEFAULT NULL,
    FOREIGN KEY (ds_strike_id) REFERENCES `driver_strikes` (`id`)
);

CREATE TABLE strike_reason_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reason_id INT NOT NULL,
    changes VARCHAR(50),
    action_by INT NOT NULL,
    change_from VARCHAR(50),
    change_to VARCHAR(50),
    remark VARCHAR(100),
    action_type INT NOT NULL DEFAULT 0,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reason_id) REFERENCES `strike_reasons` (`id`),
    FOREIGN KEY (action_by) REFERENCES `users` (`user_id`)
);


CREATE TABLE `user_analytics_token` (
  `user_analytics_id` INT NOT NULL,
  `user_analytics_token` VARCHAR(255) NOT NULL DEFAULT '',
  `user_analytics_device` VARCHAR(15) NOT NULL DEFAULT 'android',
  PRIMARY KEY (`user_analytics_id`, `user_analytics_device`),
  CONSTRAINT `user_analytics_token_ibfk_1` 
    FOREIGN KEY (`user_analytics_id`) REFERENCES `users` (`user_id`)
);


CREATE TABLE `driver_analytics_token` (
  `driver_analytics_id` INT NOT NULL,
  `driver_analytics_token` VARCHAR(255) NOT NULL DEFAULT '',
  `driver_analytics_device` VARCHAR(15) NOT NULL DEFAULT 'android',
  PRIMARY KEY (`driver_analytics_id`, `driver_analytics_device`),
  CONSTRAINT `driver_analytics_token_ibfk_1` 
    FOREIGN KEY (`driver_analytics_id`) REFERENCES `drivers` (`driver_id`)
);
-- Add new column user_mobile_country_code to store the country code
ALTER TABLE users
ADD COLUMN user_mobile_country_code VARCHAR(15) NULL;

-- create columns for emergency contact and emergency country code 
ALTER TABLE users ADD COLUMN emergency_contact_country_code VARCHAR(10);
ALTER TABLE users ADD COLUMN emergency_contact_number VARCHAR(20);



-- app changes

-- by <EMAIL>

ALTER TABLE driver_search
DROP COLUMN search_insurance,
ADD COLUMN search_gear_type INT NOT NULL;


ALTER TABLE users
ADD COLUMN is_verified_email TINYINT(1) NOT NULL DEFAULT 0;



CREATE TABLE applied_coupon (
    ac_id INT AUTO_INCREMENT PRIMARY KEY,
    ac_user INT,
    ac_code_id INT,
    ac_code VARCHAR(255),
    ac_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE driver_search
ADD COLUMN search_coupon_id INTEGER DEFAULT NULL;


ALTER TABLE driver_search ADD COLUMN search_reloc text;
ALTER TABLE driver_search ADD COLUMN search_destlat FLOAT;
ALTER TABLE driver_search ADD COLUMN search_destlong FLOAT;
ALTER TABLE driver_search ADD COLUMN search_destloc text;


ALTER TABLE bookings
DROP COLUMN book_insurance,

ALTER TABLE book_pricing
ADD COLUMN book_pricing_driver_type_ch FLOAT DEFAULT 0,
ADD COLUMN book_pricing_coupon_discount FLOAT DEFAULT 0;

ALTER TABLE bookings ADD COLUMN book_immediate BOOLEAN DEFAULT FALSE;

START TRANSACTION;

INSERT INTO driver_search_history
SELECT * FROM driver_search
WHERE search_timestamp < NOW() - INTERVAL 2 YEAR;

DELETE FROM driver_search
WHERE search_timestamp < NOW() - INTERVAL 2 YEAR;

COMMIT;