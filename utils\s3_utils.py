#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  s3_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import boto3
from botocore.exceptions import ClientError

from flask import current_app as app

def upload_log_to_s3(log_str, prefix='logs/'):
    filename = f"{prefix}teardown_log_commit_failed.log"

    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    bucket = app.config["FLASKS3_BUCKET_NAME_REAL"]

    try:
        # Try to get existing log content
        existing_obj = s3.get_object(Bucket=bucket, Key=filename)
        existing_log = existing_obj['Body'].read().decode('utf-8')
    except C<PERSON><PERSON>rror as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            existing_log = ''  # No previous log file exists
        else:
            raise  # Re-raise if other error

    # Append new content
    combined_log = existing_log + log_str + "\n"

    # Upload back to S3 (overwrite)
    s3.put_object(
        Bucket=bucket,
        Key=filename,
        Body=combined_log.encode('utf-8'),
        ContentType='text/plain'
    )