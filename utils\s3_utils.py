#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  s3_utils.py
#
#  Copyright 2017-2025 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import boto3
import os
import base64
import uuid
from werkzeug.utils import secure_filename
from botocore.exceptions import ClientError
import io
from flask import current_app as app

pic_base_url = "https://storage.drivers4me.com/static/uploads/"

def upload_log_to_s3(log_str, prefix='logs/'):
    filename = f"{prefix}teardown_log_commit_failed.log"

    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    bucket = app.config["FLASKS3_BUCKET_NAME_REAL"]

    try:
        # Try to get existing log content
        existing_obj = s3.get_object(Bucket=bucket, Key=filename)
        existing_log = existing_obj['Body'].read().decode('utf-8')
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            existing_log = ''  # No previous log file exists
        else:
            raise  # Re-raise if other error

    # Append new content
    combined_log = existing_log + log_str + "\n"

    # Upload back to S3 (overwrite)
    s3.put_object(
        Bucket=bucket,
        Key=filename,
        Body=combined_log.encode('utf-8'),
        ContentType='text/plain'
    )
   
def _upload_pic_local(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], path)
    if path:
        os.makedirs(full_path, exist_ok=True)
    pic.save(os.path.join(full_path, filename))
    return filename

def _upload_pic_s3(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_S3'], path)
    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    s3.upload_fileobj(pic, app.config["FLASKS3_BUCKET_NAME_REAL"], os.path.join(full_path, filename))
    return filename

 
def upload_pic(pic, path=""):
    # pic must exist
    # TO-DO: validate pic
    extension = os.path.splitext(pic.filename)[1]
    if not pic:
        return False
    # and allowed_extension(extension):
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if not use_s3:
        filename = _upload_pic_local(pic, path, extension)
    else:
        filename = _upload_pic_s3(pic, path, extension)
    return filename

def _upload_pic_s3_base64(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_S3'], path)
    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    s3.upload_fileobj(pic, app.config["FLASKS3_BUCKET_NAME_REAL"], os.path.join(full_path, filename))
    return filename

def upload_pic_base64(pic, path="driver_docs"):
    if isinstance(pic, str):
        header, base64_data = pic.split(',', 1) if ',' in pic else (None, pic)
        extension = header.split('/')[1].split(';')[0] if header else 'png'  # Assuming png if no header
        pic = io.BytesIO(base64.b64decode(base64_data))
        pic.filename = f'upload.{extension}'  # Mock a filename for compatibility
    else:
        extension = os.path.splitext(pic.filename)[1]
    if not pic:
        return False
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if not use_s3:
        filename = _upload_pic_local(pic, path, extension)
    else:
        filename = _upload_pic_s3_base64(pic, path, extension)
    return filename