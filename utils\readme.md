# Utils Directory Documentation

This directory contains utility modules organized by functionality to support the Driver4Me application.

## Directory Structure

```
utils/
├── auth/                    # Authentication utilities
│   ├── admin_login_utils.py # Admin authentication and authorization
│   ├── login_params.py      # Login-related constants and parameters
│   └── login_utils.py       # General login utilities and token management
├── bookings/                # Booking-related utilities
│   ├── booking_params.py    # Booking constants, regions, and parameters
│   └── booking_utils.py     # Booking calculation utilities
├── profile/                 # User profile utilities
│   └── profile_utils.py     # Profile management and user utilities
├── driver/                  # Driver-specific utilities
│   └── driver_utils.py      # Driver wallet and verification utilities
├── admin_utils.py           # Admin role and tab management
├── distance_utils.py        # Distance and location calculations
├── redis_utils.py           # Redis cache operations
├── response_utils.py        # Standardized API response formatting
├── s3_utils.py              # AWS S3 file operations
├── security_utils.py        # Security and encryption utilities
├── slack_utils.py           # Slack notification integration
├── time_utils.py            # Date/time conversion and timezone utilities
├── user_utils.py            # User management utilities
└── validation_utils.py      # Data validation utilities
└── metric_utils.py          # Metric conversion utilities
└── fcm_utils.py             # Firebase Cloud Messaging utilities
```

## Main Utils Files

### admin_utils.py
**Purpose**: Admin role management and tab access control

**Classes**:
- `Tabs`: Defines admin panel tab constants (DRIVERS=1, CUSTOMERS=2, etc.)

**Constants**:
- `checkadmin`: List of admin roles
- `BIT_*`: Bit manipulation constants for permissions

### metric_utils.py
**Purpose**: Metric conversion utilities

**Functions**:
- `feet_and_inches_to_cm(feet, inches)`: Converts height from feet and inches to centimeters
- `convert_cm_to_feet_inches(cm)`: Converts height from centimeters to feet and inches
- `add_inches_to_cm(old_cm,additional_inches)`: Adds inches to existing height in centimeters

### fcm_utils.py
**Purpose**: Firebase Cloud Messaging utilities

**Functions**:
- `send_fcm_msg_driver(driver, title, smalltext, bigtext)`: Sends FCM message to driver


### distance_utils.py
**Purpose**: Geographic calculations and location validation

**Functions**:
- `is_valid_lat_long_world(lat, lon)`: Validates global coordinates
- `is_valid_lat_long_for_india(lat, lon)`: Validates Indian coordinates
- `get_locality_by_lat_long(lat, long)`: Gets location details from coordinates using MapMyIndia API
- `point_inside_polygon(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city)`: Checks if coordinates are within service area
- `point_inside(dest_long, dest_lat, src_long, src_lat, book_type, location_map, city)`: Internal polygon checking
- `check_booking_type(book_type, pointsrc, pointdest, polygon_city)`: Validates booking type against location constraints
- `get_dist_on_map(source_lat, source_long, dest_lat, dest_long)`: Gets driving distance using Google Maps API

### redis_utils.py
**Purpose**: Redis cache operations with fallback handling

**Functions**:
- `read_redis_data(key)`: Reads and deserializes JSON data from Redis
- `write_redis_data(key, data)`: Serializes and writes data to Redis
- `update_redis_data(key, data)`: Updates Redis data (alias for write)

### response_utils.py
**Purpose**: Standardized API response formatting

**Functions**:
- `standard_response(success, status, message, data=None, response_status=None)`: Creates consistent API response structure

### s3_utils.py
**Purpose**: AWS S3 file operations

**Functions**:
- `upload_log_to_s3(log_str, prefix='logs/')`: Uploads/appends log content to S3 bucket

### security_utils.py
**Purpose**: Security operations and token management

**Functions**:
- `gen_otp(n)`: Generates n-digit OTP
- `get_salt()`: Generates UUID-based salt
- `get_pwd(pwd, salt)`: Creates SHA512 password hash
- `add_token_to_blacklist(token_hash, expires)`: Adds token to Redis blacklist with TTL

### slack_utils.py
**Purpose**: Slack notification integration

**Functions**:
- `send_slack_msg(channel, content)`: Sends message to Slack channel
- `send_slack_msg_new(channel, content)`: New implementation of Slack messaging
- `send_discord_msg(_channel, _content)`: Discord integration (placeholder)

**Constants**:
- Channel constants: `DRIVERS_CHANNEL`, `NEW_BOOKING_CHANNEL`, etc.
- Webhook URLs for different environments

### time_utils.py
**Purpose**: Date/time operations and timezone conversions

**Functions**:
- `strfdelta(tdelta, fmt)`: Formats timedelta with custom format
- `strfdelta2(tdelta, fmt)`: Enhanced timedelta formatting with zero-padding
- `get_dt_ist(dt, tm)`: Converts datetime to IST
- `convert_time_to_local(time_str, tz='Asia/Kolkata')`: Converts UTC time to local timezone
- `convert_to_local_time(dt, tz='Asia/Kolkata')`: Converts datetime to target timezone
- `convert_ist_to_utc(time_str, tz='Asia/Kolkata')`: Converts local time to UTC
- `convert_utc_to_ist(time_str, tz='Asia/Kolkata')`: Converts UTC time to local
- `convert_to_utc(date_str, time_str, tz='Asia/Kolkata')`: Converts local date/time to UTC
- `combine_and_convert_to_local(date_val, time_val, tz='Asia/Kolkata')`: Combines date/time and converts to local
- `split_date_time(dt)`: Splits datetime into date and time components
- `convert_datetime_to_utc(date_str, tz='Asia/Kolkata')`: Parses and converts datetime string to UTC
- `convert_time_to_utc(time_str, tz='Asia/Kolkata')`: Converts time string to UTC
- `get_trip_duration(start_time, end_time)`: Calculates trip duration in human-readable format
- `convert_timedelta(duration)`: Converts timedelta to hours:minutes:seconds format
- `ist_to_gmt(ist_time)`: Converts IST to GMT
- `convert_utc_to_local_marker_str(utc_dt, tz)`: Converts UTC to local formatted string
- `parse_duration_to_day_and_time(duration_str)`: Parses duration string to days and time

### user_utils.py
**Purpose**: User management utilities

**Functions**:
- `get_name_by_id(user_id)`: Gets user name by ID
- `isadmin(current_user)`: Checks if user has admin privileges

### validation_utils.py
**Purpose**: Data validation utilities

**Functions**:
- `account_enabled(user)`: Checks if user account is enabled
- `validate_coupon(coupon_code, user)`: Validates coupon code and usage limits

## Auth Subdirectory

### auth/admin_login_utils.py
**Purpose**: Admin authentication and authorization system

**Functions**:
- `check_if_token_revoked(refresh_token)`: Checks if refresh token is blacklisted
- `check_token_revoked(fn)`: Decorator to validate refresh token
- `get_first_non_none_region(form_data, args_data, json_data)`: Extracts region from request data
- `check_access(tab_required, json=False)`: Decorator for role-based access control with tab and region permissions
- `create_token_admin(user, admin_access)`: Creates JWT tokens for admin users

### auth/login_params.py
**Purpose**: Login-related constants

**Classes**:
- `ExpiryTime`: Lock and attempt limits
- `TokenValidity`: Token validation status codes

### auth/login_utils.py
**Purpose**: General authentication utilities

**Functions**:
- `validate_pass(user, pwd, mpwd=True)`: Validates user password with master password support
- `create_token(phone_number, secret_key=None)`: Creates HMAC-based token for phone verification
- `verify_token(phone_number, token, expiration_time=300)`: Verifies phone verification token
- `create_email_token(email, secret_key=None)`: Creates HMAC-based token for email verification
- `verify_email_token(email, token, expiration_time=300)`: Verifies email verification token

**Classes**:
- `TokenValidity`: Token validation status constants

## Driver Subdirectory

### driver/driver_utils.py
**Purpose**: Driver wallet and verification utilities

**Functions**:  
- `compute_driver_wallet(driver_details, total_owed)`: Computes driver wallet balance

## Bookings Subdirectory

### bookings/booking_utils.py
**Purpose**: Booking calculation utilities

**Functions**:
- `distance_on_earth(lat1, long1, lat2, long2)`: Calculates spherical distance between coordinates
- `get_book_code(booking_id)`: Generates and stores booking code
- `is_vip_or_faced_issue(label_value)`: Checks VIP status or issue flags using bit operations
- `check_ongoing_time(booking)`: Calculates booking time windows with buffer

### bookings/booking_params.py
**Purpose**: Comprehensive booking system configuration

**Classes**:
- `Rating`: Rating thresholds and defaults
- `Regions`: Region definitions, boundaries, and utilities
- `CountryBoundary`: Country boundary coordinates
- `BookingParams`: Booking types, limits, and business logic

**Key Features**:
- Region polygon definitions for major cities
- Booking type constants and validation
- B2B partner integration parameters
- Distance and time thresholds
- Car type and transmission mappings

## Profile Subdirectory

### profile/profile_utils.py
**Purpose**: User profile and driver management

**Functions**:
- `account_enabled(user)`: Checks if user account is enabled
- `validate_role(user, role, valid_roles=[])`: Validates user role
- `get_driver_user_id(driver_id)`: Gets user ID from driver ID
- `get_driver_name_from_id(driver_id)`: Gets driver name from driver ID
- `get_ref_code(user_id, mobile)`: Generates referral code
- `get_user_name_from_id(user_id)`: Gets user name from user ID
- `set_user_ref_code(user_id, user_mobile)`: Sets user referral code
- `get_user_ref_code(user_details)`: Gets or creates user referral code
- `get_pic_url(filename)`: Generates picture URL (S3 or local)
- `get_region_info(region_name)`: Gets office address and phone for region

**Constants**:
- `OFFICE_ADDRESSES_AND_PHONE_NUMBERS`: Regional office contact information

## Usage Examples

### Authentication
```python
from utils.auth.admin_login_utils import check_access
from utils.auth.login_utils import create_token, verify_token

# Admin access control
@check_access([1, 2])  # Requires access to tabs 1 and 2
def admin_function():
    pass

# Phone verification
token = create_token("9876543210")
is_valid = verify_token("9876543210", token)
```

### Distance and Location
```python
from utils.distance_utils import get_dist_on_map, point_inside_polygon

# Get driving distance
distance = get_dist_on_map(22.5726, 88.3639, 22.5675, 88.3762)

# Check if location is serviceable
is_serviceable = point_inside_polygon(dest_long, dest_lat, src_long, src_lat, book_type, location_data, city)
```

### Time Operations
```python
from utils.time_utils import convert_to_local_time, get_trip_duration

# Convert UTC to local time
local_time = convert_to_local_time(utc_datetime, 'Asia/Kolkata')

# Get trip duration
duration = get_trip_duration(start_time, end_time)  # Returns "2 hr 30 min"
```

### Redis Operations
```python
from utils.redis_utils import read_redis_data, write_redis_data

# Cache user data
write_redis_data("user:123", {"name": "John", "email": "<EMAIL>"})

# Retrieve cached data
user_data, found = read_redis_data("user:123")
```

## Dependencies

- **Flask**: Web framework and current_app context
- **Redis**: Caching and session management
- **JWT**: Token-based authentication
- **Shapely**: Geometric operations for location validation
- **Pytz**: Timezone handling
- **Boto3**: AWS S3 operations
- **Requests**: HTTP requests for external APIs
- **NumPy**: Mathematical operations

## Security Considerations

- All password operations use SHA512 hashing with salt
- JWT tokens include role-based claims for authorization
- Token blacklisting prevents replay attacks
- Geographic boundaries prevent service abuse
- Rate limiting through Redis-based tracking

## Configuration

Most utilities rely on Flask app configuration for:
- API keys (Google Maps, MapMyIndia)
- AWS credentials and S3 bucket names
- Redis connection parameters
- Slack webhook URLs
- Database connection settings